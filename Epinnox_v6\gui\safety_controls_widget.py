"""
Safety Controls Widget - Phase 3 GUI Component
Dedicated widget for safety monitoring, emergency controls, and ultra-conservative settings
"""

try:
    from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QGridLayout,
                                QLabel, QPushButton, QTextEdit, QProgressBar,
                                QGroupBox, QCheckBox, QSpinBox, QDoubleSpinBox,
                                QTableWidget, QTableWidgetItem, QFrame,
                                QSlider, QComboBox, QLCDNumber)
    from PyQt5.QtCore import Qt, QTimer, pyqtSignal
    from PyQt5.QtGui import QFont, QColor, QPalette
    PYQT_AVAILABLE = True
except ImportError:
    PYQT_AVAILABLE = False

import logging
from typing import Dict, Any, Optional
from datetime import datetime
import json

logger = logging.getLogger(__name__)

if PYQT_AVAILABLE:
    class SafetyControlsWidget(QWidget):
        """Dedicated safety controls and monitoring widget"""
        
        # Signals
        emergency_stop_triggered = pyqtSignal(str)
        safety_setting_changed = pyqtSignal(str, object)
        safety_check_requested = pyqtSignal()
        
        def __init__(self, parent=None):
            super().__init__(parent)
            self.gui_integration = None
            self.safety_metrics = {}
            # Optimized ultra-conservative settings for $50 account
            self.ultra_conservative_settings = {
                'max_total_exposure': 50.0,  # Never exceed account balance
                'max_position_size': 15.0,   # 30% of $50 account
                'max_daily_loss': 5.0,       # 10% of $50 account
                'max_portfolio_risk': 0.015, # 1.5% portfolio risk
                'stop_loss_percentage': 0.008, # 0.8% stop loss
                'take_profit_percentage': 0.016, # 1.6% take profit (2:1 ratio)
                'max_concurrent_positions': 1,   # Only 1 position for small account
                'emergency_stop_loss': 8.0       # Emergency stop at $8 loss
            }
            self.setup_ui()
            self.setup_timers()
            
            logger.info("Safety Controls Widget initialized")
        
        def setup_ui(self):
            """Setup the user interface"""
            main_layout = QVBoxLayout(self)
            
            # Title
            title_label = QLabel("Safety Controls & Ultra-Conservative Settings")
            title_label.setFont(QFont("Arial", 14, QFont.Bold))
            title_label.setAlignment(Qt.AlignCenter)
            main_layout.addWidget(title_label)
            
            # Emergency Controls Section
            emergency_group = self.create_emergency_controls()
            main_layout.addWidget(emergency_group)
            
            # Safety Monitoring Section
            monitoring_group = self.create_safety_monitoring()
            main_layout.addWidget(monitoring_group)
            
            # Ultra-Conservative Settings Section
            settings_group = self.create_ultra_conservative_settings()
            main_layout.addWidget(settings_group)
            
            # Safety Validation Section
            validation_group = self.create_safety_validation()
            main_layout.addWidget(validation_group)
        
        def create_emergency_controls(self):
            """Create emergency controls section"""
            group = QGroupBox("Emergency Controls")
            layout = QVBoxLayout(group)
            
            # Emergency Stop Button
            self.emergency_stop_btn = QPushButton("🚨 EMERGENCY STOP ALL TRADING 🚨")
            self.emergency_stop_btn.clicked.connect(self.emergency_stop_clicked)
            self.emergency_stop_btn.setStyleSheet("""
                QPushButton { 
                    background-color: #ff0000; 
                    color: white; 
                    font-weight: bold; 
                    font-size: 16px;
                    min-height: 50px;
                    border: 3px solid #cc0000;
                    border-radius: 10px;
                }
                QPushButton:hover { 
                    background-color: #cc0000; 
                    border: 3px solid #990000;
                }
                QPushButton:pressed {
                    background-color: #990000;
                }
            """)
            layout.addWidget(self.emergency_stop_btn)
            
            # Emergency Status
            status_layout = QHBoxLayout()
            status_layout.addWidget(QLabel("Emergency Status:"))
            self.emergency_status_label = QLabel("NORMAL")
            self.emergency_status_label.setStyleSheet("color: green; font-weight: bold; font-size: 14px;")
            status_layout.addWidget(self.emergency_status_label)
            layout.addLayout(status_layout)
            
            # Circuit Breaker Status
            breaker_layout = QHBoxLayout()
            breaker_layout.addWidget(QLabel("Circuit Breakers:"))
            self.circuit_breaker_label = QLabel("ACTIVE")
            self.circuit_breaker_label.setStyleSheet("color: green; font-weight: bold;")
            breaker_layout.addWidget(self.circuit_breaker_label)
            layout.addLayout(breaker_layout)
            
            return group
        
        def create_safety_monitoring(self):
            """Create safety monitoring section"""
            group = QGroupBox("Real-Time Safety Monitoring")
            layout = QGridLayout(group)
            
            # Safety metrics display
            self.safety_metric_labels = {}
            
            metrics = [
                ("Total Exposure", "$0.00", "green"),
                ("Daily P&L", "$0.00", "blue"),
                ("Risk Level", "LOW", "green"),
                ("Active Positions", "0", "blue"),
                ("Margin Usage", "0%", "green"),
                ("Last Safety Check", "Never", "orange")
            ]
            
            for i, (metric, default_value, color) in enumerate(metrics):
                row = i // 2
                col = (i % 2) * 2
                
                label = QLabel(f"{metric}:")
                value_label = QLabel(default_value)
                value_label.setStyleSheet(f"color: {color}; font-weight: bold; font-size: 12px;")
                
                layout.addWidget(label, row, col)
                layout.addWidget(value_label, row, col + 1)
                self.safety_metric_labels[metric] = value_label
            
            # Safety Score Display
            score_layout = QHBoxLayout()
            score_layout.addWidget(QLabel("Overall Safety Score:"))
            self.safety_score_lcd = QLCDNumber(3)
            self.safety_score_lcd.setStyleSheet("color: green;")
            self.safety_score_lcd.display(100)
            score_layout.addWidget(self.safety_score_lcd)
            score_layout.addWidget(QLabel("/100"))
            
            layout.addLayout(score_layout, 3, 0, 1, 4)
            
            return group
        
        def create_ultra_conservative_settings(self):
            """Create ultra-conservative settings section"""
            group = QGroupBox("Ultra-Conservative Trading Settings")
            layout = QGridLayout(group)
            
            # Max Total Exposure
            layout.addWidget(QLabel("Max Total Exposure ($):"), 0, 0)
            self.max_exposure_spin = QDoubleSpinBox()
            self.max_exposure_spin.setRange(10.0, 100.0)  # Reduced range for $50 account
            self.max_exposure_spin.setValue(self.ultra_conservative_settings['max_total_exposure'])
            self.max_exposure_spin.valueChanged.connect(lambda v: self.setting_changed('max_total_exposure', v))
            layout.addWidget(self.max_exposure_spin, 0, 1)

            # Max Position Size
            layout.addWidget(QLabel("Max Position Size ($):"), 0, 2)
            self.max_position_spin = QDoubleSpinBox()
            self.max_position_spin.setRange(5.0, 25.0)  # Reduced range for $50 account
            self.max_position_spin.setValue(self.ultra_conservative_settings['max_position_size'])
            self.max_position_spin.valueChanged.connect(lambda v: self.setting_changed('max_position_size', v))
            layout.addWidget(self.max_position_spin, 0, 3)

            # Max Daily Loss
            layout.addWidget(QLabel("Max Daily Loss ($):"), 1, 0)
            self.max_daily_loss_spin = QDoubleSpinBox()
            self.max_daily_loss_spin.setRange(1.0, 15.0)  # Reduced range for $50 account
            self.max_daily_loss_spin.setValue(self.ultra_conservative_settings['max_daily_loss'])
            self.max_daily_loss_spin.valueChanged.connect(lambda v: self.setting_changed('max_daily_loss', v))
            layout.addWidget(self.max_daily_loss_spin, 1, 1)
            
            # Portfolio Risk
            layout.addWidget(QLabel("Max Portfolio Risk (%):"), 1, 2)
            self.portfolio_risk_spin = QDoubleSpinBox()
            self.portfolio_risk_spin.setRange(0.1, 3.0)  # Reduced range for ultra-conservative
            self.portfolio_risk_spin.setValue(self.ultra_conservative_settings['max_portfolio_risk'] * 100)
            self.portfolio_risk_spin.setSuffix("%")
            self.portfolio_risk_spin.valueChanged.connect(lambda v: self.setting_changed('max_portfolio_risk', v/100))
            layout.addWidget(self.portfolio_risk_spin, 1, 3)

            # Stop Loss
            layout.addWidget(QLabel("Stop Loss (%):"), 2, 0)
            self.stop_loss_spin = QDoubleSpinBox()
            self.stop_loss_spin.setRange(0.1, 2.0)  # Reduced range for tight control
            self.stop_loss_spin.setValue(self.ultra_conservative_settings['stop_loss_percentage'] * 100)
            self.stop_loss_spin.setSuffix("%")
            self.stop_loss_spin.valueChanged.connect(lambda v: self.setting_changed('stop_loss_percentage', v/100))
            layout.addWidget(self.stop_loss_spin, 2, 1)

            # Take Profit
            layout.addWidget(QLabel("Take Profit (%):"), 2, 2)
            self.take_profit_spin = QDoubleSpinBox()
            self.take_profit_spin.setRange(0.1, 5.0)  # Reduced range for conservative targets
            self.take_profit_spin.setValue(self.ultra_conservative_settings['take_profit_percentage'] * 100)
            self.take_profit_spin.setSuffix("%")
            self.take_profit_spin.valueChanged.connect(lambda v: self.setting_changed('take_profit_percentage', v/100))
            layout.addWidget(self.take_profit_spin, 2, 3)
            
            # Max Concurrent Positions
            layout.addWidget(QLabel("Max Concurrent Positions:"), 3, 0)
            self.max_positions_spin = QSpinBox()
            self.max_positions_spin.setRange(1, 2)  # Limited to 1-2 for $50 account
            self.max_positions_spin.setValue(self.ultra_conservative_settings['max_concurrent_positions'])
            self.max_positions_spin.valueChanged.connect(lambda v: self.setting_changed('max_concurrent_positions', v))
            layout.addWidget(self.max_positions_spin, 3, 1)

            # Emergency Stop Loss
            layout.addWidget(QLabel("Emergency Stop Loss ($):"), 3, 2)
            self.emergency_stop_spin = QDoubleSpinBox()
            self.emergency_stop_spin.setRange(5.0, 20.0)  # Reduced range for $50 account
            self.emergency_stop_spin.setValue(self.ultra_conservative_settings['emergency_stop_loss'])
            self.emergency_stop_spin.valueChanged.connect(lambda v: self.setting_changed('emergency_stop_loss', v))
            layout.addWidget(self.emergency_stop_spin, 3, 3)
            
            # Apply Settings Button
            apply_btn = QPushButton("Apply Ultra-Conservative Settings")
            apply_btn.clicked.connect(self.apply_settings)
            apply_btn.setStyleSheet("QPushButton { background-color: #4CAF50; color: white; font-weight: bold; }")
            layout.addWidget(apply_btn, 4, 0, 1, 4)
            
            return group
        
        def create_safety_validation(self):
            """Create safety validation section"""
            group = QGroupBox("Safety System Validation")
            layout = QVBoxLayout(group)
            
            # Validation buttons
            button_layout = QHBoxLayout()
            
            self.run_safety_check_btn = QPushButton("Run Safety Check")
            self.run_safety_check_btn.clicked.connect(self.run_safety_check)
            button_layout.addWidget(self.run_safety_check_btn)
            
            self.validate_settings_btn = QPushButton("Validate Settings")
            self.validate_settings_btn.clicked.connect(self.validate_settings)
            button_layout.addWidget(self.validate_settings_btn)
            
            self.test_emergency_btn = QPushButton("Test Emergency Procedures")
            self.test_emergency_btn.clicked.connect(self.test_emergency_procedures)
            button_layout.addWidget(self.test_emergency_btn)
            
            layout.addLayout(button_layout)
            
            # Validation results
            self.validation_results = QTextEdit()
            self.validation_results.setMaximumHeight(100)
            self.validation_results.setReadOnly(True)
            layout.addWidget(self.validation_results)
            
            return group
        
        def setup_timers(self):
            """Setup update timers with thread safety"""
            from PyQt5.QtCore import QMetaObject, Qt
            from PyQt5.QtWidgets import QApplication

            # Safety monitoring timer
            self.safety_timer = QTimer()
            self.safety_timer.timeout.connect(self.update_safety_metrics)
            self.safety_timer.setSingleShot(False)

            # Start timer on main thread
            if QApplication.instance() is not None:
                QMetaObject.invokeMethod(self.safety_timer, "start", Qt.QueuedConnection, 2000)
            else:
                self.safety_timer.start(2000)  # Fallback
        
        def set_gui_integration(self, gui_integration):
            """Set the GUI integration instance"""
            self.gui_integration = gui_integration
            if gui_integration:
                # Connect to safety alerts
                gui_integration.safety_alert.connect(self.on_safety_alert)
        
        # Event handlers
        def emergency_stop_clicked(self):
            """Handle emergency stop button click"""
            reason = "Manual emergency stop from safety controls"
            self.emergency_stop_triggered.emit(reason)
            
            # Update UI
            self.emergency_status_label.setText("EMERGENCY STOP ACTIVE")
            self.emergency_status_label.setStyleSheet("color: red; font-weight: bold; font-size: 14px;")
            self.emergency_stop_btn.setEnabled(False)
            
            # Log the event
            self.log_safety_event(f"EMERGENCY STOP: {reason}")
        
        def setting_changed(self, setting_name, value):
            """Handle setting change"""
            self.ultra_conservative_settings[setting_name] = value
            self.safety_setting_changed.emit(setting_name, value)
            self.log_safety_event(f"Setting changed: {setting_name} = {value}")
        
        def apply_settings(self):
            """Apply ultra-conservative settings"""
            if self.gui_integration:
                # Apply settings through GUI integration
                # This would update the autonomous orchestrator configuration
                self.log_safety_event("Ultra-conservative settings applied")
            else:
                self.log_safety_event("Settings applied locally (no integration)")
        
        def run_safety_check(self):
            """Run comprehensive safety check"""
            self.safety_check_requested.emit()
            if self.gui_integration:
                self.gui_integration.run_safety_system_validation()
            self.log_safety_event("Safety check initiated")
        
        def validate_settings(self):
            """Validate current settings"""
            # Validate ultra-conservative settings
            issues = []
            
            if self.ultra_conservative_settings['max_total_exposure'] > 200:
                issues.append("Total exposure too high for ultra-conservative mode")
            
            if self.ultra_conservative_settings['max_position_size'] > 50:
                issues.append("Position size too high for ultra-conservative mode")
            
            if self.ultra_conservative_settings['max_portfolio_risk'] > 0.05:
                issues.append("Portfolio risk too high for ultra-conservative mode")
            
            if issues:
                self.log_safety_event(f"Validation issues: {'; '.join(issues)}")
            else:
                self.log_safety_event("All settings validated successfully")
        
        def test_emergency_procedures(self):
            """Test emergency procedures"""
            self.log_safety_event("Testing emergency procedures...")
            # This would trigger a test of emergency stop mechanisms
            if self.gui_integration:
                # Test emergency procedures without actually stopping trading
                self.log_safety_event("Emergency procedures test completed")
        
        def on_safety_alert(self, level, message):
            """Handle safety alerts"""
            self.log_safety_event(f"ALERT ({level}): {message}")
            
            # Update safety score based on alert level
            if level == "CRITICAL":
                self.safety_score_lcd.setStyleSheet("color: red;")
                self.safety_score_lcd.display(0)
            elif level == "WARNING":
                self.safety_score_lcd.setStyleSheet("color: orange;")
        
        def update_safety_metrics(self):
            """Update safety metrics display"""
            if self.gui_integration:
                metrics = self.gui_integration.get_safety_metrics()
                
                # Update metric labels
                for metric, value in metrics.items():
                    if metric in self.safety_metric_labels:
                        self.safety_metric_labels[metric].setText(str(value))
                
                # Update last safety check time
                self.safety_metric_labels["Last Safety Check"].setText(
                    datetime.now().strftime("%H:%M:%S")
                )
        
        def log_safety_event(self, message):
            """Log safety event"""
            timestamp = datetime.now().strftime("%H:%M:%S")
            self.validation_results.append(f"[{timestamp}] {message}")
        
        def get_ultra_conservative_settings(self):
            """Get current ultra-conservative settings"""
            return self.ultra_conservative_settings.copy()

else:
    # Fallback class when PyQt5 is not available
    class SafetyControlsWidget:
        def __init__(self, parent=None):
            logger.warning("PyQt5 not available - SafetyControlsWidget disabled")
