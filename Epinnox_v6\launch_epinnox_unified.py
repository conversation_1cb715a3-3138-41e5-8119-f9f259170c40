#!/usr/bin/env python3
"""
EPINNOX UNIFIED LAUNCHER
Single master script for all autonomous trading functionality

USAGE:
    python launch_epinnox_unified.py                                    # GUI with paper trading
    python launch_epinnox_unified.py --live --risk ultra-conservative   # GUI with live trading
    python launch_epinnox_unified.py --paper                           # GUI with paper trading
    python launch_epinnox_unified.py --simulation                      # GUI with simulation only
    python launch_epinnox_unified.py --gui-only                        # GUI monitoring only
    python launch_epinnox_unified.py --live --headless                 # Headless live trading
"""

import sys
import os
import asyncio
import argparse
import logging
from datetime import datetime
from typing import Optional, Dict, Any

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Configure Unicode-safe logging
try:
    from core.unicode_safe_logging import configure_unicode_safe_logging
    configure_unicode_safe_logging()
except ImportError:
    pass

logger = logging.getLogger(__name__)

# Check PyQt5 availability
try:
    from PyQt5.QtWidgets import QApplication
    from PyQt5.QtCore import QTimer
    PYQT_AVAILABLE = True
except ImportError:
    PYQT_AVAILABLE = False

class EpinnoxUnifiedLauncher:
    """
    Unified launcher for all Epinnox autonomous trading functionality
    """
    
    def __init__(self, args):
        self.args = args
        self.mode = args.mode
        self.risk_level = args.risk
        self.headless = args.headless
        self.max_capital = args.capital
        
        # Component references
        self.gui_app = None
        self.dashboard = None
        self.autonomous_trader = None
        self.components = {}
        
        logger.info(f"Epinnox Unified Launcher initialized - Mode: {self.mode}, Risk: {self.risk_level}")
    
    async def initialize_core_components(self):
        """Initialize all core trading components"""
        
        try:
            logger.info("Initializing core components...")
            
            # Initialize risk management
            from core.dynamic_risk_manager import dynamic_risk_manager, RiskLevel
            risk_level_map = {
                'ultra-conservative': RiskLevel.ULTRA_CONSERVATIVE,
                'conservative': RiskLevel.CONSERVATIVE,
                'moderate': RiskLevel.MODERATE,
                'aggressive': RiskLevel.AGGRESSIVE,
                'high-risk': RiskLevel.HIGH_RISK
            }
            
            dynamic_risk_manager.set_risk_level(
                risk_level_map.get(self.risk_level, RiskLevel.ULTRA_CONSERVATIVE),
                "Unified launcher initialization"
            )
            self.components['risk_manager'] = dynamic_risk_manager
            
            # Initialize emergency coordinator
            from core.emergency_stop_coordinator import emergency_coordinator
            self.components['emergency_coordinator'] = emergency_coordinator
            
            # Initialize execution engine
            from core.unified_execution_engine import unified_execution_engine
            self.components['execution_engine'] = unified_execution_engine
            
            # Initialize timer coordinator
            from core.timer_coordinator import timer_coordinator
            self.components['timer_coordinator'] = timer_coordinator
            
            # Initialize ScalperGPT
            from core.scalper_gpt import ScalperGPT
            self.components['scalper_gpt'] = ScalperGPT()
            
            logger.info("Core components initialized successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to initialize core components: {e}")
            return False
    
    async def create_autonomous_trader(self):
        """Create autonomous trader based on mode"""
        
        try:
            logger.info(f"Creating autonomous trader for {self.mode} mode...")
            
            # Create integrated autonomous trader
            from core.autonomous_trading_orchestrator import AutonomousTradingOrchestrator, TradingMode
            
            # Map mode to TradingMode
            mode_map = {
                'live': TradingMode.LIVE,
                'paper': TradingMode.PAPER,
                'simulation': TradingMode.SIMULATION
            }
            
            trading_mode = mode_map.get(self.mode, TradingMode.PAPER)
            
            # Create configuration
            config = {
                'trading_mode': trading_mode,
                'max_trading_capital': self.max_capital,
                'position_size_pct': 1.0 if self.risk_level == 'ultra-conservative' else 2.0,
                'portfolio_risk_pct': 2.0,
                'validation_mode': True
            }
            
            # Create orchestrator
            self.autonomous_trader = AutonomousTradingOrchestrator(trading_mode, config)
            
            # Initialize orchestrator
            await self.autonomous_trader.initialize()
            
            logger.info("Autonomous trader created successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to create autonomous trader: {e}")
            return False
    
    def create_gui_application(self):
        """Create GUI application"""
        
        if not PYQT_AVAILABLE:
            logger.error("PyQt5 not available - cannot create GUI")
            return False
        
        try:
            logger.info("Creating GUI application...")
            
            # Create QApplication
            self.gui_app = QApplication(sys.argv)
            self.gui_app.setApplicationName("Epinnox Unified Trading System")
            self.gui_app.setApplicationVersion("6.0")
            
            # Apply Matrix theme
            from gui.matrix_theme import MatrixTheme
            self.gui_app.setStyleSheet(MatrixTheme.get_stylesheet())
            
            # Create enhanced monitoring dashboard
            from gui.integrated_monitoring_dashboard import IntegratedMonitoringDashboard
            self.dashboard = IntegratedMonitoringDashboard()
            
            # Integrate autonomous trading controls
            self.integrate_autonomous_controls()
            
            logger.info("GUI application created successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to create GUI application: {e}")
            return False
    
    def integrate_autonomous_controls(self):
        """Integrate autonomous trading controls into dashboard"""
        
        try:
            logger.info("Integrating autonomous trading controls...")
            
            # Import autonomous trading controls
            from gui.autonomous_trading_controls import AutonomousTradingControlPanel
            
            # Create control panel
            control_panel = AutonomousTradingControlPanel()
            
            # Connect signals
            control_panel.trading_started.connect(self.on_trading_started)
            control_panel.trading_stopped.connect(self.on_trading_stopped)
            control_panel.mode_changed.connect(self.on_mode_changed)
            control_panel.risk_level_changed.connect(self.on_risk_level_changed)
            
            # Add to dashboard (this would need to be implemented in the dashboard)
            if hasattr(self.dashboard, 'add_autonomous_controls'):
                self.dashboard.add_autonomous_controls(control_panel)
            
            logger.info("Autonomous trading controls integrated successfully")
            
        except Exception as e:
            logger.error(f"Failed to integrate autonomous controls: {e}")
    
    def on_trading_started(self):
        """Handle trading started signal"""
        logger.info("Autonomous trading started from GUI")
    
    def on_trading_stopped(self):
        """Handle trading stopped signal"""
        logger.info("Autonomous trading stopped from GUI")
    
    def on_mode_changed(self, new_mode):
        """Handle mode change signal"""
        logger.info(f"Trading mode changed to: {new_mode}")
        self.mode = new_mode
    
    def on_risk_level_changed(self, new_risk_level):
        """Handle risk level change signal"""
        logger.info(f"Risk level changed to: {new_risk_level}")
        self.risk_level = new_risk_level
        
        # Update risk manager
        if 'risk_manager' in self.components:
            from core.dynamic_risk_manager import RiskLevel
            risk_level_map = {
                'ultra-conservative': RiskLevel.ULTRA_CONSERVATIVE,
                'conservative': RiskLevel.CONSERVATIVE,
                'moderate': RiskLevel.MODERATE,
                'aggressive': RiskLevel.AGGRESSIVE,
                'high-risk': RiskLevel.HIGH_RISK
            }
            
            self.components['risk_manager'].set_risk_level(
                risk_level_map.get(new_risk_level, RiskLevel.ULTRA_CONSERVATIVE),
                "GUI risk level change"
            )
    
    async def start_unified_system(self):
        """Start the unified system based on mode"""
        
        try:
            logger.info(f"Starting unified system in {self.mode} mode...")
            
            # Initialize core components
            if not await self.initialize_core_components():
                return False
            
            # Create autonomous trader if not GUI-only mode
            if self.mode != 'gui-only':
                if not await self.create_autonomous_trader():
                    return False
            
            # Start GUI or headless mode
            if self.headless:
                return await self.start_headless_mode()
            else:
                return await self.start_gui_mode()
                
        except Exception as e:
            logger.error(f"Failed to start unified system: {e}")
            return False
    
    async def start_gui_mode(self):
        """Start GUI mode"""
        
        try:
            logger.info("Starting GUI mode...")
            
            # Create GUI application
            if not self.create_gui_application():
                return False
            
            # Show dashboard
            self.dashboard.show()
            
            # Start autonomous trading if enabled
            if self.mode != 'gui-only' and self.autonomous_trader:
                success = await self.autonomous_trader.start_autonomous_trading()
                if success:
                    logger.info("Autonomous trading started successfully")
                else:
                    logger.warning("Failed to start autonomous trading")
            
            logger.info("GUI mode started successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to start GUI mode: {e}")
            return False
    
    async def start_headless_mode(self):
        """Start headless mode"""
        
        try:
            logger.info("Starting headless mode...")
            
            if not self.autonomous_trader:
                logger.error("No autonomous trader available for headless mode")
                return False
            
            # Start autonomous trading
            success = await self.autonomous_trader.start_autonomous_trading()
            
            if success:
                logger.info("Headless autonomous trading started successfully")
                
                # Keep running until interrupted
                try:
                    while self.autonomous_trader.autonomous_trading_active:
                        await asyncio.sleep(10)
                        
                        # Log periodic status
                        if hasattr(self.autonomous_trader, 'get_performance_metrics'):
                            metrics = self.autonomous_trader.get_performance_metrics()
                            if metrics:
                                logger.info(f"Trading status - Trades: {metrics.get('total_trades', 0)}")
                        
                except KeyboardInterrupt:
                    logger.info("Stopping headless trading...")
                    await self.autonomous_trader.stop_autonomous_trading()
                
                return True
            else:
                logger.error("Failed to start headless autonomous trading")
                return False
                
        except Exception as e:
            logger.error(f"Failed to start headless mode: {e}")
            return False
    
    def run_gui_event_loop(self):
        """Run the GUI event loop"""
        
        if self.gui_app:
            logger.info("Starting GUI event loop...")
            return self.gui_app.exec_()
        else:
            logger.error("No GUI application to run")
            return 1
    
    async def shutdown(self):
        """Shutdown the unified system"""
        
        try:
            logger.info("Shutting down unified system...")
            
            # Stop autonomous trading
            if self.autonomous_trader:
                await self.autonomous_trader.stop_autonomous_trading()
            
            # Stop timer coordinator
            if 'timer_coordinator' in self.components:
                if hasattr(self.components['timer_coordinator'], 'stop'):
                    await self.components['timer_coordinator'].stop()
            
            logger.info("Unified system shutdown complete")
            
        except Exception as e:
            logger.error(f"Error during shutdown: {e}")

def parse_arguments():
    """Parse command-line arguments"""
    
    parser = argparse.ArgumentParser(
        description="Epinnox Unified Autonomous Trading Launcher",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python launch_epinnox_unified.py                                    # GUI with paper trading
  python launch_epinnox_unified.py --live --risk ultra-conservative   # GUI with live trading
  python launch_epinnox_unified.py --paper                           # GUI with paper trading
  python launch_epinnox_unified.py --simulation                      # GUI with simulation only
  python launch_epinnox_unified.py --gui-only                        # GUI monitoring only
  python launch_epinnox_unified.py --live --headless                 # Headless live trading
        """
    )
    
    parser.add_argument('--mode', 
                       choices=['gui', 'live', 'paper', 'simulation', 'gui-only'], 
                       default='paper',
                       help='Launch mode (default: paper)')
    
    parser.add_argument('--risk', 
                       choices=['ultra-conservative', 'conservative', 'moderate', 'aggressive', 'high-risk'], 
                       default='ultra-conservative',
                       help='Risk level (default: ultra-conservative)')
    
    parser.add_argument('--capital', 
                       type=float, 
                       default=100.0,
                       help='Maximum trading capital in USDT (default: 100.0)')
    
    parser.add_argument('--headless', 
                       action='store_true',
                       help='Run without GUI (headless mode)')
    
    parser.add_argument('--config', 
                       type=str,
                       help='Configuration file path')
    
    # Convenience flags
    parser.add_argument('--live', 
                       action='store_const', 
                       const='live', 
                       dest='mode',
                       help='Start in live trading mode')
    
    parser.add_argument('--paper', 
                       action='store_const', 
                       const='paper', 
                       dest='mode',
                       help='Start in paper trading mode')
    
    parser.add_argument('--simulation', 
                       action='store_const', 
                       const='simulation', 
                       dest='mode',
                       help='Start in simulation mode')
    
    return parser.parse_args()

async def main():
    """Main launcher function"""
    
    try:
        # Parse arguments
        args = parse_arguments()
        
        # Display banner
        print("🚀 EPINNOX UNIFIED AUTONOMOUS TRADING LAUNCHER")
        print("=" * 60)
        print(f"Mode: {args.mode.upper()}")
        print(f"Risk Level: {args.risk.upper()}")
        print(f"Max Capital: ${args.capital:.2f} USDT")
        print(f"Headless: {'YES' if args.headless else 'NO'}")
        print("=" * 60)
        
        # Create launcher
        launcher = EpinnoxUnifiedLauncher(args)
        
        # Start unified system
        success = await launcher.start_unified_system()
        
        if success:
            if not args.headless:
                # Run GUI event loop
                exit_code = launcher.run_gui_event_loop()
            else:
                # Headless mode already handles its own loop
                exit_code = 0
        else:
            print("❌ Failed to start unified system")
            exit_code = 1
        
        # Shutdown
        await launcher.shutdown()
        
        return exit_code
        
    except KeyboardInterrupt:
        print("\n⏹️ Launcher interrupted by user")
        return 0
    except Exception as e:
        print(f"\n❌ LAUNCHER ERROR: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
