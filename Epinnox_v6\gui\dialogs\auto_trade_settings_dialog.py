"""
Auto Trade Settings Dialog

This dialog allows users to modify auto trade parameters while the script is running.
"""

import os
import yaml
from PySide6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QLabel, QLineEdit,
    QPushButton, QTabWidget, QWidget, QFormLayout, QSpinBox,
    QDoubleSpinBox, QCheckBox, QComboBox, QGroupBox, QMessageBox,
    QGridLayout, QRadioButton
)
from PySide6.QtCore import Qt, Signal

class AutoTradeSettingsDialog(QDialog):
    """Dialog for modifying auto trade settings."""

    # Signal emitted when settings are applied
    settings_applied = Signal(dict)

    def __init__(self, parent=None, config=None):
        super().__init__(parent)
        self.setWindowTitle("Auto Trade Settings")
        self.setMinimumWidth(500)
        self.setMinimumHeight(550)  # Reduced height
        self.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                margin-top: 6px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 7px;
                padding: 0px 5px 0px 5px;
            }
            QCheckBox {
                spacing: 2px;
            }
            QRadioButton {
                spacing: 2px;
            }
        """)

        # Store the current configuration
        self.config = config or {}

        # Initialize UI
        self.init_ui()

        # Load current settings
        self.load_settings()

        # Initialize UI state based on loaded settings
        self.update_signal_mode_ui()

    def init_ui(self):
        """Initialize the dialog UI."""
        # Main layout
        main_layout = QVBoxLayout(self)

        # Create tabs
        self.tabs = QTabWidget()

        # Create tabs for different setting categories
        self.strategy_tab = QWidget()
        self.trading_tab = QWidget()

        # Add tabs to tab widget
        self.tabs.addTab(self.strategy_tab, "Strategy Parameters")
        self.tabs.addTab(self.trading_tab, "Trading Parameters")

        # Initialize tab contents
        self.init_strategy_tab()
        self.init_trading_tab()

        # Add tabs to main layout
        main_layout.addWidget(self.tabs)

        # Buttons layout
        buttons_layout = QHBoxLayout()

        # Apply button
        self.apply_btn = QPushButton("Apply")
        self.apply_btn.clicked.connect(self.apply_settings)

        # Save button
        self.save_btn = QPushButton("Save")
        self.save_btn.clicked.connect(self.save_settings)

        # Cancel button
        self.cancel_btn = QPushButton("Cancel")
        self.cancel_btn.clicked.connect(self.reject)

        # Add buttons to layout
        buttons_layout.addWidget(self.apply_btn)
        buttons_layout.addWidget(self.save_btn)
        buttons_layout.addWidget(self.cancel_btn)

        # Add buttons layout to main layout
        main_layout.addLayout(buttons_layout)

    def init_strategy_tab(self):
        """Initialize the strategy parameters tab."""
        layout = QVBoxLayout(self.strategy_tab)

        # Indicator toggles group
        indicator_group = QGroupBox("Indicator Toggles")
        indicator_layout = QGridLayout(indicator_group)
        indicator_layout.setVerticalSpacing(2)
        indicator_layout.setHorizontalSpacing(10)

        # Create checkboxes for each indicator and add them directly to the grid
        # First column
        self.use_ema_cb = QCheckBox("EMA")
        indicator_layout.addWidget(self.use_ema_cb, 0, 0)

        self.use_rsi_cb = QCheckBox("RSI")
        indicator_layout.addWidget(self.use_rsi_cb, 0, 1)

        self.use_ema_slope_cb = QCheckBox("EMA Slope")
        indicator_layout.addWidget(self.use_ema_slope_cb, 0, 2)

        # Second row
        self.use_atr_cb = QCheckBox("ATR")
        indicator_layout.addWidget(self.use_atr_cb, 1, 0)

        self.use_macd_cb = QCheckBox("MACD")
        indicator_layout.addWidget(self.use_macd_cb, 1, 1)

        self.use_price_velocity_cb = QCheckBox("Price Velocity")
        indicator_layout.addWidget(self.use_price_velocity_cb, 1, 2)

        # Third row
        self.use_atr_breakout_cb = QCheckBox("ATR Breakout")
        indicator_layout.addWidget(self.use_atr_breakout_cb, 2, 0)

        self.use_band_break_cb = QCheckBox("Band Break")
        indicator_layout.addWidget(self.use_band_break_cb, 2, 1)

        self.use_wick_rejection_cb = QCheckBox("Wick Rejection")
        indicator_layout.addWidget(self.use_wick_rejection_cb, 2, 2)

        # Fourth row
        self.use_band_width_cb = QCheckBox("Band Width")
        indicator_layout.addWidget(self.use_band_width_cb, 3, 0)

        self.use_htf_alignment_cb = QCheckBox("HTF Alignment")
        indicator_layout.addWidget(self.use_htf_alignment_cb, 3, 1)

        # Set column stretching
        indicator_layout.setColumnStretch(0, 1)
        indicator_layout.setColumnStretch(1, 1)
        indicator_layout.setColumnStretch(2, 1)

        # Add the indicator group to the main layout
        layout.addWidget(indicator_group)

        # Signal Mode group
        signal_mode_group = QGroupBox("Signal Generation Mode")
        signal_mode_layout = QHBoxLayout(signal_mode_group)
        signal_mode_layout.setSpacing(20)

        # Signal mode radio buttons
        self.threshold_mode_rb = QRadioButton("Threshold Mode")
        self.crossover_mode_rb = QRadioButton("Band Crossover Mode")

        # Add radio buttons to layout
        signal_mode_layout.addWidget(self.threshold_mode_rb)
        signal_mode_layout.addWidget(self.crossover_mode_rb)
        signal_mode_layout.addStretch(1)  # Add stretch to push buttons to the left

        # Connect radio buttons to enable/disable appropriate controls
        self.threshold_mode_rb.toggled.connect(self.update_signal_mode_ui)
        self.crossover_mode_rb.toggled.connect(self.update_signal_mode_ui)

        # Add the signal mode group to the layout
        layout.addWidget(signal_mode_group)

        # Threshold Mode parameters group
        self.threshold_group = QGroupBox("Threshold Mode Parameters")
        threshold_layout = QGridLayout(self.threshold_group)
        threshold_layout.setVerticalSpacing(5)
        threshold_layout.setHorizontalSpacing(10)

        # Buy threshold
        threshold_layout.addWidget(QLabel("Buy Threshold:"), 0, 0)
        self.buy_threshold_sb = QDoubleSpinBox()
        self.buy_threshold_sb.setRange(0.1, 1.0)
        self.buy_threshold_sb.setSingleStep(0.05)
        self.buy_threshold_sb.setDecimals(2)
        self.buy_threshold_sb.setValue(0.6)
        threshold_layout.addWidget(self.buy_threshold_sb, 0, 1)

        # Sell threshold
        threshold_layout.addWidget(QLabel("Sell Threshold:"), 0, 2)
        self.sell_threshold_sb = QDoubleSpinBox()
        self.sell_threshold_sb.setRange(0.0, 0.9)
        self.sell_threshold_sb.setSingleStep(0.05)
        self.sell_threshold_sb.setDecimals(2)
        self.sell_threshold_sb.setValue(0.4)
        threshold_layout.addWidget(self.sell_threshold_sb, 0, 3)

        # Set column stretching
        threshold_layout.setColumnStretch(0, 1)
        threshold_layout.setColumnStretch(1, 1)
        threshold_layout.setColumnStretch(2, 1)
        threshold_layout.setColumnStretch(3, 1)

        # Add the threshold group to the layout
        layout.addWidget(self.threshold_group)

        # Crossover Mode parameters group
        self.crossover_group = QGroupBox("Band Crossover Mode Parameters")
        crossover_layout = QGridLayout(self.crossover_group)
        crossover_layout.setVerticalSpacing(5)
        crossover_layout.setHorizontalSpacing(10)

        # Direction controls in the first row
        # Enable long crossovers
        self.enable_long_crossovers_cb = QCheckBox("Long Signals")
        self.enable_long_crossovers_cb.setChecked(True)
        crossover_layout.addWidget(self.enable_long_crossovers_cb, 0, 0)

        # Enable short crossovers
        self.enable_short_crossovers_cb = QCheckBox("Short Signals")
        self.enable_short_crossovers_cb.setChecked(True)
        crossover_layout.addWidget(self.enable_short_crossovers_cb, 0, 1)

        # RSI filter
        self.use_rsi_filter_cb = QCheckBox("RSI Filter (<30 long, >70 short)")
        self.use_rsi_filter_cb.setChecked(True)
        crossover_layout.addWidget(self.use_rsi_filter_cb, 1, 0, 1, 2)  # Span two columns

        # Band selection section header
        crossover_layout.addWidget(QLabel("Long Signal Bands:"), 2, 0)
        crossover_layout.addWidget(QLabel("Short Signal Bands:"), 2, 1)

        # Create a grid for band selection
        # Long signal bands
        long_bands_layout = QGridLayout()
        long_bands_layout.setVerticalSpacing(2)

        # Create checkboxes for each band for long signals
        self.use_ema_cross_cb = QCheckBox("EMA (Center)")
        self.use_ema_cross_cb.setChecked(False)
        long_bands_layout.addWidget(self.use_ema_cross_cb, 0, 0)

        self.use_ema_minus_1_cross_cb = QCheckBox("EMA - 1 ATR")
        self.use_ema_minus_1_cross_cb.setChecked(False)
        long_bands_layout.addWidget(self.use_ema_minus_1_cross_cb, 1, 0)

        self.use_ema_minus_2_cross_cb = QCheckBox("EMA - 2 ATR")
        self.use_ema_minus_2_cross_cb.setChecked(True)
        long_bands_layout.addWidget(self.use_ema_minus_2_cross_cb, 2, 0)

        self.use_ema_minus_3_cross_cb = QCheckBox("EMA - 3 ATR")
        self.use_ema_minus_3_cross_cb.setChecked(True)
        long_bands_layout.addWidget(self.use_ema_minus_3_cross_cb, 3, 0)

        # Add long bands layout to crossover layout
        crossover_layout.addLayout(long_bands_layout, 3, 0)

        # Short signal bands
        short_bands_layout = QGridLayout()
        short_bands_layout.setVerticalSpacing(2)

        # Create checkboxes for each band for short signals
        # Note: EMA center line is shared between long and short, but we'll duplicate it for UI clarity
        self.use_ema_plus_1_cross_cb = QCheckBox("EMA + 1 ATR")
        self.use_ema_plus_1_cross_cb.setChecked(False)
        short_bands_layout.addWidget(self.use_ema_plus_1_cross_cb, 1, 0)

        self.use_ema_plus_2_cross_cb = QCheckBox("EMA + 2 ATR")
        self.use_ema_plus_2_cross_cb.setChecked(True)
        short_bands_layout.addWidget(self.use_ema_plus_2_cross_cb, 2, 0)

        self.use_ema_plus_3_cross_cb = QCheckBox("EMA + 3 ATR")
        self.use_ema_plus_3_cross_cb.setChecked(True)
        short_bands_layout.addWidget(self.use_ema_plus_3_cross_cb, 3, 0)

        # Add short bands layout to crossover layout
        crossover_layout.addLayout(short_bands_layout, 3, 1)

        # Add the crossover group to the layout
        layout.addWidget(self.crossover_group)

        # ATR-EMA Bands parameters group
        atr_ema_group = QGroupBox("ATR-EMA Bands Parameters")
        atr_ema_layout = QGridLayout(atr_ema_group)
        atr_ema_layout.setVerticalSpacing(5)
        atr_ema_layout.setHorizontalSpacing(10)

        # First column - Basic parameters
        # ATR period
        atr_ema_layout.addWidget(QLabel("ATR Period:"), 0, 0)
        self.atr_period_sb = QSpinBox()
        self.atr_period_sb.setRange(1, 100)
        self.atr_period_sb.setValue(14)
        atr_ema_layout.addWidget(self.atr_period_sb, 0, 1)

        # EMA period
        atr_ema_layout.addWidget(QLabel("EMA Period:"), 1, 0)
        self.ema_period_sb = QSpinBox()
        self.ema_period_sb.setRange(1, 100)
        self.ema_period_sb.setValue(20)
        atr_ema_layout.addWidget(self.ema_period_sb, 1, 1)

        # Lookback periods
        atr_ema_layout.addWidget(QLabel("Lookback:"), 2, 0)
        self.lookback_periods_sb = QSpinBox()
        self.lookback_periods_sb.setRange(1, 20)
        self.lookback_periods_sb.setValue(3)
        atr_ema_layout.addWidget(self.lookback_periods_sb, 2, 1)

        # Second column - EMA Slope parameters
        # EMA slope periods
        atr_ema_layout.addWidget(QLabel("EMA Slope Periods:"), 0, 2)
        self.ema_slope_periods_sb = QSpinBox()
        self.ema_slope_periods_sb.setRange(1, 50)
        self.ema_slope_periods_sb.setValue(10)
        atr_ema_layout.addWidget(self.ema_slope_periods_sb, 0, 3)

        # EMA slope weight
        atr_ema_layout.addWidget(QLabel("EMA Slope Weight:"), 1, 2)
        self.ema_slope_weight_sb = QDoubleSpinBox()
        self.ema_slope_weight_sb.setRange(0.0, 1.0)
        self.ema_slope_weight_sb.setSingleStep(0.05)
        self.ema_slope_weight_sb.setDecimals(2)
        self.ema_slope_weight_sb.setValue(0.5)
        atr_ema_layout.addWidget(self.ema_slope_weight_sb, 1, 3)

        # ATR breakout multiplier
        atr_ema_layout.addWidget(QLabel("ATR Breakout Mult:"), 2, 2)
        self.atr_breakout_multiplier_sb = QDoubleSpinBox()
        self.atr_breakout_multiplier_sb.setRange(0.5, 5.0)
        self.atr_breakout_multiplier_sb.setSingleStep(0.1)
        self.atr_breakout_multiplier_sb.setDecimals(1)
        self.atr_breakout_multiplier_sb.setValue(1.5)
        atr_ema_layout.addWidget(self.atr_breakout_multiplier_sb, 2, 3)

        # ATR breakout weight
        atr_ema_layout.addWidget(QLabel("ATR Breakout Weight:"), 3, 2)
        self.atr_breakout_weight_sb = QDoubleSpinBox()
        self.atr_breakout_weight_sb.setRange(0.0, 1.0)
        self.atr_breakout_weight_sb.setSingleStep(0.05)
        self.atr_breakout_weight_sb.setDecimals(2)
        self.atr_breakout_weight_sb.setValue(0.1)
        atr_ema_layout.addWidget(self.atr_breakout_weight_sb, 3, 3)

        # Set column stretching
        atr_ema_layout.setColumnStretch(0, 1)
        atr_ema_layout.setColumnStretch(1, 1)
        atr_ema_layout.setColumnStretch(2, 1)
        atr_ema_layout.setColumnStretch(3, 1)

        # Add the group to the layout
        layout.addWidget(atr_ema_group)

        # Add stretch to push everything to the top
        layout.addStretch()

    def update_signal_mode_ui(self):
        """Update UI based on selected signal mode."""
        # If neither radio button is checked, default to threshold mode
        if not self.threshold_mode_rb.isChecked() and not self.crossover_mode_rb.isChecked():
            self.threshold_mode_rb.setChecked(True)

        # Enable/disable appropriate controls based on selected mode
        threshold_mode = self.threshold_mode_rb.isChecked()
        crossover_mode = self.crossover_mode_rb.isChecked()

        # Enable/disable threshold group
        self.threshold_group.setEnabled(threshold_mode)

        # Enable/disable crossover group
        self.crossover_group.setEnabled(crossover_mode)

        # Disable/enable specific strategy parameters based on mode
        # Parameters that are only relevant for threshold mode
        self.buy_threshold_sb.setEnabled(threshold_mode)
        self.sell_threshold_sb.setEnabled(threshold_mode)

        # RSI filter is only relevant in crossover mode
        self.use_rsi_filter_cb.setEnabled(crossover_mode)

        # Parameters that are used in both modes but might be less important in crossover mode
        self.use_rsi_cb.setEnabled(True)  # Always enable RSI as it's used for filtering in crossover mode
        self.use_macd_cb.setEnabled(threshold_mode or not crossover_mode)
        self.use_price_velocity_cb.setEnabled(threshold_mode or not crossover_mode)
        self.use_atr_breakout_cb.setEnabled(threshold_mode or not crossover_mode)
        self.use_band_break_cb.setEnabled(threshold_mode or not crossover_mode)
        self.use_wick_rejection_cb.setEnabled(threshold_mode or not crossover_mode)
        self.use_band_width_cb.setEnabled(threshold_mode or not crossover_mode)
        self.use_htf_alignment_cb.setEnabled(threshold_mode or not crossover_mode)

    def init_trading_tab(self):
        """Initialize the trading parameters tab."""
        layout = QVBoxLayout(self.trading_tab)

        # Trading parameters group
        trading_group = QGroupBox("Trading Parameters")
        trading_layout = QFormLayout(trading_group)

        # Check interval
        self.check_interval_sb = QSpinBox()
        self.check_interval_sb.setRange(10, 300)
        self.check_interval_sb.setValue(60)
        self.check_interval_sb.setSuffix(" seconds")
        trading_layout.addRow("Check Interval:", self.check_interval_sb)

        # Take profit percentage
        self.take_profit_sb = QDoubleSpinBox()
        self.take_profit_sb.setRange(0.1, 10.0)
        self.take_profit_sb.setSingleStep(0.1)
        self.take_profit_sb.setDecimals(1)
        self.take_profit_sb.setValue(0.3)
        self.take_profit_sb.setSuffix("%")
        trading_layout.addRow("Take Profit:", self.take_profit_sb)

        # Stop loss percentage
        self.stop_loss_sb = QDoubleSpinBox()
        self.stop_loss_sb.setRange(0.1, 10.0)
        self.stop_loss_sb.setSingleStep(0.1)
        self.stop_loss_sb.setDecimals(1)
        self.stop_loss_sb.setValue(2.0)
        self.stop_loss_sb.setSuffix("%")
        trading_layout.addRow("Stop Loss:", self.stop_loss_sb)

        # Add the group to the layout
        layout.addWidget(trading_group)

        # Add stretch to push everything to the top
        layout.addStretch()

    def load_settings(self):
        """Load current settings from config."""
        if not self.config:
            try:
                # Try to load from file
                config_path = os.path.join('me3 - Copy', 'config', 'strategy_config.yaml')
                if os.path.exists(config_path):
                    with open(config_path, 'r') as f:
                        self.config = yaml.safe_load(f)
            except Exception as e:
                print(f"Error loading config: {e}")
                return

        # Get strategy parameters
        strategy_config = self.config.get('strategies', {}).get('atr_ema_bands', {})

        # Set indicator toggles
        self.use_ema_cb.setChecked(strategy_config.get('use_ema', True))
        self.use_atr_cb.setChecked(strategy_config.get('use_atr', True))
        self.use_rsi_cb.setChecked(strategy_config.get('use_rsi', False))
        self.use_macd_cb.setChecked(strategy_config.get('use_macd', False))
        self.use_ema_slope_cb.setChecked(strategy_config.get('use_ema_slope', True))
        self.use_price_velocity_cb.setChecked(strategy_config.get('use_price_velocity', True))
        self.use_atr_breakout_cb.setChecked(strategy_config.get('use_atr_breakout', True))
        self.use_band_break_cb.setChecked(strategy_config.get('use_band_break', True))
        self.use_wick_rejection_cb.setChecked(strategy_config.get('use_wick_rejection', True))
        self.use_band_width_cb.setChecked(strategy_config.get('use_band_width', True))
        self.use_htf_alignment_cb.setChecked(strategy_config.get('use_htf_alignment', False))

        # Set signal mode
        signal_mode = strategy_config.get('signal_mode', 'threshold')
        self.threshold_mode_rb.setChecked(signal_mode == 'threshold')
        self.crossover_mode_rb.setChecked(signal_mode == 'crossover')

        # Set band crossover parameters
        self.use_ema_cross_cb.setChecked(strategy_config.get('use_ema_cross', False))
        self.use_ema_plus_1_cross_cb.setChecked(strategy_config.get('use_ema_plus_1_cross', False))
        self.use_ema_plus_2_cross_cb.setChecked(strategy_config.get('use_ema_plus_2_cross', True))
        self.use_ema_plus_3_cross_cb.setChecked(strategy_config.get('use_ema_plus_3_cross', True))
        self.use_ema_minus_1_cross_cb.setChecked(strategy_config.get('use_ema_minus_1_cross', False))
        self.use_ema_minus_2_cross_cb.setChecked(strategy_config.get('use_ema_minus_2_cross', True))
        self.use_ema_minus_3_cross_cb.setChecked(strategy_config.get('use_ema_minus_3_cross', True))
        self.enable_long_crossovers_cb.setChecked(strategy_config.get('enable_long_crossovers', True))
        self.enable_short_crossovers_cb.setChecked(strategy_config.get('enable_short_crossovers', True))
        self.use_rsi_filter_cb.setChecked(strategy_config.get('use_rsi_filter', True))

        # Set strategy parameters
        self.atr_period_sb.setValue(strategy_config.get('atr_period', 14))
        self.ema_period_sb.setValue(strategy_config.get('ema_period', 20))
        self.buy_threshold_sb.setValue(strategy_config.get('buy_threshold', 0.6))
        self.sell_threshold_sb.setValue(strategy_config.get('sell_threshold', 0.4))
        self.lookback_periods_sb.setValue(strategy_config.get('lookback_periods', 3))
        self.ema_slope_periods_sb.setValue(strategy_config.get('ema_slope_periods', 10))
        self.ema_slope_weight_sb.setValue(strategy_config.get('ema_slope_weight', 0.5))
        self.atr_breakout_multiplier_sb.setValue(strategy_config.get('atr_breakout_multiplier', 1.5))
        self.atr_breakout_weight_sb.setValue(strategy_config.get('atr_breakout_weight', 0.1))

        # Get trading parameters
        trading_config = self.config.get('trading', {})

        # Set trading parameters
        self.check_interval_sb.setValue(trading_config.get('check_interval_seconds', 60))
        self.take_profit_sb.setValue(trading_config.get('take_profit_pct', 0.3))
        self.stop_loss_sb.setValue(trading_config.get('stop_loss_pct', 2.0))

    def get_settings(self):
        """Get current settings from UI."""
        # Create config dictionary
        config = {
            'strategies': {
                'atr_ema_bands': {
                    # Indicator toggles
                    'use_ema': self.use_ema_cb.isChecked(),
                    'use_atr': self.use_atr_cb.isChecked(),
                    'use_rsi': self.use_rsi_cb.isChecked(),
                    'use_macd': self.use_macd_cb.isChecked(),
                    'use_ema_slope': self.use_ema_slope_cb.isChecked(),
                    'use_price_velocity': self.use_price_velocity_cb.isChecked(),
                    'use_atr_breakout': self.use_atr_breakout_cb.isChecked(),
                    'use_band_break': self.use_band_break_cb.isChecked(),
                    'use_wick_rejection': self.use_wick_rejection_cb.isChecked(),
                    'use_band_width': self.use_band_width_cb.isChecked(),
                    'use_htf_alignment': self.use_htf_alignment_cb.isChecked(),

                    # Signal mode
                    'signal_mode': 'threshold' if self.threshold_mode_rb.isChecked() else 'crossover',

                    # Band crossover parameters
                    'use_ema_cross': self.use_ema_cross_cb.isChecked(),
                    'use_ema_plus_1_cross': self.use_ema_plus_1_cross_cb.isChecked(),
                    'use_ema_plus_2_cross': self.use_ema_plus_2_cross_cb.isChecked(),
                    'use_ema_plus_3_cross': self.use_ema_plus_3_cross_cb.isChecked(),
                    'use_ema_minus_1_cross': self.use_ema_minus_1_cross_cb.isChecked(),
                    'use_ema_minus_2_cross': self.use_ema_minus_2_cross_cb.isChecked(),
                    'use_ema_minus_3_cross': self.use_ema_minus_3_cross_cb.isChecked(),
                    'enable_long_crossovers': self.enable_long_crossovers_cb.isChecked(),
                    'enable_short_crossovers': self.enable_short_crossovers_cb.isChecked(),
                    'use_rsi_filter': self.use_rsi_filter_cb.isChecked(),

                    # Strategy parameters
                    'atr_period': self.atr_period_sb.value(),
                    'ema_period': self.ema_period_sb.value(),
                    'buy_threshold': self.buy_threshold_sb.value(),
                    'sell_threshold': self.sell_threshold_sb.value(),
                    'lookback_periods': self.lookback_periods_sb.value(),
                    'ema_slope_periods': self.ema_slope_periods_sb.value(),
                    'ema_slope_weight': self.ema_slope_weight_sb.value(),
                    'atr_breakout_multiplier': self.atr_breakout_multiplier_sb.value(),
                    'atr_breakout_weight': self.atr_breakout_weight_sb.value()
                }
            },
            'trading': {
                'check_interval_seconds': self.check_interval_sb.value(),
                'take_profit_pct': self.take_profit_sb.value(),
                'stop_loss_pct': self.stop_loss_sb.value()
            }
        }

        return config

    def apply_settings(self):
        """Apply settings without saving to file."""
        config = self.get_settings()

        # Import the update_strategy_config function
        try:
            from checkbox_functions import update_strategy_config

            # Update the strategy configuration
            if update_strategy_config(config):
                # Emit the signal with the updated config
                self.settings_applied.emit(config)
                QMessageBox.information(self, "Settings Applied", "Auto trade settings have been applied.")
            else:
                QMessageBox.warning(self, "Settings Error", "Failed to update strategy configuration.")
        except ImportError:
            # Fall back to just emitting the signal
            self.settings_applied.emit(config)
            QMessageBox.information(self, "Settings Applied", "Auto trade settings have been applied, but strategy may not be updated until restart.")

    def save_settings(self):
        """Save settings to file and apply them."""
        config = self.get_settings()

        try:
            # Save to file
            config_path = os.path.join('me3 - Copy', 'config', 'strategy_config.yaml')

            # Ensure directory exists
            os.makedirs(os.path.dirname(config_path), exist_ok=True)

            # Merge with existing config if it exists
            existing_config = {}
            if os.path.exists(config_path):
                with open(config_path, 'r') as f:
                    existing_config = yaml.safe_load(f) or {}

            # Update existing config with new values
            if 'strategies' not in existing_config:
                existing_config['strategies'] = {}
            if 'atr_ema_bands' not in existing_config['strategies']:
                existing_config['strategies']['atr_ema_bands'] = {}

            existing_config['strategies']['atr_ema_bands'].update(config['strategies']['atr_ema_bands'])

            if 'trading' not in existing_config:
                existing_config['trading'] = {}

            existing_config['trading'].update(config['trading'])

            # Write to file
            with open(config_path, 'w') as f:
                yaml.dump(existing_config, f, default_flow_style=False)

            # Update the strategy configuration
            try:
                from checkbox_functions import update_strategy_config

                if update_strategy_config(existing_config):
                    # Apply settings
                    self.settings_applied.emit(existing_config)
                    QMessageBox.information(self, "Settings Saved", "Auto trade settings have been saved and applied.")
                else:
                    # Still save but warn about strategy update failure
                    self.settings_applied.emit(existing_config)
                    QMessageBox.warning(self, "Settings Saved", "Settings saved but strategy update failed. Changes may not take effect until restart.")
            except ImportError:
                # Fall back to just emitting the signal
                self.settings_applied.emit(existing_config)
                QMessageBox.information(self, "Settings Saved", "Settings saved but strategy may not be updated until restart.")

            # Close dialog
            self.accept()
        except Exception as e:
            QMessageBox.critical(self, "Error", f"Failed to save settings: {str(e)}")
