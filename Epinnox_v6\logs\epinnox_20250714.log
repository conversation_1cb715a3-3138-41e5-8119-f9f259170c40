2025-07-14 00:12:49,147 - main - INFO - Epinnox v6 starting up...
2025-07-14 00:12:49,161 - core.performance_monitor - INFO - Performance monitoring started
2025-07-14 00:12:49,162 - core.performance_monitor - INFO - Performance monitor initialized with 10s interval
2025-07-14 00:12:49,162 - main - INFO - Performance monitoring initialized
2025-07-14 00:12:49,174 - utils.cache_manager - INFO - Cache manager initialized: cache, max_age=24h, max_size=100MB
2025-07-14 00:12:49,174 - utils.cache_manager - INFO - Starting automatic cache cleanup
2025-07-14 00:12:49,175 - utils.cache_manager - INFO - Auto cleanup complete: 0 files removed, 0.00 MB freed
2025-07-14 00:13:02,222 - config.autonomous_config - INFO - Configuration loaded from configs/autonomous_trading.yaml
2025-07-14 00:13:03,190 - data.live_data_manager - INFO - Successfully subscribed to live data for BTC/USDT:USDT on timeframes: ['1m', '5m', '15m']
2025-07-14 00:13:07,509 - llama.runner - INFO - Loaded OpenAI API key from models_config.yaml
2025-07-14 00:13:07,510 - llama.runner - INFO - Using model path: C:\Users\<USER>\.lmstudio\models\lmstudio-community\Phi-3.1-mini-128k-instruct-GGUF\Phi-3.1-mini-128k-instruct-Q4_K_M.gguf
2025-07-14 00:13:07,510 - llama.runner - INFO - Using guard path: C:\Users\<USER>\Documents\dev\Epinnox_v6\models\Llama-Guard-3-8B.gguf
2025-07-14 00:13:07,511 - llama.runner - INFO - Using binary path: C:\Users\<USER>\Documents\dev\llama.cpp\main
2025-07-14 00:13:07,517 - llama.lmstudio_runner - INFO - Loaded LMStudio configuration from models_config.yaml
2025-07-14 00:13:09,561 - llama.lmstudio_runner - INFO - Discovered 8 models: ['phi-3.1-mini-128k-instruct', 'openthinker3-7b', 'mistral-7b-claude-chat', 'fine_tuned_qwen1.7b', 'tsfc-model-0.0.1', 'codestral-22b-v0.1', 'deepseek-r1-distill-qwen-7b', 'text-embedding-nomic-embed-text-v1.5']
2025-07-14 00:13:09,562 - llama.lmstudio_runner - INFO - Selected preferred model from config: phi-3.1-mini-128k-instruct
2025-07-14 00:13:09,562 - llama.lmstudio_runner - INFO - Initialized LMStudio with model: phi-3.1-mini-128k-instruct
2025-07-14 00:13:09,563 - trading.intelligent_limit_order_manager - INFO - Intelligent Limit Order Manager initialized for professional scalping
2025-07-14 00:13:09,564 - core.llm_action_executors - INFO - ✅ Intelligent Limit Order Manager initialized
2025-07-14 00:13:09,564 - core.llm_action_executors - INFO - 🚀 LLM Action Executors initialized with intelligent limit order system
2025-07-14 00:13:09,565 - utils.cache_manager - INFO - Cache manager initialized: cache, max_age=24h, max_size=100MB
2025-07-14 00:13:09,565 - core.llm_orchestrator - INFO - LLM Prompt Orchestrator initialized
2025-07-14 00:13:09,572 - core.signal_hierarchy - INFO - Intelligent Signal Hierarchy initialized
2025-07-14 00:13:09,573 - trading.signal_trading_engine - INFO - Signal Trading Engine initialized
2025-07-14 00:13:09,590 - storage.database_manager - INFO - Database tables initialized successfully with performance optimizations
2025-07-14 00:13:09,591 - storage.database_manager - INFO - Database manager initialized: data\epinnox_trading.db
2025-07-14 00:13:09,591 - storage.session_manager - INFO - Session Manager initialized
2025-07-14 00:13:09,598 - storage.database_manager - INFO - Created session: live_BTCUSDTUSDT_20250714_001309_5c7ae76e
2025-07-14 00:13:09,598 - storage.session_manager - INFO - Started session: live_BTCUSDTUSDT_20250714_001309_5c7ae76e
2025-07-14 00:13:10,076 - core.risk_management_system - INFO - 🛡️ Risk Management System initialized
2025-07-14 00:13:10,078 - core.error_handling_system - INFO - 🛡️ Error Handling System initialized
2025-07-14 00:13:10,078 - core.error_handling_system - INFO - 📊 Component registered: exchange_connection
2025-07-14 00:13:10,079 - core.error_handling_system - INFO - 📊 Component registered: trading_interface
2025-07-14 00:13:10,079 - core.error_handling_system - INFO - 📊 Component registered: market_data
2025-07-14 00:13:10,079 - core.error_handling_system - INFO - 📊 Component registered: llm_orchestrator
2025-07-14 00:13:10,081 - core.error_handling_system - INFO - 🔍 Health monitoring started
2025-07-14 00:13:10,086 - core.monitoring_dashboard - INFO - 📊 Monitoring Dashboard initialized
2025-07-14 00:13:10,087 - symbol_scanner - INFO - SymbolScanner initialized with 8 symbols
2025-07-14 00:13:10,088 - core.llm_action_executors - INFO - 🚀 LLM Action Executors initialized with intelligent limit order system
2025-07-14 00:13:11,344 - websocket - INFO - Websocket connected
2025-07-14 00:14:04,059 - core.llm_orchestrator - INFO - 🔍 FULL MODE: Executing all 8 prompts for comprehensive analysis
2025-07-14 00:14:04,060 - core.llm_orchestrator - INFO - 📊 LLM Thread Usage Start: 0/16 threads
2025-07-14 00:14:04,060 - core.llm_orchestrator - INFO - 🧠 Executing prompt 2/8: market_regime
2025-07-14 00:14:04,060 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-14 00:14:04,061 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-14 00:14:04,061 - llama.lmstudio_runner - INFO - 💬 User Prompt: 📊 MARKET REGIME ANALYST - BTC/USDT:USDT

🔍 MULTI-TIMEFRAME ANALYSIS:
1m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
5m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
15m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%

📈 VOLATILITY METRICS:
ATR: 0.001000 | Spread: 0.100%
Volume Ratio: 1.0x | Price Range: 1.00%

🎯 REGIME CLASSIFICATION:
- TRENDING_BULL: Strong upward momentum, high volume, clear direction
- TRENDING_BEAR: Strong downward momentum, high volume, clear direction
- RANGING_TIGHT: Low volatility, narrow price...
2025-07-14 00:14:04,061 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-07-14 00:14:08,521 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 272 chars
2025-07-14 00:14:08,521 - llama.lmstudio_runner - INFO - 📄 Response Preview: ```json
{
  "REGIME": "RANGING_TIGHT",
  "CONFIDENCE": 75,
  "SCALP_SUITABILITY": "MEDIUM",
  "RECOMMENDED_TIMEFRAME": "1m",
  "RISK_LEVEL": "MEDIUM",
  "REASONING": "Consolidation phase with moderate volatility, suitable for scalping under careful risk management."
}
```...
2025-07-14 00:14:08,522 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 705, Completion: 117, Total: 822
2025-07-14 00:14:08,527 - core.llm_response_parsers - INFO - Market regime parsed: RANGING_TIGHT (MEDIUM scalp suitability)
2025-07-14 00:14:08,628 - core.llm_orchestrator - INFO - 🧠 Executing prompt 3/8: risk_assessment
2025-07-14 00:14:08,629 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-14 00:14:08,629 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-14 00:14:08,630 - llama.lmstudio_runner - INFO - 💬 User Prompt: ...
2025-07-14 00:14:08,630 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-07-14 00:14:12,262 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 234 chars
2025-07-14 00:14:12,262 - llama.lmstudio_runner - INFO - 📄 Response Preview: ```json
{
  "action": "LONG",
  "confidence": 90,
  "entry_reason": "Market broke through resistance level with a rapid move of more than 2 pip.",
  "take_profit": 1.5,
  "stop_loss": 0.3,
  "hold_time": "3min",
  "leverage": 40
}
```...
2025-07-14 00:14:12,263 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 174, Completion: 97, Total: 271
2025-07-14 00:14:12,264 - core.llm_response_parsers - INFO - Risk assessment parsed: REJECTED (Risk: 50)
2025-07-14 00:14:12,364 - core.llm_orchestrator - INFO - 🧠 Executing prompt 4/8: entry_timing
2025-07-14 00:14:12,365 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-14 00:14:12,365 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-14 00:14:12,365 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🎯 ENTRY TIMING SPECIALIST - BTC/USDT:USDT

🎯 SIGNAL ANALYSIS:
ML Ensemble: NEUTRAL (50.0%)
Technical: NEUTRAL | Momentum: 0.00
Volume: PENDING | Orderflow: NEUTRAL

📊 MARKET MICROSTRUCTURE:
Bid/Ask: $0.000000/$0.000000
Spread: 0.100% | L2 Imbalance: 0.0%
Recent Flow: BALANCED | Volume Spike: False

📈 KEY LEVELS:
Support: $0.000000
Resistance: $0.000000
Distance to Support: 0.00%

⏰ TIMING FACTORS:
- Price near key levels: NEUTRAL
- Volume confirmation: PENDING
- Spread conditions: FAVORABLE
- Mo...
2025-07-14 00:14:12,366 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-07-14 00:14:15,844 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 167 chars
2025-07-14 00:14:15,845 - llama.lmstudio_runner - INFO - 📄 Response Preview: {
  "action": "WAIT",
  "entry_type": "MARKET/LIMIT",
  "wait_for": "VOLUME_CONFIRMATION",
  "max_wait_seconds": 60,
  "reasoning": "Waiting for volume confirmation"
}...
2025-07-14 00:14:15,845 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 603, Completion: 74, Total: 677
2025-07-14 00:14:15,845 - core.llm_response_parsers - INFO - Entry timing parsed: WAIT (LIMIT order)
2025-07-14 00:14:15,946 - core.llm_orchestrator - INFO - 🧠 Executing prompt 5/8: opportunity_scanner
2025-07-14 00:14:15,947 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-14 00:14:15,947 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a conservative futures trading analyst. Analyze market data carefully and provide measured recommendations. Use format: DECISION: [LONG/SHORT/WAIT], CONFIDENCE: [50-100]%, TAKE_PROFIT: [%], ST...
2025-07-14 00:14:15,947 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🔍 INTELLIGENT OPPORTUNITY SCANNER - Account Health Aware

🚨 ACCOUNT HEALTH STATUS: HEALTHY
💰 Balance: $1000.00 | Health: Healthy - Normal Trading
📊 Current Exposure: $0.00 (0.0% of account)
🎯 Trading Allowed: YES

📊 SYMBOL ANALYSIS:
• DOGE/USDT:USDT: $0.000000 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• BTC/USDT:USDT: $0.000000 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• ETH/USDT:USDT: $3500.000000 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• SOL/USDT:USDT: $200.000000 | Mom: +0.0% | Vol: 1.0x | Set...
2025-07-14 00:14:15,947 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-07-14 00:14:22,324 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 1054 chars
2025-07-14 00:14:22,325 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: SHORT, CONFIDENCE: 70%, TAKE_PROFIT: -1.2%, STOP_LOSS: -3.0%, EXPLANATION: The market is currently in a neutral sector momentum with normal volatility and average volume profile indicating stability but not necessarily growth or decline, which aligns well for shorting as it allows us to ca...
2025-07-14 00:14:22,325 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 922, Completion: 239, Total: 1161
2025-07-14 00:14:22,325 - core.llm_response_parsers - INFO - Parsed structured text: {'DECISION': 'SHORT', 'CONFIDENCE': 70.0, 'EXPLANATION': "THE MARKET IS CURRENTLY IN A NEUTRAL SECTOR MOMENTUM WITH NORMAL VOLATILITY AND AVERAGE VOLUME PROFILE INDICATING STABILITY BUT NOT NECESSARILY GROWTH OR DECLINE, WHICH ALIGNS WELL FOR SHORTING AS IT ALLOWS US TO CAPITALIZE ON POTENTIAL DOWNWARD MOVEMENTS WITHOUT EXCESSIVE RISK EXPOSURE GIVEN THE ACCOUNT'S HEALTHY STATUS. A CONSERVATIVE ENTRY STRATEGY IS RECOMMENDED DUE TO MARKET CONDITIONS FAVORABLE IN TERMS OF LIQUIDITY AND VOLATILITY WITHIN A MODERATE RANGE THAT DOES NOT POSE HIGH RISKS, HENCE AN AGGRESSIVE APPROACH WOULD BE UNSUITABLE FOR THIS SCENARIO CONSIDERING BOTH OPPORTUNITY CRITERIA AND RISK PRESERVATION PARAMETERS. THE TAKE-PROFIT AT -1.2% ENSURES WE LOCK IN PROFITS BEFORE THE MARKET POTENTIALLY REVERSES WHILE MAINTAINING SAFETY WITH A STOP LOSS SET TO -3%, WHICH PROVIDES AMPLE PROTECTION AGAINST SUDDEN ADVERSE MOVEMENTS WITHOUT OVERLY LIMITING POTENTIAL GAINS, THUS ALIGNING WELL WITHIN OUR HEALTHY ACCOUNT CAPACITY AND RISK BUDGET CONSTRAINTS.", 'ACTION': 'ENTER_NOW'}
2025-07-14 00:14:22,325 - core.llm_response_parsers - INFO - Opportunity scanner parsed: NONE (NONE)
2025-07-14 00:14:22,426 - core.llm_orchestrator - INFO - 🧠 Executing prompt 8/8: strategy_adaptation
2025-07-14 00:14:22,426 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-14 00:14:22,426 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-14 00:14:22,427 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🔄 STRATEGY ADAPTATION SPECIALIST

📊 PERFORMANCE ANALYSIS (Last 24h):
Trades: 0 | Win Rate: 50.0%
Avg Profit: 0.80% | Avg Loss: -0.30%
Sharpe Ratio: 1.00 | Max Drawdown: 0.0%
Total PnL: $0.00 | ROI: 0.0%

🎯 CURRENT STRATEGY:
Risk per Trade: 2.0% | Avg Hold Time: 8.0min
Entry Threshold: 70% | Exit Threshold: 60%
Position Size Method: FIXED_RISK | Max Positions: 3

📈 MARKET REGIME: UNKNOWN
Regime Confidence: 50.0%
Scalp Suitability: MEDIUM

🔧 ADAPTATION FACTORS:
- Win rate trending: DOWN
- Drawdown...
2025-07-14 00:14:22,427 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-07-14 00:14:26,794 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 338 chars
2025-07-14 00:14:26,794 - llama.lmstudio_runner - INFO - 📄 Response Preview: ```json
{
  "risk_adjustment": [0.5, 2.0],
  "hold_time_target": "8",
  "entry_threshold": 70,
  "exit_threshold": 60,
  "sizing_method": "FIXED_RISK",
  "reasoning": "Maintain current strategy - performing well with a slight risk reduction to account for recent poor performance and downward trend i...
2025-07-14 00:14:26,794 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 619, Completion: 124, Total: 743
2025-07-14 00:14:26,795 - core.llm_response_parsers - INFO - Strategy adaptation parsed: Risk adj 1.0x
2025-07-14 00:14:26,795 - core.llm_action_executors - INFO - Applying strategy adaptations: Risk 1.0x, Hold time 8min
2025-07-14 00:14:26,896 - core.llm_orchestrator - INFO - 📊 LLM Thread Usage End: 0/16 threads
2025-07-14 00:14:26,896 - core.llm_orchestrator - INFO - 🧠 LLM prompt cycle completed in 22.84s - 5 prompts executed sequentially
2025-07-14 00:14:34,058 - core.llm_orchestrator - INFO - 🔍 FULL MODE: Executing all 8 prompts for comprehensive analysis
2025-07-14 00:14:34,059 - core.llm_orchestrator - INFO - 📊 LLM Thread Usage Start: 0/16 threads
2025-07-14 00:14:34,060 - core.llm_orchestrator - INFO - 🧠 Executing prompt 2/8: market_regime
2025-07-14 00:14:34,060 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-14 00:14:34,061 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-14 00:14:34,061 - llama.lmstudio_runner - INFO - 💬 User Prompt: 📊 MARKET REGIME ANALYST - BTC/USDT:USDT

🔍 MULTI-TIMEFRAME ANALYSIS:
1m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
5m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
15m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%

📈 VOLATILITY METRICS:
ATR: 0.001000 | Spread: 0.100%
Volume Ratio: 1.0x | Price Range: 1.00%

🎯 REGIME CLASSIFICATION:
- TRENDING_BULL: Strong upward momentum, high volume, clear direction
- TRENDING_BEAR: Strong downward momentum, high volume, clear direction
- RANGING_TIGHT: Low volatility, narrow price...
2025-07-14 00:14:34,062 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-07-14 00:14:38,302 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 218 chars
2025-07-14 00:14:38,303 - llama.lmstudio_runner - INFO - 📄 Response Preview: ```json
{
  "REGIME": "RANGING_TIGHT",
  "CONFIDENCE": 75,
  "SCALP_SUITABILITY": "MEDIUM",
  "RECOMMENDED_TIMEFRAME": "1m",
  "RISK_LEVEL": "MEDIUM",
  "REASONING": "Consolidation phase with moderate volatility"
}
```...
2025-07-14 00:14:38,303 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 705, Completion: 108, Total: 813
2025-07-14 00:14:38,303 - core.llm_response_parsers - INFO - Market regime parsed: RANGING_TIGHT (MEDIUM scalp suitability)
2025-07-14 00:14:38,404 - core.llm_orchestrator - INFO - 🧠 Executing prompt 4/8: entry_timing
2025-07-14 00:14:38,405 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-14 00:14:38,405 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-14 00:14:38,405 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🎯 ENTRY TIMING SPECIALIST - BTC/USDT:USDT

🎯 SIGNAL ANALYSIS:
ML Ensemble: NEUTRAL (50.0%)
Technical: NEUTRAL | Momentum: 0.00
Volume: PENDING | Orderflow: NEUTRAL

📊 MARKET MICROSTRUCTURE:
Bid/Ask: $0.000000/$0.000000
Spread: 0.100% | L2 Imbalance: 0.0%
Recent Flow: BALANCED | Volume Spike: False

📈 KEY LEVELS:
Support: $0.000000
Resistance: $0.000000
Distance to Support: 0.00%

⏰ TIMING FACTORS:
- Price near key levels: NEUTRAL
- Volume confirmation: PENDING
- Spread conditions: FAVORABLE
- Mo...
2025-07-14 00:14:38,405 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-07-14 00:14:41,843 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 167 chars
2025-07-14 00:14:41,844 - llama.lmstudio_runner - INFO - 📄 Response Preview: {
  "action": "WAIT",
  "entry_type": "MARKET/LIMIT",
  "wait_for": "VOLUME_CONFIRMATION",
  "max_wait_seconds": 60,
  "reasoning": "Waiting for volume confirmation"
}...
2025-07-14 00:14:41,844 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 603, Completion: 74, Total: 677
2025-07-14 00:14:41,844 - core.llm_response_parsers - INFO - Entry timing parsed: WAIT (LIMIT order)
2025-07-14 00:14:41,945 - core.llm_orchestrator - INFO - 📊 LLM Thread Usage End: 0/16 threads
2025-07-14 00:14:41,945 - core.llm_orchestrator - INFO - 🧠 LLM prompt cycle completed in 7.89s - 2 prompts executed sequentially
2025-07-14 00:15:04,534 - core.llm_orchestrator - INFO - 🔍 FULL MODE: Executing all 8 prompts for comprehensive analysis
2025-07-14 00:15:04,534 - core.llm_orchestrator - INFO - 📊 LLM Thread Usage Start: 0/16 threads
2025-07-14 00:15:04,534 - core.llm_orchestrator - INFO - 🧠 Executing prompt 2/8: market_regime
2025-07-14 00:15:04,534 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-14 00:15:04,534 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-14 00:15:04,535 - llama.lmstudio_runner - INFO - 💬 User Prompt: 📊 MARKET REGIME ANALYST - BTC/USDT:USDT

🔍 MULTI-TIMEFRAME ANALYSIS:
1m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
5m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
15m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%

📈 VOLATILITY METRICS:
ATR: 0.001000 | Spread: 0.100%
Volume Ratio: 1.0x | Price Range: 1.00%

🎯 REGIME CLASSIFICATION:
- TRENDING_BULL: Strong upward momentum, high volume, clear direction
- TRENDING_BEAR: Strong downward momentum, high volume, clear direction
- RANGING_TIGHT: Low volatility, narrow price...
2025-07-14 00:15:04,535 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-07-14 00:15:08,623 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 218 chars
2025-07-14 00:15:08,623 - llama.lmstudio_runner - INFO - 📄 Response Preview: ```json
{
  "REGIME": "RANGING_TIGHT",
  "CONFIDENCE": 75,
  "SCALP_SUITABILITY": "MEDIUM",
  "RECOMMENDED_TIMEFRAME": "1m",
  "RISK_LEVEL": "MEDIUM",
  "REASONING": "Consolidation phase with moderate volatility"
}
```...
2025-07-14 00:15:08,624 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 705, Completion: 108, Total: 813
2025-07-14 00:15:08,624 - core.llm_response_parsers - INFO - Market regime parsed: RANGING_TIGHT (MEDIUM scalp suitability)
2025-07-14 00:15:08,724 - core.llm_orchestrator - INFO - 🧠 Executing prompt 3/8: risk_assessment
2025-07-14 00:15:08,725 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-14 00:15:08,725 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-14 00:15:08,725 - llama.lmstudio_runner - INFO - 💬 User Prompt: ...
2025-07-14 00:15:08,726 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-07-14 00:15:12,264 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 227 chars
2025-07-14 00:15:12,264 - llama.lmstudio_runner - INFO - 📄 Response Preview: ```json
{
  "action": "LONG",
  "confidence": 90,
  "entry_reason": "Market broke through resistance level with a sudden spike in volume.",
  "take_profit": 2.5,
  "stop_loss": 1.0,
  "hold_time": "3min",
  "leverage": 40
}
```...
2025-07-14 00:15:12,265 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 174, Completion: 94, Total: 268
2025-07-14 00:15:12,265 - core.llm_response_parsers - INFO - Risk assessment parsed: REJECTED (Risk: 50)
2025-07-14 00:15:12,366 - core.llm_orchestrator - INFO - 🧠 Executing prompt 4/8: entry_timing
2025-07-14 00:15:12,366 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-14 00:15:12,366 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-14 00:15:12,366 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🎯 ENTRY TIMING SPECIALIST - BTC/USDT:USDT

🎯 SIGNAL ANALYSIS:
ML Ensemble: NEUTRAL (50.0%)
Technical: NEUTRAL | Momentum: 0.00
Volume: PENDING | Orderflow: NEUTRAL

📊 MARKET MICROSTRUCTURE:
Bid/Ask: $0.000000/$0.000000
Spread: 0.100% | L2 Imbalance: 0.0%
Recent Flow: BALANCED | Volume Spike: False

📈 KEY LEVELS:
Support: $0.000000
Resistance: $0.000000
Distance to Support: 0.00%

⏰ TIMING FACTORS:
- Price near key levels: NEUTRAL
- Volume confirmation: PENDING
- Spread conditions: FAVORABLE
- Mo...
2025-07-14 00:15:12,366 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-07-14 00:15:15,852 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 167 chars
2025-07-14 00:15:15,852 - llama.lmstudio_runner - INFO - 📄 Response Preview: {
  "action": "WAIT",
  "entry_type": "MARKET/LIMIT",
  "wait_for": "VOLUME_CONFIRMATION",
  "max_wait_seconds": 60,
  "reasoning": "Waiting for volume confirmation"
}...
2025-07-14 00:15:15,852 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 603, Completion: 74, Total: 677
2025-07-14 00:15:15,852 - core.llm_response_parsers - INFO - Entry timing parsed: WAIT (LIMIT order)
2025-07-14 00:15:15,953 - core.llm_orchestrator - INFO - 🧠 Executing prompt 5/8: opportunity_scanner
2025-07-14 00:15:15,954 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-14 00:15:15,954 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a conservative futures trading analyst. Analyze market data carefully and provide measured recommendations. Use format: DECISION: [LONG/SHORT/WAIT], CONFIDENCE: [50-100]%, TAKE_PROFIT: [%], ST...
2025-07-14 00:15:15,954 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🔍 INTELLIGENT OPPORTUNITY SCANNER - Account Health Aware

🚨 ACCOUNT HEALTH STATUS: HEALTHY
💰 Balance: $1000.00 | Health: Healthy - Normal Trading
📊 Current Exposure: $0.00 (0.0% of account)
🎯 Trading Allowed: YES

📊 SYMBOL ANALYSIS:
• DOGE/USDT:USDT: $0.000000 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• BTC/USDT:USDT: $0.000000 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• ETH/USDT:USDT: $3500.000000 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• SOL/USDT:USDT: $200.000000 | Mom: +0.0% | Vol: 1.0x | Set...
2025-07-14 00:15:15,955 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-07-14 00:15:22,806 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 1115 chars
2025-07-14 00:15:22,807 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: SHORT, CONFIDENCE: 70%, TAKE_PROFIT: -1.2%, STOP_LOSS: -3.0%, EXPLANATION: The market is currently in a neutral sector momentum phase with normal volatility and average volume profile indicating stability but not necessarily growth or decline, which aligns well for shorting as it allows us...
2025-07-14 00:15:22,807 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 922, Completion: 264, Total: 1186
2025-07-14 00:15:22,807 - core.llm_response_parsers - INFO - Parsed structured text: {'DECISION': 'SHORT', 'CONFIDENCE': 70.0, 'EXPLANATION': "THE MARKET IS CURRENTLY IN A NEUTRAL SECTOR MOMENTUM PHASE WITH NORMAL VOLATILITY AND AVERAGE VOLUME PROFILE INDICATING STABILITY BUT NOT NECESSARILY GROWTH OR DECLINE, WHICH ALIGNS WELL FOR SHORTING AS IT ALLOWS US TO CAPITALIZE ON POTENTIAL DOWNWARD MOVEMENTS WITHOUT SIGNIFICANT RISK OF LOSS DUE TO THE ACCOUNT'S HEALTHY STATUS. A CONSERVATIVE ENTRY STRATEGY IS RECOMMENDED GIVEN THAT ALL SYMBOLS HAVE A SETUP QUALITY RATED HIGH AND LIQUIDITY SPREAD REQUIREMENTS ARE MET WITH LESS THAN 0.2%. THE CHOSEN SYMBOL, BTC/USDT, HAS BEEN IDENTIFIED AS HAVING CLEAR PATTERNS OR STRONG SIGNALS WHICH SUGGEST IT COULD BE POISED FOR A SHORT-TERM DECLINE WITHIN THE CONSERVATIVE RANGE OF VOLATILITY PREFERRED (1-3%). A TAKE PROFIT AT -1.2% ENSURES WE CAPITALIZE ON ANY POSITIVE MOVEMENT WHILE MAINTAINING RISK EXPOSURE BELOW 2% PER TRADE, AND SETTING STOP LOSS TO -3.0% PROVIDES AN ADDITIONAL LAYER OF PROTECTION AGAINST POTENTIAL ADVERSE MOVEMENTS BEYOND OUR CALCULATED THRESHOLD FOR A HEALTHY ACCOUNT'S MAXIMUM TOTAL EXPOSURE LIMIT (70%).", 'ACTION': 'ENTER_NOW'}
2025-07-14 00:15:22,808 - core.llm_response_parsers - INFO - Opportunity scanner parsed: NONE (NONE)
2025-07-14 00:15:22,908 - core.llm_orchestrator - INFO - 📊 LLM Thread Usage End: 0/16 threads
2025-07-14 00:15:22,908 - core.llm_orchestrator - INFO - 🧠 LLM prompt cycle completed in 18.37s - 4 prompts executed sequentially
2025-07-14 00:15:34,018 - core.llm_orchestrator - INFO - 🔍 FULL MODE: Executing all 8 prompts for comprehensive analysis
2025-07-14 00:15:34,018 - core.llm_orchestrator - INFO - 📊 LLM Thread Usage Start: 0/16 threads
2025-07-14 00:15:34,018 - core.llm_orchestrator - INFO - 🧠 Executing prompt 4/8: entry_timing
2025-07-14 00:15:34,019 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-14 00:15:34,019 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-14 00:15:34,019 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🎯 ENTRY TIMING SPECIALIST - BTC/USDT:USDT

🎯 SIGNAL ANALYSIS:
ML Ensemble: NEUTRAL (50.0%)
Technical: NEUTRAL | Momentum: 0.00
Volume: PENDING | Orderflow: NEUTRAL

📊 MARKET MICROSTRUCTURE:
Bid/Ask: $0.000000/$0.000000
Spread: 0.100% | L2 Imbalance: 0.0%
Recent Flow: BALANCED | Volume Spike: False

📈 KEY LEVELS:
Support: $0.000000
Resistance: $0.000000
Distance to Support: 0.00%

⏰ TIMING FACTORS:
- Price near key levels: NEUTRAL
- Volume confirmation: PENDING
- Spread conditions: FAVORABLE
- Mo...
2025-07-14 00:15:34,019 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-07-14 00:15:38,337 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 380 chars
2025-07-14 00:15:38,337 - llama.lmstudio_runner - INFO - 📄 Response Preview: {
  "action": "ENTER_NOW",
  "entry_type": "LIMIT",
  "confidence": 90,
  "wait_for": null,
  "max_wait_seconds": 30,
  "reasoning": "Market conditions are favorable with a low spread and no significant imbalance. Despite the lack of volume confirmation yet, historical context shows that entering no...
2025-07-14 00:15:38,338 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 610, Completion: 110, Total: 720
2025-07-14 00:15:38,339 - core.llm_response_parsers - INFO - Entry timing parsed: WAIT (LIMIT order)
2025-07-14 00:15:38,439 - core.llm_orchestrator - INFO - 📊 LLM Thread Usage End: 0/16 threads
2025-07-14 00:15:38,440 - core.llm_orchestrator - INFO - 🧠 LLM prompt cycle completed in 4.42s - 1 prompts executed sequentially
2025-07-14 00:16:04,487 - core.llm_orchestrator - INFO - 🔍 FULL MODE: Executing all 8 prompts for comprehensive analysis
2025-07-14 00:16:04,487 - core.llm_orchestrator - INFO - 📊 LLM Thread Usage Start: 0/16 threads
2025-07-14 00:16:04,488 - core.llm_orchestrator - INFO - 🧠 Executing prompt 2/8: market_regime
2025-07-14 00:16:04,488 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-14 00:16:04,488 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-14 00:16:04,488 - llama.lmstudio_runner - INFO - 💬 User Prompt: 📊 MARKET REGIME ANALYST - BTC/USDT:USDT

🔍 MULTI-TIMEFRAME ANALYSIS:
1m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
5m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
15m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%

📈 VOLATILITY METRICS:
ATR: 0.001000 | Spread: 0.100%
Volume Ratio: 1.0x | Price Range: 1.00%

🎯 REGIME CLASSIFICATION:
- TRENDING_BULL: Strong upward momentum, high volume, clear direction
- TRENDING_BEAR: Strong downward momentum, high volume, clear direction
- RANGING_TIGHT: Low volatility, narrow price...
2025-07-14 00:16:04,489 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-07-14 00:16:08,732 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 218 chars
2025-07-14 00:16:08,732 - llama.lmstudio_runner - INFO - 📄 Response Preview: ```json
{
  "REGIME": "RANGING_TIGHT",
  "CONFIDENCE": 75,
  "SCALP_SUITABILITY": "MEDIUM",
  "RECOMMENDED_TIMEFRAME": "1m",
  "RISK_LEVEL": "MEDIUM",
  "REASONING": "Consolidation phase with moderate volatility"
}
```...
2025-07-14 00:16:08,732 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 705, Completion: 108, Total: 813
2025-07-14 00:16:08,733 - core.llm_response_parsers - INFO - Market regime parsed: RANGING_TIGHT (MEDIUM scalp suitability)
2025-07-14 00:16:08,833 - core.llm_orchestrator - INFO - 🧠 Executing prompt 3/8: risk_assessment
2025-07-14 00:16:08,833 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-14 00:16:08,833 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-14 00:16:08,834 - llama.lmstudio_runner - INFO - 💬 User Prompt: ...
2025-07-14 00:16:08,834 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-07-14 00:16:12,513 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 221 chars
2025-07-14 00:16:12,513 - llama.lmstudio_runner - INFO - 📄 Response Preview: ```json
{
  "action": "LONG",
  "confidence": 90,
  "entry_reason": "Breakout above resistance level with strong volume indication.",
  "take_profit": 2.5,
  "stop_loss": 1.0,
  "hold_time": "3min",
  "leverage": 40
}
```...
2025-07-14 00:16:12,514 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 174, Completion: 92, Total: 266
2025-07-14 00:16:12,514 - core.llm_response_parsers - INFO - Risk assessment parsed: REJECTED (Risk: 50)
2025-07-14 00:16:12,615 - core.llm_orchestrator - INFO - 🧠 Executing prompt 4/8: entry_timing
2025-07-14 00:16:12,616 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-14 00:16:12,616 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-14 00:16:12,616 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🎯 ENTRY TIMING SPECIALIST - BTC/USDT:USDT

🎯 SIGNAL ANALYSIS:
ML Ensemble: NEUTRAL (50.0%)
Technical: NEUTRAL | Momentum: 0.00
Volume: PENDING | Orderflow: NEUTRAL

📊 MARKET MICROSTRUCTURE:
Bid/Ask: $0.000000/$0.000000
Spread: 0.100% | L2 Imbalance: 0.0%
Recent Flow: BALANCED | Volume Spike: False

📈 KEY LEVELS:
Support: $0.000000
Resistance: $0.000000
Distance to Support: 0.00%

⏰ TIMING FACTORS:
- Price near key levels: NEUTRAL
- Volume confirmation: PENDING
- Spread conditions: FAVORABLE
- Mo...
2025-07-14 00:16:12,616 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-07-14 00:16:16,083 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 167 chars
2025-07-14 00:16:16,084 - llama.lmstudio_runner - INFO - 📄 Response Preview: {
  "action": "WAIT",
  "entry_type": "MARKET/LIMIT",
  "wait_for": "VOLUME_CONFIRMATION",
  "max_wait_seconds": 60,
  "reasoning": "Waiting for volume confirmation"
}...
2025-07-14 00:16:16,084 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 603, Completion: 74, Total: 677
2025-07-14 00:16:16,085 - core.llm_response_parsers - INFO - Entry timing parsed: WAIT (LIMIT order)
2025-07-14 00:16:16,186 - core.llm_orchestrator - INFO - 🧠 Executing prompt 5/8: opportunity_scanner
2025-07-14 00:16:16,186 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-14 00:16:16,186 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a conservative futures trading analyst. Analyze market data carefully and provide measured recommendations. Use format: DECISION: [LONG/SHORT/WAIT], CONFIDENCE: [50-100]%, TAKE_PROFIT: [%], ST...
2025-07-14 00:16:16,187 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🔍 INTELLIGENT OPPORTUNITY SCANNER - Account Health Aware

🚨 ACCOUNT HEALTH STATUS: HEALTHY
💰 Balance: $1000.00 | Health: Healthy - Normal Trading
📊 Current Exposure: $0.00 (0.0% of account)
🎯 Trading Allowed: YES

📊 SYMBOL ANALYSIS:
• DOGE/USDT:USDT: $0.000000 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• BTC/USDT:USDT: $0.000000 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• ETH/USDT:USDT: $3500.000000 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• SOL/USDT:USDT: $200.000000 | Mom: +0.0% | Vol: 1.0x | Set...
2025-07-14 00:16:16,187 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-07-14 00:16:21,798 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 796 chars
2025-07-14 00:16:21,798 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: LONG, CONFIDENCE: 80%, TAKE_PROFIT: 2.5%, STOP_LOSS: -3.5%, EXPLANATION: The market overview indicates a neutral sector momentum with normal volatility and average volume profile which aligns well for conservative trading strategies, especially considering the account's healthy status. BTC...
2025-07-14 00:16:21,798 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 922, Completion: 192, Total: 1114
2025-07-14 00:16:21,799 - core.llm_response_parsers - INFO - Parsed structured text: {'DECISION': 'LONG', 'CONFIDENCE': 80.0, 'EXPLANATION': "THE MARKET OVERVIEW INDICATES A NEUTRAL SECTOR MOMENTUM WITH NORMAL VOLATILITY AND AVERAGE VOLUME PROFILE WHICH ALIGNS WELL FOR CONSERVATIVE TRADING STRATEGIES, ESPECIALLY CONSIDERING THE ACCOUNT'S HEALTHY STATUS. BTC/USDT SHOWS NO IMMEDIATE SETUP QUALITY BUT GIVEN ITS HISTORICAL STABILITY AS A LEADING CRYPTOCURRENCY PAIRED WITH EXCELLENT LIQUIDITY (AS INDICATED BY USDT PAIR), IT PRESENTS AN OPPORTUNITY TO ENTER AT MEDIUM RISK LEVEL WHILE ADHERING TO OUR CONSERVATIVE CRITERIA OF 1-3% VOLATILITY AND MAINTAINING THE ACCOUNT'S HEALTH. THE TAKE PROFIT IS SET SLIGHTLY ABOVE THE MAX POSITION SIZE, ENSURING A PROFITABLE EXIT WITHOUT EXCEEDING THE MAXIMUM EXPOSURE LIMIT OR IMPACT ON ACCOUNT HEALTH SIGNIFICANTLY.", 'ACTION': 'ENTER_NOW'}
2025-07-14 00:16:21,799 - core.llm_response_parsers - INFO - Opportunity scanner parsed: NONE (NONE)
2025-07-14 00:16:21,900 - core.llm_orchestrator - INFO - 🧠 Executing prompt 8/8: strategy_adaptation
2025-07-14 00:16:21,900 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-14 00:16:21,900 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-14 00:16:21,900 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🔄 STRATEGY ADAPTATION SPECIALIST

📊 PERFORMANCE ANALYSIS (Last 24h):
Trades: 0 | Win Rate: 50.0%
Avg Profit: 0.80% | Avg Loss: -0.30%
Sharpe Ratio: 1.00 | Max Drawdown: 0.0%
Total PnL: $0.00 | ROI: 0.0%

🎯 CURRENT STRATEGY:
Risk per Trade: 2.0% | Avg Hold Time: 8.0min
Entry Threshold: 70% | Exit Threshold: 60%
Position Size Method: FIXED_RISK | Max Positions: 3

📈 MARKET REGIME: UNKNOWN
Regime Confidence: 50.0%
Scalp Suitability: MEDIUM

🔧 ADAPTATION FACTORS:
- Win rate trending: DOWN
- Drawdown...
2025-07-14 00:16:21,901 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-07-14 00:16:26,467 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 412 chars
2025-07-14 00:16:26,468 - llama.lmstudio_runner - INFO - 📄 Response Preview: ```json
{
  "risk_adjustment": [0.5, 2.0],
  "hold_time_target": "8",
  "entry_threshold": 70,
  "exit_threshold": 60,
  "sizing_method": "FIXED_RISK",
  "reasoning": "Maintain current strategy - performing well with a moderate win rate and acceptable drawdown level. Adjust risk to account for recen...
2025-07-14 00:16:26,468 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 619, Completion: 138, Total: 757
2025-07-14 00:16:26,468 - core.llm_response_parsers - INFO - Strategy adaptation parsed: Risk adj 1.0x
2025-07-14 00:16:26,469 - core.llm_action_executors - INFO - Applying strategy adaptations: Risk 1.0x, Hold time 8min
2025-07-14 00:16:26,569 - core.llm_orchestrator - INFO - 📊 LLM Thread Usage End: 0/16 threads
2025-07-14 00:16:26,569 - core.llm_orchestrator - INFO - 🧠 LLM prompt cycle completed in 22.08s - 5 prompts executed sequentially
2025-07-14 00:16:34,043 - core.llm_orchestrator - INFO - 🔍 FULL MODE: Executing all 8 prompts for comprehensive analysis
2025-07-14 00:16:34,043 - core.llm_orchestrator - INFO - 📊 LLM Thread Usage Start: 0/16 threads
2025-07-14 00:16:34,043 - core.llm_orchestrator - INFO - 🧠 Executing prompt 4/8: entry_timing
2025-07-14 00:16:34,043 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-14 00:16:34,044 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-14 00:16:34,044 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🎯 ENTRY TIMING SPECIALIST - BTC/USDT:USDT

🎯 SIGNAL ANALYSIS:
ML Ensemble: NEUTRAL (50.0%)
Technical: NEUTRAL | Momentum: 0.00
Volume: PENDING | Orderflow: NEUTRAL

📊 MARKET MICROSTRUCTURE:
Bid/Ask: $0.000000/$0.000000
Spread: 0.100% | L2 Imbalance: 0.0%
Recent Flow: BALANCED | Volume Spike: False

📈 KEY LEVELS:
Support: $0.000000
Resistance: $0.000000
Distance to Support: 0.00%

⏰ TIMING FACTORS:
- Price near key levels: NEUTRAL
- Volume confirmation: PENDING
- Spread conditions: FAVORABLE
- Mo...
2025-07-14 00:16:34,044 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-07-14 00:16:38,132 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 299 chars
2025-07-14 00:16:38,132 - llama.lmstudio_runner - INFO - 📄 Response Preview: {
  "action": "ENTER",
  "entry_type": "LIMIT",
  "confidence": 80,
  "wait_for": null,
  "max_wait_seconds": 30,
  "reasoning": "Market conditions are favorable with a low spread and no significant volume spikes. Momentum is neutral but the risk/reward ratio of 2:1 justifies an immediate entry."
}...
2025-07-14 00:16:38,132 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 610, Completion: 102, Total: 712
2025-07-14 00:16:38,132 - core.llm_response_parsers - WARNING - JSON decode error: Expecting ',' delimiter: line 7 column 151 (char 264)
2025-07-14 00:16:38,234 - core.llm_orchestrator - INFO - 📊 LLM Thread Usage End: 0/16 threads
2025-07-14 00:16:38,235 - core.llm_orchestrator - INFO - 🧠 LLM prompt cycle completed in 4.19s - 1 prompts executed sequentially
2025-07-14 00:23:24,636 - main - INFO - Epinnox v6 starting up...
2025-07-14 00:23:24,650 - core.performance_monitor - INFO - Performance monitoring started
2025-07-14 00:23:24,650 - core.performance_monitor - INFO - Performance monitor initialized with 10s interval
2025-07-14 00:23:24,651 - main - INFO - Performance monitoring initialized
2025-07-14 00:23:24,659 - utils.cache_manager - INFO - Cache manager initialized: cache, max_age=24h, max_size=100MB
2025-07-14 00:23:24,660 - utils.cache_manager - INFO - Starting automatic cache cleanup
2025-07-14 00:23:24,661 - utils.cache_manager - INFO - Auto cleanup complete: 0 files removed, 0.00 MB freed
2025-07-14 00:23:33,617 - config.autonomous_config - INFO - Configuration loaded from configs/autonomous_trading.yaml
2025-07-14 00:23:34,532 - data.live_data_manager - INFO - Successfully subscribed to live data for BTC/USDT:USDT on timeframes: ['1m', '5m', '15m']
2025-07-14 00:23:35,798 - websocket - INFO - Websocket connected
2025-07-14 00:23:38,400 - llama.runner - INFO - Loaded OpenAI API key from models_config.yaml
2025-07-14 00:23:38,401 - llama.runner - INFO - Using model path: C:\Users\<USER>\.lmstudio\models\lmstudio-community\Phi-3.1-mini-128k-instruct-GGUF\Phi-3.1-mini-128k-instruct-Q4_K_M.gguf
2025-07-14 00:23:38,401 - llama.runner - INFO - Using guard path: C:\Users\<USER>\Documents\dev\Epinnox_v6\models\Llama-Guard-3-8B.gguf
2025-07-14 00:23:38,401 - llama.runner - INFO - Using binary path: C:\Users\<USER>\Documents\dev\llama.cpp\main
2025-07-14 00:23:38,406 - llama.lmstudio_runner - INFO - Loaded LMStudio configuration from models_config.yaml
2025-07-14 00:23:40,446 - llama.lmstudio_runner - INFO - Discovered 8 models: ['phi-3.1-mini-128k-instruct', 'openthinker3-7b', 'mistral-7b-claude-chat', 'fine_tuned_qwen1.7b', 'tsfc-model-0.0.1', 'codestral-22b-v0.1', 'deepseek-r1-distill-qwen-7b', 'text-embedding-nomic-embed-text-v1.5']
2025-07-14 00:23:40,447 - llama.lmstudio_runner - INFO - Selected preferred model from config: phi-3.1-mini-128k-instruct
2025-07-14 00:23:40,448 - llama.lmstudio_runner - INFO - Initialized LMStudio with model: phi-3.1-mini-128k-instruct
2025-07-14 00:23:40,448 - trading.intelligent_limit_order_manager - INFO - Intelligent Limit Order Manager initialized for professional scalping
2025-07-14 00:23:40,449 - core.llm_action_executors - INFO - ✅ Intelligent Limit Order Manager initialized
2025-07-14 00:23:40,449 - core.llm_action_executors - INFO - 🚀 LLM Action Executors initialized with intelligent limit order system
2025-07-14 00:23:40,449 - utils.cache_manager - INFO - Cache manager initialized: cache, max_age=24h, max_size=100MB
2025-07-14 00:23:40,450 - core.llm_orchestrator - INFO - LLM Prompt Orchestrator initialized
2025-07-14 00:23:40,461 - core.signal_hierarchy - INFO - Intelligent Signal Hierarchy initialized
2025-07-14 00:23:40,461 - trading.signal_trading_engine - INFO - Signal Trading Engine initialized
2025-07-14 00:23:40,470 - storage.database_manager - INFO - Database tables initialized successfully with performance optimizations
2025-07-14 00:23:40,470 - storage.database_manager - INFO - Database manager initialized: data\epinnox_trading.db
2025-07-14 00:23:40,471 - storage.session_manager - INFO - Session Manager initialized
2025-07-14 00:23:40,475 - storage.database_manager - INFO - Created session: live_BTCUSDTUSDT_20250714_002340_93c8b180
2025-07-14 00:23:40,477 - storage.session_manager - INFO - Started session: live_BTCUSDTUSDT_20250714_002340_93c8b180
2025-07-14 00:23:40,606 - core.risk_management_system - INFO - 🛡️ Risk Management System initialized
2025-07-14 00:23:40,608 - core.error_handling_system - INFO - 🛡️ Error Handling System initialized
2025-07-14 00:23:40,609 - core.error_handling_system - INFO - 📊 Component registered: exchange_connection
2025-07-14 00:23:40,609 - core.error_handling_system - INFO - 📊 Component registered: trading_interface
2025-07-14 00:23:40,609 - core.error_handling_system - INFO - 📊 Component registered: market_data
2025-07-14 00:23:40,609 - core.error_handling_system - INFO - 📊 Component registered: llm_orchestrator
2025-07-14 00:23:40,610 - core.error_handling_system - INFO - 🔍 Health monitoring started
2025-07-14 00:23:40,613 - core.monitoring_dashboard - INFO - 📊 Monitoring Dashboard initialized
2025-07-14 00:23:40,613 - symbol_scanner - INFO - SymbolScanner initialized with 8 symbols
2025-07-14 00:23:40,614 - core.llm_action_executors - INFO - 🚀 LLM Action Executors initialized with intelligent limit order system
2025-07-14 00:23:58,815 - data.live_data_manager - INFO - Successfully subscribed to live data for DOGE/USDT:USDT on timeframes: ['1m', '5m', '15m']
2025-07-14 00:25:00,229 - core.llm_orchestrator - INFO - 🔍 FULL MODE: Executing all 8 prompts for comprehensive analysis
2025-07-14 00:25:00,229 - core.llm_orchestrator - INFO - 📊 LLM Thread Usage Start: 0/16 threads
2025-07-14 00:25:00,230 - core.llm_orchestrator - INFO - 🧠 Executing prompt 2/8: market_regime
2025-07-14 00:25:00,230 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-14 00:25:00,230 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-14 00:25:00,231 - llama.lmstudio_runner - INFO - 💬 User Prompt: 📊 MARKET REGIME ANALYST - DOGE/USDT:USDT

🔍 MULTI-TIMEFRAME ANALYSIS:
1m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
5m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
15m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%

📈 VOLATILITY METRICS:
ATR: 0.001000 | Spread: 0.100%
Volume Ratio: 1.0x | Price Range: 1.00%

🎯 REGIME CLASSIFICATION:
- TRENDING_BULL: Strong upward momentum, high volume, clear direction
- TRENDING_BEAR: Strong downward momentum, high volume, clear direction
- RANGING_TIGHT: Low volatility, narrow pric...
2025-07-14 00:25:00,231 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-07-14 00:25:04,544 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 218 chars
2025-07-14 00:25:04,545 - llama.lmstudio_runner - INFO - 📄 Response Preview: ```json
{
  "REGIME": "RANGING_TIGHT",
  "CONFIDENCE": 75,
  "SCALP_SUITABILITY": "MEDIUM",
  "RECOMMENDED_TIMEFRAME": "1m",
  "RISK_LEVEL": "MEDIUM",
  "REASONING": "Consolidation phase with moderate volatility"
}
```...
2025-07-14 00:25:04,545 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 705, Completion: 108, Total: 813
2025-07-14 00:25:04,547 - core.llm_response_parsers - INFO - Market regime parsed: RANGING_TIGHT (MEDIUM scalp suitability)
2025-07-14 00:25:04,648 - core.llm_orchestrator - INFO - 🧠 Executing prompt 3/8: risk_assessment
2025-07-14 00:25:04,648 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-14 00:25:04,649 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-14 00:25:04,649 - llama.lmstudio_runner - INFO - 💬 User Prompt: ...
2025-07-14 00:25:04,649 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-07-14 00:25:08,208 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 227 chars
2025-07-14 00:25:08,208 - llama.lmstudio_runner - INFO - 📄 Response Preview: ```json
{
  "action": "LONG",
  "confidence": 90,
  "entry_reason": "Market broke through resistance level with a sudden spike in volume.",
  "take_profit": 2.5,
  "stop_loss": 1.0,
  "hold_time": "3min",
  "leverage": 40
}
```...
2025-07-14 00:25:08,208 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 174, Completion: 94, Total: 268
2025-07-14 00:25:08,209 - core.llm_response_parsers - INFO - Risk assessment parsed: REJECTED (Risk: 50)
2025-07-14 00:25:08,309 - core.llm_orchestrator - INFO - 🧠 Executing prompt 4/8: entry_timing
2025-07-14 00:25:08,310 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-14 00:25:08,310 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-14 00:25:08,310 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🎯 ENTRY TIMING SPECIALIST - DOGE/USDT:USDT

🎯 SIGNAL ANALYSIS:
ML Ensemble: NEUTRAL (50.0%)
Technical: NEUTRAL | Momentum: 0.00
Volume: PENDING | Orderflow: NEUTRAL

📊 MARKET MICROSTRUCTURE:
Bid/Ask: $0.000000/$0.000000
Spread: 0.100% | L2 Imbalance: 0.0%
Recent Flow: BALANCED | Volume Spike: False

📈 KEY LEVELS:
Support: $0.000000
Resistance: $0.000000
Distance to Support: 0.00%

⏰ TIMING FACTORS:
- Price near key levels: NEUTRAL
- Volume confirmation: PENDING
- Spread conditions: FAVORABLE
- M...
2025-07-14 00:25:08,311 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-07-14 00:25:11,974 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 179 chars
2025-07-14 00:25:11,975 - llama.lmstudio_runner - INFO - 📄 Response Preview: ```json
{
  "action": "WAIT",
  "entry_type": "MARKET/LIMIT",
  "wait_for": "VOLUME_CONFIRMATION",
  "max_wait_seconds": 60,
  "reasoning": "Waiting for volume confirmation"
}
```...
2025-07-14 00:25:11,975 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 603, Completion: 79, Total: 682
2025-07-14 00:25:11,976 - core.llm_response_parsers - INFO - Entry timing parsed: WAIT (LIMIT order)
2025-07-14 00:25:12,077 - core.llm_orchestrator - INFO - 🧠 Executing prompt 5/8: opportunity_scanner
2025-07-14 00:25:12,077 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-14 00:25:12,078 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a conservative futures trading analyst. Analyze market data carefully and provide measured recommendations. Use format: DECISION: [LONG/SHORT/WAIT], CONFIDENCE: [50-100]%, TAKE_PROFIT: [%], ST...
2025-07-14 00:25:12,078 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🔍 INTELLIGENT OPPORTUNITY SCANNER - Account Health Aware

🚨 ACCOUNT HEALTH STATUS: HEALTHY
💰 Balance: $1000.00 | Health: Healthy - Normal Trading
📊 Current Exposure: $0.00 (0.0% of account)
🎯 Trading Allowed: YES

📊 SYMBOL ANALYSIS:
• DOGE/USDT:USDT: $0.000000 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• BTC/USDT:USDT: $95000.000000 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• ETH/USDT:USDT: $3500.000000 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• SOL/USDT:USDT: $200.000000 | Mom: +0.0% | Vol: 1.0x |...
2025-07-14 00:25:12,078 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-07-14 00:25:17,357 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 692 chars
2025-07-14 00:25:17,357 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: LONG, CONFIDENCE: 80%, TAKE_PROFIT: 1.2%, STOP_LOSS: -3%, EXPLANATION: The account is in a healthy state with normal trading parameters and the historical context shows no significant price movement or adverse signals for DOGE/USDT, which has high setup quality according to opportunity cri...
2025-07-14 00:25:17,357 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 926, Completion: 165, Total: 1091
2025-07-14 00:25:17,358 - core.llm_response_parsers - INFO - Parsed structured text: {'DECISION': 'LONG', 'CONFIDENCE': 80.0, 'EXPLANATION': 'THE ACCOUNT IS IN A HEALTHY STATE WITH NORMAL TRADING PARAMETERS AND THE HISTORICAL CONTEXT SHOWS NO SIGNIFICANT PRICE MOVEMENT OR ADVERSE SIGNALS FOR DOGE/USDT, WHICH HAS HIGH SETUP QUALITY ACCORDING TO OPPORTUNITY CRITERIA. A CONSERVATIVE ENTRY STRATEGY ALIGNS WELL WITH BOTH MARKET VOLATILITY PREFERENCES (1-3%) AND RISK MANAGEMENT REQUIREMENTS OF NOT EXCEEDING 2% PER TRADE ON A HEALTHY ACCOUNT BALANCE. THE TAKE PROFIT IS SET AT SLIGHTLY ABOVE THE AVERAGE PRICE INCREASE DUE TO NORMAL TRADING CONDITIONS, WHILE STOP LOSS ENSURES PROTECTION AGAINST SUDDEN ADVERSE MOVEMENTS WITHIN CONSERVATIVE RANGE LIMITS.', 'ACTION': 'ENTER_NOW'}
2025-07-14 00:25:17,358 - core.llm_response_parsers - INFO - Opportunity scanner parsed: NONE (NONE)
2025-07-14 00:25:17,459 - core.llm_orchestrator - INFO - 🧠 Executing prompt 8/8: strategy_adaptation
2025-07-14 00:25:17,459 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-14 00:25:17,459 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-14 00:25:17,460 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🔄 STRATEGY ADAPTATION SPECIALIST

📊 PERFORMANCE ANALYSIS (Last 24h):
Trades: 0 | Win Rate: 50.0%
Avg Profit: 0.80% | Avg Loss: -0.30%
Sharpe Ratio: 1.00 | Max Drawdown: 0.0%
Total PnL: $0.00 | ROI: 0.0%

🎯 CURRENT STRATEGY:
Risk per Trade: 2.0% | Avg Hold Time: 8.0min
Entry Threshold: 70% | Exit Threshold: 60%
Position Size Method: FIXED_RISK | Max Positions: 3

📈 MARKET REGIME: UNKNOWN
Regime Confidence: 50.0%
Scalp Suitability: MEDIUM

🔧 ADAPTATION FACTORS:
- Win rate trending: DOWN
- Drawdown...
2025-07-14 00:25:17,460 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-07-14 00:25:21,913 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 338 chars
2025-07-14 00:25:21,913 - llama.lmstudio_runner - INFO - 📄 Response Preview: ```json
{
  "risk_adjustment": [0.5, 2.0],
  "hold_time_target": "8",
  "entry_threshold": 70,
  "exit_threshold": 60,
  "sizing_method": "FIXED_RISK",
  "reasoning": "Maintain current strategy - performing well with a slight risk reduction to account for recent poor performance and downward trendin...
2025-07-14 00:25:21,914 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 619, Completion: 123, Total: 742
2025-07-14 00:25:21,914 - core.llm_response_parsers - INFO - Strategy adaptation parsed: Risk adj 1.0x
2025-07-14 00:25:21,914 - core.llm_action_executors - INFO - Applying strategy adaptations: Risk 1.0x, Hold time 8min
2025-07-14 00:25:22,014 - core.llm_orchestrator - INFO - 📊 LLM Thread Usage End: 0/16 threads
2025-07-14 00:25:22,015 - core.llm_orchestrator - INFO - 🧠 LLM prompt cycle completed in 21.79s - 5 prompts executed sequentially
2025-07-14 00:25:30,262 - core.llm_orchestrator - INFO - 🔍 FULL MODE: Executing all 8 prompts for comprehensive analysis
2025-07-14 00:25:30,262 - core.llm_orchestrator - INFO - 📊 LLM Thread Usage Start: 0/16 threads
2025-07-14 00:25:30,262 - core.llm_orchestrator - INFO - 🧠 Executing prompt 2/8: market_regime
2025-07-14 00:25:30,262 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-14 00:25:30,262 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-14 00:25:30,262 - llama.lmstudio_runner - INFO - 💬 User Prompt: 📊 MARKET REGIME ANALYST - DOGE/USDT:USDT

🔍 MULTI-TIMEFRAME ANALYSIS:
1m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
5m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
15m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%

📈 VOLATILITY METRICS:
ATR: 0.001000 | Spread: 0.100%
Volume Ratio: 1.0x | Price Range: 1.00%

🎯 REGIME CLASSIFICATION:
- TRENDING_BULL: Strong upward momentum, high volume, clear direction
- TRENDING_BEAR: Strong downward momentum, high volume, clear direction
- RANGING_TIGHT: Low volatility, narrow pric...
2025-07-14 00:25:30,262 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-07-14 00:25:34,841 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 309 chars
2025-07-14 00:25:34,841 - llama.lmstudio_runner - INFO - 📄 Response Preview: ```json
{
  "REGIME": "RANGING_TIGHT",
  "CONFIDENCE": 75,
  "SCALP_SUITABILITY": "MEDIUM",
  "RECOMMENDED_TIMEFRAME": "1m",
  "RISK_LEVEL": "MEDIUM",
  "REASONING": "Consolidation phase with moderate volatility, suitable for scalping during clear trends or when volume is sufficient to support a mov...
2025-07-14 00:25:34,842 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 705, Completion: 126, Total: 831
2025-07-14 00:25:34,842 - core.llm_response_parsers - INFO - Market regime parsed: RANGING_TIGHT (MEDIUM scalp suitability)
2025-07-14 00:25:34,943 - core.llm_orchestrator - INFO - 🧠 Executing prompt 4/8: entry_timing
2025-07-14 00:25:34,944 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-14 00:25:34,944 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-14 00:25:34,944 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🎯 ENTRY TIMING SPECIALIST - DOGE/USDT:USDT

🎯 SIGNAL ANALYSIS:
ML Ensemble: NEUTRAL (50.0%)
Technical: NEUTRAL | Momentum: 0.00
Volume: PENDING | Orderflow: NEUTRAL

📊 MARKET MICROSTRUCTURE:
Bid/Ask: $0.000000/$0.000000
Spread: 0.100% | L2 Imbalance: 0.0%
Recent Flow: BALANCED | Volume Spike: False

📈 KEY LEVELS:
Support: $0.000000
Resistance: $0.000000
Distance to Support: 0.00%

⏰ TIMING FACTORS:
- Price near key levels: NEUTRAL
- Volume confirmation: PENDING
- Spread conditions: FAVORABLE
- M...
2025-07-14 00:25:34,944 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-07-14 00:25:38,499 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 167 chars
2025-07-14 00:25:38,500 - llama.lmstudio_runner - INFO - 📄 Response Preview: {
  "action": "WAIT",
  "entry_type": "MARKET/LIMIT",
  "wait_for": "VOLUME_CONFIRMATION",
  "max_wait_seconds": 60,
  "reasoning": "Waiting for volume confirmation"
}...
2025-07-14 00:25:38,501 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 603, Completion: 74, Total: 677
2025-07-14 00:25:38,502 - core.llm_response_parsers - INFO - Entry timing parsed: WAIT (LIMIT order)
2025-07-14 00:25:38,602 - core.llm_orchestrator - INFO - 📊 LLM Thread Usage End: 0/16 threads
2025-07-14 00:25:38,603 - core.llm_orchestrator - INFO - 🧠 LLM prompt cycle completed in 8.34s - 2 prompts executed sequentially
2025-07-14 00:26:00,303 - core.llm_orchestrator - INFO - 🔍 FULL MODE: Executing all 8 prompts for comprehensive analysis
2025-07-14 00:26:00,303 - core.llm_orchestrator - INFO - 📊 LLM Thread Usage Start: 0/16 threads
2025-07-14 00:26:00,304 - core.llm_orchestrator - INFO - 🧠 Executing prompt 2/8: market_regime
2025-07-14 00:26:00,304 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-14 00:26:00,305 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-14 00:26:00,305 - llama.lmstudio_runner - INFO - 💬 User Prompt: 📊 MARKET REGIME ANALYST - DOGE/USDT:USDT

🔍 MULTI-TIMEFRAME ANALYSIS:
1m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
5m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
15m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%

📈 VOLATILITY METRICS:
ATR: 0.001000 | Spread: 0.100%
Volume Ratio: 1.0x | Price Range: 1.00%

🎯 REGIME CLASSIFICATION:
- TRENDING_BULL: Strong upward momentum, high volume, clear direction
- TRENDING_BEAR: Strong downward momentum, high volume, clear direction
- RANGING_TIGHT: Low volatility, narrow pric...
2025-07-14 00:26:00,306 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-07-14 00:26:04,652 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 265 chars
2025-07-14 00:26:04,654 - llama.lmstudio_runner - INFO - 📄 Response Preview: ```json
{
  "REGIME": "RANGING_TIGHT",
  "CONFIDENCE": 75,
  "SCALP_SUITABILITY": "MEDIUM",
  "RECOMMENDED_TIMEFRAME": "1m",
  "RISK_LEVEL": "MEDIUM",
  "REASONING": "Consolidation phase with moderate volatility, suitable for medium-risk scalping strategies."
}
```...
2025-07-14 00:26:04,654 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 705, Completion: 119, Total: 824
2025-07-14 00:26:04,655 - core.llm_response_parsers - INFO - Market regime parsed: RANGING_TIGHT (MEDIUM scalp suitability)
2025-07-14 00:26:04,756 - core.llm_orchestrator - INFO - 🧠 Executing prompt 3/8: risk_assessment
2025-07-14 00:26:04,757 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-14 00:26:04,757 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-14 00:26:04,758 - llama.lmstudio_runner - INFO - 💬 User Prompt: ...
2025-07-14 00:26:04,758 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-07-14 00:26:08,248 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 209 chars
2025-07-14 00:26:08,248 - llama.lmstudio_runner - INFO - 📄 Response Preview: ```json
{
  "action": "LONG",
  "confidence": 90,
  "entry_reason": "Breakout above resistance level with volume spike.",
  "take_profit": 2.5,
  "stop_loss": 1.0,
  "hold_time": "3min",
  "leverage": 40
}
```...
2025-07-14 00:26:08,249 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 174, Completion: 91, Total: 265
2025-07-14 00:26:08,249 - core.llm_response_parsers - INFO - Risk assessment parsed: REJECTED (Risk: 50)
2025-07-14 00:26:08,350 - core.llm_orchestrator - INFO - 🧠 Executing prompt 4/8: entry_timing
2025-07-14 00:26:08,351 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-14 00:26:08,351 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-14 00:26:08,352 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🎯 ENTRY TIMING SPECIALIST - DOGE/USDT:USDT

🎯 SIGNAL ANALYSIS:
ML Ensemble: NEUTRAL (50.0%)
Technical: NEUTRAL | Momentum: 0.00
Volume: PENDING | Orderflow: NEUTRAL

📊 MARKET MICROSTRUCTURE:
Bid/Ask: $0.000000/$0.000000
Spread: 0.100% | L2 Imbalance: 0.0%
Recent Flow: BALANCED | Volume Spike: False

📈 KEY LEVELS:
Support: $0.000000
Resistance: $0.000000
Distance to Support: 0.00%

⏰ TIMING FACTORS:
- Price near key levels: NEUTRAL
- Volume confirmation: PENDING
- Spread conditions: FAVORABLE
- M...
2025-07-14 00:26:08,353 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-07-14 00:26:11,871 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 167 chars
2025-07-14 00:26:11,871 - llama.lmstudio_runner - INFO - 📄 Response Preview: {
  "action": "WAIT",
  "entry_type": "MARKET/LIMIT",
  "wait_for": "VOLUME_CONFIRMATION",
  "max_wait_seconds": 60,
  "reasoning": "Waiting for volume confirmation"
}...
2025-07-14 00:26:11,872 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 603, Completion: 74, Total: 677
2025-07-14 00:26:11,872 - core.llm_response_parsers - INFO - Entry timing parsed: WAIT (LIMIT order)
2025-07-14 00:26:11,972 - core.llm_orchestrator - INFO - 🧠 Executing prompt 5/8: opportunity_scanner
2025-07-14 00:26:11,973 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-14 00:26:11,973 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a conservative futures trading analyst. Analyze market data carefully and provide measured recommendations. Use format: DECISION: [LONG/SHORT/WAIT], CONFIDENCE: [50-100]%, TAKE_PROFIT: [%], ST...
2025-07-14 00:26:11,974 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🔍 INTELLIGENT OPPORTUNITY SCANNER - Account Health Aware

🚨 ACCOUNT HEALTH STATUS: HEALTHY
💰 Balance: $1000.00 | Health: Healthy - Normal Trading
📊 Current Exposure: $0.00 (0.0% of account)
🎯 Trading Allowed: YES

📊 SYMBOL ANALYSIS:
• DOGE/USDT:USDT: $0.000000 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• BTC/USDT:USDT: $95000.000000 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• ETH/USDT:USDT: $3500.000000 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• SOL/USDT:USDT: $200.000000 | Mom: +0.0% | Vol: 1.0x |...
2025-07-14 00:26:11,974 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-07-14 00:26:17,353 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 577 chars
2025-07-14 00:26:17,354 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: LONG, CONFIDENCE: 80%, TAKE_PROFIT: 1.2%, STOP_LOSS: -3%, EXPLANATION: The account is in a healthy state with normal trading parameters and the historical context shows no significant price movement or signals that would suggest an immediate risk of loss, making DOGE/USDT a suitable conser...
2025-07-14 00:26:17,354 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 926, Completion: 168, Total: 1094
2025-07-14 00:26:17,354 - core.llm_response_parsers - INFO - Parsed structured text: {'DECISION': 'LONG', 'CONFIDENCE': 80.0, 'EXPLANATION': 'THE ACCOUNT IS IN A HEALTHY STATE WITH NORMAL TRADING PARAMETERS AND THE HISTORICAL CONTEXT SHOWS NO SIGNIFICANT PRICE MOVEMENT OR SIGNALS THAT WOULD SUGGEST AN IMMEDIATE RISK OF LOSS, MAKING DOGE/USDT A SUITABLE CONSERVATIVE ENTRY POINT FOR LONG POSITION.', 'ACTION': 'ENTER_NOW'}
2025-07-14 00:26:17,354 - core.llm_response_parsers - INFO - Opportunity scanner parsed: NONE (NONE)
2025-07-14 00:26:17,455 - core.llm_orchestrator - INFO - 📊 LLM Thread Usage End: 0/16 threads
2025-07-14 00:26:17,455 - core.llm_orchestrator - INFO - 🧠 LLM prompt cycle completed in 17.15s - 4 prompts executed sequentially
2025-07-14 00:26:30,624 - core.llm_orchestrator - INFO - 🔍 FULL MODE: Executing all 8 prompts for comprehensive analysis
2025-07-14 00:26:30,624 - core.llm_orchestrator - INFO - 📊 LLM Thread Usage Start: 0/16 threads
2025-07-14 00:26:30,625 - core.llm_orchestrator - INFO - 🧠 Executing prompt 2/8: market_regime
2025-07-14 00:26:30,625 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-14 00:26:30,625 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-14 00:26:30,625 - llama.lmstudio_runner - INFO - 💬 User Prompt: 📊 MARKET REGIME ANALYST - DOGE/USDT:USDT

🔍 MULTI-TIMEFRAME ANALYSIS:
1m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
5m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
15m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%

📈 VOLATILITY METRICS:
ATR: 0.001000 | Spread: 0.100%
Volume Ratio: 1.0x | Price Range: 1.00%

🎯 REGIME CLASSIFICATION:
- TRENDING_BULL: Strong upward momentum, high volume, clear direction
- TRENDING_BEAR: Strong downward momentum, high volume, clear direction
- RANGING_TIGHT: Low volatility, narrow pric...
2025-07-14 00:26:30,626 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-07-14 00:26:35,072 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 218 chars
2025-07-14 00:26:35,072 - llama.lmstudio_runner - INFO - 📄 Response Preview: ```json
{
  "REGIME": "RANGING_TIGHT",
  "CONFIDENCE": 75,
  "SCALP_SUITABILITY": "MEDIUM",
  "RECOMMENDED_TIMEFRAME": "1m",
  "RISK_LEVEL": "MEDIUM",
  "REASONING": "Consolidation phase with moderate volatility"
}
```...
2025-07-14 00:26:35,073 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 705, Completion: 108, Total: 813
2025-07-14 00:26:35,073 - core.llm_response_parsers - INFO - Market regime parsed: RANGING_TIGHT (MEDIUM scalp suitability)
2025-07-14 00:26:35,174 - core.llm_orchestrator - INFO - 🧠 Executing prompt 4/8: entry_timing
2025-07-14 00:26:35,174 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-14 00:26:35,174 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-14 00:26:35,174 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🎯 ENTRY TIMING SPECIALIST - DOGE/USDT:USDT

🎯 SIGNAL ANALYSIS:
ML Ensemble: NEUTRAL (50.0%)
Technical: NEUTRAL | Momentum: 0.00
Volume: PENDING | Orderflow: NEUTRAL

📊 MARKET MICROSTRUCTURE:
Bid/Ask: $0.000000/$0.000000
Spread: 0.100% | L2 Imbalance: 0.0%
Recent Flow: BALANCED | Volume Spike: False

📈 KEY LEVELS:
Support: $0.000000
Resistance: $0.000000
Distance to Support: 0.00%

⏰ TIMING FACTORS:
- Price near key levels: NEUTRAL
- Volume confirmation: PENDING
- Spread conditions: FAVORABLE
- M...
2025-07-14 00:26:35,175 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-07-14 00:26:38,692 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 167 chars
2025-07-14 00:26:38,693 - llama.lmstudio_runner - INFO - 📄 Response Preview: {
  "action": "WAIT",
  "entry_type": "MARKET/LIMIT",
  "wait_for": "VOLUME_CONFIRMATION",
  "max_wait_seconds": 60,
  "reasoning": "Waiting for volume confirmation"
}...
2025-07-14 00:26:38,693 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 603, Completion: 74, Total: 677
2025-07-14 00:26:38,693 - core.llm_response_parsers - INFO - Entry timing parsed: WAIT (LIMIT order)
2025-07-14 00:26:38,794 - core.llm_orchestrator - INFO - 📊 LLM Thread Usage End: 0/16 threads
2025-07-14 00:26:38,795 - core.llm_orchestrator - INFO - 🧠 LLM prompt cycle completed in 8.17s - 2 prompts executed sequentially
2025-07-14 00:27:00,318 - core.llm_orchestrator - INFO - 🔍 FULL MODE: Executing all 8 prompts for comprehensive analysis
2025-07-14 00:27:00,318 - core.llm_orchestrator - INFO - 📊 LLM Thread Usage Start: 0/16 threads
2025-07-14 00:27:00,319 - core.llm_orchestrator - INFO - 🧠 Executing prompt 3/8: risk_assessment
2025-07-14 00:27:00,319 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-14 00:27:00,319 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-14 00:27:00,320 - llama.lmstudio_runner - INFO - 💬 User Prompt: ...
2025-07-14 00:27:00,320 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-07-14 00:27:04,087 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 227 chars
2025-07-14 00:27:04,087 - llama.lmstudio_runner - INFO - 📄 Response Preview: ```json
{
  "action": "LONG",
  "confidence": 90,
  "entry_reason": "Market broke through resistance level with a sudden spike in volume.",
  "take_profit": 2.5,
  "stop_loss": 1.0,
  "hold_time": "3min",
  "leverage": 40
}
```...
2025-07-14 00:27:04,087 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 174, Completion: 94, Total: 268
2025-07-14 00:27:04,088 - core.llm_response_parsers - INFO - Risk assessment parsed: REJECTED (Risk: 50)
2025-07-14 00:27:04,189 - core.llm_orchestrator - INFO - 🧠 Executing prompt 4/8: entry_timing
2025-07-14 00:27:04,189 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-14 00:27:04,189 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-14 00:27:04,190 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🎯 ENTRY TIMING SPECIALIST - DOGE/USDT:USDT

🎯 SIGNAL ANALYSIS:
ML Ensemble: NEUTRAL (50.0%)
Technical: NEUTRAL | Momentum: 0.00
Volume: PENDING | Orderflow: NEUTRAL

📊 MARKET MICROSTRUCTURE:
Bid/Ask: $0.000000/$0.000000
Spread: 0.100% | L2 Imbalance: 0.0%
Recent Flow: BALANCED | Volume Spike: False

📈 KEY LEVELS:
Support: $0.000000
Resistance: $0.000000
Distance to Support: 0.00%

⏰ TIMING FACTORS:
- Price near key levels: NEUTRAL
- Volume confirmation: PENDING
- Spread conditions: FAVORABLE
- M...
2025-07-14 00:27:04,190 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-07-14 00:27:07,961 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 167 chars
2025-07-14 00:27:07,962 - llama.lmstudio_runner - INFO - 📄 Response Preview: {
  "action": "WAIT",
  "entry_type": "MARKET/LIMIT",
  "wait_for": "VOLUME_CONFIRMATION",
  "max_wait_seconds": 60,
  "reasoning": "Waiting for volume confirmation"
}...
2025-07-14 00:27:07,962 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 603, Completion: 74, Total: 677
2025-07-14 00:27:07,962 - core.llm_response_parsers - INFO - Entry timing parsed: WAIT (LIMIT order)
2025-07-14 00:27:08,063 - core.llm_orchestrator - INFO - 🧠 Executing prompt 5/8: opportunity_scanner
2025-07-14 00:27:08,064 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-14 00:27:08,064 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a conservative futures trading analyst. Analyze market data carefully and provide measured recommendations. Use format: DECISION: [LONG/SHORT/WAIT], CONFIDENCE: [50-100]%, TAKE_PROFIT: [%], ST...
2025-07-14 00:27:08,064 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🔍 INTELLIGENT OPPORTUNITY SCANNER - Account Health Aware

🚨 ACCOUNT HEALTH STATUS: HEALTHY
💰 Balance: $1000.00 | Health: Healthy - Normal Trading
📊 Current Exposure: $0.00 (0.0% of account)
🎯 Trading Allowed: YES

📊 SYMBOL ANALYSIS:
• DOGE/USDT:USDT: $0.000000 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• BTC/USDT:USDT: $95000.000000 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• ETH/USDT:USDT: $3500.000000 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• SOL/USDT:USDT: $200.000000 | Mom: +0.0% | Vol: 1.0x |...
2025-07-14 00:27:08,064 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-07-14 00:27:15,016 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 1029 chars
2025-07-14 00:27:15,016 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: LONG, CONFIDENCE: 80%, TAKE_PROFIT: 1.2%, STOP_LOSS: -3%, EXPLANATION: The market data indicates a neutral sector momentum with normal volatility and average volume profile which aligns well for conservative trading strategies, especially considering the account's healthy status. DOGE/USDT...
2025-07-14 00:27:15,016 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 926, Completion: 243, Total: 1169
2025-07-14 00:27:15,017 - core.llm_response_parsers - INFO - Parsed structured text: {'DECISION': 'LONG', 'CONFIDENCE': 80.0, 'EXPLANATION': "THE MARKET DATA INDICATES A NEUTRAL SECTOR MOMENTUM WITH NORMAL VOLATILITY AND AVERAGE VOLUME PROFILE WHICH ALIGNS WELL FOR CONSERVATIVE TRADING STRATEGIES, ESPECIALLY CONSIDERING THE ACCOUNT'S HEALTHY STATUS. DOGE/USDT SHOWS CLEAR SETUP QUALITY AS PER OPPORTUNITY CRITERIA (HIGH), HAS AN EXCELLENT LIQUIDITY SPREAD OF LESS THAN 0.2%, A STRONG ALIGNMENT IN MOMENTUM WITH NO SIGNIFICANT CHANGES OVER TIME AND FALLS WITHIN THE PREFERRED VOLATILITY RANGE AT ONLY +0.0% MOM, WHICH IS INDICATIVE OF STABILITY RATHER THAN HIGH RISK OR REWARD POTENTIAL ALONE BUT STILL MEETS OUR CONSERVATIVE CRITERIA FOR HEALTH-ADJUSTED TRADING PARAMETERS. THE TAKE PROFIT (1.2%) SLIGHTLY EXCEEDS THE ACCOUNT'S MAXIMUM ALLOWED EXPOSURE PER TRADE AND TOTAL MAX EXPOSURE TO MAINTAIN A BALANCED APPROACH WITHOUT OVEREXPOSING, WHILE STOP LOSS IS SET AT -3% WHICH PROVIDES AMPLE PROTECTION IN LINE WITH MODERATE RISK ENVIRONMENT EXPECTATIONS FOR THIS HEALTHY-ACCOUNT SCENARIO.", 'ACTION': 'ENTER_NOW'}
2025-07-14 00:27:15,018 - core.llm_response_parsers - INFO - Opportunity scanner parsed: NONE (NONE)
2025-07-14 00:27:15,118 - core.llm_orchestrator - INFO - 🧠 Executing prompt 8/8: strategy_adaptation
2025-07-14 00:27:15,118 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-14 00:27:15,119 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-14 00:27:15,119 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🔄 STRATEGY ADAPTATION SPECIALIST

📊 PERFORMANCE ANALYSIS (Last 24h):
Trades: 0 | Win Rate: 50.0%
Avg Profit: 0.80% | Avg Loss: -0.30%
Sharpe Ratio: 1.00 | Max Drawdown: 0.0%
Total PnL: $0.00 | ROI: 0.0%

🎯 CURRENT STRATEGY:
Risk per Trade: 2.0% | Avg Hold Time: 8.0min
Entry Threshold: 70% | Exit Threshold: 60%
Position Size Method: FIXED_RISK | Max Positions: 3

📈 MARKET REGIME: UNKNOWN
Regime Confidence: 50.0%
Scalp Suitability: MEDIUM

🔧 ADAPTATION FACTORS:
- Win rate trending: DOWN
- Drawdown...
2025-07-14 00:27:15,119 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-07-14 00:27:19,538 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 338 chars
2025-07-14 00:27:19,539 - llama.lmstudio_runner - INFO - 📄 Response Preview: ```json
{
  "risk_adjustment": [0.5, 2.0],
  "hold_time_target": "8",
  "entry_threshold": 70,
  "exit_threshold": 60,
  "sizing_method": "FIXED_RISK",
  "reasoning": "Maintain current strategy - performing well with a slight risk reduction to account for recent poor performance and downward trend i...
2025-07-14 00:27:19,539 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 619, Completion: 124, Total: 743
2025-07-14 00:27:19,539 - core.llm_response_parsers - INFO - Strategy adaptation parsed: Risk adj 1.0x
2025-07-14 00:27:19,540 - core.llm_action_executors - INFO - Applying strategy adaptations: Risk 1.0x, Hold time 8min
2025-07-14 00:27:19,640 - core.llm_orchestrator - INFO - 📊 LLM Thread Usage End: 0/16 threads
2025-07-14 00:27:19,641 - core.llm_orchestrator - INFO - 🧠 LLM prompt cycle completed in 19.32s - 4 prompts executed sequentially
2025-07-14 00:27:30,769 - core.llm_orchestrator - INFO - 🔍 FULL MODE: Executing all 8 prompts for comprehensive analysis
2025-07-14 00:27:30,770 - core.llm_orchestrator - INFO - 📊 LLM Thread Usage Start: 0/16 threads
2025-07-14 00:27:30,770 - core.llm_orchestrator - INFO - 🧠 Executing prompt 2/8: market_regime
2025-07-14 00:27:30,770 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-14 00:27:30,770 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-14 00:27:30,771 - llama.lmstudio_runner - INFO - 💬 User Prompt: 📊 MARKET REGIME ANALYST - DOGE/USDT:USDT

🔍 MULTI-TIMEFRAME ANALYSIS:
1m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
5m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
15m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%

📈 VOLATILITY METRICS:
ATR: 0.001000 | Spread: 0.100%
Volume Ratio: 1.0x | Price Range: 1.00%

🎯 REGIME CLASSIFICATION:
- TRENDING_BULL: Strong upward momentum, high volume, clear direction
- TRENDING_BEAR: Strong downward momentum, high volume, clear direction
- RANGING_TIGHT: Low volatility, narrow pric...
2025-07-14 00:27:30,771 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-07-14 00:27:34,935 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 218 chars
2025-07-14 00:27:34,936 - llama.lmstudio_runner - INFO - 📄 Response Preview: ```json
{
  "REGIME": "RANGING_TIGHT",
  "CONFIDENCE": 75,
  "SCALP_SUITABILITY": "MEDIUM",
  "RECOMMENDED_TIMEFRAME": "1m",
  "RISK_LEVEL": "MEDIUM",
  "REASONING": "Consolidation phase with moderate volatility"
}
```...
2025-07-14 00:27:34,936 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 705, Completion: 108, Total: 813
2025-07-14 00:27:34,936 - core.llm_response_parsers - INFO - Market regime parsed: RANGING_TIGHT (MEDIUM scalp suitability)
2025-07-14 00:27:35,037 - core.llm_orchestrator - INFO - 🧠 Executing prompt 4/8: entry_timing
2025-07-14 00:27:35,038 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-14 00:27:35,038 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-14 00:27:35,038 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🎯 ENTRY TIMING SPECIALIST - DOGE/USDT:USDT

🎯 SIGNAL ANALYSIS:
ML Ensemble: NEUTRAL (50.0%)
Technical: NEUTRAL | Momentum: 0.00
Volume: PENDING | Orderflow: NEUTRAL

📊 MARKET MICROSTRUCTURE:
Bid/Ask: $0.000000/$0.000000
Spread: 0.100% | L2 Imbalance: 0.0%
Recent Flow: BALANCED | Volume Spike: False

📈 KEY LEVELS:
Support: $0.000000
Resistance: $0.000000
Distance to Support: 0.00%

⏰ TIMING FACTORS:
- Price near key levels: NEUTRAL
- Volume confirmation: PENDING
- Spread conditions: FAVORABLE
- M...
2025-07-14 00:27:35,038 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-07-14 00:27:38,667 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 167 chars
2025-07-14 00:27:38,668 - llama.lmstudio_runner - INFO - 📄 Response Preview: {
  "action": "WAIT",
  "entry_type": "MARKET/LIMIT",
  "wait_for": "VOLUME_CONFIRMATION",
  "max_wait_seconds": 60,
  "reasoning": "Waiting for volume confirmation"
}...
2025-07-14 00:27:38,668 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 603, Completion: 74, Total: 677
2025-07-14 00:27:38,669 - core.llm_response_parsers - INFO - Entry timing parsed: WAIT (LIMIT order)
2025-07-14 00:27:38,769 - core.llm_orchestrator - INFO - 📊 LLM Thread Usage End: 0/16 threads
2025-07-14 00:27:38,770 - core.llm_orchestrator - INFO - 🧠 LLM prompt cycle completed in 8.00s - 2 prompts executed sequentially
2025-07-14 00:28:00,314 - core.llm_orchestrator - INFO - 🔍 FULL MODE: Executing all 8 prompts for comprehensive analysis
2025-07-14 00:28:00,314 - core.llm_orchestrator - INFO - 📊 LLM Thread Usage Start: 0/16 threads
2025-07-14 00:28:00,314 - core.llm_orchestrator - INFO - 🧠 Executing prompt 3/8: risk_assessment
2025-07-14 00:28:00,315 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-14 00:28:00,315 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-14 00:28:00,315 - llama.lmstudio_runner - INFO - 💬 User Prompt: ...
2025-07-14 00:28:00,315 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-07-14 00:28:04,152 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 227 chars
2025-07-14 00:28:04,152 - llama.lmstudio_runner - INFO - 📄 Response Preview: ```json
{
  "action": "LONG",
  "confidence": 90,
  "entry_reason": "Market broke through resistance level with a sudden spike in volume.",
  "take_profit": 2.5,
  "stop_loss": 1.0,
  "hold_time": "3min",
  "leverage": 40
}
```...
2025-07-14 00:28:04,153 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 174, Completion: 94, Total: 268
2025-07-14 00:28:04,154 - core.llm_response_parsers - INFO - Risk assessment parsed: REJECTED (Risk: 50)
2025-07-14 00:28:04,255 - core.llm_orchestrator - INFO - 🧠 Executing prompt 4/8: entry_timing
2025-07-14 00:28:04,256 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-14 00:28:04,257 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-14 00:28:04,257 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🎯 ENTRY TIMING SPECIALIST - DOGE/USDT:USDT

🎯 SIGNAL ANALYSIS:
ML Ensemble: NEUTRAL (50.0%)
Technical: NEUTRAL | Momentum: 0.00
Volume: PENDING | Orderflow: NEUTRAL

📊 MARKET MICROSTRUCTURE:
Bid/Ask: $0.000000/$0.000000
Spread: 0.100% | L2 Imbalance: 0.0%
Recent Flow: BALANCED | Volume Spike: False

📈 KEY LEVELS:
Support: $0.000000
Resistance: $0.000000
Distance to Support: 0.00%

⏰ TIMING FACTORS:
- Price near key levels: NEUTRAL
- Volume confirmation: PENDING
- Spread conditions: FAVORABLE
- M...
2025-07-14 00:28:04,258 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-07-14 00:28:07,822 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 167 chars
2025-07-14 00:28:07,822 - llama.lmstudio_runner - INFO - 📄 Response Preview: {
  "action": "WAIT",
  "entry_type": "MARKET/LIMIT",
  "wait_for": "VOLUME_CONFIRMATION",
  "max_wait_seconds": 60,
  "reasoning": "Waiting for volume confirmation"
}...
2025-07-14 00:28:07,823 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 603, Completion: 74, Total: 677
2025-07-14 00:28:07,824 - core.llm_response_parsers - INFO - Entry timing parsed: WAIT (LIMIT order)
2025-07-14 00:28:07,925 - core.llm_orchestrator - INFO - 🧠 Executing prompt 5/8: opportunity_scanner
2025-07-14 00:28:07,926 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-14 00:28:07,927 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a conservative futures trading analyst. Analyze market data carefully and provide measured recommendations. Use format: DECISION: [LONG/SHORT/WAIT], CONFIDENCE: [50-100]%, TAKE_PROFIT: [%], ST...
2025-07-14 00:28:07,927 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🔍 INTELLIGENT OPPORTUNITY SCANNER - Account Health Aware

🚨 ACCOUNT HEALTH STATUS: HEALTHY
💰 Balance: $1000.00 | Health: Healthy - Normal Trading
📊 Current Exposure: $0.00 (0.0% of account)
🎯 Trading Allowed: YES

📊 SYMBOL ANALYSIS:
• DOGE/USDT:USDT: $0.000000 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• BTC/USDT:USDT: $95000.000000 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• ETH/USDT:USDT: $3500.000000 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• SOL/USDT:USDT: $200.000000 | Mom: +0.0% | Vol: 1.0x |...
2025-07-14 00:28:07,928 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-07-14 00:28:13,025 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 496 chars
2025-07-14 00:28:13,026 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: LONG, CONFIDENCE: 80%, TAKE_PROFIT: 1.2%, STOP_LOSS: -3%, EXPLANATION: The account is in a healthy state with normal trading parameters and the historical context shows no significant price movement or signals that would suggest an immediate risk of loss, making DOGE/USDT a suitable conser...
2025-07-14 00:28:13,026 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 926, Completion: 158, Total: 1084
2025-07-14 00:28:13,027 - core.llm_response_parsers - INFO - Parsed structured text: {'DECISION': 'LONG', 'CONFIDENCE': 80.0, 'EXPLANATION': 'THE ACCOUNT IS IN A HEALTHY STATE WITH NORMAL TRADING PARAMETERS AND THE HISTORICAL CONTEXT SHOWS NO SIGNIFICANT PRICE MOVEMENT OR SIGNALS THAT WOULD SUGGEST AN IMMEDIATE RISK OF LOSS, MAKING DOGE/USDT A SUITABLE CONSERVATIVE ENTRY POINT FOR LONG POSITION.', 'ACTION': 'ENTER_NOW'}
2025-07-14 00:28:13,027 - core.llm_response_parsers - INFO - Opportunity scanner parsed: NONE (NONE)
2025-07-14 00:28:13,128 - core.llm_orchestrator - INFO - 📊 LLM Thread Usage End: 0/16 threads
2025-07-14 00:28:13,129 - core.llm_orchestrator - INFO - 🧠 LLM prompt cycle completed in 12.81s - 3 prompts executed sequentially
2025-07-14 00:28:30,756 - core.llm_orchestrator - INFO - 🔍 FULL MODE: Executing all 8 prompts for comprehensive analysis
2025-07-14 00:28:30,757 - core.llm_orchestrator - INFO - 📊 LLM Thread Usage Start: 0/16 threads
2025-07-14 00:28:30,757 - core.llm_orchestrator - INFO - 🧠 Executing prompt 2/8: market_regime
2025-07-14 00:28:30,757 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-14 00:28:30,757 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-14 00:28:30,758 - llama.lmstudio_runner - INFO - 💬 User Prompt: 📊 MARKET REGIME ANALYST - DOGE/USDT:USDT

🔍 MULTI-TIMEFRAME ANALYSIS:
1m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
5m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
15m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%

📈 VOLATILITY METRICS:
ATR: 0.001000 | Spread: 0.100%
Volume Ratio: 1.0x | Price Range: 1.00%

🎯 REGIME CLASSIFICATION:
- TRENDING_BULL: Strong upward momentum, high volume, clear direction
- TRENDING_BEAR: Strong downward momentum, high volume, clear direction
- RANGING_TIGHT: Low volatility, narrow pric...
2025-07-14 00:28:30,758 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-07-14 00:28:35,097 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 218 chars
2025-07-14 00:28:35,097 - llama.lmstudio_runner - INFO - 📄 Response Preview: ```json
{
  "REGIME": "RANGING_TIGHT",
  "CONFIDENCE": 75,
  "SCALP_SUITABILITY": "MEDIUM",
  "RECOMMENDED_TIMEFRAME": "1m",
  "RISK_LEVEL": "MEDIUM",
  "REASONING": "Consolidation phase with moderate volatility"
}
```...
2025-07-14 00:28:35,097 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 705, Completion: 108, Total: 813
2025-07-14 00:28:35,098 - core.llm_response_parsers - INFO - Market regime parsed: RANGING_TIGHT (MEDIUM scalp suitability)
2025-07-14 00:28:35,198 - core.llm_orchestrator - INFO - 🧠 Executing prompt 4/8: entry_timing
2025-07-14 00:28:35,199 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-14 00:28:35,199 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-14 00:28:35,199 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🎯 ENTRY TIMING SPECIALIST - DOGE/USDT:USDT

🎯 SIGNAL ANALYSIS:
ML Ensemble: NEUTRAL (50.0%)
Technical: NEUTRAL | Momentum: 0.00
Volume: PENDING | Orderflow: NEUTRAL

📊 MARKET MICROSTRUCTURE:
Bid/Ask: $0.000000/$0.000000
Spread: 0.100% | L2 Imbalance: 0.0%
Recent Flow: BALANCED | Volume Spike: False

📈 KEY LEVELS:
Support: $0.000000
Resistance: $0.000000
Distance to Support: 0.00%

⏰ TIMING FACTORS:
- Price near key levels: NEUTRAL
- Volume confirmation: PENDING
- Spread conditions: FAVORABLE
- M...
2025-07-14 00:28:35,199 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-07-14 00:28:38,708 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 167 chars
2025-07-14 00:28:38,708 - llama.lmstudio_runner - INFO - 📄 Response Preview: {
  "action": "WAIT",
  "entry_type": "MARKET/LIMIT",
  "wait_for": "VOLUME_CONFIRMATION",
  "max_wait_seconds": 60,
  "reasoning": "Waiting for volume confirmation"
}...
2025-07-14 00:28:38,709 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 603, Completion: 74, Total: 677
2025-07-14 00:28:38,709 - core.llm_response_parsers - INFO - Entry timing parsed: WAIT (LIMIT order)
2025-07-14 00:28:38,810 - core.llm_orchestrator - INFO - 📊 LLM Thread Usage End: 0/16 threads
2025-07-14 00:28:38,810 - core.llm_orchestrator - INFO - 🧠 LLM prompt cycle completed in 8.05s - 2 prompts executed sequentially
2025-07-14 00:29:00,290 - core.llm_orchestrator - INFO - 🔍 FULL MODE: Executing all 8 prompts for comprehensive analysis
2025-07-14 00:29:00,291 - core.llm_orchestrator - INFO - 📊 LLM Thread Usage Start: 0/16 threads
2025-07-14 00:29:00,291 - core.llm_orchestrator - INFO - 🧠 Executing prompt 3/8: risk_assessment
2025-07-14 00:29:00,291 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-14 00:29:00,291 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-14 00:29:00,292 - llama.lmstudio_runner - INFO - 💬 User Prompt: ...
2025-07-14 00:29:00,292 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-07-14 00:29:04,036 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 227 chars
2025-07-14 00:29:04,036 - llama.lmstudio_runner - INFO - 📄 Response Preview: ```json
{
  "action": "LONG",
  "confidence": 90,
  "entry_reason": "Market broke through resistance level with a sudden spike in volume.",
  "take_profit": 2.5,
  "stop_loss": 1.0,
  "hold_time": "3min",
  "leverage": 40
}
```...
2025-07-14 00:29:04,037 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 174, Completion: 94, Total: 268
2025-07-14 00:29:04,037 - core.llm_response_parsers - INFO - Risk assessment parsed: REJECTED (Risk: 50)
2025-07-14 00:29:04,137 - core.llm_orchestrator - INFO - 🧠 Executing prompt 4/8: entry_timing
2025-07-14 00:29:04,138 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-14 00:29:04,138 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-14 00:29:04,138 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🎯 ENTRY TIMING SPECIALIST - DOGE/USDT:USDT

🎯 SIGNAL ANALYSIS:
ML Ensemble: NEUTRAL (50.0%)
Technical: NEUTRAL | Momentum: 0.00
Volume: PENDING | Orderflow: NEUTRAL

📊 MARKET MICROSTRUCTURE:
Bid/Ask: $0.000000/$0.000000
Spread: 0.100% | L2 Imbalance: 0.0%
Recent Flow: BALANCED | Volume Spike: False

📈 KEY LEVELS:
Support: $0.000000
Resistance: $0.000000
Distance to Support: 0.00%

⏰ TIMING FACTORS:
- Price near key levels: NEUTRAL
- Volume confirmation: PENDING
- Spread conditions: FAVORABLE
- M...
2025-07-14 00:29:04,139 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-07-14 00:29:07,755 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 179 chars
2025-07-14 00:29:07,756 - llama.lmstudio_runner - INFO - 📄 Response Preview: ```json
{
  "action": "WAIT",
  "entry_type": "MARKET/LIMIT",
  "wait_for": "VOLUME_CONFIRMATION",
  "max_wait_seconds": 60,
  "reasoning": "Waiting for volume confirmation"
}
```...
2025-07-14 00:29:07,756 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 603, Completion: 79, Total: 682
2025-07-14 00:29:07,756 - core.llm_response_parsers - INFO - Entry timing parsed: WAIT (LIMIT order)
2025-07-14 00:29:07,857 - core.llm_orchestrator - INFO - 🧠 Executing prompt 5/8: opportunity_scanner
2025-07-14 00:29:07,859 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-14 00:29:07,860 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a conservative futures trading analyst. Analyze market data carefully and provide measured recommendations. Use format: DECISION: [LONG/SHORT/WAIT], CONFIDENCE: [50-100]%, TAKE_PROFIT: [%], ST...
2025-07-14 00:29:07,860 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🔍 INTELLIGENT OPPORTUNITY SCANNER - Account Health Aware

🚨 ACCOUNT HEALTH STATUS: HEALTHY
💰 Balance: $1000.00 | Health: Healthy - Normal Trading
📊 Current Exposure: $0.00 (0.0% of account)
🎯 Trading Allowed: YES

📊 SYMBOL ANALYSIS:
• DOGE/USDT:USDT: $0.000000 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• BTC/USDT:USDT: $95000.000000 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• ETH/USDT:USDT: $3500.000000 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• SOL/USDT:USDT: $200.000000 | Mom: +0.0% | Vol: 1.0x |...
2025-07-14 00:29:07,861 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-07-14 00:29:13,424 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 793 chars
2025-07-14 00:29:13,425 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: LONG, CONFIDENCE: 80%, TAKE_PROFIT: 1.2%, STOP_LOSS: -3%, EXPLANATION: The account is in a healthy state with normal trading parameters and the opportunity for DOGE/USDT shows high setup quality indicating clear patterns or strong signals, which aligns well with our conservative approach g...
2025-07-14 00:29:13,425 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 926, Completion: 185, Total: 1111
2025-07-14 00:29:13,425 - core.llm_response_parsers - INFO - Parsed structured text: {'DECISION': 'LONG', 'CONFIDENCE': 80.0, 'EXPLANATION': 'THE ACCOUNT IS IN A HEALTHY STATE WITH NORMAL TRADING PARAMETERS AND THE OPPORTUNITY FOR DOGE/USDT SHOWS HIGH SETUP QUALITY INDICATING CLEAR PATTERNS OR STRONG SIGNALS, WHICH ALIGNS WELL WITH OUR CONSERVATIVE APPROACH GIVEN ITS LIQUIDITY AND VOLATILITY WITHIN PREFERRED RANGES. A 1:3 RISK REWARD RATIO PROVIDES AN ADEQUATE SAFETY MARGIN WHILE ALLOWING POTENTIAL GAINS IN LINE WITH ACCOUNT HEALTH-ADJUSTED CRITERIA. THE TAKE PROFIT IS SET AT A SLIGHTLY HIGHER LEVEL THAN THE STOP LOSS TO ENSURE WE CAPTURE PROFITS WITHOUT OVERLY AGGRESSIVE POSITIONING, RESPECTING OUR CONSERVATIVE TRADING STRATEGY AND MAINTAINING FOCUS ON PRESERVATION OF CAPITAL WITHIN ACCEPTABLE RISK PARAMETERS FOR THIS PARTICULAR OPPORTUNITY.', 'ACTION': 'ENTER_NOW'}
2025-07-14 00:29:13,426 - core.llm_response_parsers - INFO - Opportunity scanner parsed: NONE (NONE)
2025-07-14 00:29:13,526 - core.llm_orchestrator - INFO - 🧠 Executing prompt 8/8: strategy_adaptation
2025-07-14 00:29:13,526 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-14 00:29:13,526 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-14 00:29:13,526 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🔄 STRATEGY ADAPTATION SPECIALIST

📊 PERFORMANCE ANALYSIS (Last 24h):
Trades: 0 | Win Rate: 50.0%
Avg Profit: 0.80% | Avg Loss: -0.30%
Sharpe Ratio: 1.00 | Max Drawdown: 0.0%
Total PnL: $0.00 | ROI: 0.0%

🎯 CURRENT STRATEGY:
Risk per Trade: 2.0% | Avg Hold Time: 8.0min
Entry Threshold: 70% | Exit Threshold: 60%
Position Size Method: FIXED_RISK | Max Positions: 3

📈 MARKET REGIME: UNKNOWN
Regime Confidence: 50.0%
Scalp Suitability: MEDIUM

🔧 ADAPTATION FACTORS:
- Win rate trending: DOWN
- Drawdown...
2025-07-14 00:29:13,527 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-07-14 00:29:17,881 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 338 chars
2025-07-14 00:29:17,881 - llama.lmstudio_runner - INFO - 📄 Response Preview: ```json
{
  "risk_adjustment": [0.5, 2.0],
  "hold_time_target": "8",
  "entry_threshold": 70,
  "exit_threshold": 60,
  "sizing_method": "FIXED_RISK",
  "reasoning": "Maintain current strategy - performing well with a slight risk reduction to account for recent poor performance and downward trend i...
2025-07-14 00:29:17,882 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 619, Completion: 124, Total: 743
2025-07-14 00:29:17,882 - core.llm_response_parsers - INFO - Strategy adaptation parsed: Risk adj 1.0x
2025-07-14 00:29:17,883 - core.llm_action_executors - INFO - Applying strategy adaptations: Risk 1.0x, Hold time 8min
2025-07-14 00:29:17,987 - core.llm_orchestrator - INFO - 📊 LLM Thread Usage End: 0/16 threads
2025-07-14 00:29:17,987 - core.llm_orchestrator - INFO - 🧠 LLM prompt cycle completed in 17.70s - 4 prompts executed sequentially
2025-07-14 00:29:30,730 - core.llm_orchestrator - INFO - 🔍 FULL MODE: Executing all 8 prompts for comprehensive analysis
2025-07-14 00:29:30,731 - core.llm_orchestrator - INFO - 📊 LLM Thread Usage Start: 0/16 threads
2025-07-14 00:29:30,731 - core.llm_orchestrator - INFO - 🧠 Executing prompt 2/8: market_regime
2025-07-14 00:29:30,731 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-14 00:29:30,732 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-14 00:29:30,732 - llama.lmstudio_runner - INFO - 💬 User Prompt: 📊 MARKET REGIME ANALYST - DOGE/USDT:USDT

🔍 MULTI-TIMEFRAME ANALYSIS:
1m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
5m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
15m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%

📈 VOLATILITY METRICS:
ATR: 0.001000 | Spread: 0.100%
Volume Ratio: 1.0x | Price Range: 1.00%

🎯 REGIME CLASSIFICATION:
- TRENDING_BULL: Strong upward momentum, high volume, clear direction
- TRENDING_BEAR: Strong downward momentum, high volume, clear direction
- RANGING_TIGHT: Low volatility, narrow pric...
2025-07-14 00:29:30,732 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-07-14 00:29:35,112 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 218 chars
2025-07-14 00:29:35,112 - llama.lmstudio_runner - INFO - 📄 Response Preview: ```json
{
  "REGIME": "RANGING_TIGHT",
  "CONFIDENCE": 75,
  "SCALP_SUITABILITY": "MEDIUM",
  "RECOMMENDED_TIMEFRAME": "1m",
  "RISK_LEVEL": "MEDIUM",
  "REASONING": "Consolidation phase with moderate volatility"
}
```...
2025-07-14 00:29:35,112 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 705, Completion: 108, Total: 813
2025-07-14 00:29:35,113 - core.llm_response_parsers - INFO - Market regime parsed: RANGING_TIGHT (MEDIUM scalp suitability)
2025-07-14 00:29:35,213 - core.llm_orchestrator - INFO - 🧠 Executing prompt 4/8: entry_timing
2025-07-14 00:29:35,213 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-14 00:29:35,214 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-14 00:29:35,214 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🎯 ENTRY TIMING SPECIALIST - DOGE/USDT:USDT

🎯 SIGNAL ANALYSIS:
ML Ensemble: NEUTRAL (50.0%)
Technical: NEUTRAL | Momentum: 0.00
Volume: PENDING | Orderflow: NEUTRAL

📊 MARKET MICROSTRUCTURE:
Bid/Ask: $0.000000/$0.000000
Spread: 0.100% | L2 Imbalance: 0.0%
Recent Flow: BALANCED | Volume Spike: False

📈 KEY LEVELS:
Support: $0.000000
Resistance: $0.000000
Distance to Support: 0.00%

⏰ TIMING FACTORS:
- Price near key levels: NEUTRAL
- Volume confirmation: PENDING
- Spread conditions: FAVORABLE
- M...
2025-07-14 00:29:35,214 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-07-14 00:29:38,670 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 167 chars
2025-07-14 00:29:38,670 - llama.lmstudio_runner - INFO - 📄 Response Preview: {
  "action": "WAIT",
  "entry_type": "MARKET/LIMIT",
  "wait_for": "VOLUME_CONFIRMATION",
  "max_wait_seconds": 60,
  "reasoning": "Waiting for volume confirmation"
}...
2025-07-14 00:29:38,670 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 603, Completion: 74, Total: 677
2025-07-14 00:29:38,671 - core.llm_response_parsers - INFO - Entry timing parsed: WAIT (LIMIT order)
2025-07-14 00:29:38,771 - core.llm_orchestrator - INFO - 📊 LLM Thread Usage End: 0/16 threads
2025-07-14 00:29:38,771 - core.llm_orchestrator - INFO - 🧠 LLM prompt cycle completed in 8.04s - 2 prompts executed sequentially
2025-07-14 00:30:00,273 - core.llm_orchestrator - INFO - 🔍 FULL MODE: Executing all 8 prompts for comprehensive analysis
2025-07-14 00:30:00,274 - core.llm_orchestrator - INFO - 📊 LLM Thread Usage Start: 0/16 threads
2025-07-14 00:30:00,274 - core.llm_orchestrator - INFO - 🧠 Executing prompt 3/8: risk_assessment
2025-07-14 00:30:00,274 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-14 00:30:00,274 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-14 00:30:00,275 - llama.lmstudio_runner - INFO - 💬 User Prompt: ...
2025-07-14 00:30:00,275 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-07-14 00:30:04,033 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 225 chars
2025-07-14 00:30:04,033 - llama.lmstudio_runner - INFO - 📄 Response Preview: ```json
{
  "action": "LONG",
  "confidence": 90,
  "entry_reason": "Market broke through resistance level with a rapid price increase.",
  "take_profit": 2.5,
  "stop_loss": 1.0,
  "hold_time": "3min",
  "leverage": 40
}
```...
2025-07-14 00:30:04,034 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 174, Completion: 92, Total: 266
2025-07-14 00:30:04,035 - core.llm_response_parsers - INFO - Risk assessment parsed: REJECTED (Risk: 50)
2025-07-14 00:30:04,136 - core.llm_orchestrator - INFO - 🧠 Executing prompt 4/8: entry_timing
2025-07-14 00:30:04,136 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-14 00:30:04,136 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-14 00:30:04,137 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🎯 ENTRY TIMING SPECIALIST - DOGE/USDT:USDT

🎯 SIGNAL ANALYSIS:
ML Ensemble: NEUTRAL (50.0%)
Technical: NEUTRAL | Momentum: 0.00
Volume: PENDING | Orderflow: NEUTRAL

📊 MARKET MICROSTRUCTURE:
Bid/Ask: $0.000000/$0.000000
Spread: 0.100% | L2 Imbalance: 0.0%
Recent Flow: BALANCED | Volume Spike: False

📈 KEY LEVELS:
Support: $0.000000
Resistance: $0.000000
Distance to Support: 0.00%

⏰ TIMING FACTORS:
- Price near key levels: NEUTRAL
- Volume confirmation: PENDING
- Spread conditions: FAVORABLE
- M...
2025-07-14 00:30:04,137 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-07-14 00:30:07,782 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 167 chars
2025-07-14 00:30:07,782 - llama.lmstudio_runner - INFO - 📄 Response Preview: {
  "action": "WAIT",
  "entry_type": "MARKET/LIMIT",
  "wait_for": "VOLUME_CONFIRMATION",
  "max_wait_seconds": 60,
  "reasoning": "Waiting for volume confirmation"
}...
2025-07-14 00:30:07,782 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 603, Completion: 74, Total: 677
2025-07-14 00:30:07,783 - core.llm_response_parsers - INFO - Entry timing parsed: WAIT (LIMIT order)
2025-07-14 00:30:07,884 - core.llm_orchestrator - INFO - 🧠 Executing prompt 5/8: opportunity_scanner
2025-07-14 00:30:07,885 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-14 00:30:07,885 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a conservative futures trading analyst. Analyze market data carefully and provide measured recommendations. Use format: DECISION: [LONG/SHORT/WAIT], CONFIDENCE: [50-100]%, TAKE_PROFIT: [%], ST...
2025-07-14 00:30:07,886 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🔍 INTELLIGENT OPPORTUNITY SCANNER - Account Health Aware

🚨 ACCOUNT HEALTH STATUS: HEALTHY
💰 Balance: $1000.00 | Health: Healthy - Normal Trading
📊 Current Exposure: $0.00 (0.0% of account)
🎯 Trading Allowed: YES

📊 SYMBOL ANALYSIS:
• DOGE/USDT:USDT: $0.000000 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• BTC/USDT:USDT: $95000.000000 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• ETH/USDT:USDT: $3500.000000 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• SOL/USDT:USDT: $200.000000 | Mom: +0.0% | Vol: 1.0x |...
2025-07-14 00:30:07,886 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-07-14 00:30:13,458 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 539 chars
2025-07-14 00:30:13,458 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: LONG, CONFIDENCE: 80%, TAKE_PROFIT: 1.2%, STOP_LOSS: -3%, EXPLANATION: The account is in a healthy state with normal trading parameters and the historical context indicates that there are no recent price or signal anomalies, suggesting stability for entry into DOGE/USDT at current levels w...
2025-07-14 00:30:13,458 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 926, Completion: 164, Total: 1090
2025-07-14 00:30:13,459 - core.llm_response_parsers - INFO - Parsed structured text: {'DECISION': 'LONG', 'CONFIDENCE': 80.0, 'EXPLANATION': 'THE ACCOUNT IS IN A HEALTHY STATE WITH NORMAL TRADING PARAMETERS AND THE HISTORICAL CONTEXT INDICATES THAT THERE ARE NO RECENT PRICE OR SIGNAL ANOMALIES, SUGGESTING STABILITY FOR ENTRY INTO DOGE/USDT AT CURRENT LEVELS WHILE MAINTAINING CONSERVATIVE RISK MANAGEMENT.', 'ACTION': 'ENTER_NOW'}
2025-07-14 00:30:13,459 - core.llm_response_parsers - INFO - Opportunity scanner parsed: NONE (NONE)
2025-07-14 00:30:13,560 - core.llm_orchestrator - INFO - 📊 LLM Thread Usage End: 0/16 threads
2025-07-14 00:30:13,561 - core.llm_orchestrator - INFO - 🧠 LLM prompt cycle completed in 13.29s - 3 prompts executed sequentially
2025-07-14 00:30:30,332 - core.llm_orchestrator - INFO - 🔍 FULL MODE: Executing all 8 prompts for comprehensive analysis
2025-07-14 00:30:30,332 - core.llm_orchestrator - INFO - 📊 LLM Thread Usage Start: 0/16 threads
2025-07-14 00:30:30,333 - core.llm_orchestrator - INFO - 🧠 Executing prompt 2/8: market_regime
2025-07-14 00:30:30,333 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-14 00:30:30,333 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-14 00:30:30,333 - llama.lmstudio_runner - INFO - 💬 User Prompt: 📊 MARKET REGIME ANALYST - DOGE/USDT:USDT

🔍 MULTI-TIMEFRAME ANALYSIS:
1m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
5m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
15m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%

📈 VOLATILITY METRICS:
ATR: 0.001000 | Spread: 0.100%
Volume Ratio: 1.0x | Price Range: 1.00%

🎯 REGIME CLASSIFICATION:
- TRENDING_BULL: Strong upward momentum, high volume, clear direction
- TRENDING_BEAR: Strong downward momentum, high volume, clear direction
- RANGING_TIGHT: Low volatility, narrow pric...
2025-07-14 00:30:30,333 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-07-14 00:30:34,749 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 218 chars
2025-07-14 00:30:34,750 - llama.lmstudio_runner - INFO - 📄 Response Preview: ```json
{
  "REGIME": "RANGING_TIGHT",
  "CONFIDENCE": 75,
  "SCALP_SUITABILITY": "MEDIUM",
  "RECOMMENDED_TIMEFRAME": "1m",
  "RISK_LEVEL": "MEDIUM",
  "REASONING": "Consolidation phase with moderate volatility"
}
```...
2025-07-14 00:30:34,751 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 705, Completion: 108, Total: 813
2025-07-14 00:30:34,752 - core.llm_response_parsers - INFO - Market regime parsed: RANGING_TIGHT (MEDIUM scalp suitability)
2025-07-14 00:30:34,853 - core.llm_orchestrator - INFO - 🧠 Executing prompt 4/8: entry_timing
2025-07-14 00:30:34,854 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-14 00:30:34,854 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-14 00:30:34,854 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🎯 ENTRY TIMING SPECIALIST - DOGE/USDT:USDT

🎯 SIGNAL ANALYSIS:
ML Ensemble: NEUTRAL (50.0%)
Technical: NEUTRAL | Momentum: 0.00
Volume: PENDING | Orderflow: NEUTRAL

📊 MARKET MICROSTRUCTURE:
Bid/Ask: $0.000000/$0.000000
Spread: 0.100% | L2 Imbalance: 0.0%
Recent Flow: BALANCED | Volume Spike: False

📈 KEY LEVELS:
Support: $0.000000
Resistance: $0.000000
Distance to Support: 0.00%

⏰ TIMING FACTORS:
- Price near key levels: NEUTRAL
- Volume confirmation: PENDING
- Spread conditions: FAVORABLE
- M...
2025-07-14 00:30:34,855 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-07-14 00:30:38,352 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 167 chars
2025-07-14 00:30:38,353 - llama.lmstudio_runner - INFO - 📄 Response Preview: {
  "action": "WAIT",
  "entry_type": "MARKET/LIMIT",
  "wait_for": "VOLUME_CONFIRMATION",
  "max_wait_seconds": 60,
  "reasoning": "Waiting for volume confirmation"
}...
2025-07-14 00:30:38,353 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 603, Completion: 74, Total: 677
2025-07-14 00:30:38,353 - core.llm_response_parsers - INFO - Entry timing parsed: WAIT (LIMIT order)
2025-07-14 00:30:38,454 - core.llm_orchestrator - INFO - 📊 LLM Thread Usage End: 0/16 threads
2025-07-14 00:30:38,454 - core.llm_orchestrator - INFO - 🧠 LLM prompt cycle completed in 8.12s - 2 prompts executed sequentially
2025-07-14 00:31:00,273 - core.llm_orchestrator - INFO - 🔍 FULL MODE: Executing all 8 prompts for comprehensive analysis
2025-07-14 00:31:00,273 - core.llm_orchestrator - INFO - 📊 LLM Thread Usage Start: 0/16 threads
2025-07-14 00:31:00,273 - core.llm_orchestrator - INFO - 🧠 Executing prompt 3/8: risk_assessment
2025-07-14 00:31:00,273 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-14 00:31:00,273 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-14 00:31:00,274 - llama.lmstudio_runner - INFO - 💬 User Prompt: ...
2025-07-14 00:31:00,274 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-07-14 00:31:04,058 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 227 chars
2025-07-14 00:31:04,059 - llama.lmstudio_runner - INFO - 📄 Response Preview: ```json
{
  "action": "LONG",
  "confidence": 90,
  "entry_reason": "Market broke through resistance level with a sudden spike in volume.",
  "take_profit": 2.5,
  "stop_loss": 1.0,
  "hold_time": "3min",
  "leverage": 40
}
```...
2025-07-14 00:31:04,059 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 174, Completion: 94, Total: 268
2025-07-14 00:31:04,059 - core.llm_response_parsers - INFO - Risk assessment parsed: REJECTED (Risk: 50)
2025-07-14 00:31:04,160 - core.llm_orchestrator - INFO - 🧠 Executing prompt 4/8: entry_timing
2025-07-14 00:31:04,161 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-14 00:31:04,161 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-14 00:31:04,161 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🎯 ENTRY TIMING SPECIALIST - DOGE/USDT:USDT

🎯 SIGNAL ANALYSIS:
ML Ensemble: NEUTRAL (50.0%)
Technical: NEUTRAL | Momentum: 0.00
Volume: PENDING | Orderflow: NEUTRAL

📊 MARKET MICROSTRUCTURE:
Bid/Ask: $0.000000/$0.000000
Spread: 0.100% | L2 Imbalance: 0.0%
Recent Flow: BALANCED | Volume Spike: False

📈 KEY LEVELS:
Support: $0.000000
Resistance: $0.000000
Distance to Support: 0.00%

⏰ TIMING FACTORS:
- Price near key levels: NEUTRAL
- Volume confirmation: PENDING
- Spread conditions: FAVORABLE
- M...
2025-07-14 00:31:04,161 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-07-14 00:31:07,765 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 167 chars
2025-07-14 00:31:07,765 - llama.lmstudio_runner - INFO - 📄 Response Preview: {
  "action": "WAIT",
  "entry_type": "MARKET/LIMIT",
  "wait_for": "VOLUME_CONFIRMATION",
  "max_wait_seconds": 60,
  "reasoning": "Waiting for volume confirmation"
}...
2025-07-14 00:31:07,765 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 603, Completion: 74, Total: 677
2025-07-14 00:31:07,766 - core.llm_response_parsers - INFO - Entry timing parsed: WAIT (LIMIT order)
2025-07-14 00:31:07,867 - core.llm_orchestrator - INFO - 🧠 Executing prompt 5/8: opportunity_scanner
2025-07-14 00:31:07,868 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-14 00:31:07,868 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a conservative futures trading analyst. Analyze market data carefully and provide measured recommendations. Use format: DECISION: [LONG/SHORT/WAIT], CONFIDENCE: [50-100]%, TAKE_PROFIT: [%], ST...
2025-07-14 00:31:07,868 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🔍 INTELLIGENT OPPORTUNITY SCANNER - Account Health Aware

🚨 ACCOUNT HEALTH STATUS: HEALTHY
💰 Balance: $1000.00 | Health: Healthy - Normal Trading
📊 Current Exposure: $0.00 (0.0% of account)
🎯 Trading Allowed: YES

📊 SYMBOL ANALYSIS:
• DOGE/USDT:USDT: $0.000000 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• BTC/USDT:USDT: $95000.000000 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• ETH/USDT:USDT: $3500.000000 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• SOL/USDT:USDT: $200.000000 | Mom: +0.0% | Vol: 1.0x |...
2025-07-14 00:31:07,868 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-07-14 00:31:13,109 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 566 chars
2025-07-14 00:31:13,109 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: LONG, CONFIDENCE: 80%, TAKE_PROFIT: 1.2%, STOP_LOSS: -3%, EXPLANATION: The account is in a healthy state with normal trading parameters and the historical context shows no significant price movement or signals that would suggest an immediate risk of loss, making DOGE/USDT a suitable conser...
2025-07-14 00:31:13,109 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 926, Completion: 165, Total: 1091
2025-07-14 00:31:13,110 - core.llm_response_parsers - INFO - Parsed structured text: {'DECISION': 'LONG', 'CONFIDENCE': 80.0, 'EXPLANATION': 'THE ACCOUNT IS IN A HEALTHY STATE WITH NORMAL TRADING PARAMETERS AND THE HISTORICAL CONTEXT SHOWS NO SIGNIFICANT PRICE MOVEMENT OR SIGNALS THAT WOULD SUGGEST AN IMMEDIATE RISK OF LOSS, MAKING DOGE/USDT A SUITABLE CONSERVATIVE ENTRY POINT FOR LONG POSITION.', 'ACTION': 'ENTER_NOW'}
2025-07-14 00:31:13,110 - core.llm_response_parsers - INFO - Opportunity scanner parsed: NONE (NONE)
2025-07-14 00:31:13,211 - core.llm_orchestrator - INFO - 🧠 Executing prompt 8/8: strategy_adaptation
2025-07-14 00:31:13,211 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-14 00:31:13,211 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-14 00:31:13,211 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🔄 STRATEGY ADAPTATION SPECIALIST

📊 PERFORMANCE ANALYSIS (Last 24h):
Trades: 0 | Win Rate: 50.0%
Avg Profit: 0.80% | Avg Loss: -0.30%
Sharpe Ratio: 1.00 | Max Drawdown: 0.0%
Total PnL: $0.00 | ROI: 0.0%

🎯 CURRENT STRATEGY:
Risk per Trade: 2.0% | Avg Hold Time: 8.0min
Entry Threshold: 70% | Exit Threshold: 60%
Position Size Method: FIXED_RISK | Max Positions: 3

📈 MARKET REGIME: UNKNOWN
Regime Confidence: 50.0%
Scalp Suitability: MEDIUM

🔧 ADAPTATION FACTORS:
- Win rate trending: DOWN
- Drawdown...
2025-07-14 00:31:13,212 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-07-14 00:31:17,530 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 338 chars
2025-07-14 00:31:17,531 - llama.lmstudio_runner - INFO - 📄 Response Preview: ```json
{
  "risk_adjustment": [0.5, 2.0],
  "hold_time_target": "8",
  "entry_threshold": 70,
  "exit_threshold": 60,
  "sizing_method": "FIXED_RISK",
  "reasoning": "Maintain current strategy - performing well with a slight risk reduction to account for recent poor performance and downward trend i...
2025-07-14 00:31:17,532 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 619, Completion: 124, Total: 743
2025-07-14 00:31:17,532 - core.llm_response_parsers - INFO - Strategy adaptation parsed: Risk adj 1.0x
2025-07-14 00:31:17,533 - core.llm_action_executors - INFO - Applying strategy adaptations: Risk 1.0x, Hold time 8min
2025-07-14 00:31:17,635 - core.llm_orchestrator - INFO - 📊 LLM Thread Usage End: 0/16 threads
2025-07-14 00:31:17,636 - core.llm_orchestrator - INFO - 🧠 LLM prompt cycle completed in 17.36s - 4 prompts executed sequentially
2025-07-14 00:31:30,283 - core.llm_orchestrator - INFO - 🔍 FULL MODE: Executing all 8 prompts for comprehensive analysis
2025-07-14 00:31:30,283 - core.llm_orchestrator - INFO - 📊 LLM Thread Usage Start: 0/16 threads
2025-07-14 00:31:30,283 - core.llm_orchestrator - INFO - 🧠 Executing prompt 2/8: market_regime
2025-07-14 00:31:30,283 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-14 00:31:30,284 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-14 00:31:30,284 - llama.lmstudio_runner - INFO - 💬 User Prompt: 📊 MARKET REGIME ANALYST - DOGE/USDT:USDT

🔍 MULTI-TIMEFRAME ANALYSIS:
1m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
5m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
15m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%

📈 VOLATILITY METRICS:
ATR: 0.001000 | Spread: 0.100%
Volume Ratio: 1.0x | Price Range: 1.00%

🎯 REGIME CLASSIFICATION:
- TRENDING_BULL: Strong upward momentum, high volume, clear direction
- TRENDING_BEAR: Strong downward momentum, high volume, clear direction
- RANGING_TIGHT: Low volatility, narrow pric...
2025-07-14 00:31:30,284 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-07-14 00:31:34,605 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 265 chars
2025-07-14 00:31:34,605 - llama.lmstudio_runner - INFO - 📄 Response Preview: ```json
{
  "REGIME": "RANGING_TIGHT",
  "CONFIDENCE": 75,
  "SCALP_SUITABILITY": "MEDIUM",
  "RECOMMENDED_TIMEFRAME": "1m",
  "RISK_LEVEL": "MEDIUM",
  "REASONING": "Consolidation phase with moderate volatility, suitable for medium-risk scalping strategies."
}
```...
2025-07-14 00:31:34,606 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 705, Completion: 119, Total: 824
2025-07-14 00:31:34,606 - core.llm_response_parsers - INFO - Market regime parsed: RANGING_TIGHT (MEDIUM scalp suitability)
2025-07-14 00:31:34,706 - core.llm_orchestrator - INFO - 🧠 Executing prompt 4/8: entry_timing
2025-07-14 00:31:34,707 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-14 00:31:34,707 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-14 00:31:34,707 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🎯 ENTRY TIMING SPECIALIST - DOGE/USDT:USDT

🎯 SIGNAL ANALYSIS:
ML Ensemble: NEUTRAL (50.0%)
Technical: NEUTRAL | Momentum: 0.00
Volume: PENDING | Orderflow: NEUTRAL

📊 MARKET MICROSTRUCTURE:
Bid/Ask: $0.000000/$0.000000
Spread: 0.100% | L2 Imbalance: 0.0%
Recent Flow: BALANCED | Volume Spike: False

📈 KEY LEVELS:
Support: $0.000000
Resistance: $0.000000
Distance to Support: 0.00%

⏰ TIMING FACTORS:
- Price near key levels: NEUTRAL
- Volume confirmation: PENDING
- Spread conditions: FAVORABLE
- M...
2025-07-14 00:31:34,707 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-07-14 00:31:38,150 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 167 chars
2025-07-14 00:31:38,150 - llama.lmstudio_runner - INFO - 📄 Response Preview: {
  "action": "WAIT",
  "entry_type": "MARKET/LIMIT",
  "wait_for": "VOLUME_CONFIRMATION",
  "max_wait_seconds": 60,
  "reasoning": "Waiting for volume confirmation"
}...
2025-07-14 00:31:38,150 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 603, Completion: 74, Total: 677
2025-07-14 00:31:38,151 - core.llm_response_parsers - INFO - Entry timing parsed: WAIT (LIMIT order)
2025-07-14 00:31:38,251 - core.llm_orchestrator - INFO - 📊 LLM Thread Usage End: 0/16 threads
2025-07-14 00:31:38,252 - core.llm_orchestrator - INFO - 🧠 LLM prompt cycle completed in 7.97s - 2 prompts executed sequentially
2025-07-14 00:32:00,797 - core.llm_orchestrator - INFO - 🔍 FULL MODE: Executing all 8 prompts for comprehensive analysis
2025-07-14 00:32:00,797 - core.llm_orchestrator - INFO - 📊 LLM Thread Usage Start: 0/16 threads
2025-07-14 00:32:00,797 - core.llm_orchestrator - INFO - 🧠 Executing prompt 2/8: market_regime
2025-07-14 00:32:00,797 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-14 00:32:00,798 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-14 00:32:00,798 - llama.lmstudio_runner - INFO - 💬 User Prompt: 📊 MARKET REGIME ANALYST - DOGE/USDT:USDT

🔍 MULTI-TIMEFRAME ANALYSIS:
1m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
5m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
15m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%

📈 VOLATILITY METRICS:
ATR: 0.001000 | Spread: 0.100%
Volume Ratio: 1.0x | Price Range: 1.00%

🎯 REGIME CLASSIFICATION:
- TRENDING_BULL: Strong upward momentum, high volume, clear direction
- TRENDING_BEAR: Strong downward momentum, high volume, clear direction
- RANGING_TIGHT: Low volatility, narrow pric...
2025-07-14 00:32:00,798 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-07-14 00:32:05,131 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 265 chars
2025-07-14 00:32:05,132 - llama.lmstudio_runner - INFO - 📄 Response Preview: ```json
{
  "REGIME": "RANGING_TIGHT",
  "CONFIDENCE": 75,
  "SCALP_SUITABILITY": "MEDIUM",
  "RECOMMENDED_TIMEFRAME": "1m",
  "RISK_LEVEL": "MEDIUM",
  "REASONING": "Consolidation phase with moderate volatility, suitable for medium-risk scalping strategies."
}
```...
2025-07-14 00:32:05,132 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 705, Completion: 119, Total: 824
2025-07-14 00:32:05,132 - core.llm_response_parsers - INFO - Market regime parsed: RANGING_TIGHT (MEDIUM scalp suitability)
2025-07-14 00:32:05,233 - core.llm_orchestrator - INFO - 🧠 Executing prompt 3/8: risk_assessment
2025-07-14 00:32:05,233 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-14 00:32:05,234 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-14 00:32:05,234 - llama.lmstudio_runner - INFO - 💬 User Prompt: ...
2025-07-14 00:32:05,234 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-07-14 00:32:08,784 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 209 chars
2025-07-14 00:32:08,784 - llama.lmstudio_runner - INFO - 📄 Response Preview: ```json
{
  "action": "LONG",
  "confidence": 90,
  "entry_reason": "Breakout above resistance level with volume spike.",
  "take_profit": 2.5,
  "stop_loss": 1.0,
  "hold_time": "3min",
  "leverage": 40
}
```...
2025-07-14 00:32:08,784 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 174, Completion: 91, Total: 265
2025-07-14 00:32:08,784 - core.llm_response_parsers - INFO - Risk assessment parsed: REJECTED (Risk: 50)
2025-07-14 00:32:08,885 - core.llm_orchestrator - INFO - 🧠 Executing prompt 4/8: entry_timing
2025-07-14 00:32:08,886 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-14 00:32:08,886 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-14 00:32:08,886 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🎯 ENTRY TIMING SPECIALIST - DOGE/USDT:USDT

🎯 SIGNAL ANALYSIS:
ML Ensemble: NEUTRAL (50.0%)
Technical: NEUTRAL | Momentum: 0.00
Volume: PENDING | Orderflow: NEUTRAL

📊 MARKET MICROSTRUCTURE:
Bid/Ask: $0.000000/$0.000000
Spread: 0.100% | L2 Imbalance: 0.0%
Recent Flow: BALANCED | Volume Spike: False

📈 KEY LEVELS:
Support: $0.000000
Resistance: $0.000000
Distance to Support: 0.00%

⏰ TIMING FACTORS:
- Price near key levels: NEUTRAL
- Volume confirmation: PENDING
- Spread conditions: FAVORABLE
- M...
2025-07-14 00:32:08,886 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-07-14 00:32:12,502 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 167 chars
2025-07-14 00:32:12,503 - llama.lmstudio_runner - INFO - 📄 Response Preview: {
  "action": "WAIT",
  "entry_type": "MARKET/LIMIT",
  "wait_for": "VOLUME_CONFIRMATION",
  "max_wait_seconds": 60,
  "reasoning": "Waiting for volume confirmation"
}...
2025-07-14 00:32:12,503 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 603, Completion: 74, Total: 677
2025-07-14 00:32:12,504 - core.llm_response_parsers - INFO - Entry timing parsed: WAIT (LIMIT order)
2025-07-14 00:32:12,605 - core.llm_orchestrator - INFO - 🧠 Executing prompt 5/8: opportunity_scanner
2025-07-14 00:32:12,606 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-14 00:32:12,606 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a conservative futures trading analyst. Analyze market data carefully and provide measured recommendations. Use format: DECISION: [LONG/SHORT/WAIT], CONFIDENCE: [50-100]%, TAKE_PROFIT: [%], ST...
2025-07-14 00:32:12,606 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🔍 INTELLIGENT OPPORTUNITY SCANNER - Account Health Aware

🚨 ACCOUNT HEALTH STATUS: HEALTHY
💰 Balance: $1000.00 | Health: Healthy - Normal Trading
📊 Current Exposure: $0.00 (0.0% of account)
🎯 Trading Allowed: YES

📊 SYMBOL ANALYSIS:
• DOGE/USDT:USDT: $0.000000 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• BTC/USDT:USDT: $95000.000000 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• ETH/USDT:USDT: $3500.000000 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• SOL/USDT:USDT: $200.000000 | Mom: +0.0% | Vol: 1.0x |...
2025-07-14 00:32:12,607 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-07-14 00:32:18,301 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 676 chars
2025-07-14 00:32:18,302 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: LONG, CONFIDENCE: 80%, TAKE_PROFIT: 2.5%, STOP_LOSS: -3.5%, EXPLANATION: The account is in a healthy state with normal trading parameters and the historical context shows no significant price movement or signals that would suggest an immediate risk of loss, making DOGE/USDT a suitable cons...
2025-07-14 00:32:18,302 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 926, Completion: 194, Total: 1120
2025-07-14 00:32:18,302 - core.llm_response_parsers - INFO - Parsed structured text: {'DECISION': 'LONG', 'CONFIDENCE': 80.0, 'EXPLANATION': 'THE ACCOUNT IS IN A HEALTHY STATE WITH NORMAL TRADING PARAMETERS AND THE HISTORICAL CONTEXT SHOWS NO SIGNIFICANT PRICE MOVEMENT OR SIGNALS THAT WOULD SUGGEST AN IMMEDIATE RISK OF LOSS, MAKING DOGE/USDT A SUITABLE CONSERVATIVE ENTRY POINT FOR LONG POSITION CONSIDERING ITS SETUP QUALITY AS HIGH ACCORDING TO OPPORTUNITY CRITERIA.', 'ACTION': 'ENTER_NOW'}
2025-07-14 00:32:18,303 - core.llm_response_parsers - INFO - Opportunity scanner parsed: NONE (NONE)
2025-07-14 00:32:18,403 - core.llm_orchestrator - INFO - 📊 LLM Thread Usage End: 0/16 threads
2025-07-14 00:32:18,403 - core.llm_orchestrator - INFO - 🧠 LLM prompt cycle completed in 17.61s - 4 prompts executed sequentially
2025-07-14 00:32:30,428 - core.llm_orchestrator - INFO - 🔍 FULL MODE: Executing all 8 prompts for comprehensive analysis
2025-07-14 00:32:30,428 - core.llm_orchestrator - INFO - 📊 LLM Thread Usage Start: 0/16 threads
2025-07-14 00:32:30,428 - core.llm_orchestrator - INFO - 🧠 Executing prompt 4/8: entry_timing
2025-07-14 00:32:30,428 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-14 00:32:30,428 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-14 00:32:30,429 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🎯 ENTRY TIMING SPECIALIST - DOGE/USDT:USDT

🎯 SIGNAL ANALYSIS:
ML Ensemble: NEUTRAL (50.0%)
Technical: NEUTRAL | Momentum: 0.00
Volume: PENDING | Orderflow: NEUTRAL

📊 MARKET MICROSTRUCTURE:
Bid/Ask: $0.000000/$0.000000
Spread: 0.100% | L2 Imbalance: 0.0%
Recent Flow: BALANCED | Volume Spike: False

📈 KEY LEVELS:
Support: $0.000000
Resistance: $0.000000
Distance to Support: 0.00%

⏰ TIMING FACTORS:
- Price near key levels: NEUTRAL
- Volume confirmation: PENDING
- Spread conditions: FAVORABLE
- M...
2025-07-14 00:32:30,429 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-07-14 00:32:34,434 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 305 chars
2025-07-14 00:32:34,434 - llama.lmstudio_runner - INFO - 📄 Response Preview: {
  "action": "ENTER",
  "entry_type": "LIMIT",
  "confidence": 80,
  "wait_for": null,
  "max_wait_seconds": 30,
  "reasoning": "Market conditions are favorable with a low spread and no significant imbalance. The risk/reward ratio is acceptable at 2:1 for the potential move towards resistance level...
2025-07-14 00:32:34,435 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 610, Completion: 100, Total: 710
2025-07-14 00:32:34,435 - core.llm_response_parsers - WARNING - JSON decode error: Expecting ',' delimiter: line 7 column 137 (char 250)
2025-07-14 00:32:34,537 - core.llm_orchestrator - INFO - 📊 LLM Thread Usage End: 0/16 threads
2025-07-14 00:32:34,537 - core.llm_orchestrator - INFO - 🧠 LLM prompt cycle completed in 4.11s - 1 prompts executed sequentially
2025-07-14 00:33:00,296 - core.llm_orchestrator - INFO - 🔍 FULL MODE: Executing all 8 prompts for comprehensive analysis
2025-07-14 00:33:00,297 - core.llm_orchestrator - INFO - 📊 LLM Thread Usage Start: 0/16 threads
2025-07-14 00:33:00,297 - core.llm_orchestrator - INFO - 🧠 Executing prompt 2/8: market_regime
2025-07-14 00:33:00,298 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-14 00:33:00,298 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-14 00:33:00,299 - llama.lmstudio_runner - INFO - 💬 User Prompt: 📊 MARKET REGIME ANALYST - DOGE/USDT:USDT

🔍 MULTI-TIMEFRAME ANALYSIS:
1m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
5m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
15m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%

📈 VOLATILITY METRICS:
ATR: 0.001000 | Spread: 0.100%
Volume Ratio: 1.0x | Price Range: 1.00%

🎯 REGIME CLASSIFICATION:
- TRENDING_BULL: Strong upward momentum, high volume, clear direction
- TRENDING_BEAR: Strong downward momentum, high volume, clear direction
- RANGING_TIGHT: Low volatility, narrow pric...
2025-07-14 00:33:00,300 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-07-14 00:33:04,529 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 218 chars
2025-07-14 00:33:04,530 - llama.lmstudio_runner - INFO - 📄 Response Preview: ```json
{
  "REGIME": "RANGING_TIGHT",
  "CONFIDENCE": 75,
  "SCALP_SUITABILITY": "MEDIUM",
  "RECOMMENDED_TIMEFRAME": "1m",
  "RISK_LEVEL": "MEDIUM",
  "REASONING": "Consolidation phase with moderate volatility"
}
```...
2025-07-14 00:33:04,530 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 705, Completion: 108, Total: 813
2025-07-14 00:33:04,531 - core.llm_response_parsers - INFO - Market regime parsed: RANGING_TIGHT (MEDIUM scalp suitability)
2025-07-14 00:33:04,632 - core.llm_orchestrator - INFO - 🧠 Executing prompt 3/8: risk_assessment
2025-07-14 00:33:04,632 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-14 00:33:04,632 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-14 00:33:04,632 - llama.lmstudio_runner - INFO - 💬 User Prompt: ...
2025-07-14 00:33:04,633 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-07-14 00:33:08,234 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 221 chars
2025-07-14 00:33:08,235 - llama.lmstudio_runner - INFO - 📄 Response Preview: ```json
{
  "action": "LONG",
  "confidence": 90,
  "entry_reason": "Breakout above resistance level with strong volume indicators.",
  "take_profit": 2.5,
  "stop_loss": 1.0,
  "hold_time": "3min",
  "leverage": 40
}
```...
2025-07-14 00:33:08,235 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 174, Completion: 92, Total: 266
2025-07-14 00:33:08,236 - core.llm_response_parsers - INFO - Risk assessment parsed: REJECTED (Risk: 50)
2025-07-14 00:33:08,339 - core.llm_orchestrator - INFO - 🧠 Executing prompt 4/8: entry_timing
2025-07-14 00:33:08,339 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-14 00:33:08,340 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-14 00:33:08,340 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🎯 ENTRY TIMING SPECIALIST - DOGE/USDT:USDT

🎯 SIGNAL ANALYSIS:
ML Ensemble: NEUTRAL (50.0%)
Technical: NEUTRAL | Momentum: 0.00
Volume: PENDING | Orderflow: NEUTRAL

📊 MARKET MICROSTRUCTURE:
Bid/Ask: $0.000000/$0.000000
Spread: 0.100% | L2 Imbalance: 0.0%
Recent Flow: BALANCED | Volume Spike: False

📈 KEY LEVELS:
Support: $0.000000
Resistance: $0.000000
Distance to Support: 0.00%

⏰ TIMING FACTORS:
- Price near key levels: NEUTRAL
- Volume confirmation: PENDING
- Spread conditions: FAVORABLE
- M...
2025-07-14 00:33:08,340 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-07-14 00:33:11,845 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 167 chars
2025-07-14 00:33:11,845 - llama.lmstudio_runner - INFO - 📄 Response Preview: {
  "action": "WAIT",
  "entry_type": "MARKET/LIMIT",
  "wait_for": "VOLUME_CONFIRMATION",
  "max_wait_seconds": 60,
  "reasoning": "Waiting for volume confirmation"
}...
2025-07-14 00:33:11,845 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 603, Completion: 74, Total: 677
2025-07-14 00:33:11,846 - core.llm_response_parsers - INFO - Entry timing parsed: WAIT (LIMIT order)
2025-07-14 00:33:11,946 - core.llm_orchestrator - INFO - 🧠 Executing prompt 5/8: opportunity_scanner
2025-07-14 00:33:11,947 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-14 00:33:11,947 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a conservative futures trading analyst. Analyze market data carefully and provide measured recommendations. Use format: DECISION: [LONG/SHORT/WAIT], CONFIDENCE: [50-100]%, TAKE_PROFIT: [%], ST...
2025-07-14 00:33:11,947 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🔍 INTELLIGENT OPPORTUNITY SCANNER - Account Health Aware

🚨 ACCOUNT HEALTH STATUS: HEALTHY
💰 Balance: $1000.00 | Health: Healthy - Normal Trading
📊 Current Exposure: $0.00 (0.0% of account)
🎯 Trading Allowed: YES

📊 SYMBOL ANALYSIS:
• DOGE/USDT:USDT: $0.000000 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• BTC/USDT:USDT: $95000.000000 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• ETH/USDT:USDT: $3500.000000 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• SOL/USDT:USDT: $200.000000 | Mom: +0.0% | Vol: 1.0x |...
2025-07-14 00:33:11,948 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-07-14 00:33:17,164 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 698 chars
2025-07-14 00:33:17,165 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: LONG, CONFIDENCE: 80%, TAKE_PROFIT: 1.2%, STOP_LOSS: -3%, EXPLANATION: The account is in a healthy state with normal trading parameters and the historical context shows stability for DOGE/USDT which aligns well with our conservative opportunity criteria, including liquidity requirements of...
2025-07-14 00:33:17,165 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 926, Completion: 164, Total: 1090
2025-07-14 00:33:17,166 - core.llm_response_parsers - INFO - Parsed structured text: {'DECISION': 'LONG', 'CONFIDENCE': 80.0, 'EXPLANATION': 'THE ACCOUNT IS IN A HEALTHY STATE WITH NORMAL TRADING PARAMETERS AND THE HISTORICAL CONTEXT SHOWS STABILITY FOR DOGE/USDT WHICH ALIGNS WELL WITH OUR CONSERVATIVE OPPORTUNITY CRITERIA, INCLUDING LIQUIDITY REQUIREMENTS OF LESS THAN 0.2% SPREAD AND STRONG MOMENTUM ALIGNMENT WITHIN A MODERATE VOLATILITY RANGE (1-3%). THE SETUP QUALITY IS HIGH AS INDICATED BY CLEAR PATTERNS OR SIGNALS IN THE MARKET DATA PROVIDED; THUS MAKING IT AN INTELLIGENT CHOICE FOR LONG POSITIONING WITH APPROPRIATE RISK MANAGEMENT MEASURES SET AT TAKE PROFIT SLIGHTLY ABOVE ENTRY PRICE TO ENSURE POTENTIAL GAINS WHILE MAINTAINING ACCOUNT HEALTH.', 'ACTION': 'ENTER_NOW'}
2025-07-14 00:33:17,166 - core.llm_response_parsers - INFO - Opportunity scanner parsed: NONE (NONE)
2025-07-14 00:33:17,267 - core.llm_orchestrator - INFO - 🧠 Executing prompt 8/8: strategy_adaptation
2025-07-14 00:33:17,267 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-14 00:33:17,267 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-14 00:33:17,268 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🔄 STRATEGY ADAPTATION SPECIALIST

📊 PERFORMANCE ANALYSIS (Last 24h):
Trades: 0 | Win Rate: 50.0%
Avg Profit: 0.80% | Avg Loss: -0.30%
Sharpe Ratio: 1.00 | Max Drawdown: 0.0%
Total PnL: $0.00 | ROI: 0.0%

🎯 CURRENT STRATEGY:
Risk per Trade: 2.0% | Avg Hold Time: 8.0min
Entry Threshold: 70% | Exit Threshold: 60%
Position Size Method: FIXED_RISK | Max Positions: 3

📈 MARKET REGIME: UNKNOWN
Regime Confidence: 50.0%
Scalp Suitability: MEDIUM

🔧 ADAPTATION FACTORS:
- Win rate trending: DOWN
- Drawdown...
2025-07-14 00:33:17,268 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-07-14 00:33:21,903 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 412 chars
2025-07-14 00:33:21,904 - llama.lmstudio_runner - INFO - 📄 Response Preview: ```json
{
  "risk_adjustment": [0.5, 2.0],
  "hold_time_target": "8",
  "entry_threshold": 70,
  "exit_threshold": 60,
  "sizing_method": "FIXED_RISK",
  "reasoning": "Maintain current strategy - performing well with a moderate win rate and acceptable drawdown level. Adjust risk to account for recen...
2025-07-14 00:33:21,904 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 619, Completion: 138, Total: 757
2025-07-14 00:33:21,905 - core.llm_response_parsers - INFO - Strategy adaptation parsed: Risk adj 1.0x
2025-07-14 00:33:21,905 - core.llm_action_executors - INFO - Applying strategy adaptations: Risk 1.0x, Hold time 8min
2025-07-14 00:33:22,006 - core.llm_orchestrator - INFO - 📊 LLM Thread Usage End: 0/16 threads
2025-07-14 00:33:22,007 - core.llm_orchestrator - INFO - 🧠 LLM prompt cycle completed in 21.71s - 5 prompts executed sequentially
2025-07-14 00:33:30,290 - core.llm_orchestrator - INFO - 🔍 FULL MODE: Executing all 8 prompts for comprehensive analysis
2025-07-14 00:33:30,291 - core.llm_orchestrator - INFO - 📊 LLM Thread Usage Start: 0/16 threads
2025-07-14 00:33:30,291 - core.llm_orchestrator - INFO - 🧠 Executing prompt 4/8: entry_timing
2025-07-14 00:33:30,292 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-14 00:33:30,292 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-14 00:33:30,293 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🎯 ENTRY TIMING SPECIALIST - DOGE/USDT:USDT

🎯 SIGNAL ANALYSIS:
ML Ensemble: NEUTRAL (50.0%)
Technical: NEUTRAL | Momentum: 0.00
Volume: PENDING | Orderflow: NEUTRAL

📊 MARKET MICROSTRUCTURE:
Bid/Ask: $0.000000/$0.000000
Spread: 0.100% | L2 Imbalance: 0.0%
Recent Flow: BALANCED | Volume Spike: False

📈 KEY LEVELS:
Support: $0.000000
Resistance: $0.000000
Distance to Support: 0.00%

⏰ TIMING FACTORS:
- Price near key levels: NEUTRAL
- Volume confirmation: PENDING
- Spread conditions: FAVORABLE
- M...
2025-07-14 00:33:30,293 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-07-14 00:33:34,563 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 381 chars
2025-07-14 00:33:34,564 - llama.lmstudio_runner - INFO - 📄 Response Preview: {
  "action": "ENTER",
  "entry_type": "LIMIT",
  "confidence": 85,
  "wait_for": null,
  "max_wait_seconds": 0,
  "reasoning": "Market conditions are favorable with a low spread and balanced recent flow. Despite the neutral signals from ML Ensemble and technical indicators, historical context shows...
2025-07-14 00:33:34,564 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 610, Completion: 108, Total: 718
2025-07-14 00:33:34,565 - core.llm_response_parsers - INFO - Entry timing parsed: WAIT (LIMIT order)
2025-07-14 00:33:34,665 - core.llm_orchestrator - INFO - 📊 LLM Thread Usage End: 0/16 threads
2025-07-14 00:33:34,666 - core.llm_orchestrator - INFO - 🧠 LLM prompt cycle completed in 4.37s - 1 prompts executed sequentially
2025-07-14 00:34:00,305 - core.llm_orchestrator - INFO - 🔍 FULL MODE: Executing all 8 prompts for comprehensive analysis
2025-07-14 00:34:00,306 - core.llm_orchestrator - INFO - 📊 LLM Thread Usage Start: 0/16 threads
2025-07-14 00:34:00,306 - core.llm_orchestrator - INFO - 🧠 Executing prompt 2/8: market_regime
2025-07-14 00:34:00,306 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-14 00:34:00,307 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-14 00:34:00,307 - llama.lmstudio_runner - INFO - 💬 User Prompt: 📊 MARKET REGIME ANALYST - DOGE/USDT:USDT

🔍 MULTI-TIMEFRAME ANALYSIS:
1m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
5m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
15m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%

📈 VOLATILITY METRICS:
ATR: 0.001000 | Spread: 0.100%
Volume Ratio: 1.0x | Price Range: 1.00%

🎯 REGIME CLASSIFICATION:
- TRENDING_BULL: Strong upward momentum, high volume, clear direction
- TRENDING_BEAR: Strong downward momentum, high volume, clear direction
- RANGING_TIGHT: Low volatility, narrow pric...
2025-07-14 00:34:00,307 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-07-14 00:34:04,611 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 218 chars
2025-07-14 00:34:04,611 - llama.lmstudio_runner - INFO - 📄 Response Preview: ```json
{
  "REGIME": "RANGING_TIGHT",
  "CONFIDENCE": 75,
  "SCALP_SUITABILITY": "MEDIUM",
  "RECOMMENDED_TIMEFRAME": "1m",
  "RISK_LEVEL": "MEDIUM",
  "REASONING": "Consolidation phase with moderate volatility"
}
```...
2025-07-14 00:34:04,611 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 705, Completion: 108, Total: 813
2025-07-14 00:34:04,612 - core.llm_response_parsers - INFO - Market regime parsed: RANGING_TIGHT (MEDIUM scalp suitability)
2025-07-14 00:34:04,712 - core.llm_orchestrator - INFO - 🧠 Executing prompt 3/8: risk_assessment
2025-07-14 00:34:04,713 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-14 00:34:04,714 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-14 00:34:04,715 - llama.lmstudio_runner - INFO - 💬 User Prompt: ...
2025-07-14 00:34:04,715 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-07-14 00:34:08,291 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 227 chars
2025-07-14 00:34:08,291 - llama.lmstudio_runner - INFO - 📄 Response Preview: ```json
{
  "action": "LONG",
  "confidence": 90,
  "entry_reason": "Market broke through resistance level with a sudden spike in volume.",
  "take_profit": 2.5,
  "stop_loss": 1.0,
  "hold_time": "3min",
  "leverage": 40
}
```...
2025-07-14 00:34:08,292 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 174, Completion: 94, Total: 268
2025-07-14 00:34:08,292 - core.llm_response_parsers - INFO - Risk assessment parsed: REJECTED (Risk: 50)
2025-07-14 00:34:08,393 - core.llm_orchestrator - INFO - 🧠 Executing prompt 4/8: entry_timing
2025-07-14 00:34:08,394 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-14 00:34:08,395 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-14 00:34:08,395 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🎯 ENTRY TIMING SPECIALIST - DOGE/USDT:USDT

🎯 SIGNAL ANALYSIS:
ML Ensemble: NEUTRAL (50.0%)
Technical: NEUTRAL | Momentum: 0.00
Volume: PENDING | Orderflow: NEUTRAL

📊 MARKET MICROSTRUCTURE:
Bid/Ask: $0.000000/$0.000000
Spread: 0.100% | L2 Imbalance: 0.0%
Recent Flow: BALANCED | Volume Spike: False

📈 KEY LEVELS:
Support: $0.000000
Resistance: $0.000000
Distance to Support: 0.00%

⏰ TIMING FACTORS:
- Price near key levels: NEUTRAL
- Volume confirmation: PENDING
- Spread conditions: FAVORABLE
- M...
2025-07-14 00:34:08,396 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-07-14 00:34:11,935 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 167 chars
2025-07-14 00:34:11,936 - llama.lmstudio_runner - INFO - 📄 Response Preview: {
  "action": "WAIT",
  "entry_type": "MARKET/LIMIT",
  "wait_for": "VOLUME_CONFIRMATION",
  "max_wait_seconds": 60,
  "reasoning": "Waiting for volume confirmation"
}...
2025-07-14 00:34:11,936 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 603, Completion: 74, Total: 677
2025-07-14 00:34:11,936 - core.llm_response_parsers - INFO - Entry timing parsed: WAIT (LIMIT order)
2025-07-14 00:34:12,037 - core.llm_orchestrator - INFO - 🧠 Executing prompt 5/8: opportunity_scanner
2025-07-14 00:34:12,039 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-14 00:34:12,039 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a conservative futures trading analyst. Analyze market data carefully and provide measured recommendations. Use format: DECISION: [LONG/SHORT/WAIT], CONFIDENCE: [50-100]%, TAKE_PROFIT: [%], ST...
2025-07-14 00:34:12,039 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🔍 INTELLIGENT OPPORTUNITY SCANNER - Account Health Aware

🚨 ACCOUNT HEALTH STATUS: HEALTHY
💰 Balance: $1000.00 | Health: Healthy - Normal Trading
📊 Current Exposure: $0.00 (0.0% of account)
🎯 Trading Allowed: YES

📊 SYMBOL ANALYSIS:
• DOGE/USDT:USDT: $0.000000 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• BTC/USDT:USDT: $95000.000000 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• ETH/USDT:USDT: $3500.000000 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• SOL/USDT:USDT: $200.000000 | Mom: +0.0% | Vol: 1.0x |...
2025-07-14 00:34:12,040 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-07-14 00:34:17,661 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 690 chars
2025-07-14 00:34:17,661 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: LONG, CONFIDENCE: 80%, TAKE_PROFIT: 2.5%, STOP_LOSS: -3.5%, EXPLANATION: The account is in a healthy state with normal trading parameters and the current setup for DOGE/USDT shows high quality indicators such as positive mom, medium volume profile, and alignment of momentum within conserva...
2025-07-14 00:34:17,661 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 926, Completion: 186, Total: 1112
2025-07-14 00:34:17,662 - core.llm_response_parsers - INFO - Parsed structured text: {'DECISION': 'LONG', 'CONFIDENCE': 80.0, 'EXPLANATION': "THE ACCOUNT IS IN A HEALTHY STATE WITH NORMAL TRADING PARAMETERS AND THE CURRENT SETUP FOR DOGE/USDT SHOWS HIGH QUALITY INDICATORS SUCH AS POSITIVE MOM, MEDIUM VOLUME PROFILE, AND ALIGNMENT OF MOMENTUM WITHIN CONSERVATIVE VOLATILITY RANGE WHICH SUITS OUR RISK-AVERSE STRATEGY. GIVEN THAT WE HAVE 3 POSITION SLOTS AVAILABLE WITHOUT EXCEEDING HEALTH-BASED LIMITS ON EXPOSURE OR MAX TOTAL TRADE VALUE (2% PER TRADE), A LONG ENTRY WITH MODERATE TAKE PROFIT AT +2.5% AND STOP LOSS SET TO -3.5% IS RECOMMENDED, ENSURING THE ACCOUNT'S SURVIVAL WHILE AIMING FOR PROFITABLE OUTCOMES WITHIN OUR RISK TOLERANCE LEVELS.", 'ACTION': 'ENTER_NOW'}
2025-07-14 00:34:17,662 - core.llm_response_parsers - INFO - Opportunity scanner parsed: NONE (NONE)
2025-07-14 00:34:17,763 - core.llm_orchestrator - INFO - 📊 LLM Thread Usage End: 0/16 threads
2025-07-14 00:34:17,763 - core.llm_orchestrator - INFO - 🧠 LLM prompt cycle completed in 17.46s - 4 prompts executed sequentially
2025-07-14 00:34:30,798 - core.llm_orchestrator - INFO - 🔍 FULL MODE: Executing all 8 prompts for comprehensive analysis
2025-07-14 00:34:30,798 - core.llm_orchestrator - INFO - 📊 LLM Thread Usage Start: 0/16 threads
2025-07-14 00:34:30,798 - core.llm_orchestrator - INFO - 🧠 Executing prompt 2/8: market_regime
2025-07-14 00:34:30,798 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-14 00:34:30,799 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-14 00:34:30,799 - llama.lmstudio_runner - INFO - 💬 User Prompt: 📊 MARKET REGIME ANALYST - DOGE/USDT:USDT

🔍 MULTI-TIMEFRAME ANALYSIS:
1m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
5m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
15m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%

📈 VOLATILITY METRICS:
ATR: 0.001000 | Spread: 0.100%
Volume Ratio: 1.0x | Price Range: 1.00%

🎯 REGIME CLASSIFICATION:
- TRENDING_BULL: Strong upward momentum, high volume, clear direction
- TRENDING_BEAR: Strong downward momentum, high volume, clear direction
- RANGING_TIGHT: Low volatility, narrow pric...
2025-07-14 00:34:30,799 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-07-14 00:34:35,070 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 218 chars
2025-07-14 00:34:35,070 - llama.lmstudio_runner - INFO - 📄 Response Preview: ```json
{
  "REGIME": "RANGING_TIGHT",
  "CONFIDENCE": 75,
  "SCALP_SUITABILITY": "MEDIUM",
  "RECOMMENDED_TIMEFRAME": "1m",
  "RISK_LEVEL": "MEDIUM",
  "REASONING": "Consolidation phase with moderate volatility"
}
```...
2025-07-14 00:34:35,070 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 705, Completion: 108, Total: 813
2025-07-14 00:34:35,071 - core.llm_response_parsers - INFO - Market regime parsed: RANGING_TIGHT (MEDIUM scalp suitability)
2025-07-14 00:34:35,171 - core.llm_orchestrator - INFO - 🧠 Executing prompt 4/8: entry_timing
2025-07-14 00:34:35,172 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-14 00:34:35,172 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-14 00:34:35,173 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🎯 ENTRY TIMING SPECIALIST - DOGE/USDT:USDT

🎯 SIGNAL ANALYSIS:
ML Ensemble: NEUTRAL (50.0%)
Technical: NEUTRAL | Momentum: 0.00
Volume: PENDING | Orderflow: NEUTRAL

📊 MARKET MICROSTRUCTURE:
Bid/Ask: $0.000000/$0.000000
Spread: 0.100% | L2 Imbalance: 0.0%
Recent Flow: BALANCED | Volume Spike: False

📈 KEY LEVELS:
Support: $0.000000
Resistance: $0.000000
Distance to Support: 0.00%

⏰ TIMING FACTORS:
- Price near key levels: NEUTRAL
- Volume confirmation: PENDING
- Spread conditions: FAVORABLE
- M...
2025-07-14 00:34:35,173 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-07-14 00:34:38,836 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 179 chars
2025-07-14 00:34:38,836 - llama.lmstudio_runner - INFO - 📄 Response Preview: ```json
{
  "action": "WAIT",
  "entry_type": "MARKET/LIMIT",
  "wait_for": "VOLUME_CONFIRMATION",
  "max_wait_seconds": 60,
  "reasoning": "Waiting for volume confirmation"
}
```...
2025-07-14 00:34:38,836 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 603, Completion: 79, Total: 682
2025-07-14 00:34:38,837 - core.llm_response_parsers - INFO - Entry timing parsed: WAIT (LIMIT order)
2025-07-14 00:34:38,937 - core.llm_orchestrator - INFO - 📊 LLM Thread Usage End: 0/16 threads
2025-07-14 00:34:38,938 - core.llm_orchestrator - INFO - 🧠 LLM prompt cycle completed in 8.14s - 2 prompts executed sequentially
2025-07-14 00:35:00,301 - core.llm_orchestrator - INFO - 🔍 FULL MODE: Executing all 8 prompts for comprehensive analysis
2025-07-14 00:35:00,301 - core.llm_orchestrator - INFO - 📊 LLM Thread Usage Start: 0/16 threads
2025-07-14 00:35:00,301 - core.llm_orchestrator - INFO - 🧠 Executing prompt 3/8: risk_assessment
2025-07-14 00:35:00,301 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-14 00:35:00,301 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-14 00:35:00,302 - llama.lmstudio_runner - INFO - 💬 User Prompt: ...
2025-07-14 00:35:00,302 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-07-14 00:35:04,149 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 234 chars
2025-07-14 00:35:04,150 - llama.lmstudio_runner - INFO - 📄 Response Preview: ```json
{
  "action": "LONG",
  "confidence": 90,
  "entry_reason": "Market broke through resistance level with a rapid move of more than 2 pip.",
  "take_profit": 1.5,
  "stop_loss": 0.3,
  "hold_time": "3min",
  "leverage": 40
}
```...
2025-07-14 00:35:04,150 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 174, Completion: 97, Total: 271
2025-07-14 00:35:04,151 - core.llm_response_parsers - INFO - Risk assessment parsed: REJECTED (Risk: 50)
2025-07-14 00:35:04,252 - core.llm_orchestrator - INFO - 🧠 Executing prompt 4/8: entry_timing
2025-07-14 00:35:04,253 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-14 00:35:04,253 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-14 00:35:04,253 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🎯 ENTRY TIMING SPECIALIST - DOGE/USDT:USDT

🎯 SIGNAL ANALYSIS:
ML Ensemble: NEUTRAL (50.0%)
Technical: NEUTRAL | Momentum: 0.00
Volume: PENDING | Orderflow: NEUTRAL

📊 MARKET MICROSTRUCTURE:
Bid/Ask: $0.000000/$0.000000
Spread: 0.100% | L2 Imbalance: 0.0%
Recent Flow: BALANCED | Volume Spike: False

📈 KEY LEVELS:
Support: $0.000000
Resistance: $0.000000
Distance to Support: 0.00%

⏰ TIMING FACTORS:
- Price near key levels: NEUTRAL
- Volume confirmation: PENDING
- Spread conditions: FAVORABLE
- M...
2025-07-14 00:35:04,253 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-07-14 00:35:07,862 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 167 chars
2025-07-14 00:35:07,862 - llama.lmstudio_runner - INFO - 📄 Response Preview: {
  "action": "WAIT",
  "entry_type": "MARKET/LIMIT",
  "wait_for": "VOLUME_CONFIRMATION",
  "max_wait_seconds": 60,
  "reasoning": "Waiting for volume confirmation"
}...
2025-07-14 00:35:07,862 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 603, Completion: 74, Total: 677
2025-07-14 00:35:07,863 - core.llm_response_parsers - INFO - Entry timing parsed: WAIT (LIMIT order)
2025-07-14 00:35:07,964 - core.llm_orchestrator - INFO - 🧠 Executing prompt 5/8: opportunity_scanner
2025-07-14 00:35:07,965 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-14 00:35:07,965 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a conservative futures trading analyst. Analyze market data carefully and provide measured recommendations. Use format: DECISION: [LONG/SHORT/WAIT], CONFIDENCE: [50-100]%, TAKE_PROFIT: [%], ST...
2025-07-14 00:35:07,966 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🔍 INTELLIGENT OPPORTUNITY SCANNER - Account Health Aware

🚨 ACCOUNT HEALTH STATUS: HEALTHY
💰 Balance: $1000.00 | Health: Healthy - Normal Trading
📊 Current Exposure: $0.00 (0.0% of account)
🎯 Trading Allowed: YES

📊 SYMBOL ANALYSIS:
• DOGE/USDT:USDT: $0.000000 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• BTC/USDT:USDT: $95000.000000 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• ETH/USDT:USDT: $3500.000000 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• SOL/USDT:USDT: $200.000000 | Mom: +0.0% | Vol: 1.0x |...
2025-07-14 00:35:07,966 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-07-14 00:35:14,166 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 887 chars
2025-07-14 00:35:14,167 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: LONG, CONFIDENCE: 80%, TAKE_PROFIT: 1.2%, STOP_LOSS: -3%, EXPLANATION: The account is in a healthy state with normal trading parameters and the opportunity for DOGE/USDT presents clear patterns indicating an uptrend, which aligns well with our conservative momentum criteria. Given that we ...
2025-07-14 00:35:14,167 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 926, Completion: 215, Total: 1141
2025-07-14 00:35:14,167 - core.llm_response_parsers - INFO - Parsed structured text: {'DECISION': 'LONG', 'CONFIDENCE': 80.0, 'EXPLANATION': "THE ACCOUNT IS IN A HEALTHY STATE WITH NORMAL TRADING PARAMETERS AND THE OPPORTUNITY FOR DOGE/USDT PRESENTS CLEAR PATTERNS INDICATING AN UPTREND, WHICH ALIGNS WELL WITH OUR CONSERVATIVE MOMENTUM CRITERIA. GIVEN THAT WE ARE LOOKING TO MAINTAIN BALANCE WITHIN RISK-BASED LIMITS WHILE CAPITALIZING ON HIGH SETUP QUALITY OPPORTUNITIES, THIS TRADE IS RECOMMENDED AS IT OFFERS A FAVORABLE 1:3 REWARD RATIO AND FALLS COMFORTABLY UNDER THE ACCOUNT'S HEALTH-ADJUSTED MAXIMUM EXPOSURE LIMIT OF $70%. THE TAKE PROFIT AT 1.2% ENSURES WE CAPTURE GAINS WITHOUT OVEREXTENDING RISK WHILE SETTING STOP LOSS TO -3% PROVIDES PROTECTION AGAINST SUDDEN REVERSALS, ADHERING TO OUR CONSERVATIVE VOLATILITY PREFERENCE AND MAINTAINING ACCOUNT INTEGRITY WITHIN THE HEALTH-ADJUSTED PARAMETERS SET FOR THIS OPPORTUNITY RANKING SYSTEM.", 'ACTION': 'ENTER_NOW'}
2025-07-14 00:35:14,167 - core.llm_response_parsers - INFO - Opportunity scanner parsed: NONE (NONE)
2025-07-14 00:35:14,268 - core.llm_orchestrator - INFO - 🧠 Executing prompt 8/8: strategy_adaptation
2025-07-14 00:35:14,268 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-14 00:35:14,269 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-14 00:35:14,269 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🔄 STRATEGY ADAPTATION SPECIALIST

📊 PERFORMANCE ANALYSIS (Last 24h):
Trades: 0 | Win Rate: 50.0%
Avg Profit: 0.80% | Avg Loss: -0.30%
Sharpe Ratio: 1.00 | Max Drawdown: 0.0%
Total PnL: $0.00 | ROI: 0.0%

🎯 CURRENT STRATEGY:
Risk per Trade: 2.0% | Avg Hold Time: 8.0min
Entry Threshold: 70% | Exit Threshold: 60%
Position Size Method: FIXED_RISK | Max Positions: 3

📈 MARKET REGIME: UNKNOWN
Regime Confidence: 50.0%
Scalp Suitability: MEDIUM

🔧 ADAPTATION FACTORS:
- Win rate trending: DOWN
- Drawdown...
2025-07-14 00:35:14,269 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-07-14 00:35:18,774 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 338 chars
2025-07-14 00:35:18,775 - llama.lmstudio_runner - INFO - 📄 Response Preview: ```json
{
  "risk_adjustment": [0.5, 2.0],
  "hold_time_target": "8",
  "entry_threshold": 70,
  "exit_threshold": 60,
  "sizing_method": "FIXED_RISK",
  "reasoning": "Maintain current strategy - performing well with a slight risk reduction to account for recent poor performance and downward trend i...
2025-07-14 00:35:18,775 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 619, Completion: 124, Total: 743
2025-07-14 00:35:18,775 - core.llm_response_parsers - INFO - Strategy adaptation parsed: Risk adj 1.0x
2025-07-14 00:35:18,775 - core.llm_action_executors - INFO - Applying strategy adaptations: Risk 1.0x, Hold time 8min
2025-07-14 00:35:18,876 - core.llm_orchestrator - INFO - 📊 LLM Thread Usage End: 0/16 threads
2025-07-14 00:35:18,877 - core.llm_orchestrator - INFO - 🧠 LLM prompt cycle completed in 18.58s - 4 prompts executed sequentially
2025-07-14 00:35:30,297 - core.llm_orchestrator - INFO - 🔍 FULL MODE: Executing all 8 prompts for comprehensive analysis
2025-07-14 00:35:30,298 - core.llm_orchestrator - INFO - 📊 LLM Thread Usage Start: 0/16 threads
2025-07-14 00:35:30,298 - core.llm_orchestrator - INFO - 🧠 Executing prompt 2/8: market_regime
2025-07-14 00:35:30,298 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-14 00:35:30,298 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-14 00:35:30,299 - llama.lmstudio_runner - INFO - 💬 User Prompt: 📊 MARKET REGIME ANALYST - DOGE/USDT:USDT

🔍 MULTI-TIMEFRAME ANALYSIS:
1m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
5m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
15m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%

📈 VOLATILITY METRICS:
ATR: 0.001000 | Spread: 0.100%
Volume Ratio: 1.0x | Price Range: 1.00%

🎯 REGIME CLASSIFICATION:
- TRENDING_BULL: Strong upward momentum, high volume, clear direction
- TRENDING_BEAR: Strong downward momentum, high volume, clear direction
- RANGING_TIGHT: Low volatility, narrow pric...
2025-07-14 00:35:30,299 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-07-14 00:35:34,569 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 218 chars
2025-07-14 00:35:34,570 - llama.lmstudio_runner - INFO - 📄 Response Preview: ```json
{
  "REGIME": "RANGING_TIGHT",
  "CONFIDENCE": 75,
  "SCALP_SUITABILITY": "MEDIUM",
  "RECOMMENDED_TIMEFRAME": "1m",
  "RISK_LEVEL": "MEDIUM",
  "REASONING": "Consolidation phase with moderate volatility"
}
```...
2025-07-14 00:35:34,570 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 705, Completion: 108, Total: 813
2025-07-14 00:35:34,570 - core.llm_response_parsers - INFO - Market regime parsed: RANGING_TIGHT (MEDIUM scalp suitability)
2025-07-14 00:35:34,671 - core.llm_orchestrator - INFO - 🧠 Executing prompt 4/8: entry_timing
2025-07-14 00:35:34,671 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-14 00:35:34,671 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-14 00:35:34,672 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🎯 ENTRY TIMING SPECIALIST - DOGE/USDT:USDT

🎯 SIGNAL ANALYSIS:
ML Ensemble: NEUTRAL (50.0%)
Technical: NEUTRAL | Momentum: 0.00
Volume: PENDING | Orderflow: NEUTRAL

📊 MARKET MICROSTRUCTURE:
Bid/Ask: $0.000000/$0.000000
Spread: 0.100% | L2 Imbalance: 0.0%
Recent Flow: BALANCED | Volume Spike: False

📈 KEY LEVELS:
Support: $0.000000
Resistance: $0.000000
Distance to Support: 0.00%

⏰ TIMING FACTORS:
- Price near key levels: NEUTRAL
- Volume confirmation: PENDING
- Spread conditions: FAVORABLE
- M...
2025-07-14 00:35:34,672 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-07-14 00:35:38,216 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 167 chars
2025-07-14 00:35:38,217 - llama.lmstudio_runner - INFO - 📄 Response Preview: {
  "action": "WAIT",
  "entry_type": "MARKET/LIMIT",
  "wait_for": "VOLUME_CONFIRMATION",
  "max_wait_seconds": 60,
  "reasoning": "Waiting for volume confirmation"
}...
2025-07-14 00:35:38,217 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 603, Completion: 74, Total: 677
2025-07-14 00:35:38,217 - core.llm_response_parsers - INFO - Entry timing parsed: WAIT (LIMIT order)
2025-07-14 00:35:38,318 - core.llm_orchestrator - INFO - 📊 LLM Thread Usage End: 0/16 threads
2025-07-14 00:35:38,319 - core.llm_orchestrator - INFO - 🧠 LLM prompt cycle completed in 8.02s - 2 prompts executed sequentially
2025-07-14 00:36:00,321 - core.llm_orchestrator - INFO - 🔍 FULL MODE: Executing all 8 prompts for comprehensive analysis
2025-07-14 00:36:00,322 - core.llm_orchestrator - INFO - 📊 LLM Thread Usage Start: 0/16 threads
2025-07-14 00:36:00,322 - core.llm_orchestrator - INFO - 🧠 Executing prompt 2/8: market_regime
2025-07-14 00:36:00,322 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-14 00:36:00,322 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-14 00:36:00,322 - llama.lmstudio_runner - INFO - 💬 User Prompt: 📊 MARKET REGIME ANALYST - DOGE/USDT:USDT

🔍 MULTI-TIMEFRAME ANALYSIS:
1m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
5m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
15m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%

📈 VOLATILITY METRICS:
ATR: 0.001000 | Spread: 0.100%
Volume Ratio: 1.0x | Price Range: 1.00%

🎯 REGIME CLASSIFICATION:
- TRENDING_BULL: Strong upward momentum, high volume, clear direction
- TRENDING_BEAR: Strong downward momentum, high volume, clear direction
- RANGING_TIGHT: Low volatility, narrow pric...
2025-07-14 00:36:00,323 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-07-14 00:36:04,798 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 265 chars
2025-07-14 00:36:04,799 - llama.lmstudio_runner - INFO - 📄 Response Preview: ```json
{
  "REGIME": "RANGING_TIGHT",
  "CONFIDENCE": 75,
  "SCALP_SUITABILITY": "MEDIUM",
  "RECOMMENDED_TIMEFRAME": "1m",
  "RISK_LEVEL": "MEDIUM",
  "REASONING": "Consolidation phase with moderate volatility, suitable for medium-risk scalping strategies."
}
```...
2025-07-14 00:36:04,799 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 705, Completion: 119, Total: 824
2025-07-14 00:36:04,799 - core.llm_response_parsers - INFO - Market regime parsed: RANGING_TIGHT (MEDIUM scalp suitability)
2025-07-14 00:36:04,900 - core.llm_orchestrator - INFO - 🧠 Executing prompt 3/8: risk_assessment
2025-07-14 00:36:04,900 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-14 00:36:04,901 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-14 00:36:04,901 - llama.lmstudio_runner - INFO - 💬 User Prompt: ...
2025-07-14 00:36:04,901 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-07-14 00:36:08,627 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 227 chars
2025-07-14 00:36:08,627 - llama.lmstudio_runner - INFO - 📄 Response Preview: ```json
{
  "action": "LONG",
  "confidence": 90,
  "entry_reason": "Market broke through resistance level with a sudden spike in volume.",
  "take_profit": 2.5,
  "stop_loss": 1.0,
  "hold_time": "3min",
  "leverage": 40
}
```...
2025-07-14 00:36:08,628 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 174, Completion: 94, Total: 268
2025-07-14 00:36:08,628 - core.llm_response_parsers - INFO - Risk assessment parsed: REJECTED (Risk: 50)
2025-07-14 00:36:08,729 - core.llm_orchestrator - INFO - 🧠 Executing prompt 4/8: entry_timing
2025-07-14 00:36:08,730 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-14 00:36:08,730 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-14 00:36:08,731 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🎯 ENTRY TIMING SPECIALIST - DOGE/USDT:USDT

🎯 SIGNAL ANALYSIS:
ML Ensemble: NEUTRAL (50.0%)
Technical: NEUTRAL | Momentum: 0.00
Volume: PENDING | Orderflow: NEUTRAL

📊 MARKET MICROSTRUCTURE:
Bid/Ask: $0.000000/$0.000000
Spread: 0.100% | L2 Imbalance: 0.0%
Recent Flow: BALANCED | Volume Spike: False

📈 KEY LEVELS:
Support: $0.000000
Resistance: $0.000000
Distance to Support: 0.00%

⏰ TIMING FACTORS:
- Price near key levels: NEUTRAL
- Volume confirmation: PENDING
- Spread conditions: FAVORABLE
- M...
2025-07-14 00:36:08,731 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-07-14 00:36:12,252 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 167 chars
2025-07-14 00:36:12,253 - llama.lmstudio_runner - INFO - 📄 Response Preview: {
  "action": "WAIT",
  "entry_type": "MARKET/LIMIT",
  "wait_for": "VOLUME_CONFIRMATION",
  "max_wait_seconds": 60,
  "reasoning": "Waiting for volume confirmation"
}...
2025-07-14 00:36:12,253 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 603, Completion: 74, Total: 677
2025-07-14 00:36:12,253 - core.llm_response_parsers - INFO - Entry timing parsed: WAIT (LIMIT order)
2025-07-14 00:36:12,354 - core.llm_orchestrator - INFO - 🧠 Executing prompt 5/8: opportunity_scanner
2025-07-14 00:36:12,355 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-14 00:36:12,355 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a conservative futures trading analyst. Analyze market data carefully and provide measured recommendations. Use format: DECISION: [LONG/SHORT/WAIT], CONFIDENCE: [50-100]%, TAKE_PROFIT: [%], ST...
2025-07-14 00:36:12,355 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🔍 INTELLIGENT OPPORTUNITY SCANNER - Account Health Aware

🚨 ACCOUNT HEALTH STATUS: HEALTHY
💰 Balance: $1000.00 | Health: Healthy - Normal Trading
📊 Current Exposure: $0.00 (0.0% of account)
🎯 Trading Allowed: YES

📊 SYMBOL ANALYSIS:
• DOGE/USDT:USDT: $0.000000 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• BTC/USDT:USDT: $95000.000000 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• ETH/USDT:USDT: $3500.000000 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• SOL/USDT:USDT: $200.000000 | Mom: +0.0% | Vol: 1.0x |...
2025-07-14 00:36:12,355 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-07-14 00:36:18,406 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 874 chars
2025-07-14 00:36:18,406 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: LONG, CONFIDENCE: 80%, TAKE_PROFIT: 2.5%, STOP_LOSS: -3.5%, EXPLANATION: The account is in a healthy state with normal trading parameters and the current setup for DOGE/USDT shows clear patterns, strong signals, excellent liquidity (assuming <0.2% spread), high momentum alignment, within c...
2025-07-14 00:36:18,407 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 926, Completion: 218, Total: 1144
2025-07-14 00:36:18,407 - core.llm_response_parsers - INFO - Parsed structured text: {'DECISION': 'LONG', 'CONFIDENCE': 80.0, 'EXPLANATION': 'THE ACCOUNT IS IN A HEALTHY STATE WITH NORMAL TRADING PARAMETERS AND THE CURRENT SETUP FOR DOGE/USDT SHOWS CLEAR PATTERNS, STRONG SIGNALS, EXCELLENT LIQUIDITY (ASSUMING <0.2% SPREAD), HIGH MOMENTUM ALIGNMENT, WITHIN CONSERVATIVE VOLATILITY RANGE OF 1-3%. GIVEN THESE FACTORS ALONG WITH AN ACCEPTABLE RISK TO REWARD RATIO GREATER THAN 3:1 AS PER THE OPPORTUNITY CRITERIA AND ACCOUNT HEALTH CONSIDERATIONS, A LONG POSITION ON DOGE/USDT IS RECOMMENDED AT $0.000000 PRICE POINT FOR ENTRY WITHIN A SHORT TIME HORIZON (5-15 MINUTES). THE TAKE PROFIT SHOULD BE SET SLIGHTLY HIGHER TO ENSURE CAPITAL PRESERVATION IN LINE WITH MODERATE RISK ENVIRONMENT WHILE THE STOP LOSS WOULD PROTECT AGAINST SIGNIFICANT LOSSES, CONSIDERING MARKET VOLATILITY AND ACCOUNT HEALTH PRIORITIZATION OVER MAXIMUM PROFITS.', 'ACTION': 'ENTER_NOW'}
2025-07-14 00:36:18,407 - core.llm_response_parsers - INFO - Opportunity scanner parsed: NONE (NONE)
2025-07-14 00:36:18,508 - core.llm_orchestrator - INFO - 📊 LLM Thread Usage End: 0/16 threads
2025-07-14 00:36:18,508 - core.llm_orchestrator - INFO - 🧠 LLM prompt cycle completed in 18.19s - 4 prompts executed sequentially
2025-07-14 00:36:30,756 - core.llm_orchestrator - INFO - 🔍 FULL MODE: Executing all 8 prompts for comprehensive analysis
2025-07-14 00:36:30,756 - core.llm_orchestrator - INFO - 📊 LLM Thread Usage Start: 0/16 threads
2025-07-14 00:36:30,757 - core.llm_orchestrator - INFO - 🧠 Executing prompt 2/8: market_regime
2025-07-14 00:36:30,757 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-14 00:36:30,758 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-14 00:36:30,758 - llama.lmstudio_runner - INFO - 💬 User Prompt: 📊 MARKET REGIME ANALYST - DOGE/USDT:USDT

🔍 MULTI-TIMEFRAME ANALYSIS:
1m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
5m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
15m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%

📈 VOLATILITY METRICS:
ATR: 0.001000 | Spread: 0.100%
Volume Ratio: 1.0x | Price Range: 1.00%

🎯 REGIME CLASSIFICATION:
- TRENDING_BULL: Strong upward momentum, high volume, clear direction
- TRENDING_BEAR: Strong downward momentum, high volume, clear direction
- RANGING_TIGHT: Low volatility, narrow pric...
2025-07-14 00:36:30,758 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-07-14 00:36:34,989 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 218 chars
2025-07-14 00:36:34,989 - llama.lmstudio_runner - INFO - 📄 Response Preview: ```json
{
  "REGIME": "RANGING_TIGHT",
  "CONFIDENCE": 75,
  "SCALP_SUITABILITY": "MEDIUM",
  "RECOMMENDED_TIMEFRAME": "1m",
  "RISK_LEVEL": "MEDIUM",
  "REASONING": "Consolidation phase with moderate volatility"
}
```...
2025-07-14 00:36:34,990 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 705, Completion: 108, Total: 813
2025-07-14 00:36:34,990 - core.llm_response_parsers - INFO - Market regime parsed: RANGING_TIGHT (MEDIUM scalp suitability)
2025-07-14 00:36:35,091 - core.llm_orchestrator - INFO - 🧠 Executing prompt 4/8: entry_timing
2025-07-14 00:36:35,091 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-14 00:36:35,091 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-14 00:36:35,092 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🎯 ENTRY TIMING SPECIALIST - DOGE/USDT:USDT

🎯 SIGNAL ANALYSIS:
ML Ensemble: NEUTRAL (50.0%)
Technical: NEUTRAL | Momentum: 0.00
Volume: PENDING | Orderflow: NEUTRAL

📊 MARKET MICROSTRUCTURE:
Bid/Ask: $0.000000/$0.000000
Spread: 0.100% | L2 Imbalance: 0.0%
Recent Flow: BALANCED | Volume Spike: False

📈 KEY LEVELS:
Support: $0.000000
Resistance: $0.000000
Distance to Support: 0.00%

⏰ TIMING FACTORS:
- Price near key levels: NEUTRAL
- Volume confirmation: PENDING
- Spread conditions: FAVORABLE
- M...
2025-07-14 00:36:35,092 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-07-14 00:36:38,587 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 167 chars
2025-07-14 00:36:38,588 - llama.lmstudio_runner - INFO - 📄 Response Preview: {
  "action": "WAIT",
  "entry_type": "MARKET/LIMIT",
  "wait_for": "VOLUME_CONFIRMATION",
  "max_wait_seconds": 60,
  "reasoning": "Waiting for volume confirmation"
}...
2025-07-14 00:36:38,588 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 603, Completion: 74, Total: 677
2025-07-14 00:36:38,588 - core.llm_response_parsers - INFO - Entry timing parsed: WAIT (LIMIT order)
2025-07-14 00:36:38,689 - core.llm_orchestrator - INFO - 📊 LLM Thread Usage End: 0/16 threads
2025-07-14 00:36:38,690 - core.llm_orchestrator - INFO - 🧠 LLM prompt cycle completed in 7.93s - 2 prompts executed sequentially
2025-07-14 00:36:45,527 - websocket - ERROR - error from callback <bound method WebSocketClient._on_message of <data.websocket_client.WebSocketClient object at 0x00000221FFC71EB0>>: wrapped C/C++ object of type WebSocketClient has been deleted
2025-07-14 00:36:45,528 - websocket - ERROR - error from callback <bound method WebSocketClient._on_error of <data.websocket_client.WebSocketClient object at 0x00000221FFC71EB0>>: wrapped C/C++ object of type WebSocketClient has been deleted
2025-07-14 00:36:45,528 - websocket - INFO - tearing down on exception wrapped C/C++ object of type WebSocketClient has been deleted
2025-07-14 01:28:34,429 - main - INFO - Epinnox v6 starting up...
2025-07-14 01:28:34,445 - core.performance_monitor - INFO - Performance monitoring started
2025-07-14 01:28:34,445 - core.performance_monitor - INFO - Performance monitor initialized with 10s interval
2025-07-14 01:28:34,446 - main - INFO - Performance monitoring initialized
2025-07-14 01:28:34,454 - utils.cache_manager - INFO - Cache manager initialized: cache, max_age=24h, max_size=100MB
2025-07-14 01:28:34,455 - utils.cache_manager - INFO - Starting automatic cache cleanup
2025-07-14 01:28:34,456 - utils.cache_manager - INFO - Auto cleanup complete: 0 files removed, 0.00 MB freed
2025-07-14 06:48:35,237 - main - INFO - Epinnox v6 starting up...
2025-07-14 06:48:35,262 - core.performance_monitor - INFO - Performance monitoring started
2025-07-14 06:48:35,262 - core.performance_monitor - INFO - Performance monitor initialized with 10s interval
2025-07-14 06:48:35,263 - main - INFO - Performance monitoring initialized
2025-07-14 06:48:35,271 - utils.cache_manager - INFO - Cache manager initialized: cache, max_age=24h, max_size=100MB
2025-07-14 06:48:35,271 - utils.cache_manager - INFO - Starting automatic cache cleanup
2025-07-14 06:48:35,272 - utils.cache_manager - INFO - Auto cleanup complete: 0 files removed, 0.00 MB freed
2025-07-14 07:12:14,568 - main - INFO - Epinnox v6 starting up...
2025-07-14 07:12:14,599 - core.performance_monitor - INFO - Performance monitoring started
2025-07-14 07:12:14,600 - core.performance_monitor - INFO - Performance monitor initialized with 10s interval
2025-07-14 07:12:14,601 - main - INFO - Performance monitoring initialized
2025-07-14 07:12:14,621 - utils.cache_manager - INFO - Cache manager initialized: cache, max_age=24h, max_size=100MB
2025-07-14 07:12:14,623 - utils.cache_manager - INFO - Starting automatic cache cleanup
2025-07-14 07:12:14,625 - utils.cache_manager - INFO - Auto cleanup complete: 0 files removed, 0.00 MB freed
2025-07-14 07:12:25,054 - config.autonomous_config - INFO - Configuration loaded from configs/autonomous_trading.yaml
2025-07-14 07:12:26,026 - data.live_data_manager - INFO - Successfully subscribed to live data for DOGE/USDT:USDT on timeframes: ['1m', '5m', '15m']
2025-07-14 07:12:26,035 - llama.runner - INFO - Loaded OpenAI API key from models_config.yaml
2025-07-14 07:12:26,036 - llama.runner - INFO - Using model path: C:\Users\<USER>\.lmstudio\models\lmstudio-community\Phi-3.1-mini-128k-instruct-GGUF\Phi-3.1-mini-128k-instruct-Q4_K_M.gguf
2025-07-14 07:12:26,037 - llama.runner - INFO - Using guard path: C:\Users\<USER>\Documents\dev\Epinnox_v6\models\Llama-Guard-3-8B.gguf
2025-07-14 07:12:26,037 - llama.runner - INFO - Using binary path: C:\Users\<USER>\Documents\dev\llama.cpp\main
2025-07-14 07:12:26,043 - llama.lmstudio_runner - INFO - Loaded LMStudio configuration from models_config.yaml
2025-07-14 07:12:27,294 - websocket - INFO - Websocket connected
2025-07-14 07:12:28,098 - llama.lmstudio_runner - INFO - Discovered 8 models: ['openthinker3-7b', 'mistral-7b-claude-chat', 'fine_tuned_qwen1.7b', 'tsfc-model-0.0.1', 'codestral-22b-v0.1', 'phi-3.1-mini-128k-instruct', 'deepseek-r1-distill-qwen-7b', 'text-embedding-nomic-embed-text-v1.5']
2025-07-14 07:12:28,102 - llama.lmstudio_runner - INFO - Selected preferred model from config: phi-3.1-mini-128k-instruct
2025-07-14 07:12:28,102 - llama.lmstudio_runner - INFO - Initialized LMStudio with model: phi-3.1-mini-128k-instruct
2025-07-14 07:12:28,104 - core.llm_action_executors - INFO - 🚀 LLM Action Executors initialized with intelligent limit order system
2025-07-14 07:12:28,106 - utils.cache_manager - INFO - Cache manager initialized: cache, max_age=24h, max_size=100MB
2025-07-14 07:12:28,106 - core.llm_orchestrator - INFO - LLM Prompt Orchestrator initialized
2025-07-14 07:12:28,250 - core.risk_management_system - INFO - 🛡️ Risk Management System initialized
2025-07-14 07:12:28,255 - core.error_handling_system - INFO - 🛡️ Error Handling System initialized
2025-07-14 07:12:28,256 - core.error_handling_system - INFO - 📊 Component registered: exchange_connection
2025-07-14 07:12:28,256 - core.error_handling_system - INFO - 📊 Component registered: trading_interface
2025-07-14 07:12:28,256 - core.error_handling_system - INFO - 📊 Component registered: market_data
2025-07-14 07:12:28,256 - core.error_handling_system - INFO - 📊 Component registered: llm_orchestrator
2025-07-14 07:12:28,258 - core.error_handling_system - INFO - 🔍 Health monitoring started
2025-07-14 07:12:28,260 - core.symbol_scanner - INFO - SymbolScanner initialized with 8 symbols
2025-07-14 07:12:28,262 - core.llm_action_executors - INFO - 🚀 LLM Action Executors initialized with intelligent limit order system
2025-07-14 07:13:40,909 - main - INFO - Epinnox v6 starting up...
2025-07-14 07:13:40,938 - core.performance_monitor - INFO - Performance monitoring started
2025-07-14 07:13:40,939 - core.performance_monitor - INFO - Performance monitor initialized with 10s interval
2025-07-14 07:13:40,942 - main - INFO - Performance monitoring initialized
2025-07-14 07:13:40,962 - utils.cache_manager - INFO - Cache manager initialized: cache, max_age=24h, max_size=100MB
2025-07-14 07:13:40,965 - utils.cache_manager - INFO - Starting automatic cache cleanup
2025-07-14 07:13:40,967 - utils.cache_manager - INFO - Auto cleanup complete: 0 files removed, 0.00 MB freed
2025-07-14 07:13:49,677 - config.autonomous_config - INFO - Configuration loaded from configs/autonomous_trading.yaml
2025-07-14 07:13:50,741 - data.live_data_manager - INFO - Successfully subscribed to live data for DOGE/USDT:USDT on timeframes: ['1m', '5m', '15m']
2025-07-14 07:13:50,756 - llama.runner - INFO - Loaded OpenAI API key from models_config.yaml
2025-07-14 07:13:50,757 - llama.runner - INFO - Using model path: C:\Users\<USER>\.lmstudio\models\lmstudio-community\Phi-3.1-mini-128k-instruct-GGUF\Phi-3.1-mini-128k-instruct-Q4_K_M.gguf
2025-07-14 07:13:50,757 - llama.runner - INFO - Using guard path: C:\Users\<USER>\Documents\dev\Epinnox_v6\models\Llama-Guard-3-8B.gguf
2025-07-14 07:13:50,758 - llama.runner - INFO - Using binary path: C:\Users\<USER>\Documents\dev\llama.cpp\main
2025-07-14 07:13:50,766 - llama.lmstudio_runner - INFO - Loaded LMStudio configuration from models_config.yaml
2025-07-14 07:13:52,741 - websocket - INFO - Websocket connected
2025-07-14 07:13:52,827 - llama.lmstudio_runner - INFO - Discovered 8 models: ['openthinker3-7b', 'mistral-7b-claude-chat', 'fine_tuned_qwen1.7b', 'tsfc-model-0.0.1', 'codestral-22b-v0.1', 'phi-3.1-mini-128k-instruct', 'deepseek-r1-distill-qwen-7b', 'text-embedding-nomic-embed-text-v1.5']
2025-07-14 07:13:52,832 - llama.lmstudio_runner - INFO - Selected preferred model from config: phi-3.1-mini-128k-instruct
2025-07-14 07:13:52,833 - llama.lmstudio_runner - INFO - Initialized LMStudio with model: phi-3.1-mini-128k-instruct
2025-07-14 07:13:52,840 - core.llm_action_executors - INFO - 🚀 LLM Action Executors initialized with intelligent limit order system
2025-07-14 07:13:52,842 - utils.cache_manager - INFO - Cache manager initialized: cache, max_age=24h, max_size=100MB
2025-07-14 07:13:52,842 - core.llm_orchestrator - INFO - LLM Prompt Orchestrator initialized
2025-07-14 07:13:53,106 - core.risk_management_system - INFO - 🛡️ Risk Management System initialized
2025-07-14 07:13:53,108 - core.error_handling_system - INFO - 🛡️ Error Handling System initialized
2025-07-14 07:13:53,110 - core.error_handling_system - INFO - 📊 Component registered: exchange_connection
2025-07-14 07:13:53,111 - core.error_handling_system - INFO - 📊 Component registered: trading_interface
2025-07-14 07:13:53,111 - core.error_handling_system - INFO - 📊 Component registered: market_data
2025-07-14 07:13:53,112 - core.error_handling_system - INFO - 📊 Component registered: llm_orchestrator
2025-07-14 07:13:53,114 - core.error_handling_system - INFO - 🔍 Health monitoring started
2025-07-14 07:13:53,121 - core.monitoring_dashboard - INFO - 📊 Monitoring Dashboard initialized
2025-07-14 07:13:53,122 - core.symbol_scanner - INFO - SymbolScanner initialized with 8 symbols
2025-07-14 07:13:53,125 - core.llm_action_executors - INFO - 🚀 LLM Action Executors initialized with intelligent limit order system
2025-07-14 07:33:16,447 - main - INFO - Epinnox v6 starting up...
2025-07-14 07:33:16,469 - core.performance_monitor - INFO - Performance monitoring started
2025-07-14 07:33:16,470 - core.performance_monitor - INFO - Performance monitor initialized with 10s interval
2025-07-14 07:33:16,470 - main - INFO - Performance monitoring initialized
2025-07-14 07:33:16,479 - utils.cache_manager - INFO - Cache manager initialized: cache, max_age=24h, max_size=100MB
2025-07-14 07:33:16,480 - utils.cache_manager - INFO - Starting automatic cache cleanup
2025-07-14 07:33:16,482 - utils.cache_manager - INFO - Auto cleanup complete: 0 files removed, 0.00 MB freed
2025-07-14 07:33:26,688 - config.autonomous_config - INFO - Configuration loaded from configs/autonomous_trading.yaml
2025-07-14 07:33:27,627 - data.live_data_manager - INFO - Successfully subscribed to live data for BTC/USDT:USDT on timeframes: ['1m', '5m', '15m']
2025-07-14 07:33:27,636 - llama.runner - INFO - Loaded OpenAI API key from models_config.yaml
2025-07-14 07:33:27,636 - llama.runner - INFO - Using model path: C:\Users\<USER>\.lmstudio\models\lmstudio-community\Phi-3.1-mini-128k-instruct-GGUF\Phi-3.1-mini-128k-instruct-Q4_K_M.gguf
2025-07-14 07:33:27,636 - llama.runner - INFO - Using guard path: C:\Users\<USER>\Documents\dev\Epinnox_v6\models\Llama-Guard-3-8B.gguf
2025-07-14 07:33:27,637 - llama.runner - INFO - Using binary path: C:\Users\<USER>\Documents\dev\llama.cpp\main
2025-07-14 07:33:27,642 - llama.lmstudio_runner - INFO - Loaded LMStudio configuration from models_config.yaml
2025-07-14 07:33:28,882 - websocket - INFO - Websocket connected
2025-07-14 07:33:29,666 - llama.lmstudio_runner - INFO - Discovered 8 models: ['openthinker3-7b', 'mistral-7b-claude-chat', 'fine_tuned_qwen1.7b', 'tsfc-model-0.0.1', 'codestral-22b-v0.1', 'phi-3.1-mini-128k-instruct', 'deepseek-r1-distill-qwen-7b', 'text-embedding-nomic-embed-text-v1.5']
2025-07-14 07:33:29,668 - llama.lmstudio_runner - INFO - Selected preferred model from config: phi-3.1-mini-128k-instruct
2025-07-14 07:33:29,668 - llama.lmstudio_runner - INFO - Initialized LMStudio with model: phi-3.1-mini-128k-instruct
2025-07-14 07:33:29,668 - core.llm_action_executors - INFO - 🚀 LLM Action Executors initialized with intelligent limit order system
2025-07-14 07:33:29,668 - utils.cache_manager - INFO - Cache manager initialized: cache, max_age=24h, max_size=100MB
2025-07-14 07:33:29,674 - core.llm_orchestrator - INFO - LLM Prompt Orchestrator initialized
2025-07-14 07:33:29,705 - storage.database_manager - INFO - Database tables initialized successfully with performance optimizations
2025-07-14 07:33:29,707 - storage.database_manager - INFO - Database manager initialized: data\epinnox_trading.db
2025-07-14 07:33:29,707 - storage.session_manager - INFO - Session Manager initialized
2025-07-14 07:33:29,707 - storage.database_manager - INFO - Created session: live_BTCUSDTUSDT_20250714_073329_0c94c586
2025-07-14 07:33:29,716 - storage.session_manager - INFO - Started session: live_BTCUSDTUSDT_20250714_073329_0c94c586
2025-07-14 07:33:29,850 - core.risk_management_system - INFO - 🛡️ Risk Management System initialized
2025-07-14 07:33:29,852 - core.error_handling_system - INFO - 🛡️ Error Handling System initialized
2025-07-14 07:33:29,853 - core.error_handling_system - INFO - 📊 Component registered: exchange_connection
2025-07-14 07:33:29,853 - core.error_handling_system - INFO - 📊 Component registered: trading_interface
2025-07-14 07:33:29,853 - core.error_handling_system - INFO - 📊 Component registered: market_data
2025-07-14 07:33:29,855 - core.error_handling_system - INFO - 📊 Component registered: llm_orchestrator
2025-07-14 07:33:29,856 - core.error_handling_system - INFO - 🔍 Health monitoring started
2025-07-14 07:33:29,857 - core.monitoring_dashboard - INFO - 📊 Monitoring Dashboard initialized
2025-07-14 07:33:29,857 - core.symbol_scanner - INFO - SymbolScanner initialized with 8 symbols
2025-07-14 07:33:29,861 - core.llm_action_executors - INFO - 🚀 LLM Action Executors initialized with intelligent limit order system
2025-07-14 07:36:20,101 - main - INFO - Epinnox v6 starting up...
2025-07-14 07:36:20,118 - core.performance_monitor - INFO - Performance monitoring started
2025-07-14 07:36:20,118 - core.performance_monitor - INFO - Performance monitor initialized with 10s interval
2025-07-14 07:36:20,120 - main - INFO - Performance monitoring initialized
2025-07-14 07:36:20,129 - utils.cache_manager - INFO - Cache manager initialized: cache, max_age=24h, max_size=100MB
2025-07-14 07:36:20,129 - utils.cache_manager - INFO - Starting automatic cache cleanup
2025-07-14 07:36:20,129 - utils.cache_manager - INFO - Auto cleanup complete: 0 files removed, 0.00 MB freed
2025-07-14 07:36:29,965 - config.autonomous_config - INFO - Configuration loaded from configs/autonomous_trading.yaml
2025-07-14 07:36:30,843 - data.live_data_manager - INFO - Successfully subscribed to live data for BTC/USDT:USDT on timeframes: ['1m', '5m', '15m']
2025-07-14 07:36:30,852 - llama.runner - INFO - Loaded OpenAI API key from models_config.yaml
2025-07-14 07:36:30,852 - llama.runner - INFO - Using model path: C:\Users\<USER>\.lmstudio\models\lmstudio-community\Phi-3.1-mini-128k-instruct-GGUF\Phi-3.1-mini-128k-instruct-Q4_K_M.gguf
2025-07-14 07:36:30,853 - llama.runner - INFO - Using guard path: C:\Users\<USER>\Documents\dev\Epinnox_v6\models\Llama-Guard-3-8B.gguf
2025-07-14 07:36:30,854 - llama.runner - INFO - Using binary path: C:\Users\<USER>\Documents\dev\llama.cpp\main
2025-07-14 07:36:30,857 - llama.lmstudio_runner - INFO - Loaded LMStudio configuration from models_config.yaml
2025-07-14 07:36:32,116 - websocket - INFO - Websocket connected
2025-07-14 07:36:32,879 - llama.lmstudio_runner - INFO - Discovered 8 models: ['openthinker3-7b', 'mistral-7b-claude-chat', 'fine_tuned_qwen1.7b', 'tsfc-model-0.0.1', 'codestral-22b-v0.1', 'phi-3.1-mini-128k-instruct', 'deepseek-r1-distill-qwen-7b', 'text-embedding-nomic-embed-text-v1.5']
2025-07-14 07:36:32,880 - llama.lmstudio_runner - INFO - Selected preferred model from config: phi-3.1-mini-128k-instruct
2025-07-14 07:36:32,880 - llama.lmstudio_runner - INFO - Initialized LMStudio with model: phi-3.1-mini-128k-instruct
2025-07-14 07:36:32,883 - core.llm_action_executors - INFO - 🚀 LLM Action Executors initialized with intelligent limit order system
2025-07-14 07:36:32,884 - utils.cache_manager - INFO - Cache manager initialized: cache, max_age=24h, max_size=100MB
2025-07-14 07:36:32,884 - core.llm_orchestrator - INFO - LLM Prompt Orchestrator initialized
2025-07-14 07:36:32,895 - storage.database_manager - INFO - Database tables initialized successfully with performance optimizations
2025-07-14 07:36:32,896 - storage.database_manager - INFO - Database manager initialized: data\epinnox_trading.db
2025-07-14 07:36:32,896 - storage.session_manager - INFO - Session Manager initialized
2025-07-14 07:36:32,904 - storage.database_manager - INFO - Created session: live_BTCUSDTUSDT_20250714_073632_016c2df5
2025-07-14 07:36:32,907 - storage.session_manager - INFO - Started session: live_BTCUSDTUSDT_20250714_073632_016c2df5
2025-07-14 07:36:33,039 - core.risk_management_system - INFO - 🛡️ Risk Management System initialized
2025-07-14 07:36:33,042 - core.error_handling_system - INFO - 🛡️ Error Handling System initialized
2025-07-14 07:36:33,042 - core.error_handling_system - INFO - 📊 Component registered: exchange_connection
2025-07-14 07:36:33,042 - core.error_handling_system - INFO - 📊 Component registered: trading_interface
2025-07-14 07:36:33,044 - core.error_handling_system - INFO - 📊 Component registered: market_data
2025-07-14 07:36:33,044 - core.error_handling_system - INFO - 📊 Component registered: llm_orchestrator
2025-07-14 07:36:33,047 - core.error_handling_system - INFO - 🔍 Health monitoring started
2025-07-14 07:36:33,050 - core.monitoring_dashboard - INFO - 📊 Monitoring Dashboard initialized
2025-07-14 07:36:33,051 - core.symbol_scanner - INFO - SymbolScanner initialized with 8 symbols
2025-07-14 07:36:33,052 - core.llm_action_executors - INFO - 🚀 LLM Action Executors initialized with intelligent limit order system
2025-07-14 07:36:42,842 - data.live_data_manager - INFO - Successfully subscribed to live data for DOGE/USDT:USDT on timeframes: ['1m', '5m', '15m']
2025-07-14 07:38:16,355 - main - INFO - Epinnox v6 starting up...
2025-07-14 07:38:16,389 - core.performance_monitor - INFO - Performance monitoring started
2025-07-14 07:38:16,389 - core.performance_monitor - INFO - Performance monitor initialized with 10s interval
2025-07-14 07:38:16,389 - main - INFO - Performance monitoring initialized
2025-07-14 07:38:16,401 - utils.cache_manager - INFO - Cache manager initialized: cache, max_age=24h, max_size=100MB
2025-07-14 07:38:16,402 - utils.cache_manager - INFO - Starting automatic cache cleanup
2025-07-14 07:38:16,403 - utils.cache_manager - INFO - Auto cleanup complete: 0 files removed, 0.00 MB freed
2025-07-14 07:38:26,255 - config.autonomous_config - INFO - Configuration loaded from configs/autonomous_trading.yaml
2025-07-14 07:38:27,222 - data.live_data_manager - INFO - Successfully subscribed to live data for BTC/USDT:USDT on timeframes: ['1m', '5m', '15m']
2025-07-14 07:38:27,236 - llama.runner - INFO - Loaded OpenAI API key from models_config.yaml
2025-07-14 07:38:27,236 - llama.runner - INFO - Using model path: C:\Users\<USER>\.lmstudio\models\lmstudio-community\Phi-3.1-mini-128k-instruct-GGUF\Phi-3.1-mini-128k-instruct-Q4_K_M.gguf
2025-07-14 07:38:27,236 - llama.runner - INFO - Using guard path: C:\Users\<USER>\Documents\dev\Epinnox_v6\models\Llama-Guard-3-8B.gguf
2025-07-14 07:38:27,236 - llama.runner - INFO - Using binary path: C:\Users\<USER>\Documents\dev\llama.cpp\main
2025-07-14 07:38:27,244 - llama.lmstudio_runner - INFO - Loaded LMStudio configuration from models_config.yaml
2025-07-14 07:38:28,495 - websocket - INFO - Websocket connected
2025-07-14 07:38:29,318 - llama.lmstudio_runner - INFO - Discovered 8 models: ['openthinker3-7b', 'mistral-7b-claude-chat', 'fine_tuned_qwen1.7b', 'tsfc-model-0.0.1', 'codestral-22b-v0.1', 'phi-3.1-mini-128k-instruct', 'deepseek-r1-distill-qwen-7b', 'text-embedding-nomic-embed-text-v1.5']
2025-07-14 07:38:29,319 - llama.lmstudio_runner - INFO - Selected preferred model from config: phi-3.1-mini-128k-instruct
2025-07-14 07:38:29,319 - llama.lmstudio_runner - INFO - Initialized LMStudio with model: phi-3.1-mini-128k-instruct
2025-07-14 07:38:29,322 - core.llm_action_executors - INFO - 🚀 LLM Action Executors initialized with intelligent limit order system
2025-07-14 07:38:29,323 - utils.cache_manager - INFO - Cache manager initialized: cache, max_age=24h, max_size=100MB
2025-07-14 07:38:29,323 - core.llm_orchestrator - INFO - LLM Prompt Orchestrator initialized
2025-07-14 07:38:29,348 - storage.database_manager - INFO - Database tables initialized successfully with performance optimizations
2025-07-14 07:38:29,349 - storage.database_manager - INFO - Database manager initialized: data\epinnox_trading.db
2025-07-14 07:38:29,349 - storage.session_manager - INFO - Session Manager initialized
2025-07-14 07:38:29,355 - storage.database_manager - INFO - Created session: live_BTCUSDTUSDT_20250714_073829_44539e5c
2025-07-14 07:38:29,355 - storage.session_manager - INFO - Started session: live_BTCUSDTUSDT_20250714_073829_44539e5c
2025-07-14 07:38:29,500 - core.risk_management_system - INFO - 🛡️ Risk Management System initialized
2025-07-14 07:38:29,505 - core.error_handling_system - INFO - 🛡️ Error Handling System initialized
2025-07-14 07:38:29,506 - core.error_handling_system - INFO - 📊 Component registered: exchange_connection
2025-07-14 07:38:29,506 - core.error_handling_system - INFO - 📊 Component registered: trading_interface
2025-07-14 07:38:29,506 - core.error_handling_system - INFO - 📊 Component registered: market_data
2025-07-14 07:38:29,506 - core.error_handling_system - INFO - 📊 Component registered: llm_orchestrator
2025-07-14 07:38:29,507 - core.error_handling_system - INFO - 🔍 Health monitoring started
2025-07-14 07:38:29,512 - core.monitoring_dashboard - INFO - 📊 Monitoring Dashboard initialized
2025-07-14 07:38:29,513 - core.symbol_scanner - INFO - SymbolScanner initialized with 8 symbols
2025-07-14 07:38:29,513 - core.llm_action_executors - INFO - 🚀 LLM Action Executors initialized with intelligent limit order system
2025-07-14 07:59:05,368 - main - INFO - Epinnox v6 starting up...
2025-07-14 07:59:05,383 - core.performance_monitor - INFO - Performance monitoring started
2025-07-14 07:59:05,383 - core.performance_monitor - INFO - Performance monitor initialized with 10s interval
2025-07-14 07:59:05,383 - main - INFO - Performance monitoring initialized
2025-07-14 07:59:05,392 - utils.cache_manager - INFO - Cache manager initialized: cache, max_age=24h, max_size=100MB
2025-07-14 07:59:05,393 - utils.cache_manager - INFO - Starting automatic cache cleanup
2025-07-14 07:59:05,393 - utils.cache_manager - INFO - Auto cleanup complete: 0 files removed, 0.00 MB freed
2025-07-14 07:59:15,330 - config.autonomous_config - INFO - Configuration loaded from configs/autonomous_trading.yaml
2025-07-14 07:59:16,231 - data.live_data_manager - INFO - Successfully subscribed to live data for BTC/USDT:USDT on timeframes: ['1m', '5m', '15m']
2025-07-14 07:59:16,239 - llama.runner - INFO - Loaded OpenAI API key from models_config.yaml
2025-07-14 07:59:16,239 - llama.runner - INFO - Using model path: C:\Users\<USER>\.lmstudio\models\lmstudio-community\Phi-3.1-mini-128k-instruct-GGUF\Phi-3.1-mini-128k-instruct-Q4_K_M.gguf
2025-07-14 07:59:16,240 - llama.runner - INFO - Using guard path: C:\Users\<USER>\Documents\dev\Epinnox_v6\models\Llama-Guard-3-8B.gguf
2025-07-14 07:59:16,240 - llama.runner - INFO - Using binary path: C:\Users\<USER>\Documents\dev\llama.cpp\main
2025-07-14 07:59:16,245 - llama.lmstudio_runner - INFO - Loaded LMStudio configuration from models_config.yaml
2025-07-14 07:59:17,662 - websocket - INFO - Websocket connected
2025-07-14 07:59:18,272 - llama.lmstudio_runner - INFO - Discovered 8 models: ['openthinker3-7b', 'mistral-7b-claude-chat', 'fine_tuned_qwen1.7b', 'tsfc-model-0.0.1', 'codestral-22b-v0.1', 'phi-3.1-mini-128k-instruct', 'deepseek-r1-distill-qwen-7b', 'text-embedding-nomic-embed-text-v1.5']
2025-07-14 07:59:18,275 - llama.lmstudio_runner - INFO - Selected preferred model from config: phi-3.1-mini-128k-instruct
2025-07-14 07:59:18,275 - llama.lmstudio_runner - INFO - Initialized LMStudio with model: phi-3.1-mini-128k-instruct
2025-07-14 07:59:18,277 - core.llm_action_executors - ERROR - Failed to initialize limit order manager: 'NoneType' object has no attribute 'trading_engine'
2025-07-14 07:59:18,278 - core.llm_action_executors - INFO - 🚀 LLM Action Executors initialized with intelligent limit order system
2025-07-14 07:59:18,279 - utils.cache_manager - INFO - Cache manager initialized: cache, max_age=24h, max_size=100MB
2025-07-14 07:59:18,279 - core.llm_orchestrator - INFO - LLM Prompt Orchestrator initialized
2025-07-14 07:59:18,297 - storage.database_manager - INFO - Database tables initialized successfully with performance optimizations
2025-07-14 07:59:18,297 - storage.database_manager - INFO - Database manager initialized: data\epinnox_trading.db
2025-07-14 07:59:18,298 - storage.session_manager - INFO - Session Manager initialized
2025-07-14 07:59:18,300 - storage.database_manager - INFO - Created session: live_BTCUSDTUSDT_20250714_075918_16086ac8
2025-07-14 07:59:18,301 - storage.session_manager - INFO - Started session: live_BTCUSDTUSDT_20250714_075918_16086ac8
2025-07-14 07:59:18,447 - core.risk_management_system - INFO - 🛡️ Risk Management System initialized
2025-07-14 07:59:18,449 - core.error_handling_system - INFO - 🛡️ Error Handling System initialized
2025-07-14 07:59:18,450 - core.error_handling_system - INFO - 📊 Component registered: exchange_connection
2025-07-14 07:59:18,450 - core.error_handling_system - INFO - 📊 Component registered: trading_interface
2025-07-14 07:59:18,451 - core.error_handling_system - INFO - 📊 Component registered: market_data
2025-07-14 07:59:18,451 - core.error_handling_system - INFO - 📊 Component registered: llm_orchestrator
2025-07-14 07:59:18,452 - core.error_handling_system - INFO - 🔍 Health monitoring started
2025-07-14 07:59:18,455 - core.monitoring_dashboard - INFO - 📊 Monitoring Dashboard initialized
2025-07-14 07:59:18,456 - core.symbol_scanner - INFO - SymbolScanner initialized with 8 symbols
2025-07-14 07:59:18,457 - core.llm_action_executors - INFO - 🚀 LLM Action Executors initialized with intelligent limit order system
2025-07-14 08:00:30,216 - main - INFO - Epinnox v6 starting up...
2025-07-14 08:00:30,231 - core.performance_monitor - INFO - Performance monitoring started
2025-07-14 08:00:30,231 - core.performance_monitor - INFO - Performance monitor initialized with 10s interval
2025-07-14 08:00:30,231 - main - INFO - Performance monitoring initialized
2025-07-14 08:00:30,242 - utils.cache_manager - INFO - Cache manager initialized: cache, max_age=24h, max_size=100MB
2025-07-14 08:00:30,242 - utils.cache_manager - INFO - Starting automatic cache cleanup
2025-07-14 08:00:30,243 - utils.cache_manager - INFO - Auto cleanup complete: 0 files removed, 0.00 MB freed
2025-07-14 08:00:39,193 - config.autonomous_config - INFO - Configuration loaded from configs/autonomous_trading.yaml
2025-07-14 08:00:40,069 - data.live_data_manager - INFO - Successfully subscribed to live data for BTC/USDT:USDT on timeframes: ['1m', '5m', '15m']
2025-07-14 08:00:40,078 - llama.runner - INFO - Loaded OpenAI API key from models_config.yaml
2025-07-14 08:00:40,078 - llama.runner - INFO - Using model path: C:\Users\<USER>\.lmstudio\models\lmstudio-community\Phi-3.1-mini-128k-instruct-GGUF\Phi-3.1-mini-128k-instruct-Q4_K_M.gguf
2025-07-14 08:00:40,078 - llama.runner - INFO - Using guard path: C:\Users\<USER>\Documents\dev\Epinnox_v6\models\Llama-Guard-3-8B.gguf
2025-07-14 08:00:40,079 - llama.runner - INFO - Using binary path: C:\Users\<USER>\Documents\dev\llama.cpp\main
2025-07-14 08:00:40,083 - llama.lmstudio_runner - INFO - Loaded LMStudio configuration from models_config.yaml
2025-07-14 08:00:41,335 - websocket - INFO - Websocket connected
2025-07-14 08:00:42,108 - llama.lmstudio_runner - INFO - Discovered 8 models: ['openthinker3-7b', 'mistral-7b-claude-chat', 'fine_tuned_qwen1.7b', 'tsfc-model-0.0.1', 'codestral-22b-v0.1', 'phi-3.1-mini-128k-instruct', 'deepseek-r1-distill-qwen-7b', 'text-embedding-nomic-embed-text-v1.5']
2025-07-14 08:00:42,109 - llama.lmstudio_runner - INFO - Selected preferred model from config: phi-3.1-mini-128k-instruct
2025-07-14 08:00:42,109 - llama.lmstudio_runner - INFO - Initialized LMStudio with model: phi-3.1-mini-128k-instruct
2025-07-14 08:00:42,111 - core.llm_action_executors - ERROR - Failed to initialize limit order manager: 'NoneType' object has no attribute 'trading_engine'
2025-07-14 08:00:42,111 - core.llm_action_executors - INFO - 🚀 LLM Action Executors initialized with intelligent limit order system
2025-07-14 08:00:42,112 - utils.cache_manager - INFO - Cache manager initialized: cache, max_age=24h, max_size=100MB
2025-07-14 08:00:42,112 - core.llm_orchestrator - INFO - LLM Prompt Orchestrator initialized
2025-07-14 08:00:42,124 - storage.database_manager - INFO - Database tables initialized successfully with performance optimizations
2025-07-14 08:00:42,124 - storage.database_manager - INFO - Database manager initialized: data\epinnox_trading.db
2025-07-14 08:00:42,125 - storage.session_manager - INFO - Session Manager initialized
2025-07-14 08:00:42,129 - storage.database_manager - INFO - Created session: live_BTCUSDTUSDT_20250714_080042_bf36b11a
2025-07-14 08:00:42,130 - storage.session_manager - INFO - Started session: live_BTCUSDTUSDT_20250714_080042_bf36b11a
2025-07-14 08:00:42,289 - core.risk_management_system - INFO - 🛡️ Risk Management System initialized
2025-07-14 08:00:42,292 - core.error_handling_system - INFO - 🛡️ Error Handling System initialized
2025-07-14 08:00:42,292 - core.error_handling_system - INFO - 📊 Component registered: exchange_connection
2025-07-14 08:00:42,293 - core.error_handling_system - INFO - 📊 Component registered: trading_interface
2025-07-14 08:00:42,293 - core.error_handling_system - INFO - 📊 Component registered: market_data
2025-07-14 08:00:42,293 - core.error_handling_system - INFO - 📊 Component registered: llm_orchestrator
2025-07-14 08:00:42,295 - core.error_handling_system - INFO - 🔍 Health monitoring started
2025-07-14 08:00:42,298 - core.monitoring_dashboard - INFO - 📊 Monitoring Dashboard initialized
2025-07-14 08:00:42,299 - core.symbol_scanner - INFO - SymbolScanner initialized with 8 symbols
2025-07-14 08:00:42,300 - core.llm_action_executors - INFO - 🚀 LLM Action Executors initialized with intelligent limit order system
2025-07-14 08:04:57,781 - main - INFO - Epinnox v6 starting up...
2025-07-14 08:04:57,805 - core.performance_monitor - INFO - Performance monitoring started
2025-07-14 08:04:57,805 - core.performance_monitor - INFO - Performance monitor initialized with 10s interval
2025-07-14 08:04:57,806 - main - INFO - Performance monitoring initialized
2025-07-14 08:04:57,816 - utils.cache_manager - INFO - Cache manager initialized: cache, max_age=24h, max_size=100MB
2025-07-14 08:04:57,817 - utils.cache_manager - INFO - Starting automatic cache cleanup
2025-07-14 08:04:57,818 - utils.cache_manager - INFO - Auto cleanup complete: 0 files removed, 0.00 MB freed
2025-07-14 08:05:08,710 - config.autonomous_config - INFO - Configuration loaded from configs/autonomous_trading.yaml
2025-07-14 08:05:09,637 - data.live_data_manager - INFO - Successfully subscribed to live data for BTC/USDT:USDT on timeframes: ['1m', '5m', '15m']
2025-07-14 08:05:09,645 - llama.runner - INFO - Loaded OpenAI API key from models_config.yaml
2025-07-14 08:05:09,645 - llama.runner - INFO - Using model path: C:\Users\<USER>\.lmstudio\models\lmstudio-community\Phi-3.1-mini-128k-instruct-GGUF\Phi-3.1-mini-128k-instruct-Q4_K_M.gguf
2025-07-14 08:05:09,645 - llama.runner - INFO - Using guard path: C:\Users\<USER>\Documents\dev\Epinnox_v6\models\Llama-Guard-3-8B.gguf
2025-07-14 08:05:09,646 - llama.runner - INFO - Using binary path: C:\Users\<USER>\Documents\dev\llama.cpp\main
2025-07-14 08:05:09,651 - llama.lmstudio_runner - INFO - Loaded LMStudio configuration from models_config.yaml
2025-07-14 08:05:11,679 - llama.lmstudio_runner - INFO - Discovered 8 models: ['openthinker3-7b', 'mistral-7b-claude-chat', 'fine_tuned_qwen1.7b', 'tsfc-model-0.0.1', 'codestral-22b-v0.1', 'phi-3.1-mini-128k-instruct', 'deepseek-r1-distill-qwen-7b', 'text-embedding-nomic-embed-text-v1.5']
2025-07-14 08:05:11,679 - llama.lmstudio_runner - INFO - Selected preferred model from config: phi-3.1-mini-128k-instruct
2025-07-14 08:05:11,680 - llama.lmstudio_runner - INFO - Initialized LMStudio with model: phi-3.1-mini-128k-instruct
2025-07-14 08:05:11,682 - core.llm_action_executors - ERROR - Failed to initialize limit order manager: 'NoneType' object has no attribute 'trading_engine'
2025-07-14 08:05:11,683 - core.llm_action_executors - INFO - 🚀 LLM Action Executors initialized with intelligent limit order system
2025-07-14 08:05:11,683 - utils.cache_manager - INFO - Cache manager initialized: cache, max_age=24h, max_size=100MB
2025-07-14 08:05:11,683 - core.llm_orchestrator - INFO - LLM Prompt Orchestrator initialized
2025-07-14 08:05:11,695 - storage.database_manager - INFO - Database tables initialized successfully with performance optimizations
2025-07-14 08:05:11,695 - storage.database_manager - INFO - Database manager initialized: data\epinnox_trading.db
2025-07-14 08:05:11,695 - storage.session_manager - INFO - Session Manager initialized
2025-07-14 08:05:11,700 - storage.database_manager - INFO - Created session: live_BTCUSDTUSDT_20250714_080511_d939709d
2025-07-14 08:05:11,700 - storage.session_manager - INFO - Started session: live_BTCUSDTUSDT_20250714_080511_d939709d
2025-07-14 08:05:11,837 - core.risk_management_system - INFO - 🛡️ Risk Management System initialized
2025-07-14 08:05:11,840 - core.error_handling_system - INFO - 🛡️ Error Handling System initialized
2025-07-14 08:05:11,840 - core.error_handling_system - INFO - 📊 Component registered: exchange_connection
2025-07-14 08:05:11,840 - core.error_handling_system - INFO - 📊 Component registered: trading_interface
2025-07-14 08:05:11,840 - core.error_handling_system - INFO - 📊 Component registered: market_data
2025-07-14 08:05:11,840 - core.error_handling_system - INFO - 📊 Component registered: llm_orchestrator
2025-07-14 08:05:11,842 - core.error_handling_system - INFO - 🔍 Health monitoring started
2025-07-14 08:05:11,844 - core.monitoring_dashboard - INFO - 📊 Monitoring Dashboard initialized
2025-07-14 08:05:11,845 - core.symbol_scanner - INFO - SymbolScanner initialized with 8 symbols
2025-07-14 08:05:11,846 - core.llm_action_executors - INFO - 🚀 LLM Action Executors initialized with intelligent limit order system
2025-07-14 08:05:25,894 - websocket - ERROR - Handshake status 504 Gateway Time-out -+-+- {'content-type': 'text/html', 'content-length': '164', 'connection': 'close', 'server': 'openresty', 'date': 'Mon, 14 Jul 2025 13:05:24 GMT', 'x-request-id': 'ddeb9588c2bca131d49194882d970f27', 'x-cache': 'Error from cloudfront', 'via': '1.1 4de62e0f8bb36f486176ce5d831470b4.cloudfront.net (CloudFront)', 'x-amz-cf-pop': 'MAN51-P3', 'x-amz-cf-id': 'Kk1JIVHXVKjCX7ksbypNttm0nS3XNyAlKoM-u5nW8IEX2Ad06NOStg=='} -+-+- b'<html>\r\n<head><title>504 Gateway Time-out</title></head>\r\n<body>\r\n<center><h1>504 Gateway Time-out</h1></center>\r\n<hr><center>openresty</center>\r\n</body>\r\n</html>\r\n' - goodbye
2025-07-14 08:05:50,446 - main - INFO - Epinnox v6 starting up...
2025-07-14 08:05:50,460 - core.performance_monitor - INFO - Performance monitoring started
2025-07-14 08:05:50,461 - core.performance_monitor - INFO - Performance monitor initialized with 10s interval
2025-07-14 08:05:50,461 - main - INFO - Performance monitoring initialized
2025-07-14 08:05:50,470 - utils.cache_manager - INFO - Cache manager initialized: cache, max_age=24h, max_size=100MB
2025-07-14 08:05:50,471 - utils.cache_manager - INFO - Starting automatic cache cleanup
2025-07-14 08:05:50,472 - utils.cache_manager - INFO - Auto cleanup complete: 0 files removed, 0.00 MB freed
2025-07-14 08:06:08,979 - main - INFO - Epinnox v6 starting up...
2025-07-14 08:06:08,995 - core.performance_monitor - INFO - Performance monitoring started
2025-07-14 08:06:08,995 - core.performance_monitor - INFO - Performance monitor initialized with 10s interval
2025-07-14 08:06:08,996 - main - INFO - Performance monitoring initialized
2025-07-14 08:06:09,004 - utils.cache_manager - INFO - Cache manager initialized: cache, max_age=24h, max_size=100MB
2025-07-14 08:06:09,005 - utils.cache_manager - INFO - Starting automatic cache cleanup
2025-07-14 08:06:09,006 - utils.cache_manager - INFO - Auto cleanup complete: 0 files removed, 0.00 MB freed
2025-07-14 08:06:17,634 - config.autonomous_config - INFO - Configuration loaded from configs/autonomous_trading.yaml
2025-07-14 08:06:18,527 - data.live_data_manager - INFO - Successfully subscribed to live data for BTC/USDT:USDT on timeframes: ['1m', '5m', '15m']
2025-07-14 08:06:18,539 - llama.runner - INFO - Loaded OpenAI API key from models_config.yaml
2025-07-14 08:06:18,540 - llama.runner - INFO - Using model path: C:\Users\<USER>\.lmstudio\models\lmstudio-community\Phi-3.1-mini-128k-instruct-GGUF\Phi-3.1-mini-128k-instruct-Q4_K_M.gguf
2025-07-14 08:06:18,540 - llama.runner - INFO - Using guard path: C:\Users\<USER>\Documents\dev\Epinnox_v6\models\Llama-Guard-3-8B.gguf
2025-07-14 08:06:18,540 - llama.runner - INFO - Using binary path: C:\Users\<USER>\Documents\dev\llama.cpp\main
2025-07-14 08:06:18,543 - llama.lmstudio_runner - INFO - Loaded LMStudio configuration from models_config.yaml
2025-07-14 08:06:19,786 - websocket - INFO - Websocket connected
2025-07-14 08:06:20,578 - llama.lmstudio_runner - INFO - Discovered 8 models: ['openthinker3-7b', 'mistral-7b-claude-chat', 'fine_tuned_qwen1.7b', 'tsfc-model-0.0.1', 'codestral-22b-v0.1', 'phi-3.1-mini-128k-instruct', 'deepseek-r1-distill-qwen-7b', 'text-embedding-nomic-embed-text-v1.5']
2025-07-14 08:06:20,579 - llama.lmstudio_runner - INFO - Selected preferred model from config: phi-3.1-mini-128k-instruct
2025-07-14 08:06:20,579 - llama.lmstudio_runner - INFO - Initialized LMStudio with model: phi-3.1-mini-128k-instruct
2025-07-14 08:06:20,582 - core.llm_action_executors - ERROR - Failed to initialize limit order manager: 'NoneType' object has no attribute 'trading_engine'
2025-07-14 08:06:20,583 - core.llm_action_executors - INFO - 🚀 LLM Action Executors initialized with intelligent limit order system
2025-07-14 08:06:20,584 - utils.cache_manager - INFO - Cache manager initialized: cache, max_age=24h, max_size=100MB
2025-07-14 08:06:20,584 - core.llm_orchestrator - INFO - LLM Prompt Orchestrator initialized
2025-07-14 08:06:20,596 - storage.database_manager - INFO - Database tables initialized successfully with performance optimizations
2025-07-14 08:06:20,598 - storage.database_manager - INFO - Database manager initialized: data\epinnox_trading.db
2025-07-14 08:06:20,598 - storage.session_manager - INFO - Session Manager initialized
2025-07-14 08:06:20,608 - storage.database_manager - INFO - Created session: live_BTCUSDTUSDT_20250714_080620_d9682916
2025-07-14 08:06:20,612 - storage.session_manager - INFO - Started session: live_BTCUSDTUSDT_20250714_080620_d9682916
2025-07-14 08:06:20,753 - core.risk_management_system - INFO - 🛡️ Risk Management System initialized
2025-07-14 08:06:20,755 - core.error_handling_system - INFO - 🛡️ Error Handling System initialized
2025-07-14 08:06:20,756 - core.error_handling_system - INFO - 📊 Component registered: exchange_connection
2025-07-14 08:06:20,756 - core.error_handling_system - INFO - 📊 Component registered: trading_interface
2025-07-14 08:06:20,757 - core.error_handling_system - INFO - 📊 Component registered: market_data
2025-07-14 08:06:20,757 - core.error_handling_system - INFO - 📊 Component registered: llm_orchestrator
2025-07-14 08:06:20,759 - core.error_handling_system - INFO - 🔍 Health monitoring started
2025-07-14 08:06:20,761 - core.monitoring_dashboard - INFO - 📊 Monitoring Dashboard initialized
2025-07-14 08:06:20,762 - core.symbol_scanner - INFO - SymbolScanner initialized with 8 symbols
2025-07-14 08:06:20,763 - core.llm_action_executors - INFO - 🚀 LLM Action Executors initialized with intelligent limit order system
2025-07-14 08:12:38,221 - main - INFO - Epinnox v6 starting up...
2025-07-14 08:12:38,239 - core.performance_monitor - INFO - Performance monitoring started
2025-07-14 08:12:38,239 - core.performance_monitor - INFO - Performance monitor initialized with 10s interval
2025-07-14 08:12:38,239 - main - INFO - Performance monitoring initialized
2025-07-14 08:12:38,252 - utils.cache_manager - INFO - Cache manager initialized: cache, max_age=24h, max_size=100MB
2025-07-14 08:12:38,254 - utils.cache_manager - INFO - Starting automatic cache cleanup
2025-07-14 08:12:38,255 - utils.cache_manager - INFO - Auto cleanup complete: 0 files removed, 0.00 MB freed
2025-07-14 08:12:48,235 - config.autonomous_config - INFO - Configuration loaded from configs/autonomous_trading.yaml
2025-07-14 08:12:49,113 - data.live_data_manager - INFO - Successfully subscribed to live data for BTC/USDT:USDT on timeframes: ['1m', '5m', '15m']
2025-07-14 08:12:49,121 - llama.runner - INFO - Loaded OpenAI API key from models_config.yaml
2025-07-14 08:12:49,122 - llama.runner - INFO - Using model path: C:\Users\<USER>\.lmstudio\models\lmstudio-community\Phi-3.1-mini-128k-instruct-GGUF\Phi-3.1-mini-128k-instruct-Q4_K_M.gguf
2025-07-14 08:12:49,122 - llama.runner - INFO - Using guard path: C:\Users\<USER>\Documents\dev\Epinnox_v6\models\Llama-Guard-3-8B.gguf
2025-07-14 08:12:49,123 - llama.runner - INFO - Using binary path: C:\Users\<USER>\Documents\dev\llama.cpp\main
2025-07-14 08:12:49,128 - llama.lmstudio_runner - INFO - Loaded LMStudio configuration from models_config.yaml
2025-07-14 08:12:50,354 - websocket - INFO - Websocket connected
2025-07-14 08:12:51,175 - llama.lmstudio_runner - INFO - Discovered 8 models: ['openthinker3-7b', 'mistral-7b-claude-chat', 'fine_tuned_qwen1.7b', 'tsfc-model-0.0.1', 'codestral-22b-v0.1', 'phi-3.1-mini-128k-instruct', 'deepseek-r1-distill-qwen-7b', 'text-embedding-nomic-embed-text-v1.5']
2025-07-14 08:12:51,175 - llama.lmstudio_runner - INFO - Selected preferred model from config: phi-3.1-mini-128k-instruct
2025-07-14 08:12:51,178 - llama.lmstudio_runner - INFO - Initialized LMStudio with model: phi-3.1-mini-128k-instruct
2025-07-14 08:12:51,178 - core.llm_action_executors - ERROR - Failed to initialize limit order manager: 'NoneType' object has no attribute 'trading_engine'
2025-07-14 08:12:51,181 - core.llm_action_executors - ERROR - Trading engine attribute is missing. Please check the trading interface setup.
2025-07-14 08:12:51,194 - storage.database_manager - INFO - Database tables initialized successfully with performance optimizations
2025-07-14 08:12:51,196 - storage.database_manager - INFO - Database manager initialized: data\epinnox_trading.db
2025-07-14 08:12:51,197 - storage.session_manager - INFO - Session Manager initialized
2025-07-14 08:12:51,204 - storage.database_manager - INFO - Created session: live_BTCUSDTUSDT_20250714_081251_20a10b70
2025-07-14 08:12:51,208 - storage.session_manager - INFO - Started session: live_BTCUSDTUSDT_20250714_081251_20a10b70
2025-07-14 08:12:51,953 - core.risk_management_system - INFO - 🛡️ Risk Management System initialized
2025-07-14 08:12:51,962 - core.error_handling_system - INFO - 🛡️ Error Handling System initialized
2025-07-14 08:12:51,964 - core.error_handling_system - INFO - 📊 Component registered: exchange_connection
2025-07-14 08:12:51,966 - core.error_handling_system - INFO - 📊 Component registered: trading_interface
2025-07-14 08:12:51,967 - core.error_handling_system - INFO - 📊 Component registered: market_data
2025-07-14 08:12:51,969 - core.error_handling_system - INFO - 📊 Component registered: llm_orchestrator
2025-07-14 08:12:51,974 - core.error_handling_system - INFO - 🔍 Health monitoring started
2025-07-14 08:12:51,986 - core.monitoring_dashboard - INFO - 📊 Monitoring Dashboard initialized
2025-07-14 08:12:51,998 - core.symbol_scanner - INFO - SymbolScanner initialized with 8 symbols
2025-07-14 08:12:52,006 - core.llm_action_executors - ERROR - Trading engine attribute is missing. Please check the trading interface setup.
2025-07-14 08:12:59,476 - websocket - ERROR - error from callback <bound method WebSocketClient._on_message of <data.websocket_client.WebSocketClient object at 0x0000016CA21E31F0>>: wrapped C/C++ object of type WebSocketClient has been deleted
2025-07-14 08:12:59,476 - websocket - ERROR - error from callback <bound method WebSocketClient._on_error of <data.websocket_client.WebSocketClient object at 0x0000016CA21E31F0>>: wrapped C/C++ object of type WebSocketClient has been deleted
2025-07-14 08:12:59,479 - websocket - INFO - tearing down on exception wrapped C/C++ object of type WebSocketClient has been deleted
2025-07-14 08:12:59,825 - websocket - ERROR - error from callback <bound method WebSocketClient._on_close of <data.websocket_client.WebSocketClient object at 0x0000016CA21E31F0>>: wrapped C/C++ object of type WebSocketClient has been deleted
2025-07-14 08:13:07,182 - main - INFO - Epinnox v6 starting up...
2025-07-14 08:13:07,207 - core.performance_monitor - INFO - Performance monitoring started
2025-07-14 08:13:07,207 - core.performance_monitor - INFO - Performance monitor initialized with 10s interval
2025-07-14 08:13:07,209 - main - INFO - Performance monitoring initialized
2025-07-14 08:13:07,221 - utils.cache_manager - INFO - Cache manager initialized: cache, max_age=24h, max_size=100MB
2025-07-14 08:13:07,222 - utils.cache_manager - INFO - Starting automatic cache cleanup
2025-07-14 08:13:07,224 - utils.cache_manager - INFO - Auto cleanup complete: 0 files removed, 0.00 MB freed
2025-07-14 08:13:14,934 - config.autonomous_config - INFO - Configuration loaded from configs/autonomous_trading.yaml
2025-07-14 08:13:15,808 - data.live_data_manager - INFO - Successfully subscribed to live data for BTC/USDT:USDT on timeframes: ['1m', '5m', '15m']
2025-07-14 08:13:15,817 - llama.runner - INFO - Loaded OpenAI API key from models_config.yaml
2025-07-14 08:13:15,817 - llama.runner - INFO - Using model path: C:\Users\<USER>\.lmstudio\models\lmstudio-community\Phi-3.1-mini-128k-instruct-GGUF\Phi-3.1-mini-128k-instruct-Q4_K_M.gguf
2025-07-14 08:13:15,818 - llama.runner - INFO - Using guard path: C:\Users\<USER>\Documents\dev\Epinnox_v6\models\Llama-Guard-3-8B.gguf
2025-07-14 08:13:15,819 - llama.runner - INFO - Using binary path: C:\Users\<USER>\Documents\dev\llama.cpp\main
2025-07-14 08:13:15,824 - llama.lmstudio_runner - INFO - Loaded LMStudio configuration from models_config.yaml
2025-07-14 08:13:17,063 - websocket - INFO - Websocket connected
2025-07-14 08:13:17,865 - llama.lmstudio_runner - INFO - Discovered 8 models: ['openthinker3-7b', 'mistral-7b-claude-chat', 'fine_tuned_qwen1.7b', 'tsfc-model-0.0.1', 'codestral-22b-v0.1', 'phi-3.1-mini-128k-instruct', 'deepseek-r1-distill-qwen-7b', 'text-embedding-nomic-embed-text-v1.5']
2025-07-14 08:13:17,865 - llama.lmstudio_runner - INFO - Selected preferred model from config: phi-3.1-mini-128k-instruct
2025-07-14 08:13:17,865 - llama.lmstudio_runner - INFO - Initialized LMStudio with model: phi-3.1-mini-128k-instruct
2025-07-14 08:13:17,865 - core.llm_action_executors - ERROR - Failed to initialize limit order manager: 'NoneType' object has no attribute 'trading_engine'
2025-07-14 08:13:17,865 - core.llm_action_executors - INFO - 🚀 LLM Action Executors initialized with intelligent limit order system
2025-07-14 08:13:17,872 - utils.cache_manager - INFO - Cache manager initialized: cache, max_age=24h, max_size=100MB
2025-07-14 08:13:17,872 - core.llm_orchestrator - INFO - LLM Prompt Orchestrator initialized
2025-07-14 08:13:17,881 - storage.database_manager - INFO - Database tables initialized successfully with performance optimizations
2025-07-14 08:13:17,882 - storage.database_manager - INFO - Database manager initialized: data\epinnox_trading.db
2025-07-14 08:13:17,882 - storage.session_manager - INFO - Session Manager initialized
2025-07-14 08:13:17,890 - storage.database_manager - INFO - Created session: live_BTCUSDTUSDT_20250714_081317_6cc1d98a
2025-07-14 08:13:17,894 - storage.session_manager - INFO - Started session: live_BTCUSDTUSDT_20250714_081317_6cc1d98a
2025-07-14 08:13:18,029 - core.risk_management_system - INFO - 🛡️ Risk Management System initialized
2025-07-14 08:13:18,031 - core.error_handling_system - INFO - 🛡️ Error Handling System initialized
2025-07-14 08:13:18,032 - core.error_handling_system - INFO - 📊 Component registered: exchange_connection
2025-07-14 08:13:18,032 - core.error_handling_system - INFO - 📊 Component registered: trading_interface
2025-07-14 08:13:18,032 - core.error_handling_system - INFO - 📊 Component registered: market_data
2025-07-14 08:13:18,033 - core.error_handling_system - INFO - 📊 Component registered: llm_orchestrator
2025-07-14 08:13:18,034 - core.error_handling_system - INFO - 🔍 Health monitoring started
2025-07-14 08:13:18,037 - core.monitoring_dashboard - INFO - 📊 Monitoring Dashboard initialized
2025-07-14 08:13:18,038 - core.symbol_scanner - INFO - SymbolScanner initialized with 8 symbols
2025-07-14 08:13:18,039 - core.llm_action_executors - INFO - 🚀 LLM Action Executors initialized with intelligent limit order system
2025-07-14 08:17:04,100 - main - INFO - Epinnox v6 starting up...
2025-07-14 08:17:04,121 - core.performance_monitor - INFO - Performance monitoring started
2025-07-14 08:17:04,121 - core.performance_monitor - INFO - Performance monitor initialized with 10s interval
2025-07-14 08:17:04,125 - main - INFO - Performance monitoring initialized
2025-07-14 08:17:04,137 - utils.cache_manager - INFO - Cache manager initialized: cache, max_age=24h, max_size=100MB
2025-07-14 08:17:04,138 - utils.cache_manager - INFO - Starting automatic cache cleanup
2025-07-14 08:17:04,139 - utils.cache_manager - INFO - Auto cleanup complete: 0 files removed, 0.00 MB freed
2025-07-14 08:17:11,839 - config.autonomous_config - INFO - Configuration loaded from configs/autonomous_trading.yaml
2025-07-14 08:17:12,771 - data.live_data_manager - INFO - Successfully subscribed to live data for BTC/USDT:USDT on timeframes: ['1m', '5m', '15m']
2025-07-14 08:17:12,786 - llama.runner - INFO - Loaded OpenAI API key from models_config.yaml
2025-07-14 08:17:12,786 - llama.runner - INFO - Using model path: C:\Users\<USER>\.lmstudio\models\lmstudio-community\Phi-3.1-mini-128k-instruct-GGUF\Phi-3.1-mini-128k-instruct-Q4_K_M.gguf
2025-07-14 08:17:12,788 - llama.runner - INFO - Using guard path: C:\Users\<USER>\Documents\dev\Epinnox_v6\models\Llama-Guard-3-8B.gguf
2025-07-14 08:17:12,788 - llama.runner - INFO - Using binary path: C:\Users\<USER>\Documents\dev\llama.cpp\main
2025-07-14 08:17:12,796 - llama.lmstudio_runner - INFO - Loaded LMStudio configuration from models_config.yaml
2025-07-14 08:17:14,016 - websocket - INFO - Websocket connected
2025-07-14 08:17:14,836 - llama.lmstudio_runner - INFO - Discovered 8 models: ['openthinker3-7b', 'mistral-7b-claude-chat', 'fine_tuned_qwen1.7b', 'tsfc-model-0.0.1', 'codestral-22b-v0.1', 'phi-3.1-mini-128k-instruct', 'deepseek-r1-distill-qwen-7b', 'text-embedding-nomic-embed-text-v1.5']
2025-07-14 08:17:14,837 - llama.lmstudio_runner - INFO - Selected preferred model from config: phi-3.1-mini-128k-instruct
2025-07-14 08:17:14,837 - llama.lmstudio_runner - INFO - Initialized LMStudio with model: phi-3.1-mini-128k-instruct
2025-07-14 08:17:14,839 - core.llm_action_executors - ERROR - Failed to initialize limit order manager: 'NoneType' object has no attribute 'trading_engine'
2025-07-14 08:17:14,840 - core.llm_action_executors - INFO - 🚀 LLM Action Executors initialized with intelligent limit order system
2025-07-14 08:17:14,841 - utils.cache_manager - INFO - Cache manager initialized: cache, max_age=24h, max_size=100MB
2025-07-14 08:17:14,841 - core.llm_orchestrator - INFO - LLM Prompt Orchestrator initialized
2025-07-14 08:17:14,854 - storage.database_manager - INFO - Database tables initialized successfully with performance optimizations
2025-07-14 08:17:14,855 - storage.database_manager - INFO - Database manager initialized: data\epinnox_trading.db
2025-07-14 08:17:14,855 - storage.session_manager - INFO - Session Manager initialized
2025-07-14 08:17:14,858 - storage.database_manager - INFO - Created session: live_BTCUSDTUSDT_20250714_081714_eb9debd5
2025-07-14 08:17:14,864 - storage.session_manager - INFO - Started session: live_BTCUSDTUSDT_20250714_081714_eb9debd5
2025-07-14 08:17:15,041 - core.risk_management_system - INFO - 🛡️ Risk Management System initialized
2025-07-14 08:17:15,044 - core.error_handling_system - INFO - 🛡️ Error Handling System initialized
2025-07-14 08:17:15,046 - core.error_handling_system - INFO - 📊 Component registered: exchange_connection
2025-07-14 08:17:15,046 - core.error_handling_system - INFO - 📊 Component registered: trading_interface
2025-07-14 08:17:15,047 - core.error_handling_system - INFO - 📊 Component registered: market_data
2025-07-14 08:17:15,048 - core.error_handling_system - INFO - 📊 Component registered: llm_orchestrator
2025-07-14 08:17:15,051 - core.error_handling_system - INFO - 🔍 Health monitoring started
2025-07-14 08:17:15,054 - core.monitoring_dashboard - INFO - 📊 Monitoring Dashboard initialized
2025-07-14 08:17:15,064 - core.symbol_scanner - INFO - SymbolScanner initialized with 8 symbols
2025-07-14 08:17:15,068 - core.llm_action_executors - INFO - 🚀 LLM Action Executors initialized with intelligent limit order system
2025-07-14 08:19:01,190 - main - INFO - Epinnox v6 starting up...
2025-07-14 08:19:01,210 - core.performance_monitor - INFO - Performance monitoring started
2025-07-14 08:19:01,211 - core.performance_monitor - INFO - Performance monitor initialized with 10s interval
2025-07-14 08:19:01,211 - main - INFO - Performance monitoring initialized
2025-07-14 08:19:01,223 - utils.cache_manager - INFO - Cache manager initialized: cache, max_age=24h, max_size=100MB
2025-07-14 08:19:01,224 - utils.cache_manager - INFO - Starting automatic cache cleanup
2025-07-14 08:19:01,226 - utils.cache_manager - INFO - Auto cleanup complete: 0 files removed, 0.00 MB freed
2025-07-14 08:19:08,758 - config.autonomous_config - INFO - Configuration loaded from configs/autonomous_trading.yaml
2025-07-14 08:19:09,660 - data.live_data_manager - INFO - Successfully subscribed to live data for BTC/USDT:USDT on timeframes: ['1m', '5m', '15m']
2025-07-14 08:19:09,680 - llama.runner - INFO - Loaded OpenAI API key from models_config.yaml
2025-07-14 08:19:09,681 - llama.runner - INFO - Using model path: C:\Users\<USER>\.lmstudio\models\lmstudio-community\Phi-3.1-mini-128k-instruct-GGUF\Phi-3.1-mini-128k-instruct-Q4_K_M.gguf
2025-07-14 08:19:09,681 - llama.runner - INFO - Using guard path: C:\Users\<USER>\Documents\dev\Epinnox_v6\models\Llama-Guard-3-8B.gguf
2025-07-14 08:19:09,682 - llama.runner - INFO - Using binary path: C:\Users\<USER>\Documents\dev\llama.cpp\main
2025-07-14 08:19:09,687 - llama.lmstudio_runner - INFO - Loaded LMStudio configuration from models_config.yaml
2025-07-14 08:19:10,910 - websocket - INFO - Websocket connected
2025-07-14 08:19:11,715 - llama.lmstudio_runner - INFO - Discovered 8 models: ['openthinker3-7b', 'mistral-7b-claude-chat', 'fine_tuned_qwen1.7b', 'tsfc-model-0.0.1', 'codestral-22b-v0.1', 'phi-3.1-mini-128k-instruct', 'deepseek-r1-distill-qwen-7b', 'text-embedding-nomic-embed-text-v1.5']
2025-07-14 08:19:11,716 - llama.lmstudio_runner - INFO - Selected preferred model from config: phi-3.1-mini-128k-instruct
2025-07-14 08:19:11,716 - llama.lmstudio_runner - INFO - Initialized LMStudio with model: phi-3.1-mini-128k-instruct
2025-07-14 08:19:11,718 - core.llm_action_executors - WARNING - ⚠️ Missing trading engine or live data manager - using fallback
2025-07-14 08:19:11,718 - core.llm_action_executors - INFO - 🚀 LLM Action Executors initialized with intelligent limit order system
2025-07-14 08:19:11,718 - utils.cache_manager - INFO - Cache manager initialized: cache, max_age=24h, max_size=100MB
2025-07-14 08:19:11,718 - core.llm_orchestrator - INFO - LLM Prompt Orchestrator initialized
2025-07-14 08:19:11,729 - storage.database_manager - INFO - Database tables initialized successfully with performance optimizations
2025-07-14 08:19:11,730 - storage.database_manager - INFO - Database manager initialized: data\epinnox_trading.db
2025-07-14 08:19:11,731 - storage.session_manager - INFO - Session Manager initialized
2025-07-14 08:19:11,735 - storage.database_manager - INFO - Created session: live_BTCUSDTUSDT_20250714_081911_ae41607c
2025-07-14 08:19:11,738 - storage.session_manager - INFO - Started session: live_BTCUSDTUSDT_20250714_081911_ae41607c
2025-07-14 08:19:11,869 - core.risk_management_system - INFO - 🛡️ Risk Management System initialized
2025-07-14 08:19:11,871 - core.error_handling_system - INFO - 🛡️ Error Handling System initialized
2025-07-14 08:19:11,872 - core.error_handling_system - INFO - 📊 Component registered: exchange_connection
2025-07-14 08:19:11,872 - core.error_handling_system - INFO - 📊 Component registered: trading_interface
2025-07-14 08:19:11,872 - core.error_handling_system - INFO - 📊 Component registered: market_data
2025-07-14 08:19:11,872 - core.error_handling_system - INFO - 📊 Component registered: llm_orchestrator
2025-07-14 08:19:11,875 - core.error_handling_system - INFO - 🔍 Health monitoring started
2025-07-14 08:19:11,877 - core.monitoring_dashboard - INFO - 📊 Monitoring Dashboard initialized
2025-07-14 08:19:11,878 - core.symbol_scanner - INFO - SymbolScanner initialized with 8 symbols
2025-07-14 08:19:11,879 - core.llm_action_executors - WARNING - ⚠️ Intelligent Limit Order Manager not available
2025-07-14 08:19:11,879 - core.llm_action_executors - INFO - 🚀 LLM Action Executors initialized with intelligent limit order system
2025-07-14 08:40:59,353 - main - INFO - Epinnox v6 starting up...
2025-07-14 08:40:59,374 - core.performance_monitor - INFO - Performance monitoring started
2025-07-14 08:40:59,374 - core.performance_monitor - INFO - Performance monitor initialized with 10s interval
2025-07-14 08:40:59,374 - main - INFO - Performance monitoring initialized
2025-07-14 08:40:59,385 - utils.cache_manager - INFO - Cache manager initialized: cache, max_age=24h, max_size=100MB
2025-07-14 08:40:59,386 - utils.cache_manager - INFO - Starting automatic cache cleanup
2025-07-14 08:40:59,386 - utils.cache_manager - INFO - Auto cleanup complete: 0 files removed, 0.00 MB freed
2025-07-14 08:41:07,717 - config.autonomous_config - INFO - Configuration loaded from configs/autonomous_trading.yaml
2025-07-14 08:41:08,654 - data.live_data_manager - INFO - Successfully subscribed to live data for BTC/USDT:USDT on timeframes: ['1m', '5m', '15m']
2025-07-14 08:41:08,663 - llama.runner - INFO - Loaded OpenAI API key from models_config.yaml
2025-07-14 08:41:08,664 - llama.runner - INFO - Using model path: C:\Users\<USER>\.lmstudio\models\lmstudio-community\Phi-3.1-mini-128k-instruct-GGUF\Phi-3.1-mini-128k-instruct-Q4_K_M.gguf
2025-07-14 08:41:08,664 - llama.runner - INFO - Using guard path: C:\Users\<USER>\Documents\dev\Epinnox_v6\models\Llama-Guard-3-8B.gguf
2025-07-14 08:41:08,664 - llama.runner - INFO - Using binary path: C:\Users\<USER>\Documents\dev\llama.cpp\main
2025-07-14 08:41:08,670 - llama.lmstudio_runner - INFO - Loaded LMStudio configuration from models_config.yaml
2025-07-14 08:41:10,027 - websocket - INFO - Websocket connected
2025-07-14 08:41:10,684 - llama.lmstudio_runner - INFO - Discovered 8 models: ['openthinker3-7b', 'mistral-7b-claude-chat', 'fine_tuned_qwen1.7b', 'tsfc-model-0.0.1', 'codestral-22b-v0.1', 'phi-3.1-mini-128k-instruct', 'deepseek-r1-distill-qwen-7b', 'text-embedding-nomic-embed-text-v1.5']
2025-07-14 08:41:10,684 - llama.lmstudio_runner - INFO - Selected preferred model from config: phi-3.1-mini-128k-instruct
2025-07-14 08:41:10,686 - llama.lmstudio_runner - INFO - Initialized LMStudio with model: phi-3.1-mini-128k-instruct
2025-07-14 08:41:10,686 - core.llm_action_executors - WARNING - ⚠️ Missing trading engine or live data manager - using fallback
2025-07-14 08:41:10,686 - core.llm_action_executors - INFO - 🚀 LLM Action Executors initialized with intelligent limit order system
2025-07-14 08:41:10,686 - utils.cache_manager - INFO - Cache manager initialized: cache, max_age=24h, max_size=100MB
2025-07-14 08:41:10,690 - core.llm_orchestrator - INFO - LLM Prompt Orchestrator initialized
2025-07-14 08:41:10,704 - storage.database_manager - INFO - Database tables initialized successfully with performance optimizations
2025-07-14 08:41:10,705 - storage.database_manager - INFO - Database manager initialized: data\epinnox_trading.db
2025-07-14 08:41:10,706 - storage.session_manager - INFO - Session Manager initialized
2025-07-14 08:41:10,716 - storage.database_manager - INFO - Created session: live_BTCUSDTUSDT_20250714_084110_793b882c
2025-07-14 08:41:10,720 - storage.session_manager - INFO - Started session: live_BTCUSDTUSDT_20250714_084110_793b882c
2025-07-14 08:41:10,875 - core.risk_management_system - INFO - 🛡️ Risk Management System initialized
2025-07-14 08:41:10,878 - core.error_handling_system - INFO - 🛡️ Error Handling System initialized
2025-07-14 08:41:10,878 - core.error_handling_system - INFO - 📊 Component registered: exchange_connection
2025-07-14 08:41:10,879 - core.error_handling_system - INFO - 📊 Component registered: trading_interface
2025-07-14 08:41:10,879 - core.error_handling_system - INFO - 📊 Component registered: market_data
2025-07-14 08:41:10,880 - core.error_handling_system - INFO - 📊 Component registered: llm_orchestrator
2025-07-14 08:41:10,882 - core.error_handling_system - INFO - 🔍 Health monitoring started
2025-07-14 08:41:10,885 - core.monitoring_dashboard - INFO - 📊 Monitoring Dashboard initialized
2025-07-14 08:41:10,886 - core.symbol_scanner - INFO - SymbolScanner initialized with 8 symbols
2025-07-14 08:41:10,887 - core.llm_action_executors - WARNING - ⚠️ Intelligent Limit Order Manager not available
2025-07-14 08:41:10,887 - core.llm_action_executors - INFO - 🚀 LLM Action Executors initialized with intelligent limit order system
2025-07-14 16:01:38,920 - main - INFO - Epinnox v6 starting up...
2025-07-14 16:01:38,981 - core.performance_monitor - INFO - Performance monitoring started
2025-07-14 16:01:38,981 - core.performance_monitor - INFO - Performance monitor initialized with 10s interval
2025-07-14 16:01:38,982 - main - INFO - Performance monitoring initialized
2025-07-14 16:01:39,038 - utils.cache_manager - INFO - Cache manager initialized: cache, max_age=24h, max_size=100MB
2025-07-14 16:01:39,038 - utils.cache_manager - INFO - Starting automatic cache cleanup
2025-07-14 16:01:39,038 - utils.cache_manager - INFO - Auto cleanup complete: 0 files removed, 0.00 MB freed
2025-07-14 16:01:42,502 - core.timer_coordinator - INFO - [TIMER_COORDINATOR] Initialized unified timer coordinator
2025-07-14 16:01:46,700 - config.autonomous_config - INFO - Configuration loaded from configs/autonomous_trading.yaml
2025-07-14 16:01:47,616 - data.live_data_manager - INFO - Successfully subscribed to live data for BTC/USDT:USDT on timeframes: ['1m', '5m', '15m']
2025-07-14 16:01:47,665 - llama.runner - INFO - Loaded OpenAI API key from models_config.yaml
2025-07-14 16:01:47,665 - llama.runner - INFO - Using model path: C:\Users\<USER>\.lmstudio\models\lmstudio-community\Phi-3.1-mini-128k-instruct-GGUF\Phi-3.1-mini-128k-instruct-Q4_K_M.gguf
2025-07-14 16:01:47,666 - llama.runner - INFO - Using guard path: C:\Users\<USER>\Documents\dev\Epinnox_v6\models\Llama-Guard-3-8B.gguf
2025-07-14 16:01:47,666 - llama.runner - INFO - Using binary path: C:\Users\<USER>\Documents\dev\llama.cpp\main
2025-07-14 16:01:47,674 - llama.lmstudio_runner - INFO - Loaded LMStudio configuration from models_config.yaml
2025-07-14 16:01:48,248 - websocket - INFO - Websocket connected
2025-07-14 16:01:49,770 - llama.lmstudio_runner - INFO - Discovered 8 models: ['openthinker3-7b', 'mistral-7b-claude-chat', 'fine_tuned_qwen1.7b', 'tsfc-model-0.0.1', 'codestral-22b-v0.1', 'phi-3.1-mini-128k-instruct', 'deepseek-r1-distill-qwen-7b', 'text-embedding-nomic-embed-text-v1.5']
2025-07-14 16:01:49,771 - llama.lmstudio_runner - INFO - Selected preferred model from config: phi-3.1-mini-128k-instruct
2025-07-14 16:01:49,771 - llama.lmstudio_runner - INFO - Initialized LMStudio with model: phi-3.1-mini-128k-instruct
2025-07-14 16:01:49,787 - core.llm_action_executors - WARNING - ⚠️ Missing trading engine or live data manager - using fallback
2025-07-14 16:01:49,787 - core.llm_action_executors - INFO - 🚀 LLM Action Executors initialized with intelligent limit order system
2025-07-14 16:01:49,787 - utils.cache_manager - INFO - Cache manager initialized: cache, max_age=24h, max_size=100MB
2025-07-14 16:01:49,789 - core.llm_orchestrator - INFO - LLM Prompt Orchestrator initialized
2025-07-14 16:01:49,860 - storage.database_manager - INFO - Database tables initialized successfully with performance optimizations
2025-07-14 16:01:49,861 - storage.database_manager - INFO - Database manager initialized: data\epinnox_trading.db
2025-07-14 16:01:49,863 - storage.session_manager - INFO - Session Manager initialized
2025-07-14 16:01:49,867 - storage.database_manager - INFO - Created session: live_BTCUSDTUSDT_20250714_160149_2ecbae69
2025-07-14 16:01:49,869 - storage.session_manager - INFO - Started session: live_BTCUSDTUSDT_20250714_160149_2ecbae69
2025-07-14 16:01:50,031 - core.risk_management_system - INFO - 🛡️ Risk Management System initialized
2025-07-14 16:01:50,043 - core.error_handling_system - INFO - 🛡️ Error Handling System initialized
2025-07-14 16:01:50,044 - core.error_handling_system - INFO - 📊 Component registered: exchange_connection
2025-07-14 16:01:50,045 - core.error_handling_system - INFO - 📊 Component registered: trading_interface
2025-07-14 16:01:50,046 - core.error_handling_system - INFO - 📊 Component registered: market_data
2025-07-14 16:01:50,047 - core.error_handling_system - INFO - 📊 Component registered: llm_orchestrator
2025-07-14 16:01:50,051 - core.error_handling_system - INFO - 🔍 Health monitoring started
2025-07-14 16:01:50,060 - core.monitoring_dashboard - INFO - 📊 Monitoring Dashboard initialized
2025-07-14 16:01:50,062 - core.symbol_scanner - INFO - SymbolScanner initialized with 8 symbols
2025-07-14 16:01:50,062 - core.timer_coordinator - INFO - [TIMER_COORDINATOR] Registered timer 'symbol_scanner_update': interval=30.0s, priority=MEDIUM
2025-07-14 16:01:50,063 - core.symbol_scanner - INFO - [SYMBOL_SCANNER] Registered with timer coordinator (interval: 30.0s)
2025-07-14 16:01:50,064 - core.llm_action_executors - WARNING - ⚠️ Intelligent Limit Order Manager not available
2025-07-14 16:01:50,065 - core.llm_action_executors - INFO - 🚀 LLM Action Executors initialized with intelligent limit order system
2025-07-14 16:02:01,514 - data.live_data_manager - INFO - Successfully subscribed to live data for DOGE/USDT:USDT on timeframes: ['1m', '5m', '15m']
2025-07-14 16:40:31,896 - main - INFO - Epinnox v6 starting up...
2025-07-14 16:40:31,917 - core.performance_monitor - INFO - Performance monitoring started
2025-07-14 16:40:31,917 - core.performance_monitor - INFO - Performance monitor initialized with 10s interval
2025-07-14 16:40:31,918 - main - INFO - Performance monitoring initialized
2025-07-14 16:40:31,929 - utils.cache_manager - INFO - Cache manager initialized: cache, max_age=24h, max_size=100MB
2025-07-14 16:40:31,929 - utils.cache_manager - INFO - Starting automatic cache cleanup
2025-07-14 16:40:31,930 - utils.cache_manager - INFO - Auto cleanup complete: 0 files removed, 0.00 MB freed
2025-07-14 16:40:36,228 - core.timer_coordinator - INFO - [TIMER_COORDINATOR] Initialized unified timer coordinator
2025-07-14 16:40:40,406 - config.autonomous_config - INFO - Configuration loaded from configs/autonomous_trading.yaml
2025-07-14 16:40:41,292 - data.live_data_manager - INFO - Successfully subscribed to live data for BTC/USDT:USDT on timeframes: ['1m', '5m', '15m']
2025-07-14 16:40:41,301 - llama.runner - INFO - Loaded OpenAI API key from models_config.yaml
2025-07-14 16:40:41,302 - llama.runner - INFO - Using model path: C:\Users\<USER>\.lmstudio\models\lmstudio-community\Phi-3.1-mini-128k-instruct-GGUF\Phi-3.1-mini-128k-instruct-Q4_K_M.gguf
2025-07-14 16:40:41,302 - llama.runner - INFO - Using guard path: C:\Users\<USER>\Documents\dev\Epinnox_v6\models\Llama-Guard-3-8B.gguf
2025-07-14 16:40:41,303 - llama.runner - INFO - Using binary path: C:\Users\<USER>\Documents\dev\llama.cpp\main
2025-07-14 16:40:41,308 - llama.lmstudio_runner - INFO - Loaded LMStudio configuration from models_config.yaml
2025-07-14 16:40:42,574 - websocket - INFO - Websocket connected
2025-07-14 16:40:43,359 - llama.lmstudio_runner - INFO - Discovered 8 models: ['openthinker3-7b', 'mistral-7b-claude-chat', 'fine_tuned_qwen1.7b', 'tsfc-model-0.0.1', 'codestral-22b-v0.1', 'phi-3.1-mini-128k-instruct', 'deepseek-r1-distill-qwen-7b', 'text-embedding-nomic-embed-text-v1.5']
2025-07-14 16:40:43,360 - llama.lmstudio_runner - INFO - Selected preferred model from config: phi-3.1-mini-128k-instruct
2025-07-14 16:40:43,361 - llama.lmstudio_runner - INFO - Initialized LMStudio with model: phi-3.1-mini-128k-instruct
2025-07-14 16:40:43,363 - core.llm_action_executors - WARNING - ⚠️ Missing trading engine or live data manager - using fallback
2025-07-14 16:40:43,363 - core.llm_action_executors - INFO - 🚀 LLM Action Executors initialized with intelligent limit order system
2025-07-14 16:40:43,364 - utils.cache_manager - INFO - Cache manager initialized: cache, max_age=24h, max_size=100MB
2025-07-14 16:40:43,364 - core.llm_orchestrator - INFO - LLM Prompt Orchestrator initialized
2025-07-14 16:40:43,407 - storage.database_manager - INFO - Database tables initialized successfully with performance optimizations
2025-07-14 16:40:43,408 - storage.database_manager - INFO - Database manager initialized: data\epinnox_trading.db
2025-07-14 16:40:43,409 - storage.session_manager - INFO - Session Manager initialized
2025-07-14 16:40:43,414 - storage.database_manager - INFO - Created session: live_BTCUSDTUSDT_20250714_164043_8a9eaedb
2025-07-14 16:40:43,416 - storage.session_manager - INFO - Started session: live_BTCUSDTUSDT_20250714_164043_8a9eaedb
2025-07-14 16:40:43,582 - core.risk_management_system - INFO - 🛡️ Risk Management System initialized
2025-07-14 16:40:43,584 - core.error_handling_system - INFO - 🛡️ Error Handling System initialized
2025-07-14 16:40:43,585 - core.error_handling_system - INFO - 📊 Component registered: exchange_connection
2025-07-14 16:40:43,585 - core.error_handling_system - INFO - 📊 Component registered: trading_interface
2025-07-14 16:40:43,586 - core.error_handling_system - INFO - 📊 Component registered: market_data
2025-07-14 16:40:43,586 - core.error_handling_system - INFO - 📊 Component registered: llm_orchestrator
2025-07-14 16:40:43,588 - core.error_handling_system - INFO - 🔍 Health monitoring started
2025-07-14 16:40:43,590 - core.monitoring_dashboard - INFO - 📊 Monitoring Dashboard initialized
2025-07-14 16:40:43,591 - core.symbol_scanner - INFO - SymbolScanner initialized with 8 symbols
2025-07-14 16:40:43,592 - core.timer_coordinator - INFO - [TIMER_COORDINATOR] Registered timer 'symbol_scanner_update': interval=30.0s, priority=MEDIUM
2025-07-14 16:40:43,592 - core.symbol_scanner - INFO - [SYMBOL_SCANNER] Registered with timer coordinator (interval: 30.0s)
2025-07-14 16:40:43,593 - core.llm_action_executors - WARNING - ⚠️ Intelligent Limit Order Manager not available
2025-07-14 16:40:43,594 - core.llm_action_executors - INFO - 🚀 LLM Action Executors initialized with intelligent limit order system
2025-07-14 16:42:56,564 - websocket - ERROR - error from callback <bound method WebSocketClient._on_message of <data.websocket_client.WebSocketClient object at 0x0000023EA986C160>>: wrapped C/C++ object of type WebSocketClient has been deleted
2025-07-14 16:42:56,568 - websocket - ERROR - error from callback <bound method WebSocketClient._on_error of <data.websocket_client.WebSocketClient object at 0x0000023EA986C160>>: wrapped C/C++ object of type WebSocketClient has been deleted
2025-07-14 16:42:56,570 - websocket - INFO - tearing down on exception wrapped C/C++ object of type WebSocketClient has been deleted
2025-07-14 16:43:20,607 - main - INFO - Epinnox v6 starting up...
2025-07-14 16:43:20,630 - core.performance_monitor - INFO - Performance monitoring started
2025-07-14 16:43:20,630 - core.performance_monitor - INFO - Performance monitor initialized with 10s interval
2025-07-14 16:43:20,632 - main - INFO - Performance monitoring initialized
2025-07-14 16:43:20,645 - utils.cache_manager - INFO - Cache manager initialized: cache, max_age=24h, max_size=100MB
2025-07-14 16:43:20,646 - utils.cache_manager - INFO - Starting automatic cache cleanup
2025-07-14 16:43:20,647 - utils.cache_manager - INFO - Auto cleanup complete: 0 files removed, 0.00 MB freed
2025-07-14 16:43:24,111 - core.timer_coordinator - INFO - [TIMER_COORDINATOR] Initialized unified timer coordinator
2025-07-14 16:43:28,247 - config.autonomous_config - INFO - Configuration loaded from configs/autonomous_trading.yaml
2025-07-14 16:43:29,100 - data.live_data_manager - INFO - Successfully subscribed to live data for BTC/USDT:USDT on timeframes: ['1m', '5m', '15m']
2025-07-14 16:43:30,403 - websocket - INFO - Websocket connected
2025-07-14 16:43:32,624 - trading.position_tracker - INFO - Position Tracker initialized
2025-07-14 16:45:09,087 - main - INFO - Epinnox v6 starting up...
2025-07-14 16:45:09,103 - core.performance_monitor - INFO - Performance monitoring started
2025-07-14 16:45:09,103 - core.performance_monitor - INFO - Performance monitor initialized with 10s interval
2025-07-14 16:45:09,104 - main - INFO - Performance monitoring initialized
2025-07-14 16:45:09,112 - utils.cache_manager - INFO - Cache manager initialized: cache, max_age=24h, max_size=100MB
2025-07-14 16:45:09,114 - utils.cache_manager - INFO - Starting automatic cache cleanup
2025-07-14 16:45:09,115 - utils.cache_manager - INFO - Auto cleanup complete: 0 files removed, 0.00 MB freed
2025-07-14 16:45:12,394 - core.timer_coordinator - INFO - [TIMER_COORDINATOR] Initialized unified timer coordinator
2025-07-14 16:45:16,396 - config.autonomous_config - INFO - Configuration loaded from configs/autonomous_trading.yaml
2025-07-14 16:45:17,270 - data.live_data_manager - INFO - Successfully subscribed to live data for BTC/USDT:USDT on timeframes: ['1m', '5m', '15m']
2025-07-14 16:45:17,935 - websocket - INFO - Websocket connected
2025-07-14 16:45:20,900 - trading.position_tracker - INFO - Position Tracker initialized
2025-07-14 16:46:40,373 - main - INFO - Epinnox v6 starting up...
2025-07-14 16:46:40,392 - core.performance_monitor - INFO - Performance monitoring started
2025-07-14 16:46:40,392 - core.performance_monitor - INFO - Performance monitor initialized with 10s interval
2025-07-14 16:46:40,392 - main - INFO - Performance monitoring initialized
2025-07-14 16:46:40,404 - utils.cache_manager - INFO - Cache manager initialized: cache, max_age=24h, max_size=100MB
2025-07-14 16:46:40,405 - utils.cache_manager - INFO - Starting automatic cache cleanup
2025-07-14 16:46:40,406 - utils.cache_manager - INFO - Auto cleanup complete: 0 files removed, 0.00 MB freed
2025-07-14 16:46:44,213 - core.timer_coordinator - INFO - [TIMER_COORDINATOR] Initialized unified timer coordinator
2025-07-14 16:46:48,260 - config.autonomous_config - INFO - Configuration loaded from configs/autonomous_trading.yaml
2025-07-14 16:46:49,128 - data.live_data_manager - INFO - Successfully subscribed to live data for BTC/USDT:USDT on timeframes: ['1m', '5m', '15m']
2025-07-14 16:46:50,400 - websocket - INFO - Websocket connected
2025-07-14 16:46:52,655 - trading.position_tracker - INFO - Position Tracker initialized
2025-07-14 16:46:53,071 - llama.runner - INFO - Loaded OpenAI API key from models_config.yaml
2025-07-14 16:46:53,071 - llama.runner - INFO - Using model path: C:\Users\<USER>\.lmstudio\models\lmstudio-community\Phi-3.1-mini-128k-instruct-GGUF\Phi-3.1-mini-128k-instruct-Q4_K_M.gguf
2025-07-14 16:46:53,072 - llama.runner - INFO - Using guard path: C:\Users\<USER>\Documents\dev\Epinnox_v6\models\Llama-Guard-3-8B.gguf
2025-07-14 16:46:53,073 - llama.runner - INFO - Using binary path: C:\Users\<USER>\Documents\dev\llama.cpp\main
2025-07-14 16:46:53,078 - llama.lmstudio_runner - INFO - Loaded LMStudio configuration from models_config.yaml
2025-07-14 16:46:55,113 - llama.lmstudio_runner - INFO - Discovered 8 models: ['openthinker3-7b', 'mistral-7b-claude-chat', 'fine_tuned_qwen1.7b', 'tsfc-model-0.0.1', 'codestral-22b-v0.1', 'phi-3.1-mini-128k-instruct', 'deepseek-r1-distill-qwen-7b', 'text-embedding-nomic-embed-text-v1.5']
2025-07-14 16:46:55,114 - llama.lmstudio_runner - INFO - Selected preferred model from config: phi-3.1-mini-128k-instruct
2025-07-14 16:46:55,115 - llama.lmstudio_runner - INFO - Initialized LMStudio with model: phi-3.1-mini-128k-instruct
2025-07-14 16:46:55,117 - trading.intelligent_limit_order_manager - INFO - Intelligent Limit Order Manager initialized for professional scalping
2025-07-14 16:46:55,117 - core.llm_action_executors - INFO - ✅ Intelligent Limit Order Manager initialized
2025-07-14 16:46:55,118 - core.llm_action_executors - INFO - 🚀 LLM Action Executors initialized with intelligent limit order system
2025-07-14 16:46:55,119 - utils.cache_manager - INFO - Cache manager initialized: cache, max_age=24h, max_size=100MB
2025-07-14 16:46:55,119 - core.llm_orchestrator - INFO - LLM Prompt Orchestrator initialized
2025-07-14 16:46:55,121 - core.signal_hierarchy - INFO - Signal Hierarchy initialized
2025-07-14 16:46:55,131 - storage.database_manager - INFO - Database tables initialized successfully with performance optimizations
2025-07-14 16:46:55,133 - storage.database_manager - INFO - Database manager initialized: data\epinnox_trading.db
2025-07-14 16:46:55,133 - storage.session_manager - INFO - Session Manager initialized
2025-07-14 16:46:55,141 - storage.database_manager - INFO - Created session: live_BTCUSDTUSDT_20250714_164655_2c35584b
2025-07-14 16:46:55,144 - storage.session_manager - INFO - Started session: live_BTCUSDTUSDT_20250714_164655_2c35584b
2025-07-14 16:46:55,374 - core.risk_management_system - INFO - 🛡️ Risk Management System initialized
2025-07-14 16:46:55,377 - core.error_handling_system - INFO - 🛡️ Error Handling System initialized
2025-07-14 16:46:55,378 - core.error_handling_system - INFO - 📊 Component registered: exchange_connection
2025-07-14 16:46:55,378 - core.error_handling_system - INFO - 📊 Component registered: trading_interface
2025-07-14 16:46:55,379 - core.error_handling_system - INFO - 📊 Component registered: market_data
2025-07-14 16:46:55,380 - core.error_handling_system - INFO - 📊 Component registered: llm_orchestrator
2025-07-14 16:46:55,382 - core.error_handling_system - INFO - 🔍 Health monitoring started
2025-07-14 16:46:55,385 - core.monitoring_dashboard - INFO - 📊 Monitoring Dashboard initialized
2025-07-14 16:46:55,386 - core.symbol_scanner - INFO - SymbolScanner initialized with 8 symbols
2025-07-14 16:46:55,386 - core.timer_coordinator - INFO - [TIMER_COORDINATOR] Registered timer 'symbol_scanner_update': interval=30.0s, priority=MEDIUM
2025-07-14 16:46:55,387 - core.symbol_scanner - INFO - [SYMBOL_SCANNER] Registered with timer coordinator (interval: 30.0s)
2025-07-14 16:46:55,388 - core.llm_action_executors - WARNING - ⚠️ Intelligent Limit Order Manager not available
2025-07-14 16:46:55,388 - core.llm_action_executors - INFO - 🚀 LLM Action Executors initialized with intelligent limit order system
2025-07-14 16:47:03,431 - data.live_data_manager - INFO - Successfully subscribed to live data for DOGE/USDT:USDT on timeframes: ['1m', '5m', '15m']
2025-07-14 16:48:40,190 - main - INFO - Epinnox v6 starting up...
2025-07-14 16:48:40,207 - core.performance_monitor - INFO - Performance monitoring started
2025-07-14 16:48:40,208 - core.performance_monitor - INFO - Performance monitor initialized with 10s interval
2025-07-14 16:48:40,209 - main - INFO - Performance monitoring initialized
2025-07-14 16:48:40,217 - utils.cache_manager - INFO - Cache manager initialized: cache, max_age=24h, max_size=100MB
2025-07-14 16:48:40,218 - utils.cache_manager - INFO - Starting automatic cache cleanup
2025-07-14 16:48:40,220 - utils.cache_manager - INFO - Auto cleanup complete: 0 files removed, 0.00 MB freed
2025-07-14 16:48:43,625 - core.timer_coordinator - INFO - [TIMER_COORDINATOR] Initialized unified timer coordinator
2025-07-14 16:48:47,376 - config.autonomous_config - INFO - Configuration loaded from configs/autonomous_trading.yaml
2025-07-14 16:48:48,282 - data.live_data_manager - INFO - Successfully subscribed to live data for BTC/USDT:USDT on timeframes: ['1m', '5m', '15m']
2025-07-14 16:48:48,932 - websocket - INFO - Websocket connected
2025-07-14 16:48:51,830 - trading.position_tracker - INFO - Position Tracker initialized
2025-07-14 16:48:52,225 - llama.runner - INFO - Loaded OpenAI API key from models_config.yaml
2025-07-14 16:48:52,226 - llama.runner - INFO - Using model path: C:\Users\<USER>\.lmstudio\models\lmstudio-community\Phi-3.1-mini-128k-instruct-GGUF\Phi-3.1-mini-128k-instruct-Q4_K_M.gguf
2025-07-14 16:48:52,226 - llama.runner - INFO - Using guard path: C:\Users\<USER>\Documents\dev\Epinnox_v6\models\Llama-Guard-3-8B.gguf
2025-07-14 16:48:52,226 - llama.runner - INFO - Using binary path: C:\Users\<USER>\Documents\dev\llama.cpp\main
2025-07-14 16:48:52,232 - llama.lmstudio_runner - INFO - Loaded LMStudio configuration from models_config.yaml
2025-07-14 16:48:54,286 - llama.lmstudio_runner - INFO - Discovered 8 models: ['openthinker3-7b', 'mistral-7b-claude-chat', 'fine_tuned_qwen1.7b', 'tsfc-model-0.0.1', 'codestral-22b-v0.1', 'phi-3.1-mini-128k-instruct', 'deepseek-r1-distill-qwen-7b', 'text-embedding-nomic-embed-text-v1.5']
2025-07-14 16:48:54,286 - llama.lmstudio_runner - INFO - Selected preferred model from config: phi-3.1-mini-128k-instruct
2025-07-14 16:48:54,288 - llama.lmstudio_runner - INFO - Initialized LMStudio with model: phi-3.1-mini-128k-instruct
2025-07-14 16:48:54,290 - trading.intelligent_limit_order_manager - INFO - Intelligent Limit Order Manager initialized for professional scalping
2025-07-14 16:48:54,291 - core.llm_action_executors - INFO - ✅ Intelligent Limit Order Manager initialized
2025-07-14 16:48:54,291 - core.llm_action_executors - INFO - 🚀 LLM Action Executors initialized with intelligent limit order system
2025-07-14 16:48:54,292 - utils.cache_manager - INFO - Cache manager initialized: cache, max_age=24h, max_size=100MB
2025-07-14 16:48:54,292 - core.llm_orchestrator - INFO - LLM Prompt Orchestrator initialized
2025-07-14 16:48:54,295 - core.signal_hierarchy - INFO - Signal Hierarchy initialized
2025-07-14 16:48:54,306 - storage.database_manager - INFO - Database tables initialized successfully with performance optimizations
2025-07-14 16:48:54,307 - storage.database_manager - INFO - Database manager initialized: data\epinnox_trading.db
2025-07-14 16:48:54,308 - storage.session_manager - INFO - Session Manager initialized
2025-07-14 16:48:54,317 - storage.database_manager - INFO - Created session: live_BTCUSDTUSDT_20250714_164854_071cf046
2025-07-14 16:48:54,320 - storage.session_manager - INFO - Started session: live_BTCUSDTUSDT_20250714_164854_071cf046
2025-07-14 16:48:54,514 - core.risk_management_system - INFO - 🛡️ Risk Management System initialized
2025-07-14 16:48:54,518 - core.error_handling_system - INFO - 🛡️ Error Handling System initialized
2025-07-14 16:48:54,518 - core.error_handling_system - INFO - 📊 Component registered: exchange_connection
2025-07-14 16:48:54,519 - core.error_handling_system - INFO - 📊 Component registered: trading_interface
2025-07-14 16:48:54,520 - core.error_handling_system - INFO - 📊 Component registered: market_data
2025-07-14 16:48:54,520 - core.error_handling_system - INFO - 📊 Component registered: llm_orchestrator
2025-07-14 16:48:54,522 - core.error_handling_system - INFO - 🔍 Health monitoring started
2025-07-14 16:48:54,526 - core.monitoring_dashboard - INFO - 📊 Monitoring Dashboard initialized
2025-07-14 16:48:54,526 - core.symbol_scanner - INFO - SymbolScanner initialized with 8 symbols
2025-07-14 16:48:54,528 - core.timer_coordinator - INFO - [TIMER_COORDINATOR] Registered timer 'symbol_scanner_update': interval=30.0s, priority=MEDIUM
2025-07-14 16:48:54,529 - core.symbol_scanner - INFO - [SYMBOL_SCANNER] Registered with timer coordinator (interval: 30.0s)
2025-07-14 16:48:54,530 - core.llm_action_executors - WARNING - ⚠️ Intelligent Limit Order Manager not available
2025-07-14 16:48:54,530 - core.llm_action_executors - INFO - 🚀 LLM Action Executors initialized with intelligent limit order system
2025-07-14 16:49:50,677 - main - INFO - Epinnox v6 starting up...
2025-07-14 16:49:50,713 - core.performance_monitor - INFO - Performance monitoring started
2025-07-14 16:49:50,713 - core.performance_monitor - INFO - Performance monitor initialized with 10s interval
2025-07-14 16:49:50,713 - main - INFO - Performance monitoring initialized
2025-07-14 16:49:50,737 - utils.cache_manager - INFO - Cache manager initialized: cache, max_age=24h, max_size=100MB
2025-07-14 16:49:50,738 - utils.cache_manager - INFO - Starting automatic cache cleanup
2025-07-14 16:49:50,739 - utils.cache_manager - INFO - Auto cleanup complete: 0 files removed, 0.00 MB freed
2025-07-14 16:49:56,121 - core.timer_coordinator - INFO - [TIMER_COORDINATOR] Initialized unified timer coordinator
2025-07-14 16:50:00,731 - config.autonomous_config - INFO - Configuration loaded from configs/autonomous_trading.yaml
2025-07-14 16:50:01,723 - data.live_data_manager - INFO - Successfully subscribed to live data for BTC/USDT:USDT on timeframes: ['1m', '5m', '15m']
2025-07-14 16:50:02,998 - websocket - INFO - Websocket connected
2025-07-14 16:50:05,113 - trading.position_tracker - INFO - Position Tracker initialized
2025-07-14 16:50:05,540 - llama.runner - INFO - Loaded OpenAI API key from models_config.yaml
2025-07-14 16:50:05,541 - llama.runner - INFO - Using model path: C:\Users\<USER>\.lmstudio\models\lmstudio-community\Phi-3.1-mini-128k-instruct-GGUF\Phi-3.1-mini-128k-instruct-Q4_K_M.gguf
2025-07-14 16:50:05,541 - llama.runner - INFO - Using guard path: C:\Users\<USER>\Documents\dev\Epinnox_v6\models\Llama-Guard-3-8B.gguf
2025-07-14 16:50:05,541 - llama.runner - INFO - Using binary path: C:\Users\<USER>\Documents\dev\llama.cpp\main
2025-07-14 16:50:05,549 - llama.lmstudio_runner - INFO - Loaded LMStudio configuration from models_config.yaml
2025-07-14 16:50:07,605 - llama.lmstudio_runner - INFO - Discovered 8 models: ['openthinker3-7b', 'mistral-7b-claude-chat', 'fine_tuned_qwen1.7b', 'tsfc-model-0.0.1', 'codestral-22b-v0.1', 'phi-3.1-mini-128k-instruct', 'deepseek-r1-distill-qwen-7b', 'text-embedding-nomic-embed-text-v1.5']
2025-07-14 16:50:07,605 - llama.lmstudio_runner - INFO - Selected preferred model from config: phi-3.1-mini-128k-instruct
2025-07-14 16:50:07,606 - llama.lmstudio_runner - INFO - Initialized LMStudio with model: phi-3.1-mini-128k-instruct
2025-07-14 16:50:07,608 - trading.intelligent_limit_order_manager - INFO - Intelligent Limit Order Manager initialized for professional scalping
2025-07-14 16:50:07,608 - core.llm_action_executors - INFO - ✅ Intelligent Limit Order Manager initialized
2025-07-14 16:50:07,609 - core.llm_action_executors - INFO - 🚀 LLM Action Executors initialized with intelligent limit order system
2025-07-14 16:50:07,609 - utils.cache_manager - INFO - Cache manager initialized: cache, max_age=24h, max_size=100MB
2025-07-14 16:50:07,609 - core.llm_orchestrator - INFO - LLM Prompt Orchestrator initialized
2025-07-14 16:50:07,617 - core.signal_hierarchy - INFO - Signal Hierarchy initialized
2025-07-14 16:50:07,660 - storage.database_manager - INFO - Database tables initialized successfully with performance optimizations
2025-07-14 16:50:07,660 - storage.database_manager - INFO - Database manager initialized: data\epinnox_trading.db
2025-07-14 16:50:07,660 - storage.session_manager - INFO - Session Manager initialized
2025-07-14 16:50:07,667 - storage.database_manager - INFO - Created session: live_BTCUSDTUSDT_20250714_165007_7d284c1e
2025-07-14 16:50:07,668 - storage.session_manager - INFO - Started session: live_BTCUSDTUSDT_20250714_165007_7d284c1e
2025-07-14 16:50:07,883 - core.risk_management_system - INFO - 🛡️ Risk Management System initialized
2025-07-14 16:50:07,891 - core.error_handling_system - INFO - 🛡️ Error Handling System initialized
2025-07-14 16:50:07,891 - core.error_handling_system - INFO - 📊 Component registered: exchange_connection
2025-07-14 16:50:07,892 - core.error_handling_system - INFO - 📊 Component registered: trading_interface
2025-07-14 16:50:07,892 - core.error_handling_system - INFO - 📊 Component registered: market_data
2025-07-14 16:50:07,892 - core.error_handling_system - INFO - 📊 Component registered: llm_orchestrator
2025-07-14 16:50:07,895 - core.error_handling_system - INFO - 🔍 Health monitoring started
2025-07-14 16:50:07,904 - core.monitoring_dashboard - INFO - 📊 Monitoring Dashboard initialized
2025-07-14 16:50:07,905 - core.symbol_scanner - INFO - SymbolScanner initialized with 8 symbols
2025-07-14 16:50:07,906 - core.timer_coordinator - INFO - [TIMER_COORDINATOR] Registered timer 'symbol_scanner_update': interval=30.0s, priority=MEDIUM
2025-07-14 16:50:07,906 - core.symbol_scanner - INFO - [SYMBOL_SCANNER] Registered with timer coordinator (interval: 30.0s)
2025-07-14 16:50:07,907 - core.llm_action_executors - WARNING - ⚠️ Intelligent Limit Order Manager not available
2025-07-14 16:50:07,907 - core.llm_action_executors - INFO - 🚀 LLM Action Executors initialized with intelligent limit order system
2025-07-14 16:50:57,534 - data.live_data_manager - INFO - Successfully subscribed to live data for DOGE/USDT:USDT on timeframes: ['1m', '5m', '15m']
2025-07-14 16:51:09,579 - core.symbol_scanner - INFO - Scanning 8 symbols for best trading opportunities...
2025-07-14 16:51:18,871 - core.symbol_scanner - INFO - Symbol ranking (top 1):
2025-07-14 16:51:18,872 - core.symbol_scanner - INFO -   1. BTC/USDT:USDT: 47.65 (spread: 0.000%, atr: 1.531579, depth: 12464)
2025-07-14 16:51:18,873 - data.live_data_manager - INFO - Successfully subscribed to live data for BTC/USDT:USDT on timeframes: ['1m', '5m', '15m']
2025-07-14 16:51:18,874 - data.live_data_manager - INFO - Successfully subscribed to live data for BTC/USDT:USDT on timeframes: ['1m', '5m', '15m']
2025-07-14 16:51:32,232 - core.symbol_scanner - INFO - Scanning 8 symbols for best trading opportunities...
2025-07-14 16:51:41,440 - core.symbol_scanner - INFO - Symbol ranking (top 1):
2025-07-14 16:51:41,440 - core.symbol_scanner - INFO -   1. SHIB/USDT:USDT: 57.60 (spread: 0.053%, atr: 0.000000, depth: 5318039)
2025-07-14 16:51:41,442 - data.live_data_manager - INFO - Successfully subscribed to live data for SHIB/USDT:USDT on timeframes: ['1m', '5m', '15m']
2025-07-14 16:51:41,442 - data.live_data_manager - INFO - Successfully subscribed to live data for SHIB/USDT:USDT on timeframes: ['1m', '5m', '15m']
2025-07-14 16:51:56,327 - core.symbol_scanner - INFO - Scanning 8 symbols for best trading opportunities...
2025-07-14 16:52:05,532 - core.symbol_scanner - INFO - Symbol ranking (top 1):
2025-07-14 16:52:05,533 - core.symbol_scanner - INFO -   1. SHIB/USDT:USDT: 56.93 (spread: 0.053%, atr: 0.000000, depth: 5122893)
2025-07-14 16:52:10,268 - data.live_data_manager - INFO - Successfully subscribed to live data for DOGE/USDT:USDT on timeframes: ['1m', '5m', '15m']
2025-07-14 16:52:25,997 - core.llm_orchestrator - INFO - 🏃 SCALPING MODE: Executing 3 essential prompts for speed
2025-07-14 16:52:25,997 - core.llm_orchestrator - INFO - 📊 LLM Thread Usage Start: 0/16 threads
2025-07-14 16:52:25,997 - core.llm_orchestrator - INFO - 🧠 Executing prompt 1/3: risk_assessment
2025-07-14 16:52:25,998 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-14 16:52:25,998 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-14 16:52:25,999 - llama.lmstudio_runner - INFO - 💬 User Prompt: ...
2025-07-14 16:52:25,999 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-07-14 16:52:40,443 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 243 chars
2025-07-14 16:52:40,443 - llama.lmstudio_runner - INFO - 📄 Response Preview: ```json
{
  "action": "LONG",
  "confidence": 90,
  "entry_reason": "Breakout above resistance level with volume spike indicating strong buying interest.",
  "take_profit": 2.5,
  "stop_loss": 1.0,
  "hold_time": "3min",
  "leverage": 40
}
```...
2025-07-14 16:52:40,444 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 174, Completion: 96, Total: 270
2025-07-14 16:52:40,446 - core.llm_response_parsers - INFO - Risk assessment parsed: REJECTED (Risk: 50)
2025-07-14 16:52:40,497 - core.llm_orchestrator - INFO - 🧠 Executing prompt 2/3: entry_timing
2025-07-14 16:52:40,497 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-14 16:52:40,497 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-14 16:52:40,497 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🎯 ENTRY TIMING SPECIALIST - DOGE/USDT:USDT

🎯 SIGNAL ANALYSIS:
ML Ensemble: NEUTRAL (60.0%)
Technical: NEUTRAL | Momentum: 0.00
Volume: PENDING | Orderflow: NEUTRAL

📊 MARKET MICROSTRUCTURE:
Bid/Ask: $0.198132/$0.198171
Spread: 0.100% | L2 Imbalance: 0.0%
Recent Flow: BALANCED | Volume Spike: False

📈 KEY LEVELS:
Support: $0.197141
Resistance: $0.199123
Distance to Support: 0.50%

⏰ TIMING FACTORS:
- Price near key levels: NEUTRAL
- Volume confirmation: PENDING
- Spread conditions: FAVORABLE
- M...
2025-07-14 16:52:40,498 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-07-14 16:52:44,180 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 167 chars
2025-07-14 16:52:44,180 - llama.lmstudio_runner - INFO - 📄 Response Preview: {
  "action": "WAIT",
  "entry_type": "MARKET/LIMIT",
  "wait_for": "VOLUME_CONFIRMATION",
  "max_wait_seconds": 60,
  "reasoning": "Waiting for volume confirmation"
}...
2025-07-14 16:52:44,180 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 603, Completion: 74, Total: 677
2025-07-14 16:52:44,180 - core.llm_response_parsers - INFO - Entry timing parsed: WAIT (LIMIT order)
2025-07-14 16:52:44,231 - core.llm_orchestrator - INFO - 🧠 Executing prompt 3/3: opportunity_scanner
2025-07-14 16:52:44,232 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-14 16:52:44,232 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a conservative futures trading analyst. Analyze market data carefully and provide measured recommendations. Use format: DECISION: [LONG/SHORT/WAIT], CONFIDENCE: [50-100]%, TAKE_PROFIT: [%], ST...
2025-07-14 16:52:44,232 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🔍 INTELLIGENT OPPORTUNITY SCANNER - Account Health Aware

🚨 ACCOUNT HEALTH STATUS: HEALTHY
💰 Balance: $117.21 | Health: Healthy - Normal Trading
📊 Current Exposure: $0.00 (0.0% of account)
🎯 Trading Allowed: YES

📊 SYMBOL ANALYSIS:
• DOGE/USDT:USDT: $0.198132 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• BTC/USDT:USDT: $95000.000000 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• ETH/USDT:USDT: $3500.000000 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• SOL/USDT:USDT: $200.000000 | Mom: +0.0% | Vol: 1.0x | ...
2025-07-14 16:52:44,232 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-07-14 16:52:49,795 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 702 chars
2025-07-14 16:52:49,796 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: LONG, CONFIDENCE: 80%, TAKE_PROFIT: 2.5%, STOP_LOSS: -3.5%, EXPLANATION: The account is in a healthy state with normal trading parameters and the current setup for DOGE/USDT shows medium momentum, which aligns well within our conservative criteria of low to moderate volatility (1-3%) prefe...
2025-07-14 16:52:49,797 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 923, Completion: 183, Total: 1106
2025-07-14 16:52:49,798 - core.llm_response_parsers - INFO - Parsed structured text: {'DECISION': 'LONG', 'CONFIDENCE': 80.0, 'EXPLANATION': "THE ACCOUNT IS IN A HEALTHY STATE WITH NORMAL TRADING PARAMETERS AND THE CURRENT SETUP FOR DOGE/USDT SHOWS MEDIUM MOMENTUM, WHICH ALIGNS WELL WITHIN OUR CONSERVATIVE CRITERIA OF LOW TO MODERATE VOLATILITY (1-3%) PREFERRED BY THIS STRATEGY PROFILE. GIVEN THAT THERE'S NO SIGNIFICANT RECENT PRICE MOVEMENT OR SIGNALS INDICATING AN IMMEDIATE TREND REVERSAL, A LONG POSITION IS RECOMMENDED WITH THE CONFIDENCE LEVEL SET AT 80%. THE TAKE PROFIT AND STOP LOSS ARE CALCULATED BASED ON OUR RISK BUDGET OF 2% PER TRADE (HEALTH-ADJUSTED) TO ENSURE WE DO NOT EXCEED THIS LIMIT WHILE AIMING FOR HIGHER REWARD DUE TO ACCOUNT HEALTH.", 'ACTION': 'ENTER_NOW'}
2025-07-14 16:52:49,798 - core.llm_response_parsers - INFO - Opportunity scanner parsed: NONE (NONE)
2025-07-14 16:52:49,849 - core.llm_orchestrator - INFO - 📊 LLM Thread Usage End: 0/16 threads
2025-07-14 16:52:49,849 - core.llm_orchestrator - INFO - 🧠 LLM prompt cycle completed in 23.85s - 3 prompts executed sequentially
2025-07-14 16:57:28,796 - websocket - ERROR - error from callback <bound method WebSocketClient._on_message of <data.websocket_client.WebSocketClient object at 0x0000016989381BE0>>: wrapped C/C++ object of type WebSocketClient has been deleted
2025-07-14 16:57:28,801 - websocket - ERROR - error from callback <bound method WebSocketClient._on_error of <data.websocket_client.WebSocketClient object at 0x0000016989381BE0>>: wrapped C/C++ object of type WebSocketClient has been deleted
2025-07-14 18:42:44,422 - main - INFO - Epinnox v6 starting up...
2025-07-14 18:42:44,439 - core.performance_monitor - INFO - Performance monitoring started
2025-07-14 18:42:44,439 - core.performance_monitor - INFO - Performance monitor initialized with 10s interval
2025-07-14 18:42:44,440 - main - INFO - Performance monitoring initialized
2025-07-14 18:42:44,450 - utils.cache_manager - INFO - Cache manager initialized: cache, max_age=24h, max_size=100MB
2025-07-14 18:42:44,450 - utils.cache_manager - INFO - Starting automatic cache cleanup
2025-07-14 18:42:44,451 - utils.cache_manager - INFO - Auto cleanup complete: 0 files removed, 0.00 MB freed
2025-07-14 18:42:48,330 - core.timer_coordinator - INFO - [TIMER_COORDINATOR] Initialized unified timer coordinator
2025-07-14 18:42:55,248 - config.autonomous_config - INFO - Configuration loaded from configs/autonomous_trading.yaml
2025-07-14 18:42:56,265 - data.live_data_manager - INFO - Successfully subscribed to live data for BTC/USDT:USDT on timeframes: ['1m', '5m', '15m']
2025-07-14 18:42:57,563 - websocket - INFO - Websocket connected
2025-07-14 18:42:59,976 - trading.position_tracker - INFO - Position Tracker initialized
2025-07-14 18:43:00,375 - llama.runner - INFO - Loaded OpenAI API key from models_config.yaml
2025-07-14 18:43:00,375 - llama.runner - INFO - Using model path: C:\Users\<USER>\.lmstudio\models\lmstudio-community\Phi-3.1-mini-128k-instruct-GGUF\Phi-3.1-mini-128k-instruct-Q4_K_M.gguf
2025-07-14 18:43:00,376 - llama.runner - INFO - Using guard path: C:\Users\<USER>\Documents\dev\Epinnox_v6\models\Llama-Guard-3-8B.gguf
2025-07-14 18:43:00,376 - llama.runner - INFO - Using binary path: C:\Users\<USER>\Documents\dev\llama.cpp\main
2025-07-14 18:43:00,382 - llama.lmstudio_runner - INFO - Loaded LMStudio configuration from models_config.yaml
2025-07-14 18:43:02,432 - llama.lmstudio_runner - INFO - Discovered 8 models: ['openthinker3-7b', 'mistral-7b-claude-chat', 'fine_tuned_qwen1.7b', 'tsfc-model-0.0.1', 'codestral-22b-v0.1', 'phi-3.1-mini-128k-instruct', 'deepseek-r1-distill-qwen-7b', 'text-embedding-nomic-embed-text-v1.5']
2025-07-14 18:43:02,433 - llama.lmstudio_runner - INFO - Selected preferred model from config: phi-3.1-mini-128k-instruct
2025-07-14 18:43:02,433 - llama.lmstudio_runner - INFO - Initialized LMStudio with model: phi-3.1-mini-128k-instruct
2025-07-14 18:43:02,436 - trading.intelligent_limit_order_manager - INFO - Intelligent Limit Order Manager initialized for professional scalping
2025-07-14 18:43:02,436 - core.llm_action_executors - INFO - ✅ Intelligent Limit Order Manager initialized
2025-07-14 18:43:02,436 - core.llm_action_executors - INFO - 🚀 LLM Action Executors initialized with intelligent limit order system
2025-07-14 18:43:02,437 - utils.cache_manager - INFO - Cache manager initialized: cache, max_age=24h, max_size=100MB
2025-07-14 18:43:02,437 - core.llm_orchestrator - INFO - LLM Prompt Orchestrator initialized
2025-07-14 18:43:02,443 - core.signal_hierarchy - INFO - Signal Hierarchy initialized
2025-07-14 18:43:02,482 - storage.database_manager - INFO - Database tables initialized successfully with performance optimizations
2025-07-14 18:43:02,482 - storage.database_manager - INFO - Database manager initialized: data\epinnox_trading.db
2025-07-14 18:43:02,483 - storage.session_manager - INFO - Session Manager initialized
2025-07-14 18:43:02,487 - storage.database_manager - INFO - Created session: live_BTCUSDTUSDT_20250714_184302_598bb5f7
2025-07-14 18:43:02,489 - storage.session_manager - INFO - Started session: live_BTCUSDTUSDT_20250714_184302_598bb5f7
2025-07-14 18:43:02,648 - core.risk_management_system - INFO - 🛡️ Risk Management System initialized
2025-07-14 18:43:02,651 - core.error_handling_system - INFO - 🛡️ Error Handling System initialized
2025-07-14 18:43:02,651 - core.error_handling_system - INFO - 📊 Component registered: exchange_connection
2025-07-14 18:43:02,652 - core.error_handling_system - INFO - 📊 Component registered: trading_interface
2025-07-14 18:43:02,652 - core.error_handling_system - INFO - 📊 Component registered: market_data
2025-07-14 18:43:02,652 - core.error_handling_system - INFO - 📊 Component registered: llm_orchestrator
2025-07-14 18:43:02,654 - core.error_handling_system - INFO - 🔍 Health monitoring started
2025-07-14 18:43:02,657 - core.monitoring_dashboard - INFO - 📊 Monitoring Dashboard initialized
2025-07-14 18:43:02,658 - core.symbol_scanner - INFO - SymbolScanner initialized with 8 symbols
2025-07-14 18:43:02,659 - core.timer_coordinator - INFO - [TIMER_COORDINATOR] Registered timer 'symbol_scanner_update': interval=30.0s, priority=MEDIUM
2025-07-14 18:43:02,659 - core.symbol_scanner - INFO - [SYMBOL_SCANNER] Registered with timer coordinator (interval: 30.0s)
2025-07-14 18:43:02,659 - core.llm_action_executors - WARNING - ⚠️ Intelligent Limit Order Manager not available
2025-07-14 18:43:02,660 - core.llm_action_executors - INFO - 🚀 LLM Action Executors initialized with intelligent limit order system
