"""
Timer Coordinator for Autonomous Trading System

Manages and coordinates all timer intervals across autonomous trading modules
to prevent conflicts and ensure optimal performance.

CRITICAL: Ensures 30-second decision loops work correctly with all other timers
"""

import asyncio
import time
import logging
from typing import Dict, List, Callable, Optional, Any
from dataclasses import dataclass
from enum import Enum
from datetime import datetime, timedelta

logger = logging.getLogger(__name__)


class TimerPriority(Enum):
    """Timer priority levels for conflict resolution"""
    CRITICAL = 1    # Emergency stops, safety systems
    HIGH = 2        # Trading decisions, execution
    MEDIUM = 3      # Market data, analysis
    LOW = 4         # GUI updates, logging


@dataclass
class TimerTask:
    """Represents a scheduled timer task"""
    name: str
    func: Callable
    interval: float
    priority: TimerPriority
    last_run: float = 0.0
    next_run: float = 0.0
    run_count: int = 0
    error_count: int = 0
    enabled: bool = True
    max_execution_time: float = 5.0  # Maximum allowed execution time
    args: tuple = ()
    kwargs: dict = None
    
    def __post_init__(self):
        if self.kwargs is None:
            self.kwargs = {}
        self.next_run = time.time() + self.interval


class TimerCoordinator:
    """
    CRITICAL: Unified timer coordinator for autonomous trading system
    
    Manages all timer intervals to prevent conflicts and ensure:
    - 30-second autonomous decision loops
    - 5-second ScalperGPT analysis intervals  
    - 30-second symbol scanner updates
    - Real-time WebSocket message handling
    """
    
    def __init__(self):
        self.tasks: Dict[str, TimerTask] = {}
        self.is_running = False
        self.coordination_interval = 0.1  # 100ms coordination loop
        self.performance_metrics = {
            'total_executions': 0,
            'total_errors': 0,
            'avg_execution_time': 0.0,
            'timer_conflicts': 0,
            'coordination_cycles': 0
        }
        
        # Timer conflict detection
        self.execution_queue = []
        self.max_concurrent_tasks = 3
        self.conflict_resolution_enabled = True
        
        logger.info("[TIMER_COORDINATOR] Initialized unified timer coordinator")
    
    def register_timer(self, name: str, func: Callable, interval: float, 
                      priority: TimerPriority = TimerPriority.MEDIUM,
                      max_execution_time: float = 5.0, *args, **kwargs) -> bool:
        """
        Register a timer task with the coordinator
        
        Args:
            name: Unique timer name
            func: Function to execute
            interval: Execution interval in seconds
            priority: Timer priority for conflict resolution
            max_execution_time: Maximum allowed execution time
            *args, **kwargs: Function arguments
            
        Returns:
            True if registered successfully
        """
        try:
            if name in self.tasks:
                logger.warning(f"[TIMER_COORDINATOR] Timer '{name}' already registered, updating...")
            
            self.tasks[name] = TimerTask(
                name=name,
                func=func,
                interval=interval,
                priority=priority,
                max_execution_time=max_execution_time,
                args=args,
                kwargs=kwargs
            )
            
            logger.info(f"[TIMER_COORDINATOR] Registered timer '{name}': "
                       f"interval={interval}s, priority={priority.name}")
            
            return True
            
        except Exception as e:
            logger.error(f"[TIMER_COORDINATOR] Error registering timer '{name}': {e}")
            return False
    
    def unregister_timer(self, name: str) -> bool:
        """Unregister a timer task"""
        try:
            if name in self.tasks:
                del self.tasks[name]
                logger.info(f"[TIMER_COORDINATOR] Unregistered timer '{name}'")
                return True
            else:
                logger.warning(f"[TIMER_COORDINATOR] Timer '{name}' not found")
                return False
        except Exception as e:
            logger.error(f"[TIMER_COORDINATOR] Error unregistering timer '{name}': {e}")
            return False
    
    def enable_timer(self, name: str) -> bool:
        """Enable a timer task"""
        if name in self.tasks:
            self.tasks[name].enabled = True
            logger.info(f"[TIMER_COORDINATOR] Enabled timer '{name}'")
            return True
        return False
    
    def disable_timer(self, name: str) -> bool:
        """Disable a timer task"""
        if name in self.tasks:
            self.tasks[name].enabled = False
            logger.info(f"[TIMER_COORDINATOR] Disabled timer '{name}'")
            return True
        return False
    
    async def start(self):
        """Start the timer coordinator"""
        if self.is_running:
            logger.warning("[TIMER_COORDINATOR] Already running")
            return
        
        self.is_running = True
        logger.info("[TIMER_COORDINATOR] Starting unified timer coordination")
        
        try:
            await self._coordination_loop()
        except Exception as e:
            logger.error(f"[TIMER_COORDINATOR] Coordination loop error: {e}")
        finally:
            self.is_running = False
    
    def stop(self):
        """Stop the timer coordinator"""
        self.is_running = False
        logger.info("[TIMER_COORDINATOR] Stopped timer coordination")
    
    async def _coordination_loop(self):
        """Main coordination loop"""
        while self.is_running:
            try:
                cycle_start = time.time()
                current_time = time.time()
                
                # Find tasks ready to execute
                ready_tasks = []
                for task in self.tasks.values():
                    if task.enabled and current_time >= task.next_run:
                        ready_tasks.append(task)
                
                # Sort by priority and schedule execution
                ready_tasks.sort(key=lambda t: (t.priority.value, t.next_run))
                
                # Execute tasks with conflict resolution
                await self._execute_tasks(ready_tasks, current_time)
                
                # Update performance metrics
                self.performance_metrics['coordination_cycles'] += 1
                
                # Sleep for coordination interval
                cycle_time = time.time() - cycle_start
                sleep_time = max(0, self.coordination_interval - cycle_time)
                if sleep_time > 0:
                    await asyncio.sleep(sleep_time)
                
            except Exception as e:
                logger.error(f"[TIMER_COORDINATOR] Coordination cycle error: {e}")
                await asyncio.sleep(self.coordination_interval)
    
    async def _execute_tasks(self, ready_tasks: List[TimerTask], current_time: float):
        """Execute ready tasks with conflict resolution"""
        executing_tasks = []
        
        for task in ready_tasks:
            # Check for conflicts if conflict resolution is enabled
            if self.conflict_resolution_enabled:
                if len(executing_tasks) >= self.max_concurrent_tasks:
                    # Skip lower priority tasks if we're at capacity
                    logger.debug(f"[TIMER_COORDINATOR] Skipping '{task.name}' due to capacity limit")
                    self.performance_metrics['timer_conflicts'] += 1
                    continue
            
            # Execute the task
            executing_tasks.append(asyncio.create_task(self._execute_single_task(task, current_time)))
        
        # Wait for all tasks to complete
        if executing_tasks:
            await asyncio.gather(*executing_tasks, return_exceptions=True)
    
    async def _execute_single_task(self, task: TimerTask, current_time: float):
        """Execute a single timer task"""
        try:
            execution_start = time.time()
            
            # Execute the task function
            if asyncio.iscoroutinefunction(task.func):
                await asyncio.wait_for(
                    task.func(*task.args, **task.kwargs),
                    timeout=task.max_execution_time
                )
            else:
                # Run synchronous function in executor to avoid blocking
                await asyncio.get_event_loop().run_in_executor(
                    None, lambda: task.func(*task.args, **task.kwargs)
                )
            
            # Update task statistics
            execution_time = time.time() - execution_start
            task.last_run = current_time
            task.next_run = current_time + task.interval
            task.run_count += 1
            
            # Update performance metrics
            self.performance_metrics['total_executions'] += 1
            self._update_avg_execution_time(execution_time)
            
            logger.debug(f"[TIMER_COORDINATOR] Executed '{task.name}' in {execution_time:.3f}s")
            
        except asyncio.TimeoutError:
            logger.error(f"[TIMER_COORDINATOR] Task '{task.name}' timed out after {task.max_execution_time}s")
            task.error_count += 1
            task.next_run = current_time + task.interval
            self.performance_metrics['total_errors'] += 1
            
        except Exception as e:
            logger.error(f"[TIMER_COORDINATOR] Error executing task '{task.name}': {e}")
            task.error_count += 1
            task.next_run = current_time + task.interval
            self.performance_metrics['total_errors'] += 1
    
    def _update_avg_execution_time(self, execution_time: float):
        """Update average execution time metric"""
        total_executions = self.performance_metrics['total_executions']
        current_avg = self.performance_metrics['avg_execution_time']
        
        # Calculate new average
        new_avg = ((current_avg * (total_executions - 1)) + execution_time) / total_executions
        self.performance_metrics['avg_execution_time'] = new_avg
    
    def get_timer_status(self) -> Dict[str, Any]:
        """Get status of all timers"""
        current_time = time.time()
        
        timer_status = {}
        for name, task in self.tasks.items():
            timer_status[name] = {
                'enabled': task.enabled,
                'interval': task.interval,
                'priority': task.priority.name,
                'last_run': task.last_run,
                'next_run': task.next_run,
                'time_until_next': max(0, task.next_run - current_time),
                'run_count': task.run_count,
                'error_count': task.error_count,
                'error_rate': task.error_count / max(1, task.run_count)
            }
        
        return {
            'is_running': self.is_running,
            'total_timers': len(self.tasks),
            'enabled_timers': sum(1 for t in self.tasks.values() if t.enabled),
            'performance_metrics': self.performance_metrics,
            'timers': timer_status
        }
    
    def get_performance_metrics(self) -> Dict[str, Any]:
        """Get performance metrics"""
        return self.performance_metrics.copy()


# Global timer coordinator instance
timer_coordinator = TimerCoordinator()


# Standard timer intervals for autonomous trading system
class StandardIntervals:
    """Standard timer intervals for consistent coordination"""
    
    # CRITICAL: Core autonomous trading intervals
    AUTONOMOUS_DECISION_LOOP = 30.0      # 30-second LLM decision loops
    SCALPER_GPT_ANALYSIS = 5.0           # 5-second ScalperGPT analysis
    SYMBOL_SCANNER_UPDATE = 30.0         # 30-second symbol scanner updates
    
    # Market data and execution
    WEBSOCKET_HEALTH_CHECK = 10.0        # WebSocket health monitoring
    ORDERBOOK_UPDATE = 1.0               # Order book updates
    POSITION_MONITORING = 5.0            # Position monitoring
    RISK_ASSESSMENT = 15.0               # Risk limit checks
    
    # Performance and monitoring
    PERFORMANCE_METRICS = 60.0           # Performance metrics update
    SYSTEM_HEALTH_CHECK = 30.0           # System health monitoring
    GUI_UPDATE = 5.0                     # GUI refresh rate
    
    # Emergency and safety
    EMERGENCY_MONITORING = 1.0           # Emergency stop monitoring
    SAFETY_CHECKS = 10.0                 # Safety system checks


async def start_timer_coordinator():
    """Start the global timer coordinator"""
    await timer_coordinator.start()


def stop_timer_coordinator():
    """Stop the global timer coordinator"""
    timer_coordinator.stop()


def register_standard_timer(name: str, func: Callable, interval_name: str, 
                          priority: TimerPriority = TimerPriority.MEDIUM, *args, **kwargs) -> bool:
    """
    Register a timer using standard intervals
    
    Args:
        name: Timer name
        func: Function to execute
        interval_name: Name of standard interval (e.g., 'AUTONOMOUS_DECISION_LOOP')
        priority: Timer priority
        *args, **kwargs: Function arguments
    """
    try:
        interval = getattr(StandardIntervals, interval_name)
        return timer_coordinator.register_timer(name, func, interval, priority, *args, **kwargs)
    except AttributeError:
        logger.error(f"[TIMER_COORDINATOR] Unknown standard interval: {interval_name}")
        return False
