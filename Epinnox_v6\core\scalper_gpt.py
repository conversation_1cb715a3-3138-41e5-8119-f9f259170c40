"""
ScalperGPT - Advanced AI-Powered Scalping Analysis System
Provides high-frequency trading analysis with quality thresholds for autonomous trading
"""

import asyncio
import time
import logging
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
from datetime import datetime, timedelta
import statistics

# Configure Unicode-safe logging
try:
    from core.unicode_safe_logging import configure_unicode_safe_logging
    configure_unicode_safe_logging()
except ImportError:
    pass

logger = logging.getLogger(__name__)

@dataclass
class ScalpingOpportunity:
    """Represents a scalping opportunity with quality metrics"""
    symbol: str
    direction: str  # 'LONG' or 'SHORT'
    entry_price: float
    target_price: float
    stop_loss: float
    confidence: float
    spread_quality: float
    decision_quality: float
    liquidity_score: float
    volatility_score: float
    timestamp: datetime
    reasoning: str
    expected_duration: int  # seconds
    risk_reward_ratio: float

@dataclass
class MarketMicrostructure:
    """Market microstructure analysis for scalping"""
    symbol: str
    bid_ask_spread: float
    spread_pct: float
    order_book_imbalance: float
    tick_size: float
    volume_profile: Dict[str, float]
    price_momentum: float
    liquidity_depth: float
    market_impact: float
    timestamp: datetime

class ScalperGPT:
    """
    Advanced AI-powered scalping analysis system
    Provides real-time market microstructure analysis and scalping opportunities
    with strict quality thresholds for autonomous trading
    """
    
    def __init__(self, llm_provider=None, config: Dict[str, Any] = None):
        self.llm_provider = llm_provider
        self.config = config or self._get_default_config()
        
        # Quality thresholds (as specified in requirements)
        self.quality_thresholds = {
            'spread_quality': 7.0,      # Minimum spread quality score
            'decision_quality': 8.0,    # Minimum decision quality score
            'min_confidence': 0.75,     # Minimum confidence for execution
            'max_spread_pct': 0.15,     # Maximum spread percentage
            'min_liquidity': 1000,      # Minimum liquidity depth
            'min_risk_reward': 2.0      # Minimum risk/reward ratio
        }
        
        # Analysis state
        self.market_data_cache = {}
        self.opportunity_history = []
        self.performance_metrics = {
            'opportunities_generated': 0,
            'opportunities_above_threshold': 0,
            'avg_spread_quality': 0.0,
            'avg_decision_quality': 0.0,
            'success_rate': 0.0
        }
        
        # Real-time analysis components
        self.microstructure_analyzer = MarketMicrostructureAnalyzer()
        self.opportunity_detector = OpportunityDetector(self.quality_thresholds)
        
        logger.info("[TARGET] ScalperGPT initialized with quality thresholds: "
                   f"spread_quality >= {self.quality_thresholds['spread_quality']}, "
                   f"decision_quality >= {self.quality_thresholds['decision_quality']}")
    
    def _get_default_config(self) -> Dict[str, Any]:
        """Get default ScalperGPT configuration"""
        return {
            'analysis_interval': 5,      # seconds
            'max_opportunities': 10,     # maximum concurrent opportunities
            'scalping_timeframe': '1m',  # primary timeframe
            'lookback_periods': 20,      # periods for analysis
            'volatility_window': 14,     # periods for volatility calculation
            'enable_ai_enhancement': True,
            'ai_prompt_template': 'scalping_analysis'
        }
    
    async def analyze_scalping_opportunity(self, symbol: str, market_data: Dict[str, Any]) -> Optional[ScalpingOpportunity]:
        """
        Analyze market data for scalping opportunities with quality scoring
        
        Args:
            symbol: Trading symbol
            market_data: Real-time market data
            
        Returns:
            ScalpingOpportunity if quality thresholds are met, None otherwise
        """
        try:
            start_time = time.time()
            
            # 1. Analyze market microstructure
            microstructure = await self.microstructure_analyzer.analyze(symbol, market_data)
            if not microstructure:
                return None
            
            # 2. Calculate spread quality score
            spread_quality = self._calculate_spread_quality(microstructure)
            
            # 3. Detect scalping opportunity
            opportunity = await self.opportunity_detector.detect_opportunity(
                symbol, market_data, microstructure
            )
            
            if not opportunity:
                return None
            
            # 4. Enhance with AI analysis if enabled
            if self.config['enable_ai_enhancement'] and self.llm_provider:
                opportunity = await self._enhance_with_ai_analysis(opportunity, market_data)
            
            # 5. Calculate decision quality score
            decision_quality = self._calculate_decision_quality(opportunity, microstructure)
            
            # 6. Apply quality thresholds
            if not self._meets_quality_thresholds(spread_quality, decision_quality, opportunity):
                logger.debug(f"🚫 {symbol} opportunity rejected: "
                           f"spread_quality={spread_quality:.1f}, "
                           f"decision_quality={decision_quality:.1f}")
                return None
            
            # 7. Create final opportunity with quality metrics
            scalping_opportunity = ScalpingOpportunity(
                symbol=symbol,
                direction=opportunity['direction'],
                entry_price=opportunity['entry_price'],
                target_price=opportunity['target_price'],
                stop_loss=opportunity['stop_loss'],
                confidence=opportunity['confidence'],
                spread_quality=spread_quality,
                decision_quality=decision_quality,
                liquidity_score=microstructure.liquidity_depth,
                volatility_score=opportunity.get('volatility_score', 0.0),
                timestamp=datetime.now(),
                reasoning=opportunity.get('reasoning', ''),
                expected_duration=opportunity.get('duration', 60),
                risk_reward_ratio=opportunity.get('risk_reward', 0.0)
            )
            
            # 8. Update metrics and cache
            self._update_performance_metrics(scalping_opportunity)
            self.opportunity_history.append(scalping_opportunity)
            
            analysis_time = time.time() - start_time
            logger.info(f"[OK] {symbol} scalping opportunity: {scalping_opportunity.direction} "
                       f"(spread_quality={spread_quality:.1f}, "
                       f"decision_quality={decision_quality:.1f}, "
                       f"confidence={scalping_opportunity.confidence:.1%}, "
                       f"analysis_time={analysis_time:.3f}s)")
            
            return scalping_opportunity
            
        except Exception as e:
            logger.error(f"[ERROR] Error analyzing scalping opportunity for {symbol}: {e}")
            return None
    
    def _calculate_spread_quality(self, microstructure: MarketMicrostructure) -> float:
        """
        Calculate spread quality score (0-10 scale)
        Higher score = better spread conditions for scalping
        """
        try:
            # Base score from spread percentage (lower is better)
            spread_score = max(0, 10 - (microstructure.spread_pct * 100))
            
            # Adjust for liquidity depth
            liquidity_factor = min(1.0, microstructure.liquidity_depth / 5000)
            
            # Adjust for order book balance
            balance_factor = 1.0 - abs(microstructure.order_book_imbalance) / 100
            
            # Composite spread quality score
            quality_score = spread_score * liquidity_factor * balance_factor
            
            return min(10.0, max(0.0, quality_score))
            
        except Exception as e:
            logger.error(f"Error calculating spread quality: {e}")
            return 0.0
    
    def _calculate_decision_quality(self, opportunity: Dict[str, Any], microstructure: MarketMicrostructure) -> float:
        """
        Calculate decision quality score (0-10 scale)
        Higher score = higher confidence in the trading decision
        """
        try:
            # Base score from opportunity confidence
            confidence_score = opportunity.get('confidence', 0.0) * 10
            
            # Technical analysis strength
            technical_score = opportunity.get('technical_strength', 0.5) * 10
            
            # Risk/reward ratio quality
            risk_reward = opportunity.get('risk_reward', 1.0)
            rr_score = min(10.0, risk_reward * 2)  # 2:1 ratio = 4 points, 5:1 = 10 points
            
            # Market momentum alignment
            momentum_score = abs(microstructure.price_momentum) * 10
            
            # Volatility appropriateness (moderate volatility is best for scalping)
            volatility = opportunity.get('volatility_score', 0.5)
            volatility_score = 10 * (1 - abs(volatility - 0.6))  # Optimal around 0.6
            
            # Weighted composite score
            weights = {
                'confidence': 0.3,
                'technical': 0.25,
                'risk_reward': 0.2,
                'momentum': 0.15,
                'volatility': 0.1
            }
            
            decision_quality = (
                confidence_score * weights['confidence'] +
                technical_score * weights['technical'] +
                rr_score * weights['risk_reward'] +
                momentum_score * weights['momentum'] +
                volatility_score * weights['volatility']
            )
            
            return min(10.0, max(0.0, decision_quality))
            
        except Exception as e:
            logger.error(f"Error calculating decision quality: {e}")
            return 0.0
    
    def _meets_quality_thresholds(self, spread_quality: float, decision_quality: float, 
                                 opportunity: Dict[str, Any]) -> bool:
        """Check if opportunity meets all quality thresholds"""
        try:
            # Check spread quality threshold
            if spread_quality < self.quality_thresholds['spread_quality']:
                return False
            
            # Check decision quality threshold
            if decision_quality < self.quality_thresholds['decision_quality']:
                return False
            
            # Check confidence threshold
            if opportunity.get('confidence', 0.0) < self.quality_thresholds['min_confidence']:
                return False
            
            # Check risk/reward ratio
            if opportunity.get('risk_reward', 0.0) < self.quality_thresholds['min_risk_reward']:
                return False
            
            return True
            
        except Exception as e:
            logger.error(f"Error checking quality thresholds: {e}")
            return False
    
    async def _enhance_with_ai_analysis(self, opportunity: Dict[str, Any], 
                                      market_data: Dict[str, Any]) -> Dict[str, Any]:
        """Enhance opportunity analysis with AI/LLM insights"""
        try:
            if not self.llm_provider:
                return opportunity
            
            # Build AI prompt for scalping analysis
            prompt = self._build_scalping_prompt(opportunity, market_data)
            
            # Get AI analysis
            ai_response = await self.llm_provider.generate_response(prompt)
            
            if ai_response and ai_response.content:
                # Parse AI insights and enhance opportunity
                ai_insights = self._parse_ai_response(ai_response.content)
                opportunity.update(ai_insights)
            
            return opportunity
            
        except Exception as e:
            logger.error(f"Error enhancing with AI analysis: {e}")
            return opportunity
    
    def _build_scalping_prompt(self, opportunity: Dict[str, Any], market_data: Dict[str, Any]) -> str:
        """Build AI prompt for scalping analysis"""
        return f"""
        Analyze this scalping opportunity:
        
        Symbol: {opportunity.get('symbol', 'Unknown')}
        Direction: {opportunity.get('direction', 'Unknown')}
        Entry: {opportunity.get('entry_price', 0)}
        Target: {opportunity.get('target_price', 0)}
        Stop Loss: {opportunity.get('stop_loss', 0)}
        
        Market Data:
        Current Price: {market_data.get('price', 0)}
        Spread: {market_data.get('spread', 0)}
        Volume: {market_data.get('volume', 0)}
        
        Provide brief analysis focusing on:
        1. Technical strength (0-1 scale)
        2. Risk assessment
        3. Timing quality
        4. Key reasoning (max 50 words)
        
        Format: technical_strength:X.X|risk_level:LOW/MED/HIGH|timing:GOOD/FAIR/POOR|reasoning:brief explanation
        """
    
    def _parse_ai_response(self, response: str) -> Dict[str, Any]:
        """Parse AI response into structured insights"""
        insights = {}
        try:
            parts = response.split('|')
            for part in parts:
                if ':' in part:
                    key, value = part.split(':', 1)
                    key = key.strip()
                    value = value.strip()
                    
                    if key == 'technical_strength':
                        insights['technical_strength'] = float(value)
                    elif key == 'reasoning':
                        insights['reasoning'] = value
                    elif key == 'risk_level':
                        insights['risk_level'] = value
                    elif key == 'timing':
                        insights['timing_quality'] = value
        except Exception as e:
            logger.error(f"Error parsing AI response: {e}")
        
        return insights
    
    def _update_performance_metrics(self, opportunity: ScalpingOpportunity):
        """Update performance metrics"""
        self.performance_metrics['opportunities_generated'] += 1
        
        if (opportunity.spread_quality >= self.quality_thresholds['spread_quality'] and
            opportunity.decision_quality >= self.quality_thresholds['decision_quality']):
            self.performance_metrics['opportunities_above_threshold'] += 1
        
        # Update running averages
        total_ops = len(self.opportunity_history)
        if total_ops > 0:
            self.performance_metrics['avg_spread_quality'] = statistics.mean(
                [op.spread_quality for op in self.opportunity_history[-100:]]  # Last 100
            )
            self.performance_metrics['avg_decision_quality'] = statistics.mean(
                [op.decision_quality for op in self.opportunity_history[-100:]]  # Last 100
            )
    
    def get_performance_metrics(self) -> Dict[str, Any]:
        """Get current performance metrics"""
        return {
            **self.performance_metrics,
            'quality_thresholds': self.quality_thresholds,
            'recent_opportunities': len([
                op for op in self.opportunity_history 
                if (datetime.now() - op.timestamp).seconds < 300  # Last 5 minutes
            ])
        }


class MarketMicrostructureAnalyzer:
    """Analyzes market microstructure for scalping opportunities"""
    
    async def analyze(self, symbol: str, market_data: Dict[str, Any]) -> Optional[MarketMicrostructure]:
        """Analyze market microstructure"""
        try:
            # Extract order book data
            orderbook = market_data.get('orderbook', {})
            bids = orderbook.get('bids', [])
            asks = orderbook.get('asks', [])
            
            if not bids or not asks:
                return None
            
            # Calculate spread metrics
            best_bid = bids[0][0]
            best_ask = asks[0][0]
            spread = best_ask - best_bid
            spread_pct = (spread / best_ask) * 100
            
            # Calculate order book imbalance
            bid_volume = sum([bid[1] for bid in bids[:5]])
            ask_volume = sum([ask[1] for ask in asks[:5]])
            total_volume = bid_volume + ask_volume
            imbalance = ((bid_volume - ask_volume) / total_volume * 100) if total_volume > 0 else 0
            
            # Calculate liquidity depth
            liquidity_depth = bid_volume + ask_volume
            
            # Estimate market impact (simplified)
            market_impact = spread_pct * 0.5  # Rough estimate
            
            return MarketMicrostructure(
                symbol=symbol,
                bid_ask_spread=spread,
                spread_pct=spread_pct,
                order_book_imbalance=imbalance,
                tick_size=market_data.get('tick_size', 0.00001),
                volume_profile=market_data.get('volume_profile', {}),
                price_momentum=market_data.get('momentum', 0.0),
                liquidity_depth=liquidity_depth,
                market_impact=market_impact,
                timestamp=datetime.now()
            )
            
        except Exception as e:
            logger.error(f"Error analyzing microstructure for {symbol}: {e}")
            return None


class OpportunityDetector:
    """Detects scalping opportunities based on market conditions"""
    
    def __init__(self, quality_thresholds: Dict[str, float]):
        self.quality_thresholds = quality_thresholds
    
    async def detect_opportunity(self, symbol: str, market_data: Dict[str, Any], 
                               microstructure: MarketMicrostructure) -> Optional[Dict[str, Any]]:
        """Detect scalping opportunity"""
        try:
            current_price = market_data.get('price', 0)
            if not current_price:
                return None
            
            # Check if spread is acceptable for scalping
            if microstructure.spread_pct > self.quality_thresholds['max_spread_pct']:
                return None
            
            # Simple momentum-based opportunity detection
            momentum = market_data.get('momentum', 0.0)
            volatility = market_data.get('volatility', 0.0)
            
            if abs(momentum) < 0.1:  # Not enough momentum
                return None
            
            # Determine direction
            direction = 'LONG' if momentum > 0 else 'SHORT'
            
            # Calculate entry, target, and stop loss
            if direction == 'LONG':
                entry_price = current_price * 1.0005  # Slightly above current
                target_price = current_price * 1.01   # 1% target
                stop_loss = current_price * 0.995     # 0.5% stop
            else:
                entry_price = current_price * 0.9995  # Slightly below current
                target_price = current_price * 0.99   # 1% target
                stop_loss = current_price * 1.005     # 0.5% stop
            
            # Calculate risk/reward ratio
            risk = abs(entry_price - stop_loss)
            reward = abs(target_price - entry_price)
            risk_reward = reward / risk if risk > 0 else 0
            
            # Calculate confidence based on multiple factors
            confidence = min(0.95, (
                abs(momentum) * 0.4 +
                (1 - microstructure.spread_pct / 0.2) * 0.3 +
                min(1.0, microstructure.liquidity_depth / 5000) * 0.3
            ))
            
            return {
                'symbol': symbol,
                'direction': direction,
                'entry_price': entry_price,
                'target_price': target_price,
                'stop_loss': stop_loss,
                'confidence': confidence,
                'risk_reward': risk_reward,
                'volatility_score': volatility,
                'duration': 30,  # Expected 60 seconds
                'reasoning': f'{direction} momentum scalp, {momentum:.3f} momentum'
            }
            
        except Exception as e:
            logger.error(f"Error detecting opportunity for {symbol}: {e}")
            return None
