"""
Trading Strategies Module

This module contains various trading strategies that can be used by the application.
"""

from .atr_ema_bands import ATREMABandsStrategy

# Dictionary of available strategies
AVAILABLE_STRATEGIES = {
    'atr_ema_bands': ATREMABandsStrategy
}

def get_strategy(strategy_name, config):
    """
    Get a strategy instance by name.
    
    Args:
        strategy_name (str): Name of the strategy.
        config (dict): Configuration dictionary.
        
    Returns:
        object: Strategy instance.
        
    Raises:
        ValueError: If strategy_name is not found.
    """
    if strategy_name not in AVAILABLE_STRATEGIES:
        raise ValueError(f"Strategy '{strategy_name}' not found. Available strategies: {list(AVAILABLE_STRATEGIES.keys())}")
    
    return AVAILABLE_STRATEGIES[strategy_name](config)
