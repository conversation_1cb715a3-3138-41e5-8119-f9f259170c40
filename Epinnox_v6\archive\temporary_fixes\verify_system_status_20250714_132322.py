#!/usr/bin/env python3
"""
SYSTEM STATUS VERIFICATION
Comprehensive verification that all fixes are working and live trading continues
"""

import os
import sys
import logging

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def verify_unicode_fixes():
    """Verify Unicode fixes are working"""
    
    print("1. UNICODE LOGGING VERIFICATION")
    print("-" * 35)
    
    try:
        # Configure Unicode-safe logging
        from core.unicode_safe_logging import configure_unicode_safe_logging
        configure_unicode_safe_logging()
        
        # Test logging with various components
        test_components = [
            ('core.scalper_gpt', '[TARGET] ScalperGPT test'),
            ('core.autonomous_trading_orchestrator', '[LAUNCH] Orchestrator test'),
            ('validation.live_trading_validator', '[OK] Validator test'),
            ('core.emergency_stop_coordinator', '[ALERT] Emergency test'),
            ('core.dynamic_risk_manager', '[CHART] Risk manager test')
        ]
        
        unicode_errors = 0
        
        for component, message in test_components:
            try:
                logger = logging.getLogger(component)
                logger.info(message)
                print(f"   ✅ {component}: Unicode logging working")
            except UnicodeEncodeError as e:
                unicode_errors += 1
                print(f"   ❌ {component}: Unicode error - {e}")
            except Exception as e:
                print(f"   ⚠️ {component}: Other error - {e}")
        
        if unicode_errors == 0:
            print("   🎉 ALL UNICODE LOGGING TESTS PASSED")
            return True
        else:
            print(f"   ⚠️ {unicode_errors} Unicode errors remain")
            return False
            
    except Exception as e:
        print(f"   ❌ Unicode verification failed: {e}")
        return False

def verify_websocket_manager():
    """Verify WebSocket manager is available"""
    
    print("\n2. WEBSOCKET MANAGER VERIFICATION")
    print("-" * 35)
    
    try:
        from core.websocket_manager import websocket_manager, WebSocketManager
        
        # Test WebSocket manager functionality
        status = websocket_manager.get_connection_status()
        
        print(f"   ✅ WebSocket manager available")
        print(f"   📊 State: {status['state']}")
        print(f"   📊 URL: {status.get('url', 'Not configured')}")
        print(f"   📊 Connection attempts: {status['connection_attempts']}")
        
        # Test WebSocket manager methods
        if hasattr(websocket_manager, 'is_connected'):
            connected = websocket_manager.is_connected()
            print(f"   📊 Connected: {connected}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ WebSocket manager verification failed: {e}")
        return False

def verify_system_integration():
    """Verify system integration is working"""
    
    print("\n3. SYSTEM INTEGRATION VERIFICATION")
    print("-" * 35)
    
    try:
        from core.dynamic_risk_integration import dynamic_risk_integration
        
        # Get integration status
        status = dynamic_risk_integration.get_integration_status()
        
        critical_components = [
            'orchestrator_registered',
            'execution_engine_registered',
            'dynamic_risk_manager_registered',
            'emergency_coordinator_registered'
        ]
        
        all_critical_ok = True
        
        for component in critical_components:
            if status.get(component, False):
                print(f"   ✅ {component}: OK")
            else:
                print(f"   ❌ {component}: NOT REGISTERED")
                all_critical_ok = False
        
        # Optional components
        optional_components = [
            'scalper_gpt_registered',
            'symbol_scanner_registered',
            'deployment_manager_registered'
        ]
        
        for component in optional_components:
            if status.get(component, False):
                print(f"   ✅ {component}: OK")
            else:
                print(f"   ⚠️ {component}: Not registered (optional)")
        
        if all_critical_ok:
            print("   🎉 ALL CRITICAL COMPONENTS INTEGRATED")
            return True
        else:
            print("   ⚠️ Some critical components missing")
            return False
            
    except Exception as e:
        print(f"   ❌ System integration verification failed: {e}")
        return False

def verify_safety_systems():
    """Verify safety systems are operational"""
    
    print("\n4. SAFETY SYSTEMS VERIFICATION")
    print("-" * 35)
    
    try:
        # Test emergency coordinator
        from core.emergency_stop_coordinator import emergency_coordinator
        
        if emergency_coordinator.is_initialized():
            print("   ✅ Emergency coordinator: Initialized")
        else:
            print("   ❌ Emergency coordinator: Not initialized")
            return False
        
        # Test execution engine
        from core.unified_execution_engine import unified_execution_engine
        
        status = unified_execution_engine.get_execution_status()
        
        if status['enforce_limit_orders_only']:
            print("   ✅ LIMIT orders enforcement: ACTIVE")
        else:
            print("   ❌ LIMIT orders enforcement: DISABLED")
            return False
        
        print(f"   📊 Max position size: {status['max_position_size'] * 100}%")
        print(f"   📊 Max leverage: {status['max_leverage']}x")
        print(f"   📊 Max concurrent positions: {status['max_concurrent_positions']}")
        
        # Test risk management
        from core.dynamic_risk_manager import dynamic_risk_manager
        
        current_params = dynamic_risk_manager.get_current_parameters()
        
        if current_params.max_trading_capital <= 100:
            print(f"   ✅ Trading capital: ${current_params.max_trading_capital} (safe)")
        else:
            print(f"   ⚠️ Trading capital: ${current_params.max_trading_capital} (high)")
        
        if current_params.position_size_pct <= 1.0:
            print(f"   ✅ Position size: {current_params.position_size_pct}% (safe)")
        else:
            print(f"   ⚠️ Position size: {current_params.position_size_pct}% (high)")
        
        print("   🎉 ALL SAFETY SYSTEMS OPERATIONAL")
        return True
        
    except Exception as e:
        print(f"   ❌ Safety systems verification failed: {e}")
        return False

def verify_exchange_connectivity():
    """Verify exchange connectivity"""
    
    print("\n5. EXCHANGE CONNECTIVITY VERIFICATION")
    print("-" * 35)
    
    try:
        from trading.ccxt_trading_engine import CCXTTradingEngine
        
        # Test exchange connection
        exchange_engine = CCXTTradingEngine('htx', demo_mode=False)
        
        if exchange_engine.initialize_exchange():
            print("   ✅ HTX exchange: Connected")
            
            # Get balance
            balance = exchange_engine.exchange.fetch_balance()
            usdt_balance = balance.get('USDT', {}).get('free', 0)
            
            print(f"   💰 Account balance: ${usdt_balance:.2f} USDT")
            
            if usdt_balance >= 100:
                print("   ✅ Sufficient balance for validation")
            else:
                print("   ⚠️ Low balance for validation")
            
            return True
        else:
            print("   ❌ HTX exchange: Connection failed")
            return False
            
    except Exception as e:
        print(f"   ❌ Exchange connectivity verification failed: {e}")
        return False

def check_live_trading_status():
    """Check if live trading validation is running"""
    
    print("\n6. LIVE TRADING STATUS CHECK")
    print("-" * 35)
    
    try:
        from validation.live_trading_validator import live_trading_validator
        
        # Check validation status
        if hasattr(live_trading_validator, 'validation_active'):
            if live_trading_validator.validation_active:
                print("   🔴 LIVE TRADING VALIDATION: ACTIVE")
                
                if hasattr(live_trading_validator, 'validation_start_time'):
                    start_time = live_trading_validator.validation_start_time
                    if start_time:
                        from datetime import datetime
                        duration = datetime.now() - start_time
                        hours = duration.total_seconds() / 3600
                        print(f"   ⏱️ Running for: {hours:.1f} hours")
                        
                        remaining = 24 - hours
                        if remaining > 0:
                            print(f"   ⏱️ Remaining: {remaining:.1f} hours")
                        else:
                            print("   ⏱️ Validation period completed")
                
                return True
            else:
                print("   ⚪ LIVE TRADING VALIDATION: NOT ACTIVE")
                return False
        else:
            print("   ⚠️ Cannot determine validation status")
            return False
            
    except Exception as e:
        print(f"   ❌ Live trading status check failed: {e}")
        return False

def main():
    """Main verification function"""
    
    print("🔍 COMPREHENSIVE SYSTEM STATUS VERIFICATION")
    print("=" * 50)
    print("⚠️ Checking all systems after Unicode fixes")
    print("=" * 50)
    
    # Run all verifications
    results = {
        'unicode_fixes': verify_unicode_fixes(),
        'websocket_manager': verify_websocket_manager(),
        'system_integration': verify_system_integration(),
        'safety_systems': verify_safety_systems(),
        'exchange_connectivity': verify_exchange_connectivity(),
        'live_trading_status': check_live_trading_status()
    }
    
    # Summary
    print("\n" + "=" * 50)
    print("VERIFICATION SUMMARY")
    print("=" * 50)
    
    passed = sum(1 for result in results.values() if result)
    total = len(results)
    
    for check, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {check.replace('_', ' ').title()}")
    
    print(f"\n📊 OVERALL STATUS: {passed}/{total} checks passed")
    
    if passed == total:
        print("\n🎉 ALL SYSTEMS OPERATIONAL")
        print("✅ Unicode fixes successful")
        print("✅ Live trading can continue safely")
        print("✅ All safety systems active")
        return 0
    else:
        print(f"\n⚠️ {total - passed} ISSUES DETECTED")
        print("🔧 Some systems may need attention")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
