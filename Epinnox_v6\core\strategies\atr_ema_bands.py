#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
ATR-EMA Bands Strategy
----------------------
A strategy that uses EMA with ATR-based bands to identify potential entry and exit points.
"""

import pandas as pd
import numpy as np
import logging

logger = logging.getLogger('strategy.atr_ema_bands')

class ATREMABandsStrategy:
    """
    ATR-EMA Bands Strategy implementation.

    This strategy uses EMA with ATR-based bands to identify potential entry and exit points.
    """

    def __init__(self, config):
        """
        Initialize the ATR-EMA Bands Strategy.

        Args:
            config (dict): Configuration dictionary.
        """
        self.config = config
        self.name = "ATR-EMA Bands"

        # Load strategy parameters from config
        self._load_parameters()

        logger.info(f"Initialized {self.name} Strategy with ATR period: {self.atr_period}, EMA period: {self.ema_period}")

    def _load_parameters(self):
        """Load strategy parameters from config."""
        # Get strategy-specific parameters from config
        strategy_config = self.config.get('strategies', {}).get('atr_ema_bands', {})

        # Feature toggles for indicators
        self.use_ema = strategy_config.get('use_ema', True)
        self.use_atr = strategy_config.get('use_atr', True)
        self.use_rsi = strategy_config.get('use_rsi', False)
        self.use_macd = strategy_config.get('use_macd', False)
        self.use_ema_slope = strategy_config.get('use_ema_slope', True)
        self.use_price_velocity = strategy_config.get('use_price_velocity', True)
        self.use_atr_breakout = strategy_config.get('use_atr_breakout', True)
        self.use_band_break = strategy_config.get('use_band_break', True)
        self.use_wick_rejection = strategy_config.get('use_wick_rejection', True)
        self.use_band_width = strategy_config.get('use_band_width', True)
        self.use_htf_alignment = strategy_config.get('use_htf_alignment', False)

        # ATR period
        self.atr_period = strategy_config.get('atr_period', 12)

        # EMA period
        self.ema_period = strategy_config.get('ema_period', 16)

        # Band multipliers
        self.band_multipliers = strategy_config.get('band_multipliers', [1, 2, 3])

        # Signal mode - 'threshold' or 'crossover'
        self.signal_mode = strategy_config.get('signal_mode', 'crossover')  # Default to crossover mode

        # Band selection for crossovers (using checkboxes for each band)
        self.use_ema_cross = strategy_config.get('use_ema_cross', False)  # Disable EMA center line by default
        self.use_ema_plus_1_cross = strategy_config.get('use_ema_plus_1_cross', False)
        self.use_ema_plus_2_cross = strategy_config.get('use_ema_plus_2_cross', True)  # Enable EMA+2 for shorts
        self.use_ema_plus_3_cross = strategy_config.get('use_ema_plus_3_cross', True)  # Enable EMA+3 for shorts
        self.use_ema_minus_1_cross = strategy_config.get('use_ema_minus_1_cross', False)
        self.use_ema_minus_2_cross = strategy_config.get('use_ema_minus_2_cross', True)  # Enable EMA-2 for longs
        self.use_ema_minus_3_cross = strategy_config.get('use_ema_minus_3_cross', True)  # Enable EMA-3 for longs

        # Enable specific direction for crossovers
        self.enable_long_crossovers = strategy_config.get('enable_long_crossovers', True)
        self.enable_short_crossovers = strategy_config.get('enable_short_crossovers', True)

        # RSI filter for crossovers
        self.use_rsi_filter = strategy_config.get('use_rsi_filter', True)  # Enable RSI filter by default

        # Signal thresholds (used only in threshold mode)
        self.buy_threshold = strategy_config.get('buy_threshold', 0.6)
        self.sell_threshold = strategy_config.get('sell_threshold', 0.4)

        # Lookback periods for signal confirmation
        self.lookback_periods = strategy_config.get('lookback_periods', 3)

        # EMA slope parameters
        self.ema_slope_periods = strategy_config.get('ema_slope_periods', 10)
        self.ema_slope_weight = strategy_config.get('ema_slope_weight', 0.5)

        # RSI parameters
        self.rsi_period = strategy_config.get('rsi_period', 14)
        self.rsi_overbought = strategy_config.get('rsi_overbought', 70)  # Standard overbought level
        self.rsi_oversold = strategy_config.get('rsi_oversold', 30)      # Standard oversold level
        self.rsi_weight = strategy_config.get('rsi_weight', 0.3)         # Increased weight for RSI

        # MACD parameters
        self.macd_fast_period = strategy_config.get('macd_fast_period', 12)
        self.macd_slow_period = strategy_config.get('macd_slow_period', 26)
        self.macd_signal_period = strategy_config.get('macd_signal_period', 9)
        self.macd_weight = strategy_config.get('macd_weight', 0.2)

        # Price velocity parameters
        self.velocity_periods = strategy_config.get('velocity_periods', 3)
        self.velocity_threshold = strategy_config.get('velocity_threshold', 0.02)
        self.velocity_weight = strategy_config.get('velocity_weight', 0.1)

        # ATR breakout parameters
        self.atr_breakout_multiplier = strategy_config.get('atr_breakout_multiplier', 1.5)
        self.atr_breakout_weight = strategy_config.get('atr_breakout_weight', 0.1)

        # Band bounce vs break parameters
        self.band_break_weight = strategy_config.get('band_break_weight', 0.1)

        # Wick rejection parameters
        self.wick_rejection_threshold = strategy_config.get('wick_rejection_threshold', 0.5)
        self.wick_rejection_weight = strategy_config.get('wick_rejection_weight', 0.1)

        # Dynamic band width parameters
        self.band_width_threshold = strategy_config.get('band_width_threshold', 0.05)
        self.band_width_weight = strategy_config.get('band_width_weight', 0.1)

        # Higher timeframe alignment
        self.htf_alignment_weight = strategy_config.get('htf_alignment_weight', 0.15)

    def update_config(self, config):
        """
        Update the strategy configuration.

        Args:
            config (dict): The new configuration.
        """
        logger.info(f"Updating {self.name} Strategy configuration")
        self.config = config
        self._load_parameters()
        logger.info(f"Updated {self.name} Strategy with ATR period: {self.atr_period}, EMA period: {self.ema_period}")

    def calculate_true_range(self, df):
        """
        Calculate True Range.

        Args:
            df (pd.DataFrame): OHLCV DataFrame.

        Returns:
            pd.Series: True Range series.
        """
        high = df['high']
        low = df['low']
        close_prev = df['close'].shift(1)

        # True Range calculation
        tr1 = high - low
        tr2 = (high - close_prev).abs()
        tr3 = (low - close_prev).abs()

        tr = pd.concat([tr1, tr2, tr3], axis=1).max(axis=1)
        return tr

    def calculate_atr(self, df, period):
        """
        Calculate Average True Range (ATR).

        Args:
            df (pd.DataFrame): OHLCV DataFrame.
            period (int): ATR period.

        Returns:
            pd.Series: ATR series.
        """
        tr = self.calculate_true_range(df)
        atr = tr.rolling(window=period).mean()  # Simple moving average of TR
        return atr

    def calculate_rma(self, series, period):
        """
        Calculate Relative Moving Average (RMA) which is used in TradingView's ATR.

        Args:
            series (pd.Series): Input series.
            period (int): RMA period.

        Returns:
            pd.Series: RMA series.
        """
        alpha = 1.0 / period
        return series.ewm(alpha=alpha, adjust=False).mean()

    def calculate_atr_rma(self, df, period):
        """
        Calculate ATR using RMA (as in TradingView).

        Args:
            df (pd.DataFrame): OHLCV DataFrame.
            period (int): ATR period.

        Returns:
            pd.Series: ATR series using RMA.
        """
        tr = self.calculate_true_range(df)
        atr = self.calculate_rma(tr, period)
        return atr

    def calculate_rsi(self, df, period=14):
        """
        Calculate Relative Strength Index (RSI).

        Args:
            df (pd.DataFrame): OHLCV DataFrame.
            period (int): RSI period.

        Returns:
            pd.Series: RSI series.
        """
        delta = df['close'].diff()
        gain = delta.where(delta > 0, 0)
        loss = -delta.where(delta < 0, 0)

        avg_gain = self.calculate_rma(gain, period)
        avg_loss = self.calculate_rma(loss, period)

        rs = avg_gain / avg_loss.replace(0, 1e-10)  # Avoid division by zero
        rsi = 100 - (100 / (1 + rs))

        return rsi

    def calculate_macd(self, df, fast_period=12, slow_period=26, signal_period=9):
        """
        Calculate Moving Average Convergence Divergence (MACD).

        Args:
            df (pd.DataFrame): OHLCV DataFrame.
            fast_period (int): Fast EMA period.
            slow_period (int): Slow EMA period.
            signal_period (int): Signal EMA period.

        Returns:
            dict: Dictionary containing MACD line, signal line, and histogram.
        """
        # Calculate fast and slow EMAs
        fast_ema = df['close'].ewm(span=fast_period, adjust=False).mean()
        slow_ema = df['close'].ewm(span=slow_period, adjust=False).mean()

        # Calculate MACD line
        macd_line = fast_ema - slow_ema

        # Calculate signal line
        signal_line = macd_line.ewm(span=signal_period, adjust=False).mean()

        # Calculate histogram
        histogram = macd_line - signal_line

        return {
            'macd': macd_line,
            'signal': signal_line,
            'histogram': histogram
        }

    def calculate_ema_bands(self, df):
        """
        Calculate EMA and ATR-based bands.
        Uses multipliers from configuration.

        Args:
            df (pd.DataFrame): OHLCV DataFrame.

        Returns:
            dict: Dictionary containing EMA and bands.
        """
        # Calculate EMA
        ema = df['close'].ewm(span=self.ema_period, adjust=False).mean()

        # Calculate ATR using RMA (as in TradingView)
        atr = self.calculate_atr_rma(df, self.atr_period)

        # Calculate bands
        bands = {
            'ema': ema,
            'atr': atr
        }

        # Upper bands
        for mult in self.band_multipliers:
            bands[f'ema_plus_{mult}_atr'] = ema + (atr * mult)

        # Lower bands
        for mult in self.band_multipliers:
            bands[f'ema_minus_{mult}_atr'] = ema - (atr * mult)

        # Always include bands for 2 and 3 multipliers for backward compatibility
        if 2 not in self.band_multipliers:
            bands['ema_plus_2_atr'] = ema + (atr * 2)
            bands['ema_minus_2_atr'] = ema - (atr * 2)

        if 3 not in self.band_multipliers:
            bands['ema_plus_3_atr'] = ema + (atr * 3)
            bands['ema_minus_3_atr'] = ema - (atr * 3)

        return bands

    def generate_signal(self, df, htf_df=None):
        """
        Generate trading signals based on ATR-EMA bands with enhanced features.
        Modified to only use EMA±2 and EMA±3 bands for reduced volatility.
        Respects indicator toggles to enable/disable features.
        """
        try:
            if len(df) < max(self.atr_period, self.ema_period) + 10:
                return {'score': 0.5, 'direction': 'neutral'}

            # Calculate bands
            bands = self.calculate_ema_bands(df)

            # Get the latest values
            close = df['close'].iloc[-1]
            open_price = df['open'].iloc[-1]  # Using open_price to avoid conflict with built-in open()
            high = df['high'].iloc[-1]
            low = df['low'].iloc[-1]
            ema = bands['ema'].iloc[-1]
            atr = bands['atr'].iloc[-1]

            # Upper bands - only using 2 and 3
            ema_plus_2_atr = bands['ema_plus_2_atr'].iloc[-1]
            ema_plus_3_atr = bands['ema_plus_3_atr'].iloc[-1]

            # Lower bands - only using 2 and 3
            ema_minus_2_atr = bands['ema_minus_2_atr'].iloc[-1]
            ema_minus_3_atr = bands['ema_minus_3_atr'].iloc[-1]

            # Initialize signal score (0.5 is neutral)
            signal_score = 0.5

            # Check price position relative to bands - only if EMA and ATR are enabled
            if self.use_ema and self.use_atr:
                if close > ema_plus_3_atr:
                    # Price is above the highest band - potential overbought
                    signal_score = 0.3  # Bearish signal (below sell threshold of 0.4)
                elif close > ema_plus_2_atr:
                    signal_score = 0.4  # Slightly bearish (at sell threshold)
                elif close > ema:
                    # Price is between EMA and EMA+2ATR - neutral to slightly bullish
                    signal_score = 0.55  # Neutral to slightly bullish
                elif close > ema_minus_2_atr:
                    # Price is between EMA-2ATR and EMA - neutral to slightly bearish
                    signal_score = 0.45  # Neutral to slightly bearish
                elif close > ema_minus_3_atr:
                    # Price is between EMA-3ATR and EMA-2ATR - slightly more bearish
                    signal_score = 0.35  # More bearish (below sell threshold)
                else:
                    # Price is below the lowest band - potential oversold
                    signal_score = 0.7  # Bullish (above buy threshold of 0.6)

                # Check for band crossovers (last few candles)
                lookback = min(self.lookback_periods, len(df) - 1)

                # Bullish crossovers - MODIFIED to skip ±1 bands
                if (df['close'].iloc[-lookback-1] < bands['ema_minus_3_atr'].iloc[-lookback-1] and
                    df['close'].iloc[-1] > bands['ema_minus_3_atr'].iloc[-1]):
                    # Crossed above the lowest band
                    signal_score += 0.15

                if (df['close'].iloc[-lookback-1] < bands['ema'].iloc[-lookback-1] and
                    df['close'].iloc[-1] > bands['ema'].iloc[-1]):
                    # Crossed above the EMA
                    signal_score += 0.2

                # Bearish crossovers - MODIFIED to skip ±1 bands
                if (df['close'].iloc[-lookback-1] > bands['ema_plus_3_atr'].iloc[-lookback-1] and
                    df['close'].iloc[-1] < bands['ema_plus_3_atr'].iloc[-1]):
                    # Crossed below the highest band
                    signal_score -= 0.15

                if (df['close'].iloc[-lookback-1] > bands['ema'].iloc[-lookback-1] and
                    df['close'].iloc[-1] < bands['ema'].iloc[-1]):
                    # Crossed below the EMA
                    signal_score -= 0.2

            # ===== STRATEGIC UPGRADES =====

            # Add RSI indicator if enabled
            if self.use_rsi:
                rsi = self.calculate_rsi(df, self.rsi_period)
                if not rsi.empty:
                    current_rsi = rsi.iloc[-1]

                    # Oversold condition (bullish)
                    if current_rsi < self.rsi_oversold:
                        signal_score += self.rsi_weight
                        logger.debug(f"RSI oversold: {current_rsi:.2f} < {self.rsi_oversold}, adding {self.rsi_weight}")

                    # Overbought condition (bearish)
                    elif current_rsi > self.rsi_overbought:
                        signal_score -= self.rsi_weight
                        logger.debug(f"RSI overbought: {current_rsi:.2f} > {self.rsi_overbought}, subtracting {self.rsi_weight}")

            # Add MACD indicator if enabled
            if self.use_macd:
                macd_data = self.calculate_macd(df, self.macd_fast_period, self.macd_slow_period, self.macd_signal_period)

                if not macd_data['macd'].empty and not macd_data['signal'].empty:
                    current_macd = macd_data['macd'].iloc[-1]
                    current_signal = macd_data['signal'].iloc[-1]
                    current_histogram = macd_data['histogram'].iloc[-1]

                    # MACD line crosses above signal line (bullish)
                    if (macd_data['macd'].iloc[-2] < macd_data['signal'].iloc[-2] and
                        current_macd > current_signal):
                        signal_score += self.macd_weight
                        logger.debug(f"MACD bullish crossover, adding {self.macd_weight}")

                    # MACD line crosses below signal line (bearish)
                    elif (macd_data['macd'].iloc[-2] > macd_data['signal'].iloc[-2] and
                          current_macd < current_signal):
                        signal_score -= self.macd_weight
                        logger.debug(f"MACD bearish crossover, subtracting {self.macd_weight}")

                    # MACD histogram direction change (early signal)
                    if len(macd_data['histogram']) > 2:
                        prev_histogram = macd_data['histogram'].iloc[-2]

                        # Histogram turns positive (bullish)
                        if prev_histogram < 0 and current_histogram > 0:
                            signal_score += self.macd_weight * 0.5
                            logger.debug(f"MACD histogram turns positive, adding {self.macd_weight * 0.5}")

                        # Histogram turns negative (bearish)
                        elif prev_histogram > 0 and current_histogram < 0:
                            signal_score -= self.macd_weight * 0.5
                            logger.debug(f"MACD histogram turns negative, subtracting {self.macd_weight * 0.5}")

            # 1. Add EMA Slope Strength
            if self.use_ema_slope and len(bands['ema']) > self.ema_slope_periods:
                # Calculate EMA slope over the specified period
                ema_slope = bands['ema'].iloc[-1] - bands['ema'].iloc[-self.ema_slope_periods]
                # Normalize the slope as a percentage of the EMA value
                slope_strength = ema_slope / bands['ema'].iloc[-self.ema_slope_periods]
                # Apply the slope strength to the signal score
                signal_score += slope_strength * self.ema_slope_weight

                # Log the EMA slope impact
                logger.debug(f"EMA Slope: {slope_strength:.4f}, Impact: {slope_strength * self.ema_slope_weight:.4f}")

            # 2. Measure Price Velocity / Momentum
            if self.use_price_velocity and len(df) > self.velocity_periods:
                # Calculate price velocity over the specified period
                velocity = (df['close'].iloc[-1] - df['close'].iloc[-self.velocity_periods]) / df['close'].iloc[-self.velocity_periods]

                # If price is moving too fast, reduce signal score (wait for slowdown)
                if abs(velocity) > self.velocity_threshold:
                    signal_score -= self.velocity_weight
                    logger.debug(f"Price moving too fast: {velocity:.4f}, reducing signal score by {self.velocity_weight}")

            # 3. Use ATR Breakout Filter
            if self.use_atr_breakout:
                # Check if current candle range is above recent average ATR
                recent_range = high - low
                if len(bands['atr']) >= 5:
                    avg_atr = bands['atr'].iloc[-5:].mean()

                    if recent_range > avg_atr * self.atr_breakout_multiplier:
                        # Strong price move detected
                        if close > open_price:  # Bullish breakout
                            signal_score += self.atr_breakout_weight
                            logger.debug(f"Bullish ATR breakout: range {recent_range:.4f} > {avg_atr * self.atr_breakout_multiplier:.4f}")
                        else:  # Bearish breakout
                            signal_score -= self.atr_breakout_weight
                            logger.debug(f"Bearish ATR breakout: range {recent_range:.4f} > {avg_atr * self.atr_breakout_multiplier:.4f}")

            # 4. Band Bounce vs. Band Break Logic
            if self.use_band_break and len(bands['ema']) > 5:
                ema_slope_short = bands['ema'].iloc[-1] - bands['ema'].iloc[-5]

                # Bullish trend continuation
                if close > ema_plus_2_atr and ema_slope_short > 0 and close > open_price:
                    signal_score += self.band_break_weight
                    logger.debug(f"Bullish trend continuation detected, adding {self.band_break_weight}")

                # Bearish trend continuation
                elif close < ema_minus_2_atr and ema_slope_short < 0 and close < open_price:
                    signal_score -= self.band_break_weight
                    logger.debug(f"Bearish trend continuation detected, subtracting {self.band_break_weight}")

            # 5. Intrabar Rejection Detection
            if self.use_wick_rejection:
                # Calculate wick sizes
                wick_top = high - max(close, open_price)
                wick_bottom = min(close, open_price) - low
                candle_range = high - low

                # Avoid division by zero
                if candle_range > 0:
                    # Check for upper wick rejection (bearish)
                    if wick_top > candle_range * self.wick_rejection_threshold and close < ema_plus_2_atr:
                        signal_score -= self.wick_rejection_weight
                        logger.debug(f"Upper wick rejection detected, subtracting {self.wick_rejection_weight}")

                    # Check for lower wick rejection (bullish)
                    if wick_bottom > candle_range * self.wick_rejection_threshold and close > ema_minus_2_atr:
                        signal_score += self.wick_rejection_weight
                        logger.debug(f"Lower wick rejection detected, adding {self.wick_rejection_weight}")

            # 6. Higher Timeframe Alignment (if enabled and htf_df is provided)
            if self.use_htf_alignment and htf_df is not None and len(htf_df) > max(self.atr_period, self.ema_period) + 10:
                # Generate signal on higher timeframe
                htf_signal = self.generate_signal(htf_df)
                htf_direction = htf_signal['direction']

                # If current direction doesn't match higher timeframe direction, reduce signal strength
                if htf_direction != 'neutral':
                    if (signal_score > 0.5 and htf_direction == 'sell') or (signal_score < 0.5 and htf_direction == 'buy'):
                        signal_score = 0.5 + (signal_score - 0.5) * (1 - self.htf_alignment_weight)
                        logger.debug(f"Higher timeframe mismatch: {htf_direction}, reducing signal strength")

            # 7. Dynamic Band Width Detection
            if self.use_band_width:
                # Calculate band width as a percentage of EMA
                band_width = ema_plus_3_atr - ema_minus_3_atr
                band_width_pct = band_width / ema

                # If bands are wide (volatile market), reduce signal strength unless price is at extreme
                if band_width_pct > self.band_width_threshold:
                    # Only reduce if we're not at an extreme (which could be a good entry)
                    if close > ema_minus_2_atr and close < ema_plus_2_atr:
                        # Reduce signal strength (move toward neutral)
                        signal_score = 0.5 + (signal_score - 0.5) * (1 - self.band_width_weight)
                        logger.debug(f"Wide bands detected: {band_width_pct:.4f}, reducing signal strength")

            # Ensure score is between 0 and 1
            signal_score = max(0.0, min(1.0, signal_score))

            # Log the final signal score for debugging
            logger.debug(f"Final signal score: {signal_score:.4f} (buy >= {self.buy_threshold}, sell <= {self.sell_threshold})")

            # Determine direction based on signal mode
            if self.signal_mode == 'threshold':
                # Threshold mode: Use signal score with thresholds
                # Buy when score is high (above buy_threshold, default 0.6)
                # Sell when score is low (below sell_threshold, default 0.4)
                timestamp = df.index[-1].strftime('%Y-%m-%d %H:%M:%S') if hasattr(df.index[-1], 'strftime') else str(df.index[-1])

                if signal_score >= self.buy_threshold:
                    direction = 'buy'  # Long signal
                    # Get the current RSI value if available
                    rsi_value = "N/A"
                    if self.use_rsi:
                        rsi = self.calculate_rsi(df, self.rsi_period)
                        if not rsi.empty:
                            rsi_value = f"{rsi.iloc[-1]:.2f}"

                    # Log the signal with detailed information
                    logger.info(f"🟢 LONG SIGNAL @ {timestamp} | THRESHOLD MODE | Score: {signal_score:.2f} >= {self.buy_threshold} | RSI: {rsi_value} | Price: {df['close'].iloc[-1]:.2f}")

                elif signal_score <= self.sell_threshold:
                    direction = 'sell'  # Short signal
                    # Get the current RSI value if available
                    rsi_value = "N/A"
                    if self.use_rsi:
                        rsi = self.calculate_rsi(df, self.rsi_period)
                        if not rsi.empty:
                            rsi_value = f"{rsi.iloc[-1]:.2f}"

                    # Log the signal with detailed information
                    logger.info(f"🔴 SHORT SIGNAL @ {timestamp} | THRESHOLD MODE | Score: {signal_score:.2f} <= {self.sell_threshold} | RSI: {rsi_value} | Price: {df['close'].iloc[-1]:.2f}")

                else:
                    direction = 'neutral'  # No clear signal
            else:
                # Crossover mode: Check for band crossovers
                direction = 'neutral'  # Default to neutral
                lookback = min(self.lookback_periods, len(df) - 1)

                # Calculate RSI if needed for filtering
                current_rsi = None
                if self.use_rsi_filter or self.use_rsi:
                    rsi = self.calculate_rsi(df, self.rsi_period)
                    if not rsi.empty:
                        current_rsi = rsi.iloc[-1]
                        # Log RSI value with color indicators for overbought/oversold
                        rsi_status = ""
                        if current_rsi <= self.rsi_oversold:
                            rsi_status = "🟢 OVERSOLD"
                        elif current_rsi >= self.rsi_overbought:
                            rsi_status = "🔴 OVERBOUGHT"

                        timestamp = df.index[-1].strftime('%Y-%m-%d %H:%M:%S') if hasattr(df.index[-1], 'strftime') else str(df.index[-1])
                        logger.info(f"RSI @ {timestamp}: {current_rsi:.2f} {rsi_status} | Price: {df['close'].iloc[-1]:.2f}")

                # Check for long signal (price crosses above the selected bands)
                if self.enable_long_crossovers:
                    # Check if RSI is below oversold threshold (if RSI filter is enabled)
                    rsi_long_condition = True
                    if self.use_rsi_filter and current_rsi is not None:
                        rsi_long_condition = current_rsi < self.rsi_oversold
                        if not rsi_long_condition:
                            timestamp = df.index[-1].strftime('%Y-%m-%d %H:%M:%S') if hasattr(df.index[-1], 'strftime') else str(df.index[-1])
                            logger.info(f"⚠️ LONG SIGNAL REJECTED @ {timestamp} | RSI FILTER | Current RSI: {current_rsi:.2f} not below threshold: {self.rsi_oversold}")

                    if rsi_long_condition:
                        # Check EMA crossover (center line)
                        if self.use_ema_cross and 'ema' in bands:
                            prev_close = df['close'].iloc[-lookback-1]
                            current_close = df['close'].iloc[-1]
                            prev_ema = bands['ema'].iloc[-lookback-1]
                            current_ema = bands['ema'].iloc[-1]

                            if (prev_close < prev_ema and current_close > current_ema):
                                direction = 'buy'
                                timestamp = df.index[-1].strftime('%Y-%m-%d %H:%M:%S') if hasattr(df.index[-1], 'strftime') else str(df.index[-1])
                                rsi_info = f"RSI: {current_rsi:.2f} (< {self.rsi_oversold})" if current_rsi is not None else "RSI: N/A"
                                price_info = f"Price: {current_close:.2f} crossed above EMA: {current_ema:.2f} (prev: {prev_close:.2f} < {prev_ema:.2f})"
                                logger.info(f"🟢 LONG SIGNAL @ {timestamp} | {price_info} | {rsi_info} | TRIGGER: EMA CENTER CROSSOVER")

                        # Check EMA-1ATR crossover
                        if direction == 'neutral' and self.use_ema_minus_1_cross and 'ema_minus_1_atr' in bands:
                            prev_close = df['close'].iloc[-lookback-1]
                            current_close = df['close'].iloc[-1]
                            prev_band = bands['ema_minus_1_atr'].iloc[-lookback-1]
                            current_band = bands['ema_minus_1_atr'].iloc[-1]

                            if (prev_close < prev_band and current_close > current_band):
                                direction = 'buy'
                                timestamp = df.index[-1].strftime('%Y-%m-%d %H:%M:%S') if hasattr(df.index[-1], 'strftime') else str(df.index[-1])
                                rsi_info = f"RSI: {current_rsi:.2f} (< {self.rsi_oversold})" if current_rsi is not None else "RSI: N/A"
                                price_info = f"Price: {current_close:.2f} crossed above EMA-1ATR: {current_band:.2f} (prev: {prev_close:.2f} < {prev_band:.2f})"
                                logger.info(f"🟢 LONG SIGNAL @ {timestamp} | {price_info} | {rsi_info} | TRIGGER: EMA-1ATR CROSSOVER")

                        # Check EMA-2ATR crossover
                        if direction == 'neutral' and self.use_ema_minus_2_cross and 'ema_minus_2_atr' in bands:
                            prev_close = df['close'].iloc[-lookback-1]
                            current_close = df['close'].iloc[-1]
                            prev_band = bands['ema_minus_2_atr'].iloc[-lookback-1]
                            current_band = bands['ema_minus_2_atr'].iloc[-1]

                            if (prev_close < prev_band and current_close > current_band):
                                direction = 'buy'
                                timestamp = df.index[-1].strftime('%Y-%m-%d %H:%M:%S') if hasattr(df.index[-1], 'strftime') else str(df.index[-1])
                                rsi_info = f"RSI: {current_rsi:.2f} (< {self.rsi_oversold})" if current_rsi is not None else "RSI: N/A"
                                price_info = f"Price: {current_close:.2f} crossed above EMA-2ATR: {current_band:.2f} (prev: {prev_close:.2f} < {prev_band:.2f})"
                                logger.info(f"🟢 LONG SIGNAL @ {timestamp} | {price_info} | {rsi_info} | TRIGGER: EMA-2ATR CROSSOVER")

                        # Check EMA-3ATR crossover
                        if direction == 'neutral' and self.use_ema_minus_3_cross and 'ema_minus_3_atr' in bands:
                            prev_close = df['close'].iloc[-lookback-1]
                            current_close = df['close'].iloc[-1]
                            prev_band = bands['ema_minus_3_atr'].iloc[-lookback-1]
                            current_band = bands['ema_minus_3_atr'].iloc[-1]

                            if (prev_close < prev_band and current_close > current_band):
                                direction = 'buy'
                                timestamp = df.index[-1].strftime('%Y-%m-%d %H:%M:%S') if hasattr(df.index[-1], 'strftime') else str(df.index[-1])
                                rsi_info = f"RSI: {current_rsi:.2f} (< {self.rsi_oversold})" if current_rsi is not None else "RSI: N/A"
                                price_info = f"Price: {current_close:.2f} crossed above EMA-3ATR: {current_band:.2f} (prev: {prev_close:.2f} < {prev_band:.2f})"
                                logger.info(f"🟢 LONG SIGNAL @ {timestamp} | {price_info} | {rsi_info} | TRIGGER: EMA-3ATR CROSSOVER")

                # Check for short signal (price crosses below the selected bands)
                if direction == 'neutral' and self.enable_short_crossovers:
                    # Check if RSI is above overbought threshold (if RSI filter is enabled)
                    rsi_short_condition = True
                    if self.use_rsi_filter and current_rsi is not None:
                        rsi_short_condition = current_rsi > self.rsi_overbought
                        if not rsi_short_condition:
                            timestamp = df.index[-1].strftime('%Y-%m-%d %H:%M:%S') if hasattr(df.index[-1], 'strftime') else str(df.index[-1])
                            logger.info(f"⚠️ SHORT SIGNAL REJECTED @ {timestamp} | RSI FILTER | Current RSI: {current_rsi:.2f} not above threshold: {self.rsi_overbought}")

                    if rsi_short_condition:
                        # Check EMA crossover (center line)
                        if self.use_ema_cross and 'ema' in bands:
                            prev_close = df['close'].iloc[-lookback-1]
                            current_close = df['close'].iloc[-1]
                            prev_ema = bands['ema'].iloc[-lookback-1]
                            current_ema = bands['ema'].iloc[-1]

                            if (prev_close > prev_ema and current_close < current_ema):
                                direction = 'sell'
                                timestamp = df.index[-1].strftime('%Y-%m-%d %H:%M:%S') if hasattr(df.index[-1], 'strftime') else str(df.index[-1])
                                rsi_info = f"RSI: {current_rsi:.2f} (> {self.rsi_overbought})" if current_rsi is not None else "RSI: N/A"
                                price_info = f"Price: {current_close:.2f} crossed below EMA: {current_ema:.2f} (prev: {prev_close:.2f} > {prev_ema:.2f})"
                                logger.info(f"🔴 SHORT SIGNAL @ {timestamp} | {price_info} | {rsi_info} | TRIGGER: EMA CENTER CROSSOVER")

                        # Check EMA+1ATR crossover
                        if direction == 'neutral' and self.use_ema_plus_1_cross and 'ema_plus_1_atr' in bands:
                            prev_close = df['close'].iloc[-lookback-1]
                            current_close = df['close'].iloc[-1]
                            prev_band = bands['ema_plus_1_atr'].iloc[-lookback-1]
                            current_band = bands['ema_plus_1_atr'].iloc[-1]

                            if (prev_close > prev_band and current_close < current_band):
                                direction = 'sell'
                                timestamp = df.index[-1].strftime('%Y-%m-%d %H:%M:%S') if hasattr(df.index[-1], 'strftime') else str(df.index[-1])
                                rsi_info = f"RSI: {current_rsi:.2f} (> {self.rsi_overbought})" if current_rsi is not None else "RSI: N/A"
                                price_info = f"Price: {current_close:.2f} crossed below EMA+1ATR: {current_band:.2f} (prev: {prev_close:.2f} > {prev_band:.2f})"
                                logger.info(f"🔴 SHORT SIGNAL @ {timestamp} | {price_info} | {rsi_info} | TRIGGER: EMA+1ATR CROSSOVER")

                        # Check EMA+2ATR crossover
                        if direction == 'neutral' and self.use_ema_plus_2_cross and 'ema_plus_2_atr' in bands:
                            prev_close = df['close'].iloc[-lookback-1]
                            current_close = df['close'].iloc[-1]
                            prev_band = bands['ema_plus_2_atr'].iloc[-lookback-1]
                            current_band = bands['ema_plus_2_atr'].iloc[-1]

                            if (prev_close > prev_band and current_close < current_band):
                                direction = 'sell'
                                timestamp = df.index[-1].strftime('%Y-%m-%d %H:%M:%S') if hasattr(df.index[-1], 'strftime') else str(df.index[-1])
                                rsi_info = f"RSI: {current_rsi:.2f} (> {self.rsi_overbought})" if current_rsi is not None else "RSI: N/A"
                                price_info = f"Price: {current_close:.2f} crossed below EMA+2ATR: {current_band:.2f} (prev: {prev_close:.2f} > {prev_band:.2f})"
                                logger.info(f"🔴 SHORT SIGNAL @ {timestamp} | {price_info} | {rsi_info} | TRIGGER: EMA+2ATR CROSSOVER")

                        # Check EMA+3ATR crossover
                        if direction == 'neutral' and self.use_ema_plus_3_cross and 'ema_plus_3_atr' in bands:
                            prev_close = df['close'].iloc[-lookback-1]
                            current_close = df['close'].iloc[-1]
                            prev_band = bands['ema_plus_3_atr'].iloc[-lookback-1]
                            current_band = bands['ema_plus_3_atr'].iloc[-1]

                            if (prev_close > prev_band and current_close < current_band):
                                direction = 'sell'
                                timestamp = df.index[-1].strftime('%Y-%m-%d %H:%M:%S') if hasattr(df.index[-1], 'strftime') else str(df.index[-1])
                                rsi_info = f"RSI: {current_rsi:.2f} (> {self.rsi_overbought})" if current_rsi is not None else "RSI: N/A"
                                price_info = f"Price: {current_close:.2f} crossed below EMA+3ATR: {current_band:.2f} (prev: {prev_close:.2f} > {prev_band:.2f})"
                                logger.info(f"🔴 SHORT SIGNAL @ {timestamp} | {price_info} | {rsi_info} | TRIGGER: EMA+3ATR CROSSOVER")

            # Return the entire band Series for plotting
            return {
                'score': signal_score,
                'direction': direction,
                'bands': bands,  # Return the entire bands dictionary with Series
                'metrics': {
                    'ema_slope': locals().get('slope_strength', 0),
                    'price_velocity': locals().get('velocity', 0),
                    'atr_breakout': locals().get('recent_range', 0) / locals().get('avg_atr', 1),
                    'band_width': locals().get('band_width_pct', 0),
                    'rsi': locals().get('current_rsi', 0),
                    'macd': locals().get('current_macd', 0),
                    'macd_signal': locals().get('current_signal', 0),
                    'macd_histogram': locals().get('current_histogram', 0)
                },
                'enabled_indicators': {
                    'ema': self.use_ema,
                    'atr': self.use_atr,
                    'rsi': self.use_rsi,
                    'macd': self.use_macd,
                    'ema_slope': self.use_ema_slope,
                    'price_velocity': self.use_price_velocity,
                    'atr_breakout': self.use_atr_breakout,
                    'band_break': self.use_band_break,
                    'wick_rejection': self.use_wick_rejection,
                    'band_width': self.use_band_width,
                    'htf_alignment': self.use_htf_alignment
                },
                'signal_config': {
                    'mode': self.signal_mode,
                    'use_ema_cross': self.use_ema_cross,
                    'use_ema_plus_1_cross': self.use_ema_plus_1_cross,
                    'use_ema_plus_2_cross': self.use_ema_plus_2_cross,
                    'use_ema_plus_3_cross': self.use_ema_plus_3_cross,
                    'use_ema_minus_1_cross': self.use_ema_minus_1_cross,
                    'use_ema_minus_2_cross': self.use_ema_minus_2_cross,
                    'use_ema_minus_3_cross': self.use_ema_minus_3_cross,
                    'enable_long_crossovers': self.enable_long_crossovers,
                    'enable_short_crossovers': self.enable_short_crossovers,
                    'use_rsi_filter': self.use_rsi_filter,
                    'rsi_oversold': self.rsi_oversold,
                    'rsi_overbought': self.rsi_overbought,
                    'buy_threshold': self.buy_threshold,
                    'sell_threshold': self.sell_threshold
                }
            }

        except Exception as e:
            logger.error(f"Error generating ATR-EMA Bands signal: {e}", exc_info=True)
            return {'score': 0.5, 'direction': 'neutral'}
