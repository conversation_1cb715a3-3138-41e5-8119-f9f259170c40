#!/usr/bin/env python3
"""
EPINNOX UNIFIED LAUNCHER - SIMPLIFIED VERSION
Working unified launcher that bypasses WebSocket initialization issues

USAGE:
    python launch_epinnox_unified_simple.py                                    # GUI with paper trading
    python launch_epinnox_unified_simple.py --live --risk ultra-conservative   # GUI with live trading
    python launch_epinnox_unified_simple.py --paper --headless                 # Headless paper trading
"""

import sys
import os
import asyncio
import argparse
import logging
from datetime import datetime

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Configure Unicode-safe logging
try:
    from core.unicode_safe_logging import configure_unicode_safe_logging
    configure_unicode_safe_logging()
except ImportError:
    pass

logger = logging.getLogger(__name__)

# Check PyQt5 availability
try:
    from PyQt5.QtWidgets import QApplication
    PYQT_AVAILABLE = True
except ImportError:
    PYQT_AVAILABLE = False

class EpinnoxUnifiedLauncherSimple:
    """
    Simplified unified launcher that works around WebSocket initialization issues
    """
    
    def __init__(self, args):
        self.args = args
        self.mode = args.mode
        self.risk_level = args.risk
        self.max_capital = args.capital
        self.headless = args.headless
        
        # Core components
        self.components = {}
        self.autonomous_trader = None
        self.dashboard = None
        
        logger.info(f"Epinnox Unified Launcher (Simple) initialized - Mode: {self.mode}, Risk: {self.risk_level}")
    
    async def initialize_core_components(self):
        """Initialize core trading components"""
        
        try:
            logger.info("Initializing core components...")
            
            # Initialize risk manager
            from core.dynamic_risk_manager import dynamic_risk_manager
            try:
                # Convert risk level to proper format
                risk_level_map = {
                    'ultra-conservative': 'Ultra-Conservative',
                    'conservative': 'Conservative',
                    'moderate': 'Moderate',
                    'aggressive': 'Aggressive',
                    'high-risk': 'High-Risk'
                }
                formatted_risk = risk_level_map.get(self.risk_level, 'Ultra-Conservative')
                dynamic_risk_manager.set_risk_level(formatted_risk, "Unified launcher initialization")
            except Exception as e:
                logger.warning(f"Risk level setting failed: {e}")
            self.components['risk_manager'] = dynamic_risk_manager
            
            # Initialize emergency coordinator
            from core.emergency_stop_coordinator import emergency_coordinator
            self.components['emergency_coordinator'] = emergency_coordinator
            
            # Initialize execution engine
            from core.unified_execution_engine import unified_execution_engine
            self.components['execution_engine'] = unified_execution_engine
            
            # Initialize timer coordinator
            from core.timer_coordinator import timer_coordinator
            self.components['timer_coordinator'] = timer_coordinator
            
            # Initialize ScalperGPT
            try:
                from core.scalper_gpt import ScalperGPT
                scalper_gpt = ScalperGPT()
                self.components['scalper_gpt'] = scalper_gpt
            except Exception as e:
                logger.warning(f"ScalperGPT initialization failed: {e}")
                self.components['scalper_gpt'] = None
            
            logger.info("Core components initialized successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to initialize core components: {e}")
            return False
    
    async def create_simple_autonomous_trader(self):
        """Create a simplified autonomous trader that bypasses WebSocket issues"""
        
        try:
            logger.info(f"Creating simplified autonomous trader for {self.mode} mode...")
            
            # Use the working robust autonomous trader approach
            from start_autonomous_trading_robust import RobustAutonomousTrader
            
            # Create trader with appropriate settings
            self.autonomous_trader = RobustAutonomousTrader()
            
            # Configure for the specified mode
            if self.mode == 'live':
                self.autonomous_trader.demo_mode = False
                logger.info("Configured for LIVE trading")
            else:
                self.autonomous_trader.demo_mode = True
                logger.info(f"Configured for {self.mode.upper()} trading")
            
            logger.info("Simplified autonomous trader created successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to create simplified autonomous trader: {e}")
            return False
    
    async def create_gui_application(self):
        """Create GUI application if not in headless mode"""
        
        if self.headless:
            return True
        
        if not PYQT_AVAILABLE:
            logger.error("PyQt5 not available - falling back to headless mode")
            self.headless = True
            return True
        
        try:
            logger.info("Creating GUI application...")
            
            # Create QApplication
            self.app = QApplication(sys.argv)
            
            # Create enhanced monitoring dashboard
            from gui.integrated_monitoring_dashboard import IntegratedMonitoringDashboard
            self.dashboard = IntegratedMonitoringDashboard()
            
            # Apply Matrix theme
            from gui.matrix_theme import MatrixTheme
            self.app.setStyleSheet(MatrixTheme.get_stylesheet())
            
            logger.info("GUI application created successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to create GUI application: {e}")
            return False
    
    async def start_gui_mode(self):
        """Start in GUI mode"""
        
        try:
            logger.info("Starting GUI mode...")
            
            if not self.dashboard:
                logger.error("No dashboard available for GUI mode")
                return False
            
            # Show dashboard
            self.dashboard.show()
            
            # Start autonomous trading if enabled
            if self.mode != 'gui-only' and self.autonomous_trader:
                success = await self.start_autonomous_trading()
                if success:
                    logger.info("Autonomous trading started successfully")
                else:
                    logger.warning("Failed to start autonomous trading")
            
            logger.info("GUI mode started successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to start GUI mode: {e}")
            return False
    
    async def start_headless_mode(self):
        """Start in headless mode"""
        
        try:
            logger.info("Starting headless mode...")
            
            if not self.autonomous_trader:
                logger.error("No autonomous trader available for headless mode")
                return False
            
            # Start autonomous trading
            success = await self.start_autonomous_trading()
            
            if success:
                logger.info("Headless autonomous trading started successfully")
                
                # Keep running until interrupted
                try:
                    while True:
                        await asyncio.sleep(10)
                        
                        # Log periodic status
                        logger.info(f"Trading status - Mode: {self.mode.upper()}")
                        
                except KeyboardInterrupt:
                    logger.info("Stopping headless trading...")
                    await self.stop_autonomous_trading()
                
                return True
            else:
                logger.error("Failed to start headless autonomous trading")
                return False
                
        except Exception as e:
            logger.error(f"Failed to start headless mode: {e}")
            return False
    
    async def start_autonomous_trading(self):
        """Start autonomous trading using the robust approach"""
        
        try:
            if not self.autonomous_trader:
                logger.error("No autonomous trader available")
                return False
            
            # Start the robust autonomous trading
            await self.autonomous_trader.start_robust_autonomous_trading()
            return True
            
        except Exception as e:
            logger.error(f"Failed to start autonomous trading: {e}")
            return False
    
    async def stop_autonomous_trading(self):
        """Stop autonomous trading"""
        
        try:
            if self.autonomous_trader:
                # Stop the autonomous trader
                if hasattr(self.autonomous_trader, 'stop_trading'):
                    self.autonomous_trader.stop_trading()
                logger.info("Autonomous trading stopped")
            
        except Exception as e:
            logger.error(f"Error stopping autonomous trading: {e}")
    
    async def run(self):
        """Main run method"""
        
        try:
            logger.info(f"Starting unified system in {self.mode} mode...")
            
            # Initialize core components
            if not await self.initialize_core_components():
                logger.error("Failed to initialize core components")
                return False
            
            # Create simplified autonomous trader
            if not await self.create_simple_autonomous_trader():
                logger.error("Failed to create autonomous trader")
                return False
            
            # Create GUI if needed
            if not await self.create_gui_application():
                logger.error("Failed to create GUI application")
                return False
            
            # Start appropriate mode
            if self.headless:
                success = await self.start_headless_mode()
            else:
                success = await self.start_gui_mode()
                if success and self.dashboard:
                    # Run GUI event loop
                    self.app.exec_()
            
            if success:
                logger.info("✅ Unified system started successfully")
                return True
            else:
                logger.error("❌ Failed to start unified system")
                return False
            
        except Exception as e:
            logger.error(f"Error running unified system: {e}")
            return False
        
        finally:
            await self.shutdown()
    
    async def shutdown(self):
        """Shutdown the unified system"""
        
        try:
            logger.info("Shutting down unified system...")
            
            # Stop autonomous trading
            await self.stop_autonomous_trading()
            
            # Stop timer coordinator
            if 'timer_coordinator' in self.components:
                timer_coord = self.components['timer_coordinator']
                if hasattr(timer_coord, 'stop'):
                    try:
                        if asyncio.iscoroutinefunction(timer_coord.stop):
                            await timer_coord.stop()
                        else:
                            timer_coord.stop()
                    except Exception as e:
                        logger.warning(f"Error stopping timer coordinator: {e}")
            
            logger.info("Unified system shutdown complete")
            
        except Exception as e:
            logger.error(f"Error during shutdown: {e}")

def parse_arguments():
    """Parse command-line arguments"""
    
    parser = argparse.ArgumentParser(
        description="Epinnox Unified Autonomous Trading Launcher (Simplified)",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python launch_epinnox_unified_simple.py                                    # GUI with paper trading
  python launch_epinnox_unified_simple.py --live --risk ultra-conservative   # GUI with live trading
  python launch_epinnox_unified_simple.py --paper --headless                 # Headless paper trading
  python launch_epinnox_unified_simple.py --simulation                       # Simulation mode
        """
    )
    
    # Mode selection
    parser.add_argument('--mode', 
                       choices=['gui', 'live', 'paper', 'simulation', 'gui-only'], 
                       default='paper',
                       help='Trading mode (default: paper)')
    
    # Risk level
    parser.add_argument('--risk', 
                       choices=['ultra-conservative', 'conservative', 'moderate', 'aggressive', 'high-risk'], 
                       default='ultra-conservative',
                       help='Risk level (default: ultra-conservative)')
    
    # Capital limit
    parser.add_argument('--capital', 
                       type=float, 
                       default=100.0,
                       help='Maximum trading capital in USDT (default: 100.0)')
    
    # Headless mode
    parser.add_argument('--headless', 
                       action='store_true',
                       help='Run without GUI')
    
    # Convenience flags
    parser.add_argument('--live', 
                       action='store_const', 
                       const='live', 
                       dest='mode',
                       help='Start in live trading mode')
    
    parser.add_argument('--paper', 
                       action='store_const', 
                       const='paper', 
                       dest='mode',
                       help='Start in paper trading mode')
    
    parser.add_argument('--simulation', 
                       action='store_const', 
                       const='simulation', 
                       dest='mode',
                       help='Start in simulation mode')
    
    parser.add_argument('--gui-only', 
                       action='store_const', 
                       const='gui-only', 
                       dest='mode',
                       help='Start GUI monitoring only (no trading)')
    
    return parser.parse_args()

async def main():
    """Main launcher function"""
    
    try:
        # Parse arguments
        args = parse_arguments()
        
        # Display banner
        print("🚀 EPINNOX UNIFIED AUTONOMOUS TRADING LAUNCHER (SIMPLIFIED)")
        print("=" * 70)
        print(f"Mode: {args.mode.upper()}")
        print(f"Risk Level: {args.risk.upper()}")
        print(f"Max Capital: ${args.capital:.2f} USDT")
        print(f"Headless: {'YES' if args.headless else 'NO'}")
        print("=" * 70)
        
        # Create and run launcher
        launcher = EpinnoxUnifiedLauncherSimple(args)
        success = await launcher.run()
        
        if success:
            print("✅ Unified system completed successfully")
            return 0
        else:
            print("❌ Failed to start unified system")
            return 1
        
    except KeyboardInterrupt:
        print("\n🛑 Interrupted by user")
        return 0
    except Exception as e:
        print(f"\n❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
