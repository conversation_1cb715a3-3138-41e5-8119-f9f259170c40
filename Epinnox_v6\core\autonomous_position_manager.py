"""
Autonomous Position Manager for Epinnox Trading System
Provides complete autonomous TP/SL lifecycle management with trailing stops
"""

import asyncio
import logging
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, field
from enum import Enum
import numpy as np

logger = logging.getLogger(__name__)

class PositionStatus(Enum):
    OPEN = "open"
    CLOSING = "closing"
    CLOSED = "closed"
    FAILED = "failed"

class ExitReason(Enum):
    TAKE_PROFIT = "take_profit"
    STOP_LOSS = "stop_loss"
    TRAILING_STOP = "trailing_stop"
    TIME_BASED = "time_based"
    MANUAL = "manual"
    EMERGENCY = "emergency"
    RISK_LIMIT = "risk_limit"

@dataclass
class PositionLevels:
    """Position stop-loss and take-profit levels"""
    stop_loss: float
    take_profit: float
    trailing_stop: Optional[float] = None
    trailing_distance: float = 0.02  # 2% trailing distance
    break_even_stop: bool = False
    partial_tp_levels: List[Tuple[float, float]] = field(default_factory=list)  # (price, percentage)

@dataclass
class ManagedPosition:
    """Autonomous managed position"""
    id: str
    symbol: str
    side: str  # 'long' or 'short'
    size: float
    entry_price: float
    entry_time: datetime
    current_price: float
    levels: PositionLevels
    status: PositionStatus
    unrealized_pnl: float = 0.0
    unrealized_pnl_pct: float = 0.0
    max_profit: float = 0.0
    max_drawdown: float = 0.0
    last_update: datetime = field(default_factory=datetime.now)
    exit_reason: Optional[ExitReason] = None
    exit_price: Optional[float] = None
    exit_time: Optional[datetime] = None
    metadata: Dict[str, Any] = field(default_factory=dict)

class AutonomousPositionManager:
    """
    Autonomous Position Manager that provides:
    1. Complete TP/SL lifecycle management
    2. Trailing stop-loss functionality
    3. Dynamic level adjustment based on market conditions
    4. Partial profit taking
    5. Risk-based position management
    6. Emergency exit capabilities
    """
    
    def __init__(self, config: Dict[str, Any] = None):
        """Initialize the autonomous position manager"""
        self.config = config or {}
        
        # Core configuration
        self.default_stop_loss_pct = self.config.get('default_stop_loss_pct', 0.02)  # 2%
        self.default_take_profit_pct = self.config.get('default_take_profit_pct', 0.04)  # 4%
        self.trailing_stop_activation = self.config.get('trailing_stop_activation', 0.015)  # 1.5%
        self.break_even_activation = self.config.get('break_even_activation', 0.01)  # 1%
        
        # Position tracking
        self.positions: Dict[str, ManagedPosition] = {}
        self.position_counter = 0
        
        # Monitoring
        self.monitoring_enabled = True
        self.monitoring_interval = self.config.get('monitoring_interval', 5)  # 5 seconds
        self.monitoring_task = None
        
        # Execution interface
        self.execution_engine = None
        self.market_data_provider = None
        
        # Performance tracking
        self.performance_metrics = {
            'total_positions': 0,
            'successful_exits': 0,
            'stop_loss_exits': 0,
            'take_profit_exits': 0,
            'trailing_stop_exits': 0,
            'avg_hold_time': 0.0,
            'avg_profit_pct': 0.0,
            'max_concurrent_positions': 0
        }
        
        logger.info("[POSITION_MANAGER] Autonomous Position Manager initialized")
    
    async def initialize(self, execution_engine, market_data_provider):
        """Initialize with execution engine and market data provider"""
        self.execution_engine = execution_engine
        self.market_data_provider = market_data_provider
        
        # Start monitoring loop
        await self.start_monitoring()
        
        logger.info("[POSITION_MANAGER] Autonomous Position Manager ready")
        return True
    
    async def start_monitoring(self):
        """Start the position monitoring loop"""
        if self.monitoring_task:
            return
        
        self.monitoring_enabled = True
        self.monitoring_task = asyncio.create_task(self._monitoring_loop())
        
        logger.info(f"[POSITION_MANAGER] Position monitoring started (interval: {self.monitoring_interval}s)")
    
    async def stop_monitoring(self):
        """Stop the position monitoring loop"""
        self.monitoring_enabled = False
        
        if self.monitoring_task:
            self.monitoring_task.cancel()
            try:
                await self.monitoring_task
            except asyncio.CancelledError:
                pass
        
        logger.info("[POSITION_MANAGER] Position monitoring stopped")
    
    async def _monitoring_loop(self):
        """Main position monitoring loop"""
        while self.monitoring_enabled:
            try:
                await self._monitor_all_positions()
                await asyncio.sleep(self.monitoring_interval)
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"[POSITION_MANAGER] Monitoring loop error: {e}")
                await asyncio.sleep(self.monitoring_interval)
    
    async def _monitor_all_positions(self):
        """Monitor all open positions"""
        if not self.positions:
            return
        
        # Update current prices for all positions
        symbols = list(set(pos.symbol for pos in self.positions.values() if pos.status == PositionStatus.OPEN))
        
        for symbol in symbols:
            try:
                current_price = await self._get_current_price(symbol)
                if current_price:
                    await self._update_positions_for_symbol(symbol, current_price)
            except Exception as e:
                logger.error(f"[POSITION_MANAGER] Error monitoring {symbol}: {e}")
    
    async def _update_positions_for_symbol(self, symbol: str, current_price: float):
        """Update all positions for a specific symbol"""
        positions_to_update = [pos for pos in self.positions.values() 
                              if pos.symbol == symbol and pos.status == PositionStatus.OPEN]
        
        for position in positions_to_update:
            await self._update_position(position, current_price)
    
    async def _update_position(self, position: ManagedPosition, current_price: float):
        """Update a single position with current market data"""
        try:
            # Update position data
            position.current_price = current_price
            position.last_update = datetime.now()
            
            # Calculate P&L
            self._calculate_pnl(position)
            
            # Update trailing stops
            self._update_trailing_stop(position)
            
            # Check exit conditions
            exit_signal = self._check_exit_conditions(position)
            if exit_signal:
                await self._execute_exit(position, exit_signal)
            
            # Update break-even stop
            self._update_break_even_stop(position)
            
        except Exception as e:
            logger.error(f"[POSITION_MANAGER] Error updating position {position.id}: {e}")
    
    def _calculate_pnl(self, position: ManagedPosition):
        """Calculate position P&L"""
        if position.side == 'long':
            pnl = (position.current_price - position.entry_price) * position.size
            pnl_pct = (position.current_price - position.entry_price) / position.entry_price
        else:  # short
            pnl = (position.entry_price - position.current_price) * position.size
            pnl_pct = (position.entry_price - position.current_price) / position.entry_price
        
        position.unrealized_pnl = pnl
        position.unrealized_pnl_pct = pnl_pct
        
        # Update max profit and drawdown
        if pnl > position.max_profit:
            position.max_profit = pnl
        
        drawdown = position.max_profit - pnl
        if drawdown > position.max_drawdown:
            position.max_drawdown = drawdown
    
    def _update_trailing_stop(self, position: ManagedPosition):
        """Update trailing stop level"""
        if not position.levels.trailing_stop:
            # Check if we should activate trailing stop
            profit_pct = abs(position.unrealized_pnl_pct)
            if profit_pct >= self.trailing_stop_activation:
                self._activate_trailing_stop(position)
                return
        
        if position.levels.trailing_stop:
            # Update trailing stop
            trailing_distance = position.levels.trailing_distance
            
            if position.side == 'long':
                new_trailing_stop = position.current_price * (1 - trailing_distance)
                if new_trailing_stop > position.levels.trailing_stop:
                    position.levels.trailing_stop = new_trailing_stop
                    logger.debug(f"[POSITION_MANAGER] Updated trailing stop for {position.id}: {new_trailing_stop:.4f}")
            else:  # short
                new_trailing_stop = position.current_price * (1 + trailing_distance)
                if new_trailing_stop < position.levels.trailing_stop:
                    position.levels.trailing_stop = new_trailing_stop
                    logger.debug(f"[POSITION_MANAGER] Updated trailing stop for {position.id}: {new_trailing_stop:.4f}")
    
    def _activate_trailing_stop(self, position: ManagedPosition):
        """Activate trailing stop for a position"""
        trailing_distance = position.levels.trailing_distance
        
        if position.side == 'long':
            position.levels.trailing_stop = position.current_price * (1 - trailing_distance)
        else:  # short
            position.levels.trailing_stop = position.current_price * (1 + trailing_distance)
        
        logger.info(f"[POSITION_MANAGER] Activated trailing stop for {position.id}: {position.levels.trailing_stop:.4f}")
    
    def _update_break_even_stop(self, position: ManagedPosition):
        """Update break-even stop if conditions are met"""
        if position.levels.break_even_stop:
            return  # Already set
        
        profit_pct = position.unrealized_pnl_pct
        if profit_pct >= self.break_even_activation:
            # Move stop loss to break-even
            position.levels.stop_loss = position.entry_price
            position.levels.break_even_stop = True
            
            logger.info(f"[POSITION_MANAGER] Moved stop to break-even for {position.id}")
    
    def _check_exit_conditions(self, position: ManagedPosition) -> Optional[ExitReason]:
        """Check if position should be exited"""
        current_price = position.current_price
        
        # Check stop loss
        if position.side == 'long' and current_price <= position.levels.stop_loss:
            return ExitReason.STOP_LOSS
        elif position.side == 'short' and current_price >= position.levels.stop_loss:
            return ExitReason.STOP_LOSS
        
        # Check trailing stop
        if position.levels.trailing_stop:
            if position.side == 'long' and current_price <= position.levels.trailing_stop:
                return ExitReason.TRAILING_STOP
            elif position.side == 'short' and current_price >= position.levels.trailing_stop:
                return ExitReason.TRAILING_STOP
        
        # Check take profit
        if position.side == 'long' and current_price >= position.levels.take_profit:
            return ExitReason.TAKE_PROFIT
        elif position.side == 'short' and current_price <= position.levels.take_profit:
            return ExitReason.TAKE_PROFIT
        
        # Check partial take profit levels
        for tp_price, tp_percentage in position.levels.partial_tp_levels:
            if position.side == 'long' and current_price >= tp_price:
                # Execute partial take profit
                asyncio.create_task(self._execute_partial_exit(position, tp_percentage, tp_price))
                # Remove this level
                position.levels.partial_tp_levels.remove((tp_price, tp_percentage))
        
        return None
    
    async def _execute_exit(self, position: ManagedPosition, reason: ExitReason):
        """Execute position exit"""
        try:
            logger.info(f"[POSITION_MANAGER] Exiting position {position.id} - Reason: {reason.value}")
            
            position.status = PositionStatus.CLOSING
            position.exit_reason = reason
            position.exit_time = datetime.now()
            position.exit_price = position.current_price
            
            # Execute exit order through execution engine
            if self.execution_engine:
                exit_side = 'sell' if position.side == 'long' else 'buy'
                
                # Use LIMIT order for exit (as required)
                order_result = await self.execution_engine.place_limit_order(
                    symbol=position.symbol,
                    side=exit_side,
                    amount=position.size,
                    price=position.current_price,
                    params={'reduce_only': True}
                )
                
                if order_result:
                    position.status = PositionStatus.CLOSED
                    logger.info(f"[POSITION_MANAGER] Successfully exited position {position.id}")
                    
                    # Update performance metrics
                    self._update_performance_metrics(position)
                else:
                    position.status = PositionStatus.FAILED
                    logger.error(f"[POSITION_MANAGER] Failed to exit position {position.id}")
            
        except Exception as e:
            logger.error(f"[POSITION_MANAGER] Error exiting position {position.id}: {e}")
            position.status = PositionStatus.FAILED
    
    async def _execute_partial_exit(self, position: ManagedPosition, percentage: float, price: float):
        """Execute partial position exit"""
        try:
            exit_size = position.size * (percentage / 100.0)
            
            logger.info(f"[POSITION_MANAGER] Partial exit for {position.id}: {percentage}% at {price:.4f}")
            
            if self.execution_engine:
                exit_side = 'sell' if position.side == 'long' else 'buy'
                
                order_result = await self.execution_engine.place_limit_order(
                    symbol=position.symbol,
                    side=exit_side,
                    amount=exit_size,
                    price=price,
                    params={'reduce_only': True}
                )
                
                if order_result:
                    # Reduce position size
                    position.size -= exit_size
                    logger.info(f"[POSITION_MANAGER] Partial exit successful - remaining size: {position.size}")
        
        except Exception as e:
            logger.error(f"[POSITION_MANAGER] Error in partial exit for {position.id}: {e}")
    
    async def _get_current_price(self, symbol: str) -> Optional[float]:
        """Get current market price for symbol"""
        try:
            if self.market_data_provider:
                ticker = await self.market_data_provider.get_ticker(symbol)
                return ticker.get('last') if ticker else None
            return None
        except Exception as e:
            logger.error(f"[POSITION_MANAGER] Error getting price for {symbol}: {e}")
            return None

    # Public interface methods
    async def add_position(self, symbol: str, side: str, size: float, entry_price: float,
                          stop_loss_pct: float = None, take_profit_pct: float = None,
                          trailing_distance: float = None, metadata: Dict[str, Any] = None) -> str:
        """Add a new position to autonomous management"""
        try:
            # Generate position ID
            self.position_counter += 1
            position_id = f"pos_{self.position_counter}_{int(time.time())}"

            # Calculate stop loss and take profit levels
            sl_pct = stop_loss_pct or self.default_stop_loss_pct
            tp_pct = take_profit_pct or self.default_take_profit_pct

            if side == 'long':
                stop_loss = entry_price * (1 - sl_pct)
                take_profit = entry_price * (1 + tp_pct)
            else:  # short
                stop_loss = entry_price * (1 + sl_pct)
                take_profit = entry_price * (1 - tp_pct)

            # Create position levels
            levels = PositionLevels(
                stop_loss=stop_loss,
                take_profit=take_profit,
                trailing_distance=trailing_distance or self.config.get('default_trailing_distance', 0.02)
            )

            # Create managed position
            position = ManagedPosition(
                id=position_id,
                symbol=symbol,
                side=side,
                size=size,
                entry_price=entry_price,
                entry_time=datetime.now(),
                current_price=entry_price,
                levels=levels,
                status=PositionStatus.OPEN,
                metadata=metadata or {}
            )

            # Add to tracking
            self.positions[position_id] = position

            # Update performance metrics
            self.performance_metrics['total_positions'] += 1
            current_open = len([p for p in self.positions.values() if p.status == PositionStatus.OPEN])
            if current_open > self.performance_metrics['max_concurrent_positions']:
                self.performance_metrics['max_concurrent_positions'] = current_open

            logger.info(f"[POSITION_MANAGER] Added position {position_id}: {side.upper()} {size} {symbol} @ {entry_price:.4f}")
            logger.info(f"[POSITION_MANAGER] SL: {stop_loss:.4f}, TP: {take_profit:.4f}")

            return position_id

        except Exception as e:
            logger.error(f"[POSITION_MANAGER] Error adding position: {e}")
            return None

    async def remove_position(self, position_id: str, reason: ExitReason = ExitReason.MANUAL) -> bool:
        """Remove a position from management (force close)"""
        try:
            if position_id not in self.positions:
                logger.warning(f"[POSITION_MANAGER] Position {position_id} not found")
                return False

            position = self.positions[position_id]

            if position.status == PositionStatus.OPEN:
                await self._execute_exit(position, reason)

            return True

        except Exception as e:
            logger.error(f"[POSITION_MANAGER] Error removing position {position_id}: {e}")
            return False

    async def update_position_levels(self, position_id: str, stop_loss: float = None,
                                   take_profit: float = None, trailing_distance: float = None) -> bool:
        """Update position stop loss and take profit levels"""
        try:
            if position_id not in self.positions:
                logger.warning(f"[POSITION_MANAGER] Position {position_id} not found")
                return False

            position = self.positions[position_id]

            if stop_loss is not None:
                position.levels.stop_loss = stop_loss
                logger.info(f"[POSITION_MANAGER] Updated SL for {position_id}: {stop_loss:.4f}")

            if take_profit is not None:
                position.levels.take_profit = take_profit
                logger.info(f"[POSITION_MANAGER] Updated TP for {position_id}: {take_profit:.4f}")

            if trailing_distance is not None:
                position.levels.trailing_distance = trailing_distance
                logger.info(f"[POSITION_MANAGER] Updated trailing distance for {position_id}: {trailing_distance:.2%}")

            return True

        except Exception as e:
            logger.error(f"[POSITION_MANAGER] Error updating position levels: {e}")
            return False

    def get_open_positions(self) -> List[ManagedPosition]:
        """Get all open positions"""
        return [pos for pos in self.positions.values() if pos.status == PositionStatus.OPEN]

    def get_all_positions(self) -> List[ManagedPosition]:
        """Get all positions"""
        return list(self.positions.values())

    def get_position(self, position_id: str) -> Optional[ManagedPosition]:
        """Get position by ID"""
        return self.positions.get(position_id)

    def get_positions_by_symbol(self, symbol: str) -> List[ManagedPosition]:
        """Get all positions for a symbol"""
        return [pos for pos in self.positions.values() if pos.symbol == symbol]

    def get_performance_metrics(self) -> Dict[str, Any]:
        """Get performance metrics"""
        open_positions = len(self.get_open_positions())
        total_positions = self.performance_metrics['total_positions']

        return {
            **self.performance_metrics,
            'current_open_positions': open_positions,
            'success_rate': (self.performance_metrics['successful_exits'] / max(1, total_positions)) * 100,
            'stop_loss_rate': (self.performance_metrics['stop_loss_exits'] / max(1, total_positions)) * 100,
            'take_profit_rate': (self.performance_metrics['take_profit_exits'] / max(1, total_positions)) * 100
        }

    def _update_performance_metrics(self, position: ManagedPosition):
        """Update performance metrics after position close"""
        try:
            if position.exit_reason == ExitReason.TAKE_PROFIT:
                self.performance_metrics['take_profit_exits'] += 1
            elif position.exit_reason == ExitReason.STOP_LOSS:
                self.performance_metrics['stop_loss_exits'] += 1
            elif position.exit_reason == ExitReason.TRAILING_STOP:
                self.performance_metrics['trailing_stop_exits'] += 1

            if position.unrealized_pnl > 0:
                self.performance_metrics['successful_exits'] += 1

        except Exception as e:
            logger.error(f"[POSITION_MANAGER] Error updating performance metrics: {e}")

    def get_status(self) -> Dict[str, Any]:
        """Get current status of position manager"""
        open_positions = self.get_open_positions()

        return {
            'monitoring_enabled': self.monitoring_enabled,
            'total_positions': len(self.positions),
            'open_positions': len(open_positions),
            'closed_positions': len([p for p in self.positions.values() if p.status == PositionStatus.CLOSED]),
            'failed_positions': len([p for p in self.positions.values() if p.status == PositionStatus.FAILED]),
            'performance_metrics': self.get_performance_metrics(),
            'open_position_details': [
                {
                    'id': pos.id,
                    'symbol': pos.symbol,
                    'side': pos.side,
                    'size': pos.size,
                    'entry_price': pos.entry_price,
                    'current_price': pos.current_price,
                    'unrealized_pnl_pct': pos.unrealized_pnl_pct * 100,
                    'stop_loss': pos.levels.stop_loss,
                    'take_profit': pos.levels.take_profit,
                    'trailing_stop': pos.levels.trailing_stop,
                    'break_even_stop': pos.levels.break_even_stop
                }
                for pos in open_positions
            ]
        }

    async def emergency_close_all(self, reason: str = "Emergency stop") -> bool:
        """Emergency close all open positions"""
        try:
            open_positions = self.get_open_positions()

            logger.warning(f"[POSITION_MANAGER] Emergency closing {len(open_positions)} positions: {reason}")

            close_tasks = []
            for position in open_positions:
                task = self._execute_exit(position, ExitReason.EMERGENCY)
                close_tasks.append(task)

            # Execute all closes concurrently
            if close_tasks:
                await asyncio.gather(*close_tasks, return_exceptions=True)

            logger.info(f"[POSITION_MANAGER] Emergency close completed")
            return True

        except Exception as e:
            logger.error(f"[POSITION_MANAGER] Error in emergency close: {e}")
            return False

    async def shutdown(self):
        """Shutdown the position manager"""
        await self.stop_monitoring()

        # Close all open positions
        open_positions = self.get_open_positions()
        if open_positions:
            logger.info(f"[POSITION_MANAGER] Closing {len(open_positions)} positions on shutdown")
            await self.emergency_close_all("System shutdown")

        logger.info("[POSITION_MANAGER] Autonomous Position Manager shutdown complete")
