"""
Worker classes for background processing in the trading interface.
These classes handle data fetching and processing in separate threads
to keep the UI responsive.
"""

# Debug and optimization flags
DEBUG = False
USE_GPU = True  # Default to True, will be updated by main application

from PySide6.QtCore import QThread, Signal
import traceback
import pandas as pd
import numpy as np
import json
from datetime import datetime, timedelta
import time

try:
    # Import the websocket-client package
    from websocket import WebSocketApp
except ImportError:
    # Fallback to a mock WebSocketApp for development
    class WebSocketApp:
        def __init__(self, *args, **kwargs):
            self.on_open = kwargs.get('on_open')
            self.on_message = kwargs.get('on_message')
            self.on_error = kwargs.get('on_error')
            self.on_close = kwargs.get('on_close')

        def run_forever(self):
            # Simulate connection open
            if self.on_open:
                self.on_open(self)

        def close(self):
            # Simulate connection close
            if self.on_close:
                self.on_close(self, close_status_code=1000, close_msg="Manual close")

        def send(self, data):
            # Simulate sending data (do nothing)
            pass

# Import the necessary functions from the main module
# These will be imported when the module is used
exchange = None
demo_mode = None
fetch_open_positions = None
fetch_order_book = None
fetch_account_info = None
fetch_ohlcv = None
fetch_trades = None


class PositionsWorker(QThread):
    """Worker thread for fetching positions data"""
    fetched = Signal(list)
    error = Signal(str)

    def __init__(self, symbol=None, force_refresh=False):
        super().__init__()
        self.symbol = symbol
        self.force_refresh = force_refresh

    def run(self):
        try:
            positions = fetch_open_positions(self.symbol, self.force_refresh)
            self.fetched.emit(positions)
        except Exception as e:
            self.error.emit(f"Error fetching positions: {str(e)}")
            if DEBUG:
                traceback.print_exc()


class OrderBookWorker(QThread):
    """Worker thread for fetching order book data"""
    fetched = Signal(dict)
    error = Signal(str)

    def __init__(self, symbol, force_refresh=False):
        super().__init__()
        self.symbol = symbol
        self.force_refresh = force_refresh

    def run(self):
        try:
            order_book = fetch_order_book(self.symbol, self.force_refresh)
            if order_book:
                self.fetched.emit(order_book)
            else:
                self.error.emit(f"No order book data for {self.symbol}")
        except Exception as e:
            self.error.emit(f"Error fetching order book: {str(e)}")
            if DEBUG:
                traceback.print_exc()


class AccountWorker(QThread):
    """Worker thread for fetching account information"""
    fetched = Signal(dict)
    error = Signal(str)

    def __init__(self, force_refresh=False):
        super().__init__()
        self.force_refresh = force_refresh

    def run(self):
        try:
            account_info = fetch_account_info(self.force_refresh)
            self.fetched.emit(account_info)
        except Exception as e:
            self.error.emit(f"Error fetching account info: {str(e)}")
            if DEBUG:
                traceback.print_exc()


class ChartDataWorker(QThread):
    """Worker thread for fetching chart data (OHLCV or trades) with GPU acceleration"""
    fetched_ohlcv = Signal(list)
    fetched_trades = Signal(list)
    error = Signal(str)

    def __init__(self, symbol, timeframe=None, chart_type="Candle", tick_limit=None):
        super().__init__()
        self.symbol = symbol
        self.timeframe = timeframe
        self.chart_type = chart_type
        # Cap tick limit to improve performance
        self.tick_limit = min(tick_limit or 500, 1000)

        # Try to initialize GPU acceleration if enabled
        self.has_gpu = False
        if USE_GPU:
            try:
                import cupy
                self.has_gpu = True
                if DEBUG:
                    print("GPU acceleration available via CuPy")
            except ImportError:
                if DEBUG:
                    print("CuPy not available, using CPU processing")
        else:
            if DEBUG:
                print("GPU acceleration disabled by configuration")

    def run(self):
        try:
            if self.chart_type.lower() == "tick":
                # Fetch trades for tick chart with GPU processing if available
                trades = fetch_trades(self.symbol, limit=self.tick_limit)

                # If we have too many trades, use GPU for sampling if available
                if trades and len(trades) > 500:
                    if self.has_gpu:
                        try:
                            import cupy as cp
                            # Extract timestamps and prices for GPU processing
                            timestamps = [t['timestamp'] for t in trades]
                            prices = [t['price'] for t in trades]

                            # Move to GPU
                            gpu_timestamps = cp.array(timestamps)
                            gpu_prices = cp.array(prices)

                            # Sample using GPU (every nth item)
                            n = len(trades) // 500 + 1
                            indices = cp.arange(0, len(trades), n)

                            # Get sampled indices back to CPU
                            sampled_indices = cp.asnumpy(indices).tolist()

                            # Select trades at those indices
                            trades = [trades[i] for i in sampled_indices]
                            if DEBUG:
                                print(f"GPU-accelerated sampling: {len(trades)} trades")
                        except Exception as gpu_err:
                            # Fallback to CPU sampling
                            if DEBUG:
                                print(f"GPU sampling failed, using CPU: {gpu_err}")
                            n = len(trades) // 500 + 1
                            trades = trades[::n]
                    else:
                        # CPU sampling
                        n = len(trades) // 500 + 1
                        trades = trades[::n]

                self.fetched_trades.emit(trades)
            else:
                # Fetch OHLCV for candle chart with optimized limit
                print(f"Fetching OHLCV data for {self.symbol} with timeframe {self.timeframe}")
                ohlcv = fetch_ohlcv(self.symbol, timeframe=self.timeframe, limit=100)  # Increased to 100 for better visualization

                if ohlcv is None:
                    self.error.emit(f"Failed to fetch OHLCV data for {self.symbol}")
                    return

                # Check if ohlcv is a DataFrame (from fetch_ohlcv) and convert it to a list
                if hasattr(ohlcv, 'to_dict'):
                    # It's a DataFrame, convert to list of lists with GPU acceleration if available
                    print(f"Converting DataFrame to list for OHLCV data, shape: {ohlcv.shape}")

                    # Verify DataFrame has the required columns
                    required_cols = ['timestamp', 'open', 'high', 'low', 'close']
                    missing_cols = [col for col in required_cols if col not in ohlcv.columns]
                    if missing_cols:
                        self.error.emit(f"OHLCV data missing required columns: {missing_cols}")
                        return

                    # Reset index to make timestamp a column
                    if 'timestamp' not in ohlcv.columns:
                        ohlcv = ohlcv.reset_index()

                    # Convert timestamp to milliseconds if it's a datetime object
                    if pd.api.types.is_datetime64_any_dtype(ohlcv['timestamp']):
                        ohlcv['timestamp'] = ohlcv['timestamp'].astype(np.int64) // 10**6

                    # Add volume column if missing
                    if 'volume' not in ohlcv.columns:
                        ohlcv['volume'] = 0

                    # Verify data integrity
                    # Check for NaN values
                    if ohlcv.isna().any().any():
                        print("Warning: OHLCV data contains NaN values, filling with appropriate values")
                        # Fill NaN values appropriately
                        ohlcv['open'] = ohlcv['open'].fillna(method='ffill')
                        ohlcv['high'] = ohlcv['high'].fillna(method='ffill')
                        ohlcv['low'] = ohlcv['low'].fillna(method='ffill')
                        ohlcv['close'] = ohlcv['close'].fillna(method='ffill')
                        ohlcv['volume'] = ohlcv['volume'].fillna(0)

                    # Ensure high >= low (critical for candlestick display)
                    invalid_rows = ohlcv['high'] < ohlcv['low']
                    if invalid_rows.any():
                        print(f"Warning: {invalid_rows.sum()} rows have high < low, fixing...")
                        # Swap high and low where needed
                        temp = ohlcv.loc[invalid_rows, 'high'].copy()
                        ohlcv.loc[invalid_rows, 'high'] = ohlcv.loc[invalid_rows, 'low']
                        ohlcv.loc[invalid_rows, 'low'] = temp

                    # Ensure all OHLC values are positive
                    if (ohlcv[['open', 'high', 'low', 'close']] <= 0).any().any():
                        print("Warning: OHLCV data contains zero or negative values, replacing with small positive values")
                        for col in ['open', 'high', 'low', 'close']:
                            ohlcv[col] = ohlcv[col].clip(lower=0.00001)

                    # Sort by timestamp to ensure chronological order
                    ohlcv = ohlcv.sort_values('timestamp')

                    # Print some stats for debugging
                    print(f"OHLCV data stats: {len(ohlcv)} rows")
                    print(f"Timestamp range: {ohlcv['timestamp'].min()} to {ohlcv['timestamp'].max()}")
                    print(f"Price range: {ohlcv['low'].min()} to {ohlcv['high'].max()}")

                    # Try GPU acceleration for data conversion
                    if self.has_gpu:
                        try:
                            import cupy as cp
                            # Extract the columns we need
                            cols = ['timestamp', 'open', 'high', 'low', 'close', 'volume']

                            # Convert to numpy array
                            np_array = ohlcv[cols].values

                            # Transfer to GPU
                            gpu_array = cp.asarray(np_array)

                            # Any GPU processing can be done here
                            # For example, we could calculate moving averages or other indicators

                            # Transfer back to CPU
                            result_array = cp.asnumpy(gpu_array)

                            # Convert to list
                            ohlcv_list = result_array.tolist()
                            print("Using GPU acceleration for OHLCV data processing")
                        except Exception as gpu_err:
                            # Fallback to CPU processing
                            print(f"GPU processing failed, using CPU: {gpu_err}")
                            ohlcv_list = ohlcv[['timestamp', 'open', 'high', 'low', 'close', 'volume']].values.tolist()
                    else:
                        # Use numpy's optimized methods for CPU processing
                        ohlcv_list = ohlcv[['timestamp', 'open', 'high', 'low', 'close', 'volume']].values.tolist()

                    print(f"Emitting {len(ohlcv_list)} OHLCV data points")
                    self.fetched_ohlcv.emit(ohlcv_list)
                else:
                    # It's already a list, verify and emit it
                    if ohlcv and len(ohlcv) > 0:
                        print(f"Emitting {len(ohlcv)} OHLCV data points (list format)")

                        # Verify data structure
                        if len(ohlcv[0]) < 5:  # Need at least [timestamp, open, high, low, close]
                            self.error.emit(f"Invalid OHLCV data format: {ohlcv[0]}")
                            return

                        # Ensure data is sorted by timestamp
                        ohlcv.sort(key=lambda x: x[0])

                        self.fetched_ohlcv.emit(ohlcv)
                    else:
                        self.error.emit(f"No OHLCV data available for {self.symbol}")
                        return
        except Exception as e:
            self.error.emit(f"Error fetching chart data: {str(e)}")
            if DEBUG:
                traceback.print_exc()


class OrdersWorker(QThread):
    """Worker thread for fetching open orders"""
    fetched = Signal(list)
    error = Signal(str)

    def __init__(self, symbol=None, force_refresh=False):
        super().__init__()
        self.symbol = symbol
        self.force_refresh = force_refresh

    def run(self):
        try:
            # Use the imported fetch_open_orders function
            if exchange is not None:
                orders = exchange.fetch_open_orders(self.symbol)
                self.fetched.emit(orders)
            else:
                self.error.emit("No exchange available for fetching orders")
                self.fetched.emit([])
        except Exception as e:
            self.error.emit(f"Error fetching orders: {str(e)}")
            if DEBUG:
                traceback.print_exc()


class TradesWorker(QThread):
    """Worker thread for fetching recent trades"""
    fetched = Signal(list)
    error = Signal(str)

    def __init__(self, symbol, limit=10):
        super().__init__()
        self.symbol = symbol
        self.limit = limit

    def run(self):
        try:
            # Use the imported fetch_trades function
            print(f"Fetching trades for {self.symbol} with limit {self.limit}")
            trades = fetch_trades(self.symbol, limit=self.limit)
            print(f"Fetched {len(trades) if trades else 0} trades")
            self.fetched.emit(trades)
        except Exception as e:
            self.error.emit(f"Error fetching trades: {str(e)}")
            if DEBUG:
                traceback.print_exc()


class TradeHistoryWorker(QThread):
    """Worker thread for fetching trade history for the performance dashboard"""
    fetched = Signal(list)
    error = Signal(str)

    # Class-level variable to track if we've already tried and failed with API keys
    api_key_valid = True

    @classmethod
    def reset_api_key_validity(cls):
        """Reset the API key validity flag to allow retrying."""
        cls.api_key_valid = True
        print("TradeHistoryWorker: API key validity flag reset")

    def __init__(self):
        super().__init__()
        # Don't capture global variables at init time - get them fresh at runtime
        self.current_symbol = "BTC/USDT:USDT"  # Default symbol

        # Try to get the current symbol from the main window
        try:
            from me2_stable import main_window
            if hasattr(main_window, 'get_symbol'):
                symbol = main_window.get_symbol()
                if symbol:
                    self.current_symbol = symbol
        except:
            pass

    def _get_current_exchange_state(self):
        """Get the current exchange and demo_mode state at runtime."""
        try:
            # Import fresh values from the main module
            from me2_stable import exchange, demo_mode

            # Also check this module's global variables (updated by init_workers)
            import sys
            current_module = sys.modules[__name__]
            if hasattr(current_module, 'exchange') and current_module.exchange is not None:
                exchange = current_module.exchange
            if hasattr(current_module, 'demo_mode'):
                demo_mode = current_module.demo_mode

            print(f"TradeHistoryWorker: Current state - demo_mode={demo_mode}, exchange={'available' if exchange else 'None'}")

            return exchange, demo_mode
        except Exception as e:
            print(f"TradeHistoryWorker: Error getting exchange state: {e}")
            return None, True

    def run(self):
        try:
            # Get current exchange state at runtime
            exchange, demo_mode = self._get_current_exchange_state()

            # If we already know the API key is invalid, don't try again
            if not TradeHistoryWorker.api_key_valid:
                print("TradeHistoryWorker: Using fallback trade data (API key previously failed)")
                self._use_fallback_trades()
                return

            # If we're in demo mode or have no exchange, use fallback data
            if demo_mode or exchange is None:
                print("TradeHistoryWorker: Using fallback trade data (demo mode or no exchange)")
                self._use_fallback_trades()
                return

            # Try to fetch recent trades for the current symbol first
            try:
                print(f"TradeHistoryWorker: Fetching recent trades for {self.current_symbol}")
                recent_trades = exchange.fetch_trades(self.current_symbol, limit=50)

                if recent_trades and len(recent_trades) > 0:
                    # Convert trades to our format
                    formatted_trades = self._format_trades(recent_trades)
                    print(f"TradeHistoryWorker: Emitting {len(formatted_trades)} recent trades")
                    self.fetched.emit(formatted_trades)
                    return
            except Exception as e:
                # If we get an authentication error, mark the API key as invalid
                if "403" in str(e) or "Incorrect Access key" in str(e) or "Authentication failed" in str(e):
                    TradeHistoryWorker.api_key_valid = False
                    print(f"TradeHistoryWorker: API authentication failed, switching to fallback data: {e}")
                    self._use_fallback_trades()
                    return
                elif "rate limit" in str(e).lower() or "too many requests" in str(e).lower():
                    print(f"TradeHistoryWorker: Rate limit hit, using fallback data: {e}")
                    self._use_fallback_trades()
                    return
                else:
                    print(f"TradeHistoryWorker: Error fetching recent trades: {e}")
                    # Continue to try closed orders

            # If we couldn't get recent trades, try closed orders as a backup
            print("TradeHistoryWorker: Trying to fetch closed orders...")

            # Only try one symbol to avoid multiple error messages
            try:
                closed_orders = exchange.fetch_closed_orders(symbol=self.current_symbol)
                print(f"TradeHistoryWorker: Fetched {len(closed_orders)} closed orders")

                # Convert to our format
                formatted_trades = []
                for order in closed_orders:
                    # Include both closed and canceled orders as they represent trading activity
                    if order.get('status') in ['closed', 'canceled']:
                        trade = self._format_order(order)
                        if trade is not None:  # Only add valid trades
                            formatted_trades.append(trade)

                print(f"TradeHistoryWorker: Emitting {len(formatted_trades)} trades from closed orders")
                self.fetched.emit(formatted_trades)
            except Exception as e:
                # If we get an authentication error, mark the API key as invalid
                if "403" in str(e) or "Incorrect Access key" in str(e) or "Authentication failed" in str(e):
                    TradeHistoryWorker.api_key_valid = False
                    print(f"TradeHistoryWorker: API authentication failed, using fallback data: {e}")
                    self._use_fallback_trades()
                elif "rate limit" in str(e).lower() or "too many requests" in str(e).lower():
                    print(f"TradeHistoryWorker: Rate limit hit, using fallback data: {e}")
                    self._use_fallback_trades()
                else:
                    print(f"TradeHistoryWorker: Error fetching closed orders: {e}")
                    # Use fallback data
                    self._use_fallback_trades()
        except Exception as e:
            self.error.emit(f"Error in trade history worker: {str(e)}")
            if DEBUG:
                traceback.print_exc()
            # Use fallback data
            self._use_fallback_trades()

    def _format_trades(self, trades):
        """Format raw trades data into our standard format"""
        formatted_trades = []

        for trade in trades:
            # Extract trade data
            symbol = trade.get('symbol', '')
            side = trade.get('side', '').lower()
            direction = 'long' if side == 'buy' else 'short'
            price = float(trade.get('price', 0))
            amount = float(trade.get('amount', 0))

            # Create trade object with robust timestamp handling
            timestamp = trade.get('timestamp', 0)
            try:
                # Handle different timestamp formats
                if isinstance(timestamp, (int, float)):
                    if timestamp > 1e10:  # Milliseconds
                        trade_timestamp = datetime.fromtimestamp(timestamp / 1000)
                    else:  # Seconds
                        trade_timestamp = datetime.fromtimestamp(timestamp)
                elif isinstance(timestamp, str):
                    try:
                        trade_timestamp = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
                    except ValueError:
                        trade_timestamp = datetime.fromtimestamp(float(timestamp) / 1000)
                elif isinstance(timestamp, datetime):
                    trade_timestamp = timestamp
                else:
                    trade_timestamp = datetime.now()
            except (ValueError, OSError, TypeError):
                trade_timestamp = datetime.now()

            formatted_trade = {
                'timestamp': trade_timestamp,
                'symbol': symbol,
                'direction': direction,
                'entry_price': price,
                'exit_price': price,
                'size': amount,
                'pnl': 0,  # We don't have PnL for individual trades
                'pnl_percent': 0,
                'fee': float(trade.get('fee', {}).get('cost', 0)),
                'order_id': trade.get('id', ''),
                'timeframe': '1h'  # Default timeframe
            }

            formatted_trades.append(formatted_trade)

        return formatted_trades

    def _format_order(self, order):
        """Format a closed order into our standard trade format"""
        try:
            # Extract order data with safe conversion
            symbol = order.get('symbol', '')
            side = order.get('side', '').lower() if order.get('side') else ''
            direction = 'long' if side == 'buy' else 'short'

            # Safe float conversion with None handling
            price_raw = order.get('price')
            if price_raw is None:
                price_raw = order.get('average', 0)  # Try average price as fallback
            price = float(price_raw) if price_raw is not None else 0.0

            amount_raw = order.get('amount')
            if amount_raw is None:
                amount_raw = order.get('filled', 0)  # Try filled amount as fallback
            amount = float(amount_raw) if amount_raw is not None else 0.0

            # Skip orders with no amount or invalid price
            if amount <= 0 or price <= 0:
                print(f"TradeHistoryWorker: Skipping order with invalid price ({price}) or amount ({amount})")
                return None

            # Log order details for debugging
            print(f"TradeHistoryWorker: Processing order - Symbol: {symbol}, Side: {side}, Price: {price}, Amount: {amount}")

        except (ValueError, TypeError) as e:
            print(f"TradeHistoryWorker: Error extracting order data: {e}")
            return None

        # Create trade object with robust timestamp handling
        timestamp = order.get('timestamp', 0)
        try:
            # Handle different timestamp formats
            if isinstance(timestamp, (int, float)):
                if timestamp > 1e10:  # Milliseconds
                    trade_timestamp = datetime.fromtimestamp(timestamp / 1000)
                else:  # Seconds
                    trade_timestamp = datetime.fromtimestamp(timestamp)
            elif isinstance(timestamp, str):
                try:
                    trade_timestamp = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
                except ValueError:
                    trade_timestamp = datetime.fromtimestamp(float(timestamp) / 1000)
            elif isinstance(timestamp, datetime):
                trade_timestamp = timestamp
            else:
                trade_timestamp = datetime.now()
        except (ValueError, OSError, TypeError):
            trade_timestamp = datetime.now()

        # Safe fee extraction
        try:
            fee_data = order.get('fee', {})
            if isinstance(fee_data, dict):
                fee = float(fee_data.get('cost', 0)) if fee_data.get('cost') is not None else 0.0
            else:
                fee = 0.0
        except (ValueError, TypeError):
            fee = 0.0

        trade = {
            'timestamp': trade_timestamp,
            'symbol': symbol,
            'direction': direction,
            'entry_price': price,
            'exit_price': price,
            'size': amount,
            'pnl': 0,  # We don't have PnL for closed orders
            'pnl_percent': 0,
            'fee': fee,
            'order_id': str(order.get('id', '')),
            'timeframe': '1h'  # Default timeframe
        }

        return trade

    def _use_fallback_trades(self):
        """Generate and emit fallback trade data"""
        # Create some realistic-looking demo trades
        demo_trades = []
        symbols = ["BTC/USDT:USDT", "ETH/USDT:USDT", "SOL/USDT:USDT", "DOGE/USDT:USDT"]
        prices = {"BTC/USDT:USDT": 65000, "ETH/USDT:USDT": 3500, "SOL/USDT:USDT": 150, "DOGE/USDT:USDT": 0.15}

        # Generate trades over the past 30 days
        now = datetime.now()
        for i in range(20):
            # Random trade details
            symbol = symbols[i % len(symbols)]
            direction = "long" if i % 3 != 0 else "short"
            base_price = prices[symbol]

            # Random price variation
            price_variation = base_price * (0.95 + 0.1 * (i % 10) / 10)

            # Random size
            size = round(0.1 + (i % 5) * 0.2, 2)

            # Random timestamp in the past 30 days
            days_ago = i % 30
            timestamp = now - timedelta(days=days_ago, hours=i % 24)

            # Random PnL
            pnl = round((i % 5 - 2) * 50, 2)
            pnl_percent = round(pnl / (price_variation * size) * 100, 2)

            # Create trade
            trade = {
                'timestamp': timestamp,
                'symbol': symbol,
                'direction': direction,
                'entry_price': round(price_variation, 2),
                'exit_price': round(price_variation * (1 + 0.01 * (i % 5 - 2)), 2),
                'size': size,
                'pnl': pnl,
                'pnl_percent': pnl_percent,
                'fee': round(price_variation * size * 0.001, 2),
                'order_id': f"demo-{i}",
                'timeframe': '1h'
            }

            demo_trades.append(trade)

        # Sort by timestamp (newest first)
        demo_trades.sort(key=lambda x: x['timestamp'], reverse=True)

        print(f"TradeHistoryWorker: Emitting {len(demo_trades)} fallback trades")
        self.fetched.emit(demo_trades)


class TickersWorker(QThread):
    """Worker thread for fetching ticker data for multiple symbols"""
    fetched = Signal(dict)
    error = Signal(str)

    def __init__(self, symbols=None):
        super().__init__()
        self.symbols = symbols

    def run(self):
        try:
            # Use the imported fetch_tickers function
            print(f"Fetching tickers for {len(self.symbols) if self.symbols else 'all'} symbols")
            # We need to define a fetch_tickers function in the main module
            # For now, we'll use a workaround
            if exchange is not None:
                if self.symbols:
                    # Fetch specific symbols
                    tickers = {}
                    for symbol in self.symbols:
                        try:
                            ticker = exchange.fetch_ticker(symbol)
                            tickers[symbol] = ticker
                        except Exception as e:
                            print(f"Error fetching ticker for {symbol}: {e}")
                else:
                    # Fetch all tickers
                    tickers = exchange.fetch_tickers()
                print(f"Fetched {len(tickers)} tickers")
                self.fetched.emit(tickers)
            else:
                self.error.emit("No exchange available for fetching tickers")
        except Exception as e:
            self.error.emit(f"Error fetching tickers: {str(e)}")
            if DEBUG:
                traceback.print_exc()


class WebSocketWorker(QThread):
    """Worker thread for WebSocket connections

    This worker handles real-time data updates via WebSocket connections.
    It emits signals when new data is received and handles reconnection
    if the connection is lost.
    """
    connected = Signal(bool)
    orderbook_updated = Signal(dict)
    trades_updated = Signal(list)
    ticker_updated = Signal(dict)
    error = Signal(str)

    def __init__(self, symbol, exchange_name="huobi"):
        super().__init__()
        self.symbol = symbol
        self.exchange_name = exchange_name.lower()
        self.ws = None
        self.is_running = False
        self.reconnect_delay = 5  # Initial reconnect delay in seconds
        self.max_reconnect_delay = 60  # Maximum reconnect delay in seconds

    def run(self):
        """Main thread loop that establishes and maintains the WebSocket connection"""
        self.is_running = True

        while self.is_running:
            try:
                # Connect to the WebSocket
                self._connect()

                # Reset reconnect delay on successful connection
                self.reconnect_delay = 5

                # Run the WebSocket connection (this blocks until disconnection)
                self.ws.run_forever()

                # If we get here, the connection was closed
                if DEBUG:
                    print(f"WebSocket connection closed for {self.symbol}")

                # If we're still supposed to be running, reconnect
                if self.is_running:
                    time.sleep(self.reconnect_delay)
                    # Increase reconnect delay with exponential backoff
                    self.reconnect_delay = min(self.reconnect_delay * 2, self.max_reconnect_delay)
            except Exception as e:
                self.error.emit(f"WebSocket error: {str(e)}")
                if DEBUG:
                    traceback.print_exc()

                # Wait before reconnecting
                time.sleep(self.reconnect_delay)
                # Increase reconnect delay with exponential backoff
                self.reconnect_delay = min(self.reconnect_delay * 2, self.max_reconnect_delay)

    def stop(self):
        """Stop the WebSocket connection"""
        self.is_running = False
        if self.ws:
            self.ws.close()

    def _connect(self):
        """Establish the WebSocket connection"""
        # Format the symbol for the exchange
        formatted_symbol = self._format_symbol(self.symbol)

        # Get the WebSocket URL for the exchange
        ws_url = self._get_ws_url()

        if DEBUG:
            print(f"Connecting to WebSocket: {ws_url} for {formatted_symbol}")

        # Create the WebSocket connection
        self.ws = WebSocketApp(
            ws_url,
            on_open=self._on_open,
            on_message=self._on_message,
            on_error=self._on_error,
            on_close=self._on_close
        )

    def _format_symbol(self, symbol):
        """Format the symbol for the specific exchange"""
        if self.exchange_name == "huobi":
            # Huobi uses lowercase symbols without slashes
            # e.g., BTC/USDT -> btcusdt
            return symbol.lower().replace("/", "").replace(":", "")
        else:
            # Default format
            return symbol.lower().replace("/", "").replace(":", "")

    def _get_ws_url(self):
        """Get the WebSocket URL for the exchange"""
        if self.exchange_name == "huobi":
            return "wss://api.huobi.pro/ws"
        else:
            # Default to Huobi
            return "wss://api.huobi.pro/ws"

    def _on_open(self, ws_app):  # pylint: disable=unused-argument
        """Called when the WebSocket connection is established

        Args:
            ws_app: WebSocket app instance (required by websocket-client but not used)
        """
        if DEBUG:
            print(f"WebSocket connection opened for {self.symbol}")

        # Signal that we're connected
        self.connected.emit(True)

        # Subscribe to market data
        self._subscribe()

    def _subscribe(self):
        """Subscribe to market data channels"""
        formatted_symbol = self._format_symbol(self.symbol)

        if self.exchange_name == "huobi":
            # Subscribe to market depth
            depth_sub = {
                "sub": f"market.{formatted_symbol}.depth.step0",
                "id": f"depth_{formatted_symbol}"
            }
            self.ws.send(json.dumps(depth_sub))

            # Subscribe to market trades
            trades_sub = {
                "sub": f"market.{formatted_symbol}.trade.detail",
                "id": f"trades_{formatted_symbol}"
            }
            self.ws.send(json.dumps(trades_sub))

            # Subscribe to ticker
            ticker_sub = {
                "sub": f"market.{formatted_symbol}.detail",
                "id": f"ticker_{formatted_symbol}"
            }
            self.ws.send(json.dumps(ticker_sub))
        else:
            # Default subscription format (Huobi)
            depth_sub = {
                "sub": f"market.{formatted_symbol}.depth.step0",
                "id": f"depth_{formatted_symbol}"
            }
            self.ws.send(json.dumps(depth_sub))

    def _on_message(self, ws_app, message):  # pylint: disable=unused-argument
        """Called when a message is received from the WebSocket

        Args:
            ws_app: WebSocket app instance (required by websocket-client but not used)
            message: The message received from the WebSocket
        """
        try:
            # Parse the message
            data = json.loads(message)

            # Handle ping messages (keep-alive)
            if "ping" in data:
                pong = {"pong": data["ping"]}
                self.ws.send(json.dumps(pong))
                return

            # Handle subscription confirmations
            if "subbed" in data:
                if DEBUG:
                    print(f"Subscribed to {data['subbed']}")
                return

            # Handle errors
            if "err-code" in data:
                self.error.emit(f"WebSocket error: {data['err-msg']}")
                return

            # Handle market data
            if "ch" in data and "tick" in data:
                channel = data["ch"]
                tick = data["tick"]

                # Handle different types of data
                if ".depth." in channel:
                    # Order book update
                    orderbook = {
                        "symbol": self.symbol,
                        "timestamp": datetime.now().timestamp() * 1000,
                        "bids": tick.get("bids", []),
                        "asks": tick.get("asks", [])
                    }
                    self.orderbook_updated.emit(orderbook)

                elif ".trade." in channel:
                    # Trades update
                    trades = []
                    for trade_data in tick.get("data", []):
                        trade = {
                            "id": str(trade_data.get("id", "")),
                            "timestamp": trade_data.get("ts", 0),
                            "datetime": datetime.fromtimestamp(trade_data.get("ts", 0) / 1000).isoformat(),
                            "symbol": self.symbol,
                            "side": "buy" if trade_data.get("direction") == "buy" else "sell",
                            "price": float(trade_data.get("price", 0)),
                            "amount": float(trade_data.get("amount", 0)),
                            "cost": float(trade_data.get("price", 0)) * float(trade_data.get("amount", 0))
                        }
                        trades.append(trade)

                    if trades:
                        self.trades_updated.emit(trades)

                elif ".detail" in channel:
                    # Ticker update
                    ticker = {
                        "symbol": self.symbol,
                        "timestamp": datetime.now().timestamp() * 1000,
                        "datetime": datetime.now().isoformat(),
                        "high": float(tick.get("high", 0)),
                        "low": float(tick.get("low", 0)),
                        "bid": float(tick.get("bid", 0)),
                        "ask": float(tick.get("ask", 0)),
                        "last": float(tick.get("close", 0)),
                        "open": float(tick.get("open", 0)),
                        "close": float(tick.get("close", 0)),
                        "percentage": ((float(tick.get("close", 0)) / float(tick.get("open", 1))) - 1) * 100 if float(tick.get("open", 0)) > 0 else 0,
                        "volume": float(tick.get("vol", 0))
                    }
                    self.ticker_updated.emit(ticker)

        except Exception as e:
            self.error.emit(f"Error processing WebSocket message: {str(e)}")
            if DEBUG:
                traceback.print_exc()

    def _on_error(self, ws_app, error):  # pylint: disable=unused-argument
        """Called when a WebSocket error occurs

        Args:
            ws_app: WebSocket app instance (required by websocket-client but not used)
            error: The error that occurred
        """
        self.error.emit(f"WebSocket error: {str(error)}")
        if DEBUG:
            print(f"WebSocket error: {str(error)}")

    def _on_close(self, ws_app, *args, **kwargs):  # pylint: disable=unused-argument
        """Called when the WebSocket connection is closed

        Args:
            ws_app: WebSocket app instance (required by websocket-client but not used)
            *args: Variable length argument list (required by websocket-client but not used)
            **kwargs: Arbitrary keyword arguments (required by websocket-client but not used)
        """
        self.connected.emit(False)
        if DEBUG:
            close_status_code = kwargs.get('close_status_code', 'Unknown')
            close_msg = kwargs.get('close_msg', 'Unknown reason')
            print(f"WebSocket connection closed: {close_status_code} {close_msg}")

