#!/usr/bin/env python3
"""
🚀 EPINNOX LIVE PRODUCTION DEPLOYMENT v2.0
Enhanced deployment script for ultra-conservative live trading with all integrated systems

CRITICAL SYSTEMS INTEGRATION:
- LIMIT Orders Enforcement
- Emergency Stop Mechanisms
- WebSocket Stability
- Unified Execution Engine
- ScalperGPT Integration (spread_quality >= 7.0, decision_quality >= 8.0)
- Dynamic Symbol Scanner (scoring > 75.0)
- Timer Coordination (30-second decision loops)

ULTRA-CONSERVATIVE SETTINGS:
- Max $100 trading capital
- 1% position size
- 2% portfolio risk
- LIMIT orders only
"""

import sys
import os
import json
import asyncio
import time
from datetime import datetime
import logging
from typing import Dict, List, Optional, Any

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Import all integrated systems
try:
    # Core trading systems
    from core.autonomous_trading_orchestrator import AutonomousTradingOrchestrator, TradingMode
    from core.autonomous_llm_integration import AutonomousLLMIntegration
    from core.unified_execution_engine import UnifiedExecutionEngine
    from core.emergency_stop_coordinator import emergency_coordinator, EmergencyType, EmergencyLevel
    from core.websocket_stability_manager import WebSocketStabilityManager
    from core.scalper_gpt import ScalperGPT
    from core.symbol_scanner import SymbolScanner, SymbolScannerConfig
    from core.timer_coordinator import timer_coordinator, TimerPriority, StandardIntervals

    # Credentials and configuration
    from credentials import CredentialsManager
    from config.production_loader import ProductionConfigLoader

    # Trading engine
    from trading.ccxt_trading_engine import CCXTTradingEngine

    print("✅ All critical systems imported successfully")

except ImportError as e:
    print(f"❌ Failed to import critical systems: {e}")
    print("⚠️  Please ensure all modules are properly installed")
    sys.exit(1)

# Import trading components
from trading.ccxt_trading_engine import CCXTTradingEngine
from portfolio.portfolio_manager import PortfolioManager

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f'epinnox_production_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# CRITICAL: Ultra-conservative live trading configuration
ULTRA_CONSERVATIVE_CONFIG = {
    'max_trading_capital': 100.0,        # Maximum $100 trading capital
    'position_size_pct': 1.0,            # 1% position size
    'portfolio_risk_pct': 2.0,           # 2% portfolio risk
    'max_daily_loss_pct': 5.0,           # 5% max daily loss
    'max_leverage': 2.0,                 # Maximum 2x leverage
    'max_concurrent_positions': 1,        # Only 1 position at a time
    'require_limit_orders_only': True,   # LIMIT orders only
    'emergency_stop_enabled': True,      # Emergency stops enabled
    'scalper_quality_thresholds': {
        'spread_quality': 7.0,           # ScalperGPT spread quality >= 7.0
        'decision_quality': 8.0,         # ScalperGPT decision quality >= 8.0
    },
    'symbol_quality_threshold': 75.0,    # Dynamic symbol scanner > 75.0
    'timer_coordination_enabled': True,  # 30-second decision loops
    'websocket_stability_required': True # WebSocket stability required
}

def display_production_banner():
    """Display production deployment banner"""
    print("🚀" + "="*58 + "🚀")
    print("🚀" + " "*15 + "EPINNOX LIVE PRODUCTION DEPLOYMENT" + " "*9 + "🚀")
    print("🚀" + "="*58 + "🚀")
    print()

def validate_production_environment():
    """Validate production environment is ready"""
    print("🔧 PRODUCTION ENVIRONMENT VALIDATION")
    print("-" * 50)
    
    # Check credentials
    if not validate_credentials():
        print("❌ Credentials validation failed")
        return False
    
    account_info = get_account_info()
    print(f"✅ Account: {account_info['account_name']}")
    print(f"✅ Exchange: {account_info['exchange']}")
    print(f"✅ API Key: {account_info['api_key_preview']}")
    print(f"✅ Mode: {'LIVE' if not account_info['sandbox_mode'] else 'SANDBOX'}")
    
    # Check configuration
    config_file = "conservative_live_trading_config_20250628_124131.json"
    if not os.path.exists(config_file):
        print(f"❌ Configuration file not found: {config_file}")
        return False
    
    print(f"✅ Configuration: {config_file}")
    
    # Display conservative settings
    print("\n🛡️ CONSERVATIVE SETTINGS:")
    settings = CONSERVATIVE_SETTINGS
    print(f"   • Initial Balance: ${settings['initial_balance']:.0f}")
    print(f"   • Portfolio Risk: {settings['max_portfolio_risk']:.1%}")
    print(f"   • Position Size: {settings['max_position_size']:.1%}")
    print(f"   • Leverage: {settings['max_leverage']:.1f}x")
    print(f"   • Daily Loss Limit: {settings['max_daily_loss']:.1%}")
    print(f"   • Max Positions: {settings['max_concurrent_positions']}")
    print(f"   • Min Confidence: {settings['minimum_confidence']:.0%}")
    print(f"   • Max Daily Trades: {settings['max_trades_per_day']}")
    print(f"   • Trading Symbol: {settings['trading_symbol']}")
    
    return True

async def test_exchange_connection():
    """Test exchange connection and account balance"""
    print("\n🔗 TESTING EXCHANGE CONNECTION")
    print("-" * 50)
    
    try:
        # Initialize exchange
        exchange_engine = CCXTTradingEngine('htx', demo_mode=False)
        if not exchange_engine.initialize_exchange():
            print("❌ Failed to connect to HTX exchange")
            return False
        
        print("✅ HTX exchange connection established")
        
        # Check account balance
        balance = exchange_engine.exchange.fetch_balance()
        usdt_balance = balance.get('USDT', {}).get('free', 0)
        
        print(f"✅ Account balance: ${usdt_balance:.2f} USDT")
        
        # Validate minimum balance
        min_balance = CONSERVATIVE_SETTINGS['initial_balance']
        if usdt_balance < min_balance:
            print(f"⚠️  Warning: Balance ${usdt_balance:.2f} is below recommended ${min_balance:.0f}")
            print("   Consider funding account or reducing initial balance")
        else:
            print(f"✅ Sufficient balance for trading")
        
        # Test market data
        ticker = exchange_engine.exchange.fetch_ticker('BTC/USDT:USDT')
        print(f"✅ Market data: BTC/USDT ${ticker['last']:.2f}")
        
        return True
        
    except Exception as e:
        print(f"❌ Exchange connection failed: {e}")
        return False

def create_production_monitoring():
    """Create production monitoring script"""
    monitor_script = f'''#!/usr/bin/env python3
"""
Epinnox Production Monitor
Real-time monitoring for live trading
"""

import time
import json
import glob
import os
from datetime import datetime

def monitor_production():
    """Monitor production trading"""
    print("📊 EPINNOX PRODUCTION MONITOR")
    print("=" * 40)
    print(f"Account: {ACCOUNT_NAME}")
    print(f"Started: {{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}}")
    print("-" * 40)
    
    while True:
        try:
            # Check for latest log
            log_files = glob.glob("epinnox_production_*.log")
            if log_files:
                latest_log = max(log_files, key=os.path.getctime)
                
                # Show last few lines
                with open(latest_log, 'r') as f:
                    lines = f.readlines()
                    if lines:
                        print(f"\\n📈 Latest Activity ({{datetime.now().strftime('%H:%M:%S')}}):")
                        for line in lines[-3:]:
                            if line.strip():
                                print(f"   {{line.strip()}}")
            
            # Check for trade reports
            report_files = glob.glob("production_trade_report_*.json")
            if report_files:
                latest_report = max(report_files, key=os.path.getctime)
                
                with open(latest_report, 'r') as f:
                    report = json.load(f)
                    
                print(f"\\n💰 Performance:")
                print(f"   • Total Trades: {{report.get('total_trades', 0)}}")
                print(f"   • Current Balance: ${{report.get('current_balance', 0):.2f}}")
                print(f"   • Total PnL: ${{report.get('total_pnl', 0):.2f}}")
            
            time.sleep(30)  # Update every 30 seconds
            
        except KeyboardInterrupt:
            print("\\n👋 Production monitoring stopped")
            break
        except Exception as e:
            print(f"❌ Monitor error: {{e}}")
            time.sleep(10)

if __name__ == "__main__":
    monitor_production()
'''
    
    with open('monitor_production.py', 'w', encoding='utf-8') as f:
        f.write(monitor_script)
    
    print("📊 Production monitor created: monitor_production.py")

async def deploy_production():
    """Deploy production trading system"""
    print("\n🚀 STARTING PRODUCTION DEPLOYMENT")
    print("-" * 50)
    
    # Load configuration
    config_file = "conservative_live_trading_config_20250628_124131.json"
    with open(config_file, 'r') as f:
        config = json.load(f)
    
    # Initialize portfolio manager
    initial_balance = config['trading_parameters']['initial_balance']
    portfolio_manager = PortfolioManager(initial_balance=initial_balance, max_positions=1)
    
    # Set conservative risk limits
    risk_config = config['risk_management']
    portfolio_manager.set_risk_limits(
        max_portfolio_risk=risk_config['max_portfolio_risk'],
        max_position_size=risk_config['max_position_size'],
        max_leverage=risk_config['max_leverage'],
        max_daily_loss=risk_config['max_daily_loss']
    )
    
    logger.info(f"✅ Production deployment initialized")
    logger.info(f"   Account: {ACCOUNT_NAME}")
    logger.info(f"   Initial Balance: ${initial_balance:.2f}")
    logger.info(f"   Risk Limits: {risk_config['max_portfolio_risk']:.1%} portfolio, {risk_config['max_position_size']:.1%} position")
    
    print("✅ Production system ready for trading")
    print(f"   Use: python start_paper_trading.py --live --balance {initial_balance}")
    print(f"   Monitor: python monitor_production.py")
    
    return True

async def main():
    """Main production deployment function"""
    display_production_banner()
    
    # Validate environment
    if not validate_production_environment():
        print("❌ Production environment validation failed")
        return False
    
    # Test exchange connection
    if not await test_exchange_connection():
        print("❌ Exchange connection test failed")
        return False
    
    # Create monitoring tools
    create_production_monitoring()
    
    # Deploy production system
    if not await deploy_production():
        print("❌ Production deployment failed")
        return False
    
    print("\n" + "="*60)
    print("🎉 EPINNOX PRODUCTION DEPLOYMENT COMPLETE!")
    print("="*60)
    print()
    print("🚀 READY FOR LIVE TRADING:")
    print(f"   Account: {ACCOUNT_NAME}")
    print(f"   Balance: ${CONSERVATIVE_SETTINGS['initial_balance']:.0f} minimum")
    print(f"   Risk: {CONSERVATIVE_SETTINGS['max_portfolio_risk']:.1%} portfolio, {CONSERVATIVE_SETTINGS['max_position_size']:.1%} position")
    print()
    print("📋 NEXT STEPS:")
    print("   1. python start_paper_trading.py --live --balance 3")
    print("   2. python monitor_production.py")
    print("   3. Monitor closely for first 24 hours")
    print()
    print("🛡️ SAFETY: Emergency stop with Ctrl+C")
    
    return True

if __name__ == "__main__":
    try:
        success = asyncio.run(main())
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\\n👋 Deployment cancelled by user")
        sys.exit(1)
    except Exception as e:
        print(f"❌ Deployment failed: {e}")
        sys.exit(1)
