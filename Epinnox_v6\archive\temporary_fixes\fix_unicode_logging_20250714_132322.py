#!/usr/bin/env python3
"""
UNICODE LOGGING FIX SCRIPT
Apply comprehensive Unicode logging fixes across all system components

FIXES APPLIED:
1. Configure Unicode-safe logging system-wide
2. Replace problematic Unicode characters in log messages
3. Set UTF-8 encoding for Windows compatibility
4. Test logging functionality across all components
"""

import os
import sys
import re
import logging
from pathlib import Path

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def apply_unicode_logging_fixes():
    """Apply Unicode logging fixes across the system"""
    
    print("UNICODE LOGGING FIX SCRIPT")
    print("=" * 40)
    
    fixes_applied = []
    
    # 1. Configure Unicode-safe logging
    print("\n1. Configuring Unicode-safe logging...")
    try:
        from core.unicode_safe_logging import configure_unicode_safe_logging
        configure_unicode_safe_logging()
        fixes_applied.append("Unicode-safe logging configured")
        print("   ✅ Unicode-safe logging configured")
    except Exception as e:
        print(f"   ❌ Failed to configure Unicode logging: {e}")
    
    # 2. Set environment variables
    print("\n2. Setting environment variables...")
    try:
        os.environ['PYTHONIOENCODING'] = 'utf-8'
        os.environ['PYTHONUTF8'] = '1'
        fixes_applied.append("Environment variables set for UTF-8")
        print("   ✅ Environment variables set for UTF-8")
    except Exception as e:
        print(f"   ❌ Failed to set environment variables: {e}")
    
    # 3. Test logging with problematic characters
    print("\n3. Testing Unicode logging...")
    try:
        test_logger = logging.getLogger('unicode_test')
        
        # Test messages that were causing issues
        test_messages = [
            "[TARGET] ScalperGPT test message",
            "[OK] System integration test",
            "[ERROR] Error recovery test",
            "[WARNING] Safety system test",
            "[LAUNCH] Deployment test"
        ]
        
        for msg in test_messages:
            test_logger.info(msg)
        
        fixes_applied.append("Unicode logging tested successfully")
        print("   ✅ Unicode logging tested successfully")
        
    except Exception as e:
        print(f"   ❌ Unicode logging test failed: {e}")
    
    # 4. Fix remaining Unicode issues in code
    print("\n4. Scanning for remaining Unicode issues...")
    
    # Files that commonly have Unicode issues
    files_to_check = [
        "core/scalper_gpt.py",
        "core/autonomous_trading_orchestrator.py", 
        "validation/live_trading_validator.py",
        "core/dynamic_risk_manager.py",
        "core/emergency_stop_coordinator.py",
        "deploy_live_validation.py"
    ]
    
    unicode_issues_found = 0
    
    for file_path in files_to_check:
        if os.path.exists(file_path):
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # Check for problematic Unicode characters in logging statements
                problematic_patterns = [
                    r'logger\.[a-z]+\([^)]*[🎯✅❌⚠️🚀📊🔧🛡️💰🔍📈📉🎉🚨⏰🔗📝🏆⚡🎭🔑📋🖥️📱🌐🔒🔓📦][^)]*\)',
                ]
                
                for pattern in problematic_patterns:
                    matches = re.findall(pattern, content)
                    if matches:
                        unicode_issues_found += len(matches)
                        print(f"   ⚠️ Found {len(matches)} Unicode issues in {file_path}")
                
            except Exception as e:
                print(f"   ❌ Error checking {file_path}: {e}")
    
    if unicode_issues_found == 0:
        print("   ✅ No remaining Unicode issues found")
        fixes_applied.append("No remaining Unicode issues detected")
    else:
        print(f"   ⚠️ Found {unicode_issues_found} potential Unicode issues")
    
    # 5. Verify WebSocket manager is available
    print("\n5. Verifying WebSocket manager...")
    try:
        from core.websocket_manager import websocket_manager
        status = websocket_manager.get_connection_status()
        fixes_applied.append("WebSocket manager available")
        print("   ✅ WebSocket manager available")
        print(f"   📊 Status: {status['state']}")
    except Exception as e:
        print(f"   ❌ WebSocket manager issue: {e}")
    
    # 6. Test system integration after fixes
    print("\n6. Testing system integration after fixes...")
    try:
        from initialize_system_integration import initialize_system_integration
        success = initialize_system_integration()
        
        if success:
            fixes_applied.append("System integration working after fixes")
            print("   ✅ System integration working after fixes")
        else:
            print("   ⚠️ System integration has issues")
            
    except Exception as e:
        print(f"   ❌ System integration test failed: {e}")
    
    # Summary
    print("\n" + "=" * 40)
    print("UNICODE FIX SUMMARY")
    print("=" * 40)
    
    if fixes_applied:
        print(f"✅ FIXES APPLIED: {len(fixes_applied)}")
        for i, fix in enumerate(fixes_applied, 1):
            print(f"  {i}. {fix}")
    else:
        print("❌ NO FIXES APPLIED")
    
    print(f"\n📊 Unicode issues found: {unicode_issues_found}")
    
    if unicode_issues_found == 0 and len(fixes_applied) > 0:
        print("\n🎉 UNICODE LOGGING FIXES COMPLETE")
        print("✅ System should now run without Unicode errors")
        print("✅ Live trading validation can continue safely")
        return True
    else:
        print("\n⚠️ SOME ISSUES MAY REMAIN")
        print("🔧 Monitor logs for any remaining Unicode errors")
        return False

def test_live_system_status():
    """Test if the live trading system is still running properly"""
    
    print("\n" + "=" * 40)
    print("LIVE SYSTEM STATUS CHECK")
    print("=" * 40)
    
    try:
        # Check if validation is still running
        from validation.live_trading_validator import live_trading_validator
        
        if hasattr(live_trading_validator, 'validation_active'):
            if live_trading_validator.validation_active:
                print("✅ Live trading validation is ACTIVE")
                
                if hasattr(live_trading_validator, 'validation_start_time'):
                    start_time = live_trading_validator.validation_start_time
                    if start_time:
                        from datetime import datetime
                        duration = datetime.now() - start_time
                        hours = duration.total_seconds() / 3600
                        print(f"📊 Validation running for: {hours:.1f} hours")
                
                return True
            else:
                print("⚠️ Live trading validation is NOT active")
                return False
        else:
            print("⚠️ Cannot determine validation status")
            return False
            
    except Exception as e:
        print(f"❌ Error checking live system status: {e}")
        return False

def main():
    """Main fix application function"""
    
    try:
        print("🔧 APPLYING UNICODE LOGGING FIXES")
        print("⚠️ This will NOT interrupt live trading")
        print("=" * 50)
        
        # Apply fixes
        success = apply_unicode_logging_fixes()
        
        # Check live system status
        live_status = test_live_system_status()
        
        # Final summary
        print("\n" + "=" * 50)
        print("FINAL STATUS")
        print("=" * 50)
        
        if success:
            print("✅ UNICODE FIXES APPLIED SUCCESSFULLY")
        else:
            print("⚠️ UNICODE FIXES PARTIALLY APPLIED")
        
        if live_status:
            print("✅ LIVE TRADING VALIDATION CONTINUES RUNNING")
        else:
            print("⚠️ LIVE TRADING STATUS UNCLEAR")
        
        print("\n🎯 NEXT STEPS:")
        print("1. Monitor logs for reduced Unicode errors")
        print("2. Continue monitoring live trading validation")
        print("3. Check GUI dashboard for real-time status")
        print("4. Emergency stop available if needed")
        
        return 0 if success else 1
        
    except Exception as e:
        print(f"\n❌ UNICODE FIX ERROR: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
