"""
Autonomous Trading Tab - Phase 3 GUI Component
Provides interface for autonomous trading controls, validation, and safety systems
"""

try:
    from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QGridLayout,
                                QLabel, QPushButton, QTextEdit, QProgressBar,
                                QGroupBox, QCheckBox, QSpinBox, QComboBox,
                                QTableWidget, QTableWidgetItem, QTabWidget,
                                QFrame, QScrollArea, QSplitter)
    from PyQt5.QtCore import Qt, QTimer, pyqtSignal
    from PyQt5.QtGui import QFont, QColor, QPalette
    PYQT_AVAILABLE = True
except ImportError:
    PYQT_AVAILABLE = False

import logging
from typing import Dict, Any, Optional
from datetime import datetime
import json

logger = logging.getLogger(__name__)

if PYQT_AVAILABLE:
    class AutonomousTradingTab(QWidget):
        """Phase 3 Autonomous Trading and Safety Controls Tab"""
        
        # Signals
        start_autonomous_trading = pyqtSignal(str)  # mode
        stop_autonomous_trading = pyqtSignal()
        run_validation = pyqtSignal(str, dict)  # validation_type, params
        emergency_stop = pyqtSignal(str)  # reason
        
        def __init__(self, parent=None):
            super().__init__(parent)
            self.gui_integration = None
            self.validation_status = {}
            self.safety_metrics = {}
            self.setup_ui()
            self.setup_timers()
            
            logger.info("Autonomous Trading Tab initialized")
        
        def setup_ui(self):
            """Setup the user interface"""
            main_layout = QVBoxLayout(self)
            
            # Title
            title_label = QLabel("Phase 3: Autonomous Trading & Safety Systems")
            title_label.setFont(QFont("Arial", 16, QFont.Bold))
            title_label.setAlignment(Qt.AlignCenter)
            main_layout.addWidget(title_label)
            
            # Create main content areas
            splitter = QSplitter(Qt.Horizontal)
            main_layout.addWidget(splitter)
            
            # Left panel - Controls
            left_panel = self.create_controls_panel()
            splitter.addWidget(left_panel)
            
            # Right panel - Status and monitoring
            right_panel = self.create_monitoring_panel()
            splitter.addWidget(right_panel)
            
            # Set splitter proportions
            splitter.setSizes([400, 600])
        
        def create_controls_panel(self):
            """Create the controls panel"""
            scroll_area = QScrollArea()
            scroll_widget = QWidget()
            layout = QVBoxLayout(scroll_widget)
            
            # Autonomous Trading Controls
            trading_group = QGroupBox("Autonomous Trading Controls")
            trading_layout = QVBoxLayout(trading_group)
            
            # Trading mode selection
            mode_layout = QHBoxLayout()
            mode_layout.addWidget(QLabel("Trading Mode:"))
            self.trading_mode_combo = QComboBox()
            self.trading_mode_combo.addItems(["Paper Trading", "Live Trading"])
            mode_layout.addWidget(self.trading_mode_combo)
            trading_layout.addLayout(mode_layout)
            
            # Start/Stop buttons
            button_layout = QHBoxLayout()
            self.start_autonomous_btn = QPushButton("Start Autonomous Trading")
            self.start_autonomous_btn.clicked.connect(self.start_autonomous_trading_clicked)
            self.start_autonomous_btn.setStyleSheet("QPushButton { background-color: #4CAF50; color: white; font-weight: bold; }")
            button_layout.addWidget(self.start_autonomous_btn)
            
            self.stop_autonomous_btn = QPushButton("Stop Autonomous Trading")
            self.stop_autonomous_btn.clicked.connect(self.stop_autonomous_trading_clicked)
            self.stop_autonomous_btn.setEnabled(False)
            self.stop_autonomous_btn.setStyleSheet("QPushButton { background-color: #f44336; color: white; font-weight: bold; }")
            button_layout.addWidget(self.stop_autonomous_btn)
            trading_layout.addLayout(button_layout)
            
            layout.addWidget(trading_group)
            
            # Validation Controls
            validation_group = QGroupBox("Phase 3 Validation Systems")
            validation_layout = QVBoxLayout(validation_group)
            
            # Paper Trading Validation
            paper_layout = QHBoxLayout()
            self.paper_validation_btn = QPushButton("Run Paper Trading Validation")
            self.paper_validation_btn.clicked.connect(self.run_paper_validation)
            paper_layout.addWidget(self.paper_validation_btn)
            
            self.paper_duration_spin = QSpinBox()
            self.paper_duration_spin.setRange(1, 30)
            self.paper_duration_spin.setValue(7)
            self.paper_duration_spin.setSuffix(" days")
            paper_layout.addWidget(QLabel("Duration:"))
            paper_layout.addWidget(self.paper_duration_spin)
            validation_layout.addLayout(paper_layout)
            
            # Market Data Testing
            market_layout = QHBoxLayout()
            self.market_test_btn = QPushButton("Run Live Market Data Test")
            self.market_test_btn.clicked.connect(self.run_market_data_test)
            market_layout.addWidget(self.market_test_btn)
            
            self.market_duration_spin = QSpinBox()
            self.market_duration_spin.setRange(5, 120)
            self.market_duration_spin.setValue(30)
            self.market_duration_spin.setSuffix(" minutes")
            market_layout.addWidget(QLabel("Duration:"))
            market_layout.addWidget(self.market_duration_spin)
            validation_layout.addLayout(market_layout)
            
            # Safety System Validation
            safety_layout = QHBoxLayout()
            self.safety_validation_btn = QPushButton("Run Safety System Validation")
            self.safety_validation_btn.clicked.connect(self.run_safety_validation)
            safety_layout.addWidget(self.safety_validation_btn)
            validation_layout.addLayout(safety_layout)
            
            # Deployment Readiness Check
            deployment_layout = QHBoxLayout()
            self.deployment_check_btn = QPushButton("Check Deployment Readiness")
            self.deployment_check_btn.clicked.connect(self.check_deployment_readiness)
            deployment_layout.addWidget(self.deployment_check_btn)
            validation_layout.addLayout(deployment_layout)
            
            layout.addWidget(validation_group)
            
            # Emergency Controls
            emergency_group = QGroupBox("Emergency Controls")
            emergency_layout = QVBoxLayout(emergency_group)
            
            self.emergency_stop_btn = QPushButton("EMERGENCY STOP")
            self.emergency_stop_btn.clicked.connect(self.emergency_stop_clicked)
            self.emergency_stop_btn.setStyleSheet("""
                QPushButton { 
                    background-color: #ff0000; 
                    color: white; 
                    font-weight: bold; 
                    font-size: 14px;
                    min-height: 40px;
                }
                QPushButton:hover { 
                    background-color: #cc0000; 
                }
            """)
            emergency_layout.addWidget(self.emergency_stop_btn)
            
            layout.addWidget(emergency_group)
            
            # Ultra-Conservative Settings (optimized for $50 account)
            settings_group = QGroupBox("Ultra-Conservative Settings ($50 Account)")
            settings_layout = QGridLayout(settings_group)

            settings_layout.addWidget(QLabel("Max Total Exposure:"), 0, 0)
            settings_layout.addWidget(QLabel("$50"), 0, 1)

            settings_layout.addWidget(QLabel("Max Position Size:"), 1, 0)
            settings_layout.addWidget(QLabel("$15"), 1, 1)

            settings_layout.addWidget(QLabel("Max Daily Loss:"), 2, 0)
            settings_layout.addWidget(QLabel("$5"), 2, 1)

            settings_layout.addWidget(QLabel("Portfolio Risk:"), 3, 0)
            settings_layout.addWidget(QLabel("1.5%"), 3, 1)

            settings_layout.addWidget(QLabel("Stop Loss:"), 4, 0)
            settings_layout.addWidget(QLabel("0.8%"), 4, 1)

            settings_layout.addWidget(QLabel("Take Profit:"), 5, 0)
            settings_layout.addWidget(QLabel("1.6%"), 5, 1)
            
            layout.addWidget(settings_group)
            
            scroll_area.setWidget(scroll_widget)
            scroll_area.setWidgetResizable(True)
            return scroll_area
        
        def create_monitoring_panel(self):
            """Create the monitoring panel"""
            tab_widget = QTabWidget()
            
            # Validation Status Tab
            validation_tab = self.create_validation_status_tab()
            tab_widget.addTab(validation_tab, "Validation Status")
            
            # Safety Monitoring Tab
            safety_tab = self.create_safety_monitoring_tab()
            tab_widget.addTab(safety_tab, "Safety Monitoring")

            # Safety Controls Tab
            safety_controls_tab = self.create_safety_controls_tab()
            tab_widget.addTab(safety_controls_tab, "Safety Controls")
            
            # Live Status Tab
            live_tab = self.create_live_status_tab()
            tab_widget.addTab(live_tab, "Live Status")
            
            return tab_widget
        
        def create_validation_status_tab(self):
            """Create validation status monitoring tab"""
            widget = QWidget()
            layout = QVBoxLayout(widget)
            
            # Validation Status Table
            self.validation_table = QTableWidget(0, 4)
            self.validation_table.setHorizontalHeaderLabels([
                "Validation Type", "Status", "Score", "Last Run"
            ])
            layout.addWidget(self.validation_table)
            
            # Validation Log
            log_label = QLabel("Validation Log:")
            log_label.setFont(QFont("Arial", 10, QFont.Bold))
            layout.addWidget(log_label)
            
            self.validation_log = QTextEdit()
            self.validation_log.setMaximumHeight(200)
            self.validation_log.setReadOnly(True)
            layout.addWidget(self.validation_log)
            
            return widget
        
        def create_safety_monitoring_tab(self):
            """Create safety monitoring tab"""
            widget = QWidget()
            layout = QVBoxLayout(widget)
            
            # Safety Metrics
            metrics_group = QGroupBox("Safety Metrics")
            metrics_layout = QGridLayout(metrics_group)
            
            self.safety_labels = {}
            safety_metrics = [
                "Emergency Stop Status", "Trading Enabled", "Cycle Count",
                "Last Decision", "Risk Level", "Position Count"
            ]
            
            for i, metric in enumerate(safety_metrics):
                label = QLabel(f"{metric}:")
                value_label = QLabel("N/A")
                value_label.setStyleSheet("font-weight: bold;")
                
                metrics_layout.addWidget(label, i, 0)
                metrics_layout.addWidget(value_label, i, 1)
                self.safety_labels[metric] = value_label
            
            layout.addWidget(metrics_group)
            
            # Safety Log
            log_label = QLabel("Safety Log:")
            log_label.setFont(QFont("Arial", 10, QFont.Bold))
            layout.addWidget(log_label)
            
            self.safety_log = QTextEdit()
            self.safety_log.setReadOnly(True)
            layout.addWidget(self.safety_log)
            
            return widget

        def create_safety_controls_tab(self):
            """Create safety controls tab"""
            try:
                from .safety_controls_widget import SafetyControlsWidget
                safety_widget = SafetyControlsWidget()

                # Connect safety widget signals
                safety_widget.emergency_stop_triggered.connect(self.emergency_stop)
                safety_widget.safety_setting_changed.connect(self.on_safety_setting_changed)
                safety_widget.safety_check_requested.connect(self.run_safety_validation)

                # Store reference for later use
                self.safety_controls_widget = safety_widget

                return safety_widget

            except ImportError as e:
                logger.error(f"Failed to import SafetyControlsWidget: {e}")
                # Create placeholder
                placeholder = QWidget()
                layout = QVBoxLayout(placeholder)
                label = QLabel("Safety Controls Widget\n(Not available)")
                label.setAlignment(Qt.AlignCenter)
                layout.addWidget(label)
                return placeholder
        
        def create_live_status_tab(self):
            """Create live status monitoring tab"""
            widget = QWidget()
            layout = QVBoxLayout(widget)
            
            # Status indicators
            status_group = QGroupBox("System Status")
            status_layout = QGridLayout(status_group)
            
            # Autonomous Trading Status
            status_layout.addWidget(QLabel("Autonomous Trading:"), 0, 0)
            self.autonomous_status_label = QLabel("STOPPED")
            self.autonomous_status_label.setStyleSheet("color: red; font-weight: bold;")
            status_layout.addWidget(self.autonomous_status_label, 0, 1)
            
            # Deployment Readiness
            status_layout.addWidget(QLabel("Deployment Ready:"), 1, 0)
            self.deployment_status_label = QLabel("NOT CHECKED")
            self.deployment_status_label.setStyleSheet("color: orange; font-weight: bold;")
            status_layout.addWidget(self.deployment_status_label, 1, 1)
            
            # Overall Safety Score
            status_layout.addWidget(QLabel("Safety Score:"), 2, 0)
            self.safety_score_label = QLabel("N/A")
            self.safety_score_label.setStyleSheet("font-weight: bold;")
            status_layout.addWidget(self.safety_score_label, 2, 1)
            
            layout.addWidget(status_group)
            
            # Real-time log
            log_label = QLabel("Real-time Activity Log:")
            log_label.setFont(QFont("Arial", 10, QFont.Bold))
            layout.addWidget(log_label)
            
            self.activity_log = QTextEdit()
            self.activity_log.setReadOnly(True)
            layout.addWidget(self.activity_log)
            
            return widget
        
        def setup_timers(self):
            """Setup update timers with thread safety"""
            # Update timer for status monitoring
            self.update_timer = QTimer()
            self.update_timer.timeout.connect(self.update_status)
            self.update_timer.setSingleShot(False)

            # Ensure timer starts on main thread
            from PyQt5.QtCore import QMetaObject, Qt
            from PyQt5.QtWidgets import QApplication

            if QApplication.instance() is not None:
                # Use thread-safe timer start
                QMetaObject.invokeMethod(self.update_timer, "start", Qt.QueuedConnection, 5000)
            else:
                self.update_timer.start(5000)  # Fallback
        
        def set_gui_integration(self, gui_integration):
            """Set the GUI integration instance"""
            self.gui_integration = gui_integration
            if gui_integration:
                # Connect signals
                gui_integration.validation_completed.connect(self.on_validation_completed)
                gui_integration.safety_alert.connect(self.on_safety_alert)
                gui_integration.deployment_status_changed.connect(self.on_deployment_status_changed)
                gui_integration.status_changed.connect(self.on_status_changed)

                # Connect safety controls widget if available
                if hasattr(self, 'safety_controls_widget') and self.safety_controls_widget:
                    self.safety_controls_widget.set_gui_integration(gui_integration)
        
        # Event handlers
        def start_autonomous_trading_clicked(self):
            """Handle start autonomous trading button click"""
            mode = "paper" if self.trading_mode_combo.currentText() == "Paper Trading" else "live"
            self.start_autonomous_trading.emit(mode)
            
            if self.gui_integration:
                success = self.gui_integration.start_autonomous_trading(mode)
                if success:
                    self.start_autonomous_btn.setEnabled(False)
                    self.stop_autonomous_btn.setEnabled(True)
                    self.autonomous_status_label.setText("RUNNING")
                    self.autonomous_status_label.setStyleSheet("color: green; font-weight: bold;")
                    self.log_activity(f"Autonomous trading started in {mode} mode")
        
        def stop_autonomous_trading_clicked(self):
            """Handle stop autonomous trading button click"""
            self.stop_autonomous_trading.emit()
            
            if self.gui_integration:
                success = self.gui_integration.stop_autonomous_trading()
                if success:
                    self.start_autonomous_btn.setEnabled(True)
                    self.stop_autonomous_btn.setEnabled(False)
                    self.autonomous_status_label.setText("STOPPED")
                    self.autonomous_status_label.setStyleSheet("color: red; font-weight: bold;")
                    self.log_activity("Autonomous trading stopped")
        
        def run_paper_validation(self):
            """Run paper trading validation"""
            duration = self.paper_duration_spin.value()
            self.run_validation.emit("paper_trading", {"duration_days": duration})
            
            if self.gui_integration:
                self.gui_integration.run_paper_trading_validation(duration)
                self.log_validation(f"Started paper trading validation ({duration} days)")
        
        def run_market_data_test(self):
            """Run market data test"""
            duration = self.market_duration_spin.value()
            self.run_validation.emit("market_data", {"duration_minutes": duration})
            
            if self.gui_integration:
                self.gui_integration.run_live_market_data_test(duration)
                self.log_validation(f"Started live market data test ({duration} minutes)")
        
        def run_safety_validation(self):
            """Run safety system validation"""
            self.run_validation.emit("safety_validation", {})
            
            if self.gui_integration:
                self.gui_integration.run_safety_system_validation()
                self.log_validation("Started safety system validation")
        
        def check_deployment_readiness(self):
            """Check deployment readiness"""
            if self.gui_integration:
                self.gui_integration.check_deployment_readiness()
                self.log_validation("Checking deployment readiness...")
        
        def emergency_stop_clicked(self):
            """Handle emergency stop button click"""
            reason = "Manual emergency stop from GUI"
            self.emergency_stop.emit(reason)
            
            if self.gui_integration:
                self.gui_integration.emergency_stop(reason)
                self.log_activity(f"EMERGENCY STOP: {reason}")
        
        # Signal handlers
        def on_validation_completed(self, result):
            """Handle validation completion"""
            validation_type = result.get('type', 'unknown')
            success = result.get('success', False)
            
            self.validation_status[validation_type] = result
            self.update_validation_table()
            
            status = "PASSED" if success else "FAILED"
            self.log_validation(f"{validation_type} validation {status}")
        
        def on_safety_alert(self, level, message):
            """Handle safety alerts"""
            timestamp = datetime.now().strftime("%H:%M:%S")
            self.safety_log.append(f"[{timestamp}] {level}: {message}")
            self.log_activity(f"SAFETY ALERT ({level}): {message}")
        
        def on_deployment_status_changed(self, status):
            """Handle deployment status changes"""
            self.deployment_status_label.setText(status)
            
            if status == "READY":
                self.deployment_status_label.setStyleSheet("color: green; font-weight: bold;")
            elif status == "NOT_READY":
                self.deployment_status_label.setStyleSheet("color: red; font-weight: bold;")
            else:
                self.deployment_status_label.setStyleSheet("color: orange; font-weight: bold;")
        
        def on_status_changed(self, status):
            """Handle general status changes"""
            self.log_activity(f"Status: {status}")

        def on_safety_setting_changed(self, setting_name, value):
            """Handle safety setting changes"""
            self.log_activity(f"Safety setting changed: {setting_name} = {value}")

            # Apply setting to GUI integration if available
            if self.gui_integration:
                # Update configuration with new safety setting
                # This would be passed to the autonomous orchestrator
                pass
        
        # Update methods
        def update_status(self):
            """Update status displays"""
            if self.gui_integration:
                # Update safety metrics
                safety_metrics = self.gui_integration.get_safety_metrics()
                for metric, value in safety_metrics.items():
                    if metric in self.safety_labels:
                        self.safety_labels[metric].setText(str(value))
                
                # Update validation status
                validation_status = self.gui_integration.get_validation_status()
                self.validation_status.update(validation_status)
                self.update_validation_table()
        
        def update_validation_table(self):
            """Update the validation status table"""
            self.validation_table.setRowCount(len(self.validation_status))
            
            for row, (validation_type, status) in enumerate(self.validation_status.items()):
                if isinstance(status, dict):
                    result_status = "PASSED" if status.get('success', False) else "FAILED"
                    score = status.get('result', {}).get('overall_safety_score', 'N/A') if isinstance(status.get('result'), dict) else 'N/A'
                    last_run = datetime.now().strftime("%Y-%m-%d %H:%M")
                else:
                    result_status = "PASSED" if status else "FAILED"
                    score = "N/A"
                    last_run = "N/A"
                
                self.validation_table.setItem(row, 0, QTableWidgetItem(validation_type))
                self.validation_table.setItem(row, 1, QTableWidgetItem(result_status))
                self.validation_table.setItem(row, 2, QTableWidgetItem(str(score)))
                self.validation_table.setItem(row, 3, QTableWidgetItem(last_run))
        
        def log_validation(self, message):
            """Log validation message"""
            timestamp = datetime.now().strftime("%H:%M:%S")
            self.validation_log.append(f"[{timestamp}] {message}")
        
        def log_activity(self, message):
            """Log activity message"""
            timestamp = datetime.now().strftime("%H:%M:%S")
            self.activity_log.append(f"[{timestamp}] {message}")

else:
    # Fallback class when PyQt5 is not available
    class AutonomousTradingTab:
        def __init__(self, parent=None):
            logger.warning("PyQt5 not available - AutonomousTradingTab disabled")
