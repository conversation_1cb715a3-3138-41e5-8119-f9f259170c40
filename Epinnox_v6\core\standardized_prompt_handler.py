"""
Standardized Prompt Handler for Autonomous Trading
Provides consistent prompt formatting and response parsing across all LLM providers
"""

import json
import logging
import re
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from datetime import datetime
from enum import Enum

logger = logging.getLogger(__name__)

class PromptTemplate(Enum):
    """Standard prompt templates for trading decisions"""
    MARKET_ANALYSIS = "market_analysis"
    ENTRY_TIMING = "entry_timing"
    EXIT_STRATEGY = "exit_strategy"
    RISK_ASSESSMENT = "risk_assessment"
    POSITION_MANAGEMENT = "position_management"
    EMERGENCY_RESPONSE = "emergency_response"

@dataclass
class TradingDecision:
    """Standardized trading decision format"""
    action: str  # BUY, SELL, HOLD, WAIT
    confidence: float  # 0.0 to 1.0
    symbol: str
    quantity: Optional[float] = None
    price: Optional[float] = None
    stop_loss: Optional[float] = None
    take_profit: Optional[float] = None
    reasoning: str = ""
    risk_level: str = "MEDIUM"  # LOW, MEDIUM, HIGH
    urgency: str = "NORMAL"  # LOW, NORMAL, HIGH, EMERGENCY
    metadata: Dict[str, Any] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization"""
        return {
            'action': self.action,
            'confidence': self.confidence,
            'symbol': self.symbol,
            'quantity': self.quantity,
            'price': self.price,
            'stop_loss': self.stop_loss,
            'take_profit': self.take_profit,
            'reasoning': self.reasoning,
            'risk_level': self.risk_level,
            'urgency': self.urgency,
            'metadata': self.metadata or {}
        }

class StandardizedPromptHandler:
    """
    Handles prompt standardization and response parsing for autonomous trading
    Ensures consistent communication with all LLM providers
    """
    
    def __init__(self):
        """Initialize the prompt handler"""
        self.prompt_templates = self._load_prompt_templates()
        self.response_patterns = self._load_response_patterns()
        
        logger.info("[PROMPT_HANDLER] Standardized prompt handler initialized")
    
    def _load_prompt_templates(self) -> Dict[str, str]:
        """Load standardized prompt templates"""
        return {
            PromptTemplate.MARKET_ANALYSIS.value: """
AUTONOMOUS TRADING MARKET ANALYSIS

You are an expert autonomous trading AI analyzing market conditions for {symbol}.

CURRENT MARKET DATA:
- Symbol: {symbol}
- Current Price: ${current_price:.4f}
- 24h Change: {price_change_24h:.2f}%
- Volume: {volume_24h:,.0f}
- Spread: {spread:.4f}%

TECHNICAL INDICATORS:
{technical_indicators}

MARKET CONTEXT:
{market_context}

CURRENT POSITIONS:
{current_positions}

RISK PARAMETERS:
- Max Position Size: {max_position_size}%
- Max Daily Loss: {max_daily_loss}%
- Current Portfolio Risk: {portfolio_risk:.1f}%

INSTRUCTIONS:
Analyze the market conditions and provide a trading recommendation.
Use ONLY LIMIT ORDERS - market orders are prohibited.

REQUIRED RESPONSE FORMAT:
DECISION: [BUY/SELL/HOLD/WAIT]
CONFIDENCE: [50-100]%
QUANTITY: [position size as % of balance]
PRICE: [specific limit order price]
STOP_LOSS: [stop loss price]
TAKE_PROFIT: [take profit price]
RISK_LEVEL: [LOW/MEDIUM/HIGH]
URGENCY: [LOW/NORMAL/HIGH/EMERGENCY]

REASONING: [Detailed explanation of your analysis and decision]

Ensure all fields are included in your response.
""",
            
            PromptTemplate.ENTRY_TIMING.value: """
AUTONOMOUS TRADING ENTRY TIMING ANALYSIS

You are analyzing optimal entry timing for {symbol}.

CURRENT SITUATION:
- Symbol: {symbol}
- Current Price: ${current_price:.4f}
- Trend Direction: {trend_direction}
- Market Volatility: {volatility}
- Entry Signal Strength: {signal_strength}/10

TECHNICAL ANALYSIS:
{technical_analysis}

MARKET CONDITIONS:
{market_conditions}

INSTRUCTIONS:
Determine if this is an optimal entry point for a {intended_action} position.
Consider market timing, volatility, and risk factors.

REQUIRED RESPONSE FORMAT:
DECISION: [ENTER/WAIT/ABORT]
CONFIDENCE: [50-100]%
OPTIMAL_PRICE: [best entry price]
TIMING: [IMMEDIATE/WAIT_5MIN/WAIT_15MIN/WAIT_1H]
RISK_LEVEL: [LOW/MEDIUM/HIGH]

REASONING: [Detailed timing analysis]
""",
            
            PromptTemplate.POSITION_MANAGEMENT.value: """
AUTONOMOUS TRADING POSITION MANAGEMENT

You are managing existing positions for optimal performance.

CURRENT POSITIONS:
{current_positions}

MARKET CONDITIONS:
{market_conditions}

PERFORMANCE METRICS:
{performance_metrics}

RISK STATUS:
- Portfolio Risk: {portfolio_risk:.1f}%
- Daily P&L: {daily_pnl:.2f}%
- Unrealized P&L: {unrealized_pnl:.2f}%

INSTRUCTIONS:
Analyze current positions and recommend management actions.
Focus on risk management and profit optimization.

REQUIRED RESPONSE FORMAT:
ACTION: [HOLD/ADJUST_SL/ADJUST_TP/CLOSE_PARTIAL/CLOSE_ALL]
CONFIDENCE: [50-100]%
POSITIONS_TO_MODIFY: [list of position IDs]
NEW_STOP_LOSS: [new SL prices if adjusting]
NEW_TAKE_PROFIT: [new TP prices if adjusting]
URGENCY: [LOW/NORMAL/HIGH/EMERGENCY]

REASONING: [Detailed position management analysis]
""",
            
            PromptTemplate.EMERGENCY_RESPONSE.value: """
EMERGENCY TRADING RESPONSE

CRITICAL SITUATION DETECTED - IMMEDIATE RESPONSE REQUIRED

EMERGENCY TYPE: {emergency_type}
SEVERITY: {severity}

CURRENT STATUS:
{current_status}

RISK METRICS:
- Portfolio Risk: {portfolio_risk:.1f}%
- Daily Loss: {daily_loss:.2f}%
- Open Positions: {open_positions}

INSTRUCTIONS:
Provide immediate emergency response to protect capital.
Priority: Capital preservation over profit.

REQUIRED RESPONSE FORMAT:
ACTION: [EMERGENCY_STOP/CLOSE_ALL/REDUCE_RISK/MONITOR]
CONFIDENCE: [80-100]%
IMMEDIATE_ACTIONS: [list of immediate actions]
URGENCY: EMERGENCY

REASONING: [Emergency analysis and justification]
"""
        }
    
    def _load_response_patterns(self) -> Dict[str, str]:
        """Load response parsing patterns"""
        return {
            'decision': r'DECISION:\s*([A-Z_]+)',
            'confidence': r'CONFIDENCE:\s*(\d+(?:\.\d+)?)%?',
            'quantity': r'QUANTITY:\s*(\d+(?:\.\d+)?)%?',
            'price': r'PRICE:\s*\$?(\d+(?:\.\d+)?)',
            'stop_loss': r'STOP_LOSS:\s*\$?(\d+(?:\.\d+)?)',
            'take_profit': r'TAKE_PROFIT:\s*\$?(\d+(?:\.\d+)?)',
            'risk_level': r'RISK_LEVEL:\s*([A-Z]+)',
            'urgency': r'URGENCY:\s*([A-Z]+)',
            'reasoning': r'REASONING:\s*(.+?)(?=\n[A-Z_]+:|$)',
            'action': r'ACTION:\s*([A-Z_]+)',
            'timing': r'TIMING:\s*([A-Z_0-9]+)'
        }
    
    def build_prompt(self, template: PromptTemplate, context: Dict[str, Any]) -> str:
        """Build standardized prompt from template and context"""
        try:
            template_str = self.prompt_templates.get(template.value, "")
            if not template_str:
                logger.error(f"[PROMPT_HANDLER] Template not found: {template.value}")
                return ""
            
            # Format template with context
            formatted_prompt = template_str.format(**context)
            
            # Add timestamp and system context
            timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S UTC")
            system_context = f"\nTIMESTAMP: {timestamp}\nSYSTEM: Epinnox Autonomous Trading v6\n"
            
            return system_context + formatted_prompt
            
        except Exception as e:
            logger.error(f"[PROMPT_HANDLER] Failed to build prompt: {e}")
            return ""
    
    def parse_response(self, response: str, template: PromptTemplate) -> Optional[TradingDecision]:
        """Parse LLM response into standardized trading decision"""
        try:
            if not response:
                return None
            
            # Extract fields using regex patterns
            extracted = {}
            for field, pattern in self.response_patterns.items():
                match = re.search(pattern, response, re.IGNORECASE | re.DOTALL)
                if match:
                    extracted[field] = match.group(1).strip()
            
            # Validate required fields based on template
            required_fields = self._get_required_fields(template)
            for field in required_fields:
                if field not in extracted:
                    logger.warning(f"[PROMPT_HANDLER] Missing required field: {field}")
            
            # Build trading decision
            decision = TradingDecision(
                action=extracted.get('decision', extracted.get('action', 'WAIT')).upper(),
                confidence=self._parse_confidence(extracted.get('confidence', '50')),
                symbol=extracted.get('symbol', ''),
                quantity=self._parse_float(extracted.get('quantity')),
                price=self._parse_float(extracted.get('price')),
                stop_loss=self._parse_float(extracted.get('stop_loss')),
                take_profit=self._parse_float(extracted.get('take_profit')),
                reasoning=extracted.get('reasoning', '').strip(),
                risk_level=extracted.get('risk_level', 'MEDIUM').upper(),
                urgency=extracted.get('urgency', 'NORMAL').upper(),
                metadata={
                    'template': template.value,
                    'raw_response': response,
                    'extracted_fields': extracted
                }
            )
            
            # Validate decision
            if self._validate_decision(decision):
                return decision
            else:
                logger.warning("[PROMPT_HANDLER] Decision validation failed")
                return None
                
        except Exception as e:
            logger.error(f"[PROMPT_HANDLER] Failed to parse response: {e}")
            return None
    
    def _get_required_fields(self, template: PromptTemplate) -> List[str]:
        """Get required fields for a template"""
        base_fields = ['decision', 'confidence']
        
        if template == PromptTemplate.MARKET_ANALYSIS:
            return base_fields + ['quantity', 'price', 'risk_level']
        elif template == PromptTemplate.ENTRY_TIMING:
            return base_fields + ['price', 'timing']
        elif template == PromptTemplate.POSITION_MANAGEMENT:
            return base_fields + ['action', 'urgency']
        elif template == PromptTemplate.EMERGENCY_RESPONSE:
            return base_fields + ['action', 'urgency']
        else:
            return base_fields
    
    def _parse_confidence(self, confidence_str: str) -> float:
        """Parse confidence value"""
        try:
            confidence = float(confidence_str.replace('%', ''))
            return max(0.0, min(1.0, confidence / 100.0))
        except:
            return 0.5
    
    def _parse_float(self, value_str: Optional[str]) -> Optional[float]:
        """Parse float value"""
        if not value_str:
            return None
        try:
            return float(value_str.replace('$', '').replace('%', ''))
        except:
            return None
    
    def _validate_decision(self, decision: TradingDecision) -> bool:
        """Validate trading decision"""
        # Check action is valid
        valid_actions = ['BUY', 'SELL', 'HOLD', 'WAIT', 'ENTER', 'ABORT', 'EMERGENCY_STOP', 'CLOSE_ALL', 'REDUCE_RISK', 'MONITOR']
        if decision.action not in valid_actions:
            return False
        
        # Check confidence is in valid range
        if not (0.0 <= decision.confidence <= 1.0):
            return False
        
        # Check risk level is valid
        valid_risk_levels = ['LOW', 'MEDIUM', 'HIGH']
        if decision.risk_level not in valid_risk_levels:
            return False
        
        # Check urgency is valid
        valid_urgency = ['LOW', 'NORMAL', 'HIGH', 'EMERGENCY']
        if decision.urgency not in valid_urgency:
            return False
        
        return True
    
    def format_context(self, symbol: str, market_data: Dict[str, Any], 
                      positions: List[Dict] = None, risk_metrics: Dict[str, Any] = None) -> Dict[str, Any]:
        """Format trading context for prompt templates"""
        context = {
            'symbol': symbol,
            'current_price': market_data.get('price', 0),
            'price_change_24h': market_data.get('change_24h', 0),
            'volume_24h': market_data.get('volume_24h', 0),
            'spread': market_data.get('spread', 0),
            'technical_indicators': self._format_technical_indicators(market_data.get('indicators', {})),
            'market_context': self._format_market_context(market_data.get('context', {})),
            'current_positions': self._format_positions(positions or []),
            'max_position_size': risk_metrics.get('max_position_size', 30) if risk_metrics else 30,
            'max_daily_loss': risk_metrics.get('max_daily_loss', 20) if risk_metrics else 20,
            'portfolio_risk': risk_metrics.get('portfolio_risk', 0) if risk_metrics else 0
        }
        
        return context
    
    def _format_technical_indicators(self, indicators: Dict[str, Any]) -> str:
        """Format technical indicators for prompt"""
        if not indicators:
            return "No technical indicators available"
        
        formatted = []
        for name, value in indicators.items():
            if isinstance(value, (int, float)):
                formatted.append(f"- {name}: {value:.4f}")
            else:
                formatted.append(f"- {name}: {value}")
        
        return "\n".join(formatted)
    
    def _format_market_context(self, context: Dict[str, Any]) -> str:
        """Format market context for prompt"""
        if not context:
            return "No market context available"
        
        formatted = []
        for key, value in context.items():
            formatted.append(f"- {key}: {value}")
        
        return "\n".join(formatted)
    
    def _format_positions(self, positions: List[Dict]) -> str:
        """Format current positions for prompt"""
        if not positions:
            return "No open positions"
        
        formatted = []
        for pos in positions:
            formatted.append(f"- {pos.get('symbol', 'Unknown')}: {pos.get('side', 'Unknown')} "
                           f"{pos.get('size', 0)} @ ${pos.get('entry_price', 0):.4f} "
                           f"(P&L: {pos.get('unrealized_pnl', 0):.2f}%)")
        
        return "\n".join(formatted)
