#!/usr/bin/env python3
"""
🔗 DYNAMIC RISK INTEGRATION MODULE
Connects dynamic risk manager with all existing systems for real-time parameter updates

INTEGRATED SYSTEMS:
- Autonomous Trading Orchestrator
- Emergency Stop Coordinator
- ScalperGPT Integration
- Symbol Scanner
- Timer Coordinator
- Deployment Scripts
- Unified Execution Engine
"""

import logging
from typing import Dict, Any, Optional
from core.dynamic_risk_manager import dynamic_risk_manager, RiskParameters

logger = logging.getLogger(__name__)


class DynamicRiskIntegration:
    """
    CRITICAL: Integration layer for dynamic risk management
    
    Connects risk manager with all existing systems to ensure
    real-time parameter updates across the entire trading system.
    """
    
    def __init__(self):
        self.orchestrator = None
        self.scalper_gpt = None
        self.symbol_scanner = None
        self.deployment_manager = None
        self.execution_engine = None
        
        # Register for parameter changes
        dynamic_risk_manager.register_parameter_change_callback(self.on_risk_parameters_changed)
        
        logger.info("Dynamic Risk Integration initialized")
    
    def register_orchestrator(self, orchestrator):
        """Register autonomous trading orchestrator"""
        self.orchestrator = orchestrator
        logger.info("Orchestrator registered with dynamic risk integration")
    
    def register_scalper_gpt(self, scalper_gpt):
        """Register ScalperGPT instance"""
        self.scalper_gpt = scalper_gpt
        logger.info("ScalperGPT registered with dynamic risk integration")
    
    def register_symbol_scanner(self, symbol_scanner):
        """Register symbol scanner"""
        self.symbol_scanner = symbol_scanner
        logger.info("Symbol scanner registered with dynamic risk integration")
    
    def register_deployment_manager(self, deployment_manager):
        """Register deployment manager"""
        self.deployment_manager = deployment_manager
        logger.info("Deployment manager registered with dynamic risk integration")
    
    def register_execution_engine(self, execution_engine):
        """Register unified execution engine"""
        self.execution_engine = execution_engine
        logger.info("Execution engine registered with dynamic risk integration")

    def register_dynamic_risk_manager(self, dynamic_risk_manager):
        """Register dynamic risk manager"""
        self.dynamic_risk_manager = dynamic_risk_manager
        logger.info("Dynamic risk manager registered with dynamic risk integration")

    def register_emergency_coordinator(self, emergency_coordinator):
        """Register emergency stop coordinator"""
        self.emergency_coordinator = emergency_coordinator
        logger.info("Emergency coordinator registered with dynamic risk integration")
    
    def on_risk_parameters_changed(self, old_parameters: RiskParameters, new_parameters: RiskParameters):
        """Handle risk parameter changes across all systems"""
        try:
            logger.info("Updating all systems with new risk parameters")
            
            # Update orchestrator configuration
            self.update_orchestrator_config(new_parameters)
            
            # Update ScalperGPT quality thresholds
            self.update_scalper_gpt_config(new_parameters)
            
            # Update symbol scanner thresholds
            self.update_symbol_scanner_config(new_parameters)
            
            # Update timer coordination intervals
            self.update_timer_coordination(new_parameters)
            
            # Update emergency stop thresholds
            self.update_emergency_stop_config(new_parameters)
            
            # Update execution engine limits
            self.update_execution_engine_config(new_parameters)
            
            # Update deployment configuration
            self.update_deployment_config(new_parameters)
            
            logger.info("All systems updated with new risk parameters")
            
        except Exception as e:
            logger.error(f"Error updating systems with new risk parameters: {e}")
    
    def update_orchestrator_config(self, parameters: RiskParameters):
        """Update autonomous trading orchestrator configuration"""
        try:
            if self.orchestrator:
                # Update orchestrator configuration
                config_updates = {
                    'max_trading_capital': parameters.max_trading_capital,
                    'position_size_pct': parameters.position_size_pct,
                    'portfolio_risk_pct': parameters.portfolio_risk_pct,
                    'max_leverage': parameters.max_leverage,
                    'max_concurrent_positions': parameters.max_concurrent_positions,
                    'max_daily_loss_pct': parameters.max_daily_loss_pct,
                    'symbol_quality_threshold': parameters.symbol_quality_threshold
                }
                
                # Apply configuration updates
                if hasattr(self.orchestrator, 'update_configuration'):
                    self.orchestrator.update_configuration(config_updates)
                    logger.info("Orchestrator configuration updated")
                else:
                    # Fallback: update individual attributes
                    for key, value in config_updates.items():
                        if hasattr(self.orchestrator, key):
                            setattr(self.orchestrator, key, value)
                    logger.info("Orchestrator attributes updated")
            
        except Exception as e:
            logger.error(f"Error updating orchestrator config: {e}")
    
    def update_scalper_gpt_config(self, parameters: RiskParameters):
        """Update ScalperGPT quality thresholds"""
        try:
            if self.scalper_gpt:
                # Update quality thresholds
                if hasattr(self.scalper_gpt, 'quality_thresholds'):
                    self.scalper_gpt.quality_thresholds.update({
                        'spread_quality': parameters.scalper_spread_quality,
                        'decision_quality': parameters.scalper_decision_quality
                    })
                    logger.info("ScalperGPT quality thresholds updated")
                
                # Update analysis interval if supported
                if hasattr(self.scalper_gpt, 'analysis_interval'):
                    self.scalper_gpt.analysis_interval = parameters.scalper_analysis_interval
                    logger.info("ScalperGPT analysis interval updated")
            
        except Exception as e:
            logger.error(f"Error updating ScalperGPT config: {e}")
    
    def update_symbol_scanner_config(self, parameters: RiskParameters):
        """Update symbol scanner configuration"""
        try:
            if self.symbol_scanner:
                # Update quality threshold
                if hasattr(self.symbol_scanner, 'quality_threshold'):
                    self.symbol_scanner.quality_threshold = parameters.symbol_quality_threshold
                    logger.info("Symbol scanner quality threshold updated")
                
                # Update scan interval if supported
                if hasattr(self.symbol_scanner, 'scan_interval'):
                    self.symbol_scanner.scan_interval = parameters.symbol_scanner_interval
                    logger.info("Symbol scanner interval updated")
            
        except Exception as e:
            logger.error(f"Error updating symbol scanner config: {e}")
    
    def update_timer_coordination(self, parameters: RiskParameters):
        """Update timer coordination intervals"""
        try:
            # Import timer coordinator
            from core.timer_coordinator import timer_coordinator
            
            # Update timer intervals
            interval_updates = {
                'autonomous_decision_loop': parameters.decision_loop_interval,
                'scalper_gpt_analysis': parameters.scalper_analysis_interval,
                'symbol_scanner_update': parameters.symbol_scanner_interval
            }
            
            # Apply interval updates
            if hasattr(timer_coordinator, 'update_intervals'):
                timer_coordinator.update_intervals(interval_updates)
                logger.info("Timer coordination intervals updated")
            else:
                # Fallback: update individual timers
                for timer_name, interval in interval_updates.items():
                    if hasattr(timer_coordinator, 'update_timer_interval'):
                        timer_coordinator.update_timer_interval(timer_name, interval)
                logger.info("Individual timer intervals updated")
            
        except Exception as e:
            logger.error(f"Error updating timer coordination: {e}")
    
    def update_emergency_stop_config(self, parameters: RiskParameters):
        """Update emergency stop coordinator thresholds"""
        try:
            # Import emergency coordinator
            from core.emergency_stop_coordinator import emergency_coordinator
            
            # Update emergency thresholds
            threshold_updates = {
                'loss_threshold': parameters.emergency_loss_threshold,
                'drawdown_threshold': parameters.emergency_drawdown_threshold,
                'daily_loss_threshold': parameters.max_daily_loss_pct
            }
            
            # Apply threshold updates
            if hasattr(emergency_coordinator, 'update_thresholds'):
                emergency_coordinator.update_thresholds(threshold_updates)
                logger.info("Emergency stop thresholds updated")
            
        except Exception as e:
            logger.error(f"Error updating emergency stop config: {e}")
    
    def update_execution_engine_config(self, parameters: RiskParameters):
        """Update unified execution engine configuration"""
        try:
            if self.execution_engine:
                # Update execution limits
                config_updates = {
                    'max_position_size': parameters.position_size_pct / 100.0,
                    'max_leverage': parameters.max_leverage,
                    'max_concurrent_positions': parameters.max_concurrent_positions,
                    'daily_loss_limit': parameters.max_daily_loss_pct / 100.0
                }
                
                # Apply configuration updates
                if hasattr(self.execution_engine, 'update_configuration'):
                    self.execution_engine.update_configuration(config_updates)
                    logger.info("Execution engine configuration updated")
                else:
                    # Fallback: update individual attributes
                    for key, value in config_updates.items():
                        if hasattr(self.execution_engine, key):
                            setattr(self.execution_engine, key, value)
                    logger.info("Execution engine attributes updated")
            
        except Exception as e:
            logger.error(f"Error updating execution engine config: {e}")
    
    def update_deployment_config(self, parameters: RiskParameters):
        """Update deployment script configuration"""
        try:
            if self.deployment_manager:
                # Update deployment configuration
                config_updates = {
                    'max_trading_capital': parameters.max_trading_capital,
                    'position_size_pct': parameters.position_size_pct,
                    'portfolio_risk_pct': parameters.portfolio_risk_pct,
                    'max_leverage': parameters.max_leverage,
                    'max_concurrent_positions': parameters.max_concurrent_positions,
                    'scalper_quality_thresholds': {
                        'spread_quality': parameters.scalper_spread_quality,
                        'decision_quality': parameters.scalper_decision_quality
                    },
                    'symbol_quality_threshold': parameters.symbol_quality_threshold
                }
                
                # Apply configuration updates
                if hasattr(self.deployment_manager, 'config'):
                    self.deployment_manager.config.update(config_updates)
                    logger.info("Deployment configuration updated")
            
        except Exception as e:
            logger.error(f"Error updating deployment config: {e}")
    
    def get_integration_status(self) -> Dict[str, Any]:
        """Get status of all integrated systems"""
        return {
            'orchestrator_registered': self.orchestrator is not None,
            'scalper_gpt_registered': self.scalper_gpt is not None,
            'symbol_scanner_registered': self.symbol_scanner is not None,
            'deployment_manager_registered': self.deployment_manager is not None,
            'execution_engine_registered': self.execution_engine is not None,
            'dynamic_risk_manager_registered': hasattr(self, 'dynamic_risk_manager') and self.dynamic_risk_manager is not None,
            'emergency_coordinator_registered': hasattr(self, 'emergency_coordinator') and self.emergency_coordinator is not None,
            'current_risk_level': dynamic_risk_manager.current_risk_level.value,
            'parameter_change_callbacks': len(dynamic_risk_manager.parameter_change_callbacks)
        }
    
    def validate_system_integration(self) -> Dict[str, bool]:
        """Validate that all systems are properly integrated"""
        validation_results = {}
        
        # Check orchestrator integration
        validation_results['orchestrator'] = (
            self.orchestrator is not None and
            hasattr(self.orchestrator, 'max_trading_capital')
        )
        
        # Check ScalperGPT integration
        validation_results['scalper_gpt'] = (
            self.scalper_gpt is not None and
            hasattr(self.scalper_gpt, 'quality_thresholds')
        )
        
        # Check symbol scanner integration
        validation_results['symbol_scanner'] = (
            self.symbol_scanner is not None and
            hasattr(self.symbol_scanner, 'quality_threshold')
        )
        
        # Check deployment manager integration
        validation_results['deployment_manager'] = (
            self.deployment_manager is not None and
            hasattr(self.deployment_manager, 'config')
        )
        
        # Check execution engine integration
        validation_results['execution_engine'] = (
            self.execution_engine is not None
        )
        
        return validation_results


# Global instance
dynamic_risk_integration = DynamicRiskIntegration()
