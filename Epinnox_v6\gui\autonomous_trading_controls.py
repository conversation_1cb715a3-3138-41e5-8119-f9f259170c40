#!/usr/bin/env python3
"""
AUTONOMOUS TRADING CONTROLS
Integrated autonomous trading control panel for the unified GUI
"""

import sys
import os
import asyncio
import logging
from datetime import datetime
from typing import Optional, Dict, Any

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

try:
    from PyQt5.QtWidgets import *
    from PyQt5.QtCore import *
    from PyQt5.QtGui import *
    PYQT_AVAILABLE = True
except ImportError:
    PYQT_AVAILABLE = False

if PYQT_AVAILABLE:
    from gui.matrix_theme import MatrixTheme

logger = logging.getLogger(__name__)

class AutonomousTradingControlPanel(QWidget):
    """
    Autonomous trading control panel with integrated controls
    """
    
    # Signals for communication with main dashboard
    trading_started = pyqtSignal()
    trading_stopped = pyqtSignal()
    mode_changed = pyqtSignal(str)
    risk_level_changed = pyqtSignal(str)
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.autonomous_trader = None
        self.trading_active = False
        self.current_mode = "paper"
        self.current_risk_level = "ultra-conservative"
        
        self.setup_ui()
        self.setup_connections()
        
        logger.info("Autonomous Trading Control Panel initialized")
    
    def setup_ui(self):
        """Setup the control panel UI"""
        
        layout = QVBoxLayout(self)
        layout.setSpacing(10)
        
        # Title
        title = QLabel("🤖 AUTONOMOUS TRADING CONTROLS")
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("font-size: 16px; font-weight: bold; color: #00ff00; margin: 10px;")
        layout.addWidget(title)
        
        # Main Control Section
        main_control_group = QGroupBox("Main Controls")
        main_control_layout = QVBoxLayout(main_control_group)
        
        # Start/Stop Trading Button
        self.start_stop_button = QPushButton("🚀 START AUTONOMOUS TRADING")
        self.start_stop_button.setMinimumHeight(50)
        self.start_stop_button.setStyleSheet("""
            QPushButton {
                background-color: #004400;
                color: #00ff00;
                border: 2px solid #00ff00;
                border-radius: 5px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #006600;
            }
            QPushButton:pressed {
                background-color: #002200;
            }
        """)
        main_control_layout.addWidget(self.start_stop_button)
        
        # Mode Selection
        mode_layout = QHBoxLayout()
        mode_layout.addWidget(QLabel("Trading Mode:"))
        
        self.mode_combo = QComboBox()
        self.mode_combo.addItems(["Paper Trading", "Live Trading", "Simulation Only"])
        self.mode_combo.setCurrentText("Paper Trading")
        mode_layout.addWidget(self.mode_combo)
        
        main_control_layout.addLayout(mode_layout)
        
        # Risk Level Selection
        risk_layout = QHBoxLayout()
        risk_layout.addWidget(QLabel("Risk Level:"))
        
        self.risk_combo = QComboBox()
        self.risk_combo.addItems([
            "Ultra-Conservative", 
            "Conservative", 
            "Moderate", 
            "Aggressive", 
            "High-Risk"
        ])
        self.risk_combo.setCurrentText("Ultra-Conservative")
        risk_layout.addWidget(self.risk_combo)
        
        main_control_layout.addLayout(risk_layout)
        
        layout.addWidget(main_control_group)
        
        # Trading Parameters Section
        params_group = QGroupBox("Trading Parameters")
        params_layout = QFormLayout(params_group)
        
        # Capital Limit
        self.capital_spinbox = QDoubleSpinBox()
        self.capital_spinbox.setRange(10.0, 10000.0)
        self.capital_spinbox.setValue(100.0)
        self.capital_spinbox.setSuffix(" USDT")
        params_layout.addRow("Max Capital:", self.capital_spinbox)
        
        # Position Size
        self.position_size_slider = QSlider(Qt.Horizontal)
        self.position_size_slider.setRange(1, 10)
        self.position_size_slider.setValue(1)
        self.position_size_label = QLabel("1.0%")
        
        position_layout = QHBoxLayout()
        position_layout.addWidget(self.position_size_slider)
        position_layout.addWidget(self.position_size_label)
        params_layout.addRow("Position Size:", position_layout)
        
        layout.addWidget(params_group)
        
        # AI Analysis Controls Section
        ai_group = QGroupBox("AI Analysis Controls")
        ai_layout = QVBoxLayout(ai_group)
        
        # LLM Decision Loop
        llm_layout = QHBoxLayout()
        self.llm_checkbox = QCheckBox("LLM Decision Loop")
        self.llm_checkbox.setChecked(True)
        self.llm_interval_combo = QComboBox()
        self.llm_interval_combo.addItems(["30 seconds", "60 seconds", "120 seconds"])
        llm_layout.addWidget(self.llm_checkbox)
        llm_layout.addWidget(QLabel("Interval:"))
        llm_layout.addWidget(self.llm_interval_combo)
        ai_layout.addLayout(llm_layout)
        
        # ScalperGPT Analysis
        scalper_layout = QHBoxLayout()
        self.scalper_checkbox = QCheckBox("ScalperGPT Analysis")
        self.scalper_checkbox.setChecked(True)
        self.scalper_quality_slider = QSlider(Qt.Horizontal)
        self.scalper_quality_slider.setRange(50, 100)
        self.scalper_quality_slider.setValue(70)
        self.scalper_quality_label = QLabel("7.0")
        
        scalper_layout.addWidget(self.scalper_checkbox)
        scalper_layout.addWidget(QLabel("Quality Threshold:"))
        scalper_layout.addWidget(self.scalper_quality_slider)
        scalper_layout.addWidget(self.scalper_quality_label)
        ai_layout.addLayout(scalper_layout)
        
        # Symbol Scanner
        scanner_layout = QHBoxLayout()
        self.scanner_checkbox = QCheckBox("Dynamic Symbol Scanner")
        self.scanner_checkbox.setChecked(True)
        scanner_layout.addWidget(self.scanner_checkbox)
        ai_layout.addLayout(scanner_layout)
        
        layout.addWidget(ai_group)
        
        # Safety Controls Section
        safety_group = QGroupBox("Safety Controls")
        safety_layout = QVBoxLayout(safety_group)
        
        # Emergency Stop
        self.emergency_stop_button = QPushButton("🚨 EMERGENCY STOP")
        self.emergency_stop_button.setMinimumHeight(40)
        self.emergency_stop_button.setStyleSheet("""
            QPushButton {
                background-color: #440000;
                color: #ff0000;
                border: 2px solid #ff0000;
                border-radius: 5px;
                font-size: 12px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #660000;
            }
            QPushButton:pressed {
                background-color: #220000;
            }
        """)
        safety_layout.addWidget(self.emergency_stop_button)
        
        # Safety Status Indicators
        safety_status_layout = QHBoxLayout()
        
        self.limit_orders_indicator = QLabel("🛡️ LIMIT Orders: ON")
        self.limit_orders_indicator.setStyleSheet("color: #00ff00;")
        safety_status_layout.addWidget(self.limit_orders_indicator)
        
        self.risk_limits_indicator = QLabel("📊 Risk Limits: ACTIVE")
        self.risk_limits_indicator.setStyleSheet("color: #00ff00;")
        safety_status_layout.addWidget(self.risk_limits_indicator)
        
        safety_layout.addLayout(safety_status_layout)
        
        layout.addWidget(safety_group)
        
        # Status Display Section
        status_group = QGroupBox("Real-Time Status")
        status_layout = QFormLayout(status_group)
        
        self.trading_status_label = QLabel("STOPPED")
        self.trading_status_label.setStyleSheet("color: #ff0000; font-weight: bold;")
        status_layout.addRow("Trading Status:", self.trading_status_label)
        
        self.last_decision_label = QLabel("Never")
        status_layout.addRow("Last LLM Decision:", self.last_decision_label)
        
        self.total_trades_label = QLabel("0")
        status_layout.addRow("Total Trades:", self.total_trades_label)
        
        self.account_balance_label = QLabel("Loading...")
        status_layout.addRow("Account Balance:", self.account_balance_label)
        
        layout.addWidget(status_group)
        
        # Apply Matrix theme
        if PYQT_AVAILABLE:
            self.setStyleSheet(MatrixTheme.get_widget_stylesheet())
    
    def setup_connections(self):
        """Setup signal connections"""
        
        self.start_stop_button.clicked.connect(self.toggle_autonomous_trading)
        self.emergency_stop_button.clicked.connect(self.emergency_stop)
        self.mode_combo.currentTextChanged.connect(self.on_mode_changed)
        self.risk_combo.currentTextChanged.connect(self.on_risk_level_changed)
        self.position_size_slider.valueChanged.connect(self.update_position_size_label)
        self.scalper_quality_slider.valueChanged.connect(self.update_scalper_quality_label)
    
    def update_position_size_label(self, value):
        """Update position size label"""
        self.position_size_label.setText(f"{value/10:.1f}%")
    
    def update_scalper_quality_label(self, value):
        """Update ScalperGPT quality label"""
        self.scalper_quality_label.setText(f"{value/10:.1f}")
    
    def on_mode_changed(self, mode_text):
        """Handle mode change"""
        mode_map = {
            "Paper Trading": "paper",
            "Live Trading": "live", 
            "Simulation Only": "simulation"
        }
        
        self.current_mode = mode_map.get(mode_text, "paper")
        self.mode_changed.emit(self.current_mode)
        
        # Update UI based on mode
        if self.current_mode == "live":
            self.start_stop_button.setStyleSheet("""
                QPushButton {
                    background-color: #440000;
                    color: #ff4444;
                    border: 2px solid #ff4444;
                    border-radius: 5px;
                    font-size: 14px;
                    font-weight: bold;
                }
                QPushButton:hover {
                    background-color: #660000;
                }
            """)
        else:
            self.start_stop_button.setStyleSheet("""
                QPushButton {
                    background-color: #004400;
                    color: #00ff00;
                    border: 2px solid #00ff00;
                    border-radius: 5px;
                    font-size: 14px;
                    font-weight: bold;
                }
                QPushButton:hover {
                    background-color: #006600;
                }
            """)
    
    def on_risk_level_changed(self, risk_text):
        """Handle risk level change"""
        risk_map = {
            "Ultra-Conservative": "ultra-conservative",
            "Conservative": "conservative",
            "Moderate": "moderate", 
            "Aggressive": "aggressive",
            "High-Risk": "high-risk"
        }
        
        self.current_risk_level = risk_map.get(risk_text, "ultra-conservative")
        self.risk_level_changed.emit(self.current_risk_level)
        
        # Update parameters based on risk level
        if self.current_risk_level == "ultra-conservative":
            self.capital_spinbox.setValue(100.0)
            self.position_size_slider.setValue(1)
        elif self.current_risk_level == "conservative":
            self.capital_spinbox.setValue(200.0)
            self.position_size_slider.setValue(2)
        elif self.current_risk_level == "moderate":
            self.capital_spinbox.setValue(500.0)
            self.position_size_slider.setValue(3)
    
    async def toggle_autonomous_trading(self):
        """Toggle autonomous trading on/off"""
        
        if not self.trading_active:
            await self.start_autonomous_trading()
        else:
            await self.stop_autonomous_trading()
    
    async def start_autonomous_trading(self):
        """Start autonomous trading"""
        
        try:
            logger.info("Starting autonomous trading from GUI controls...")
            
            # Import the robust autonomous trader logic
            # Note: This will be replaced with integrated trader in unified launcher
            logger.info("Creating integrated autonomous trader...")
            
            # Create trader instance
            self.autonomous_trader = RobustAutonomousTrader()
            
            # Configure trader based on GUI settings
            self.autonomous_trader.max_trading_capital = self.capital_spinbox.value()
            self.autonomous_trader.position_size_pct = self.position_size_slider.value() / 10.0
            
            # Start autonomous trading
            success = await self.autonomous_trader.start_autonomous_trading()
            
            if success:
                self.trading_active = True
                self.update_trading_status("ACTIVE")
                self.start_stop_button.setText("⏹️ STOP AUTONOMOUS TRADING")
                self.trading_started.emit()
                
                # Start status update timer
                self.status_timer = QTimer()
                self.status_timer.timeout.connect(self.update_status_display)
                self.status_timer.start(5000)  # Update every 5 seconds
                
                logger.info("Autonomous trading started successfully from GUI")
            else:
                logger.error("Failed to start autonomous trading from GUI")
                
        except Exception as e:
            logger.error(f"Error starting autonomous trading: {e}")
    
    async def stop_autonomous_trading(self):
        """Stop autonomous trading"""
        
        try:
            logger.info("Stopping autonomous trading from GUI controls...")
            
            if self.autonomous_trader:
                await self.autonomous_trader.stop_trading()
            
            self.trading_active = False
            self.update_trading_status("STOPPED")
            self.start_stop_button.setText("🚀 START AUTONOMOUS TRADING")
            self.trading_stopped.emit()
            
            # Stop status update timer
            if hasattr(self, 'status_timer'):
                self.status_timer.stop()
            
            logger.info("Autonomous trading stopped successfully from GUI")
            
        except Exception as e:
            logger.error(f"Error stopping autonomous trading: {e}")
    
    def emergency_stop(self):
        """Emergency stop all trading"""
        
        try:
            logger.warning("EMERGENCY STOP activated from GUI")
            
            # Stop autonomous trading immediately
            if self.autonomous_trader:
                asyncio.create_task(self.autonomous_trader.stop_trading())
            
            # Update emergency coordinator
            from core.emergency_stop_coordinator import emergency_coordinator
            emergency_coordinator.trigger_emergency_stop("GUI Emergency Stop")
            
            self.trading_active = False
            self.update_trading_status("EMERGENCY STOPPED")
            self.start_stop_button.setText("🚀 START AUTONOMOUS TRADING")
            
            # Show emergency dialog
            QMessageBox.warning(self, "Emergency Stop", 
                              "Emergency stop activated!\nAll trading has been halted.")
            
        except Exception as e:
            logger.error(f"Error in emergency stop: {e}")
    
    def update_trading_status(self, status):
        """Update trading status display"""
        
        self.trading_status_label.setText(status)
        
        if status == "ACTIVE":
            self.trading_status_label.setStyleSheet("color: #00ff00; font-weight: bold;")
        elif status == "EMERGENCY STOPPED":
            self.trading_status_label.setStyleSheet("color: #ff0000; font-weight: bold; background-color: #440000;")
        else:
            self.trading_status_label.setStyleSheet("color: #ff0000; font-weight: bold;")
    
    def update_status_display(self):
        """Update real-time status display"""
        
        try:
            if self.autonomous_trader:
                status = self.autonomous_trader.get_status()
                
                # Update last decision time
                if status.get('last_decision_time'):
                    time_diff = datetime.now() - status['last_decision_time']
                    self.last_decision_label.setText(f"{time_diff.seconds}s ago")
                
                # Update total trades
                self.total_trades_label.setText(str(status.get('total_trades', 0)))
                
                # Update account balance (if available)
                if hasattr(self.autonomous_trader, 'exchange_engine'):
                    try:
                        balance = self.autonomous_trader.exchange_engine.exchange.fetch_balance()
                        usdt_balance = balance.get('USDT', {}).get('free', 0)
                        self.account_balance_label.setText(f"${usdt_balance:.2f} USDT")
                    except:
                        pass
                        
        except Exception as e:
            logger.error(f"Error updating status display: {e}")
    
    def get_trading_config(self) -> Dict[str, Any]:
        """Get current trading configuration"""
        
        return {
            'mode': self.current_mode,
            'risk_level': self.current_risk_level,
            'max_capital': self.capital_spinbox.value(),
            'position_size_pct': self.position_size_slider.value() / 10.0,
            'llm_enabled': self.llm_checkbox.isChecked(),
            'llm_interval': int(self.llm_interval_combo.currentText().split()[0]),
            'scalper_enabled': self.scalper_checkbox.isChecked(),
            'scalper_quality_threshold': self.scalper_quality_slider.value() / 10.0,
            'scanner_enabled': self.scanner_checkbox.isChecked()
        }

# Test function
def test_autonomous_trading_controls():
    """Test the autonomous trading controls"""
    
    if not PYQT_AVAILABLE:
        print("PyQt5 not available - cannot test GUI")
        return
    
    app = QApplication(sys.argv)
    app.setStyleSheet(MatrixTheme.get_stylesheet())
    
    controls = AutonomousTradingControlPanel()
    controls.show()
    
    return app.exec_()

if __name__ == "__main__":
    test_autonomous_trading_controls()
