"""
LLM Response Parsers - Parse and validate LLM responses for different prompt types
Handles JSON parsing, validation, and fallback mechanisms
"""

import json
import re
import logging
from typing import Dict, Any, Optional

logger = logging.getLogger(__name__)

class LLMResponseParsers:
    """
    Collection of response parsers for different LLM prompt types
    Each parser handles specific response formats and validation
    """
    
    def __init__(self):
        self.default_responses = {
            'emergency': {'ACTION': 'MONITOR', 'PRIORITY': 'LOW', 'CONFIDENCE': 50},
            'position': {'ACTION': 'HOLD', 'CONFIDENCE': 50, 'URGENCY': 'LOW'},
            'profit': {'ACTION': 'HOLD', 'CONFIDENCE': 50, 'CLOSE_PERCENTAGE': 0},
            'regime': {'REGIME': 'UNKNOWN', 'CONFIDENCE': 50, 'SCALP_SUITABILITY': 'MEDIUM'},
            'risk': {'APPROVED': False, 'RISK_SCORE': 100, 'CONFIDENCE': 50},
            'entry': {'ACTION': 'WAIT', 'CONFIDENCE': 50, 'ENTRY_TYPE': 'LIMIT'},
            'strategy': {'RISK_ADJUSTMENT': 1.0, 'CONFIDENCE': 50, 'HOLD_TIME_TARGET': 8},
            'opportunity': {'BEST_OPPORTUNITY': 'NONE', 'CONFIDENCE': 50, 'SETUP_TYPE': 'NONE'}
        }
    
    def parse_emergency_response(self, response_text: str) -> Dict[str, Any]:
        """Parse emergency response LLM output"""
        try:
            # Extract JSON from response
            json_data = self.extract_json_from_response(response_text)
            
            if not json_data:
                logger.warning("No JSON found in emergency response, using fallback")
                return self.default_responses['emergency']
            
            # Validate required fields
            required_fields = ['ACTION', 'PRIORITY']
            if not all(field in json_data for field in required_fields):
                logger.warning("Missing required fields in emergency response")
                return self.default_responses['emergency']
            
            # Validate ACTION values
            valid_actions = ['CLOSE_ALL', 'CLOSE_LOSING', 'HEDGE', 'MONITOR', 'REDUCE_SIZE']
            if json_data['ACTION'] not in valid_actions:
                logger.warning(f"Invalid emergency action: {json_data['ACTION']}")
                json_data['ACTION'] = 'MONITOR'
            
            # Validate PRIORITY values
            valid_priorities = ['IMMEDIATE', 'HIGH', 'MEDIUM', 'LOW']
            if json_data['PRIORITY'] not in valid_priorities:
                json_data['PRIORITY'] = 'MEDIUM'
            
            # Ensure confidence is within range
            json_data['CONFIDENCE'] = max(0, min(100, json_data.get('CONFIDENCE', 50)))
            
            logger.info(f"Emergency response parsed: {json_data['ACTION']} ({json_data['PRIORITY']} priority)")
            return json_data
            
        except Exception as e:
            logger.error(f"Error parsing emergency response: {e}")
            return self.default_responses['emergency']
    
    def parse_position_management_response(self, response_text: str) -> Dict[str, Any]:
        """Parse position management LLM output"""
        try:
            json_data = self.extract_json_from_response(response_text)
            
            if not json_data:
                return self.default_responses['position']
            
            # Validate ACTION
            valid_actions = ['HOLD', 'CLOSE', 'PARTIAL_CLOSE']
            if json_data.get('ACTION') not in valid_actions:
                json_data['ACTION'] = 'HOLD'
            
            # Validate and convert price fields
            for price_field in ['STOP_LOSS', 'TAKE_PROFIT']:
                if price_field in json_data:
                    try:
                        json_data[price_field] = float(json_data[price_field])
                    except (ValueError, TypeError):
                        json_data[price_field] = 0.0
            
            # Validate boolean fields
            json_data['TRAIL_STOP'] = bool(json_data.get('TRAIL_STOP', False))
            
            # Validate urgency
            valid_urgency = ['LOW', 'MEDIUM', 'HIGH', 'IMMEDIATE']
            if json_data.get('URGENCY') not in valid_urgency:
                json_data['URGENCY'] = 'LOW'
            
            json_data['CONFIDENCE'] = max(0, min(100, json_data.get('CONFIDENCE', 50)))
            
            logger.info(f"Position management parsed: {json_data['ACTION']} ({json_data['URGENCY']} urgency)")
            return json_data
            
        except Exception as e:
            logger.error(f"Error parsing position management response: {e}")
            return self.default_responses['position']
    
    def parse_profit_optimization_response(self, response_text: str) -> Dict[str, Any]:
        """Parse profit optimization LLM output"""
        try:
            json_data = self.extract_json_from_response(response_text)
            
            if not json_data:
                return self.default_responses['profit']
            
            # Validate ACTION
            valid_actions = ['HOLD', 'PARTIAL_CLOSE', 'FULL_CLOSE']
            if json_data.get('ACTION') not in valid_actions:
                json_data['ACTION'] = 'HOLD'
            
            # Validate CLOSE_PERCENTAGE
            if 'CLOSE_PERCENTAGE' in json_data:
                try:
                    close_pct = float(json_data['CLOSE_PERCENTAGE'])
                    json_data['CLOSE_PERCENTAGE'] = max(0, min(100, close_pct))
                except (ValueError, TypeError):
                    json_data['CLOSE_PERCENTAGE'] = 0
            
            # Validate price fields
            for price_field in ['NEW_STOP']:
                if price_field in json_data:
                    try:
                        json_data[price_field] = float(json_data[price_field])
                    except (ValueError, TypeError):
                        json_data[price_field] = 0.0
            
            json_data['TRAIL_STOP'] = bool(json_data.get('TRAIL_STOP', False))
            json_data['CONFIDENCE'] = max(0, min(100, json_data.get('CONFIDENCE', 50)))
            
            logger.info(f"Profit optimization parsed: {json_data['ACTION']} ({json_data.get('CLOSE_PERCENTAGE', 0)}%)")
            return json_data
            
        except Exception as e:
            logger.error(f"Error parsing profit optimization response: {e}")
            return self.default_responses['profit']
    
    def parse_market_regime_response(self, response_text: str) -> Dict[str, Any]:
        """Parse market regime detection LLM output"""
        try:
            json_data = self.extract_json_from_response(response_text)
            
            if not json_data:
                return self.default_responses['regime']
            
            # Validate REGIME
            valid_regimes = ['TRENDING_BULL', 'TRENDING_BEAR', 'RANGING_TIGHT', 
                           'RANGING_VOLATILE', 'BREAKOUT_PENDING', 'NEWS_DRIVEN']
            if json_data.get('REGIME') not in valid_regimes:
                json_data['REGIME'] = 'RANGING_TIGHT'
            
            # Validate SCALP_SUITABILITY
            valid_suitability = ['HIGH', 'MEDIUM', 'LOW']
            if json_data.get('SCALP_SUITABILITY') not in valid_suitability:
                json_data['SCALP_SUITABILITY'] = 'MEDIUM'
            
            # Validate RECOMMENDED_TIMEFRAME
            valid_timeframes = ['1m', '5m', '15m']
            if json_data.get('RECOMMENDED_TIMEFRAME') not in valid_timeframes:
                json_data['RECOMMENDED_TIMEFRAME'] = '1m'
            
            # Validate RISK_LEVEL
            valid_risk_levels = ['LOW', 'MEDIUM', 'HIGH']
            if json_data.get('RISK_LEVEL') not in valid_risk_levels:
                json_data['RISK_LEVEL'] = 'MEDIUM'
            
            json_data['CONFIDENCE'] = max(0, min(100, json_data.get('CONFIDENCE', 50)))
            
            logger.info(f"Market regime parsed: {json_data['REGIME']} ({json_data['SCALP_SUITABILITY']} scalp suitability)")
            return json_data
            
        except Exception as e:
            logger.error(f"Error parsing market regime response: {e}")
            return self.default_responses['regime']
    
    def parse_risk_assessment_response(self, response_text: str) -> Dict[str, Any]:
        """Parse risk assessment LLM output with enhanced decision extraction"""
        try:
            json_data = self.extract_json_from_response(response_text)

            if not json_data:
                # 🚀 FIXED: Try to extract decision from text for risk assessment
                decision_data = self.extract_trading_decision_from_text(response_text)
                if decision_data:
                    # Convert trading decision to risk assessment format
                    decision = decision_data.get('DECISION', 'WAIT')
                    confidence = decision_data.get('CONFIDENCE', 50)

                    # 🚀 FIXED: Preserve trading decision AND map to risk approval
                    if decision in ['LONG', 'SHORT']:
                        approved = True
                        risk_score = max(10, 100 - confidence)  # Higher confidence = lower risk
                    else:
                        approved = False
                        risk_score = min(90, 50 + (100 - confidence) / 2)  # Lower confidence = higher risk

                    return {
                        'APPROVED': approved,
                        'RISK_SCORE': risk_score,
                        'CONFIDENCE': confidence,
                        'DECISION': decision,  # 🚀 PRESERVE the actual trading decision
                        'REASONING': decision_data.get('EXPLANATION', 'Risk assessment based on trading decision')
                    }
                return self.default_responses['risk']

            # Validate APPROVED
            json_data['APPROVED'] = bool(json_data.get('APPROVED', False))

            # Validate RISK_SCORE
            if 'RISK_SCORE' in json_data:
                try:
                    risk_score = float(json_data['RISK_SCORE'])
                    json_data['RISK_SCORE'] = max(0, min(100, risk_score))
                except (ValueError, TypeError):
                    json_data['RISK_SCORE'] = 50

            # Validate MAX_POSITION_SIZE
            if 'MAX_POSITION_SIZE' in json_data:
                try:
                    json_data['MAX_POSITION_SIZE'] = float(json_data['MAX_POSITION_SIZE'])
                except (ValueError, TypeError):
                    json_data['MAX_POSITION_SIZE'] = 0

            json_data['CONFIDENCE'] = max(0, min(100, json_data.get('CONFIDENCE', 50)))

            logger.info(f"Risk assessment parsed: {'APPROVED' if json_data['APPROVED'] else 'REJECTED'} (Risk: {json_data.get('RISK_SCORE', 50)})")
            return json_data

        except Exception as e:
            logger.error(f"Error parsing risk assessment response: {e}")
            return self.default_responses['risk']
    
    def parse_entry_timing_response(self, response_text: str) -> Dict[str, Any]:
        """Parse entry timing LLM output with enhanced decision extraction"""
        try:
            json_data = self.extract_json_from_response(response_text)

            if not json_data:
                # 🚀 FIXED: Try to extract trading decision for entry timing
                decision_data = self.extract_trading_decision_from_text(response_text)
                if decision_data:
                    decision = decision_data.get('DECISION', 'WAIT')
                    confidence = decision_data.get('CONFIDENCE', 50)

                    # Map trading decision to entry timing action
                    if decision in ['LONG', 'SHORT'] and confidence > 70:
                        action = 'ENTER_NOW'
                    elif decision in ['LONG', 'SHORT'] and confidence > 50:
                        action = 'WAIT'  # Wait for better entry
                    else:
                        action = 'WAIT'

                    return {
                        'ACTION': action,
                        'CONFIDENCE': confidence,
                        'ENTRY_TYPE': 'LIMIT',
                        'DECISION': decision,  # 🚀 PRESERVE the actual trading decision
                        'REASONING': decision_data.get('EXPLANATION', f'Entry timing based on {decision} decision')
                    }
                return self.default_responses['entry']

            # Validate ACTION
            valid_actions = ['ENTER_NOW', 'WAIT', 'ABORT']
            if json_data.get('ACTION') not in valid_actions:
                # Try to map from DECISION field
                decision = json_data.get('DECISION', 'WAIT')
                if decision in ['LONG', 'SHORT']:
                    json_data['ACTION'] = 'ENTER_NOW'
                else:
                    json_data['ACTION'] = 'WAIT'

            # Validate ENTRY_TYPE
            valid_entry_types = ['MARKET', 'LIMIT']
            if json_data.get('ENTRY_TYPE') not in valid_entry_types:
                json_data['ENTRY_TYPE'] = 'LIMIT'

            # Validate MAX_WAIT_SECONDS
            if 'MAX_WAIT_SECONDS' in json_data:
                try:
                    json_data['MAX_WAIT_SECONDS'] = int(json_data['MAX_WAIT_SECONDS'])
                except (ValueError, TypeError):
                    json_data['MAX_WAIT_SECONDS'] = 60

            json_data['CONFIDENCE'] = max(0, min(100, json_data.get('CONFIDENCE', 50)))

            logger.info(f"Entry timing parsed: {json_data['ACTION']} ({json_data['ENTRY_TYPE']} order)")
            return json_data

        except Exception as e:
            logger.error(f"Error parsing entry timing response: {e}")
            return self.default_responses['entry']
    
    def parse_strategy_adaptation_response(self, response_text: str) -> Dict[str, Any]:
        """Parse strategy adaptation LLM output"""
        try:
            json_data = self.extract_json_from_response(response_text)
            
            if not json_data:
                return self.default_responses['strategy']
            
            # Validate RISK_ADJUSTMENT
            if 'RISK_ADJUSTMENT' in json_data:
                try:
                    risk_adj = float(json_data['RISK_ADJUSTMENT'])
                    json_data['RISK_ADJUSTMENT'] = max(0.5, min(2.0, risk_adj))
                except (ValueError, TypeError):
                    json_data['RISK_ADJUSTMENT'] = 1.0
            
            # Validate HOLD_TIME_TARGET
            if 'HOLD_TIME_TARGET' in json_data:
                try:
                    json_data['HOLD_TIME_TARGET'] = int(json_data['HOLD_TIME_TARGET'])
                except (ValueError, TypeError):
                    json_data['HOLD_TIME_TARGET'] = 8
            
            # Validate threshold values
            for threshold in ['ENTRY_THRESHOLD', 'EXIT_THRESHOLD']:
                if threshold in json_data:
                    try:
                        value = float(json_data[threshold])
                        json_data[threshold] = max(50, min(95, value))
                    except (ValueError, TypeError):
                        json_data[threshold] = 70
            
            json_data['CONFIDENCE'] = max(0, min(100, json_data.get('CONFIDENCE', 50)))
            
            logger.info(f"Strategy adaptation parsed: Risk adj {json_data.get('RISK_ADJUSTMENT', 1.0):.1f}x")
            return json_data
            
        except Exception as e:
            logger.error(f"Error parsing strategy adaptation response: {e}")
            return self.default_responses['strategy']
    
    def parse_opportunity_scanner_response(self, response_text: str) -> Dict[str, Any]:
        """Parse opportunity scanner LLM output with enhanced decision extraction"""
        try:
            json_data = self.extract_json_from_response(response_text)

            if not json_data:
                # 🚀 FIXED: Try to extract trading decision for opportunity scanner
                decision_data = self.extract_trading_decision_from_text(response_text)
                logger.info(f"🔍 Decision data extracted: {decision_data}")

                if decision_data:
                    decision = decision_data.get('DECISION', 'WAIT')
                    confidence = decision_data.get('CONFIDENCE', 50)

                    # Map trading decision to opportunity format
                    if decision in ['LONG', 'SHORT']:
                        best_opp = 'BREAKOUT' if confidence > 75 else 'MOMENTUM'
                        setup_type = 'MOMENTUM' if decision == 'LONG' else 'REVERSAL'
                    else:
                        best_opp = 'NONE'
                        setup_type = 'NONE'

                    result = {
                        'BEST_OPPORTUNITY': best_opp,
                        'SETUP_TYPE': setup_type,
                        'CONFIDENCE': confidence,
                        'DECISION': decision,  # 🚀 PRESERVE the actual trading decision
                        'REASONING': decision_data.get('EXPLANATION', f'Opportunity based on {decision} decision')
                    }
                    logger.info(f"🎯 Opportunity scanner mapped result: {result}")
                    return result

                logger.warning("🚨 No decision data extracted, using default response")
                return self.default_responses['opportunity']

            # 🚀 FIXED: Handle case where JSON extraction succeeds but lacks opportunity-specific fields
            if 'BEST_OPPORTUNITY' not in json_data or 'SETUP_TYPE' not in json_data:
                # Extract decision and confidence from JSON data
                decision = json_data.get('DECISION') or json_data.get('ACTION', 'WAIT')
                confidence = json_data.get('CONFIDENCE', 50)

                # Map to opportunity format
                if decision in ['LONG', 'SHORT']:
                    best_opp = 'BREAKOUT' if confidence > 75 else 'MOMENTUM'
                    setup_type = 'MOMENTUM' if decision == 'LONG' else 'REVERSAL'
                else:
                    best_opp = 'NONE'
                    setup_type = 'NONE'

                json_data['BEST_OPPORTUNITY'] = best_opp
                json_data['SETUP_TYPE'] = setup_type
                json_data['DECISION'] = decision  # Preserve original decision

            # Validate SETUP_TYPE
            valid_setups = ['BREAKOUT', 'RANGE_BREAKOUT', 'TREND_CONTINUATION',
                          'REVERSAL', 'MOMENTUM', 'NONE']
            if json_data.get('SETUP_TYPE') not in valid_setups:
                json_data['SETUP_TYPE'] = 'NONE'

            json_data['CONFIDENCE'] = max(0, min(100, json_data.get('CONFIDENCE', 50)))

            logger.info(f"Opportunity scanner parsed: {json_data.get('BEST_OPPORTUNITY', 'NONE')} ({json_data.get('SETUP_TYPE', 'NONE')})")
            return json_data

        except Exception as e:
            logger.error(f"Error parsing opportunity scanner response: {e}")
            return self.default_responses['opportunity']
    
    def extract_json_from_response(self, response_text: str) -> Optional[Dict[str, Any]]:
        """🚨 CRITICAL FIX: Enhanced JSON extraction with comprehensive error handling"""
        try:
            # Clean the response
            cleaned = self.clean_response_text(response_text)
            logger.debug(f"🔍 Cleaned response: {cleaned[:200]}...")

            # 🚀 PRIORITY 1: Try to find JSON object first (more reliable for LLM responses)
            json_match = re.search(r'\{[^{}]*(?:\{[^{}]*\}[^{}]*)*\}', cleaned, re.DOTALL)

            if json_match:
                json_str = json_match.group(0)
                logger.debug(f"🔍 Found JSON candidate: {json_str[:100]}...")

                # Fix common JSON issues
                json_str = self.fix_json_syntax(json_str)
                logger.debug(f"🔧 Fixed JSON: {json_str[:100]}...")

                # Parse JSON
                result = json.loads(json_str)
                logger.info(f"✅ JSON parsing successful: {list(result.keys())}")

                # 🎯 NORMALIZE JSON KEYS: Convert to uppercase and standardize field names
                normalized_result = {}
                for key, value in result.items():
                    # Convert key to uppercase
                    upper_key = key.upper()

                    # Map common JSON field names to expected structured format
                    key_mapping = {
                        'ACTION': 'ACTION',
                        'DECISION': 'DECISION',
                        'CONFIDENCE': 'CONFIDENCE',
                        'ENTRY_TYPE': 'ENTRY_TYPE',
                        'WAIT_FOR': 'WAIT_FOR',
                        'MAX_WAIT': 'MAX_WAIT',
                        'REASONING': 'REASONING',
                        'EXPLANATION': 'EXPLANATION',
                        'TAKE_PROFIT': 'TAKE_PROFIT',
                        'STOP_LOSS': 'STOP_LOSS',
                        'LEVERAGE': 'LEVERAGE'
                    }

                    # Use mapped key or original uppercase key
                    final_key = key_mapping.get(upper_key, upper_key)
                    normalized_result[final_key] = value

                # 🎯 ENSURE REQUIRED FIELDS: Add ACTION if missing but DECISION exists
                if 'ACTION' not in normalized_result and 'DECISION' in normalized_result:
                    decision = str(normalized_result['DECISION']).upper()
                    if decision in ['LONG', 'SHORT']:
                        normalized_result['ACTION'] = 'ENTER_NOW'
                    elif decision == 'WAIT':
                        normalized_result['ACTION'] = 'WAIT'
                    elif decision == 'CLOSE':
                        normalized_result['ACTION'] = 'CLOSE'

                # 🎯 VALIDATE CONFIDENCE: Ensure numeric confidence value
                if 'CONFIDENCE' in normalized_result:
                    try:
                        normalized_result['CONFIDENCE'] = float(normalized_result['CONFIDENCE'])
                    except (ValueError, TypeError):
                        normalized_result['CONFIDENCE'] = 50.0
                else:
                    normalized_result['CONFIDENCE'] = 50.0

                logger.info(f"🎯 Normalized JSON result: {list(normalized_result.keys())}")
                return normalized_result

            # 🚀 PRIORITY 2: Try structured text parsing (handles "DECISION: SHORT, CONFIDENCE: 85%" format)
            logger.info(f"🔄 No JSON found, trying structured text parsing")
            structured_data = self.parse_structured_text(cleaned)
            if structured_data:
                logger.info(f"✅ Structured text parsing successful: {list(structured_data.keys())}")
                return structured_data

            logger.warning(f"⚠️ No JSON or structured data found in response")
            return None

        except json.JSONDecodeError as e:
            logger.warning(f"🚨 JSON decode error: {e}")
            logger.warning(f"🔍 Problematic JSON: {json_str[:200]}...")

            # 🎯 ENHANCED FALLBACK: Try additional JSON fixes
            try:
                # Try more aggressive JSON cleaning
                cleaned_json = self.aggressive_json_cleanup(json_str)
                result = json.loads(cleaned_json)
                logger.info(f"✅ JSON fixed with aggressive cleanup")
                return result
            except:
                pass

            # 🎯 FALLBACK: Try structured text parsing
            logger.info(f"🔄 Falling back to structured text parsing")
            return self.parse_structured_text(response_text)
        except Exception as e:
            logger.error(f"❌ Error extracting JSON: {e}")
            return None

    def parse_structured_text(self, text: str) -> Optional[Dict[str, Any]]:
        """🚨 CRITICAL FIX: Enhanced structured text parsing for LLM responses"""
        try:
            result = {}

            # 🎯 ENHANCED PATTERNS: More comprehensive LLM response patterns
            patterns = {
                # Core decision patterns
                'DECISION': r'DECISION:\s*([A-Z_]+)',
                'ACTION': r'ACTION:\s*([A-Z_]+)',
                'CONFIDENCE': r'CONFIDENCE:\s*(\d+(?:\.\d+)?)\s*%?',

                # Trading-specific patterns
                'ENTRY_REASON': r'ENTRY[_\s]REASON:\s*([^,\n]+)',
                'TAKE_PROFIT': r'TAKE[_\s]PROFIT:\s*(\d+(?:\.\d+)?)\s*%?',
                'STOP_LOSS': r'STOP[_\s]LOSS:\s*(-?\d+(?:\.\d+)?)\s*%?',
                'HOLD_TIME': r'HOLD[_\s]TIME:\s*([^,\n]+)',
                'LEVERAGE': r'LEVERAGE:\s*(\d+(?:\.\d+)?)',

                # Risk and regime patterns
                'REGIME': r'REGIME:\s*([A-Z_]+)',
                'APPROVED': r'APPROVED:\s*(TRUE|FALSE|YES|NO)',
                'RISK_SCORE': r'RISK[_\s]SCORE:\s*(\d+(?:\.\d+)?)',
                'ENTRY_TYPE': r'ENTRY[_\s]TYPE:\s*([A-Z]+)',
                'SCALP_SUITABILITY': r'SCALP[_\s]SUITABILITY:\s*([A-Z]+)',
                'SETUP_TYPE': r'SETUP[_\s]TYPE:\s*([A-Z_]+)',
                'BEST_OPPORTUNITY': r'BEST[_\s]OPPORTUNITY:\s*([A-Z_]+)',
                'RISK_ADJUSTMENT': r'RISK[_\s]ADJUSTMENT:\s*(\d+(?:\.\d+)?)',
                'PRIORITY': r'PRIORITY:\s*([A-Z]+)',
                'URGENCY': r'URGENCY:\s*([A-Z]+)',
                'CLOSE_PERCENTAGE': r'CLOSE[_\s]PERCENTAGE:\s*(\d+(?:\.\d+)?)',
                'REASONING': r'REASONING:\s*([^\n\r]+)',
                'EXPLANATION': r'EXPLANATION:\s*([^\n\r]+)',
            }

            # 🎯 ENHANCED EXTRACTION: Extract values using regex patterns
            for key, pattern in patterns.items():
                match = re.search(pattern, text, re.IGNORECASE)
                if match:
                    value = match.group(1).strip()

                    # Convert to appropriate type
                    if key in ['CONFIDENCE', 'RISK_SCORE', 'RISK_ADJUSTMENT', 'CLOSE_PERCENTAGE', 'TAKE_PROFIT', 'STOP_LOSS', 'LEVERAGE']:
                        try:
                            result[key] = float(value)
                        except ValueError:
                            result[key] = 50.0 if key == 'CONFIDENCE' else 0.0
                    elif key == 'APPROVED':
                        result[key] = value.upper() in ['TRUE', 'YES']
                    else:
                        result[key] = value.upper()

            # 🎯 FALLBACK PATTERNS: Try alternative formats if main patterns fail
            if not result:
                # Try simple comma-separated format: "LONG, 85%, Breakout signal"
                simple_match = re.search(r'(LONG|SHORT|WAIT|CLOSE)[,\s]+(\d+)%?[,\s]*([^,\n]*)', text, re.IGNORECASE)
                if simple_match:
                    result['ACTION'] = simple_match.group(1).upper()
                    result['CONFIDENCE'] = float(simple_match.group(2))
                    if simple_match.group(3).strip():
                        result['REASONING'] = simple_match.group(3).strip()

            # 🎯 DECISION MAPPING: Map DECISION to ACTION if ACTION not found
            if 'DECISION' in result and 'ACTION' not in result:
                decision = result['DECISION']
                if decision in ['LONG', 'SHORT']:
                    result['ACTION'] = 'ENTER_NOW'
                elif decision == 'WAIT':
                    result['ACTION'] = 'WAIT'
                elif decision == 'CLOSE':
                    result['ACTION'] = 'CLOSE'

            # 🎯 VALIDATION: Ensure minimum required fields
            if not result:
                logger.warning(f"🔍 No structured data found in: {text[:100]}...")
                return None

            # Add default confidence if missing
            if 'CONFIDENCE' not in result:
                result['CONFIDENCE'] = 50.0

            logger.info(f"Parsed structured text: {result}")
            return result if result else None

        except Exception as e:
            logger.error(f"Error parsing structured text: {e}")
            return None
    
    def clean_response_text(self, text: str) -> str:
        """Clean response text for better JSON extraction"""
        # Remove common prefixes
        prefixes = ["Here's the JSON:", "JSON response:", "Response:", "```json", "```"]
        for prefix in prefixes:
            text = text.replace(prefix, "")
        
        # Remove comments
        text = re.sub(r'//.*?(?=\n|$)', '', text, flags=re.MULTILINE)
        text = re.sub(r'/\*.*?\*/', '', text, flags=re.DOTALL)
        
        return text.strip()
    
    def fix_json_syntax(self, json_str: str) -> str:
        """🚨 CRITICAL FIX: Enhanced JSON syntax fixing with comprehensive error handling"""
        try:
            # 🎯 FIX 1: Remove trailing commas
            json_str = re.sub(r',\s*}', '}', json_str)
            json_str = re.sub(r',\s*]', ']', json_str)

            # 🎯 FIX 2: Fix unquoted keys (improved regex to avoid conflicts)
            # Only fix keys that are not already quoted
            json_str = re.sub(r'(?<!")(\b\w+)(?=\s*:)', r'"\1"', json_str)

            # 🎯 FIX 3: Fix single quotes to double quotes
            json_str = json_str.replace("'", '"')

            # 🎯 FIX 4: Fix common LLM response issues
            # Remove comments and explanations
            json_str = re.sub(r'//.*?(?=\n|$)', '', json_str, flags=re.MULTILINE)
            json_str = re.sub(r'/\*.*?\*/', '', json_str, flags=re.DOTALL)

            # 🎯 FIX 5: Fix missing quotes around string values
            # Fix values that should be quoted but aren't
            json_str = re.sub(r':\s*([A-Z_]+)(?=\s*[,}])', r': "\1"', json_str)

            # 🎯 FIX 6: Fix percentage values
            json_str = re.sub(r':\s*(\d+)%', r': \1', json_str)

            # 🎯 FIX 7: Fix boolean values
            json_str = re.sub(r':\s*True\b', ': true', json_str)
            json_str = re.sub(r':\s*False\b', ': false', json_str)
            json_str = re.sub(r':\s*None\b', ': null', json_str)

            # 🎯 FIX 8: Remove extra whitespace
            json_str = re.sub(r'\s+', ' ', json_str)

            return json_str.strip()

        except Exception as e:
            logger.warning(f"Error in JSON syntax fixing: {e}")
            return json_str

    def aggressive_json_cleanup(self, json_str: str) -> str:
        """🚨 CRITICAL FIX: Aggressive JSON cleanup for problematic LLM responses"""
        try:
            # 🎯 AGGRESSIVE FIX 1: Extract key-value pairs manually
            # Look for common patterns in LLM responses
            patterns = {
                'action': r'"?action"?\s*:\s*"?([^",}]+)"?',
                'confidence': r'"?confidence"?\s*:\s*"?(\d+(?:\.\d+)?)"?',
                'entry_reason': r'"?entry_reason"?\s*:\s*"?([^",}]+)"?',
                'take_profit': r'"?take_profit"?\s*:\s*"?(\d+(?:\.\d+)?)"?',
                'stop_loss': r'"?stop_loss"?\s*:\s*"?(\d+(?:\.\d+)?)"?',
                'hold_time': r'"?hold_time"?\s*:\s*"?([^",}]+)"?',
                'leverage': r'"?leverage"?\s*:\s*"?(\d+(?:\.\d+)?)"?',
                'decision': r'"?decision"?\s*:\s*"?([^",}]+)"?',
                'explanation': r'"?explanation"?\s*:\s*"?([^",}]+)"?',
                'reasoning': r'"?reasoning"?\s*:\s*"?([^",}]+)"?'
            }

            # Extract values using patterns
            extracted = {}
            for key, pattern in patterns.items():
                match = re.search(pattern, json_str, re.IGNORECASE | re.DOTALL)
                if match:
                    value = match.group(1).strip()
                    # Clean up the value
                    value = value.replace('"', '').replace("'", "")

                    # Convert numeric values
                    if key in ['confidence', 'take_profit', 'stop_loss', 'leverage']:
                        try:
                            if '.' in value:
                                extracted[key] = float(value)
                            else:
                                extracted[key] = int(value)
                        except:
                            extracted[key] = value
                    else:
                        extracted[key] = value

            # 🎯 AGGRESSIVE FIX 2: Build clean JSON
            if extracted:
                import json
                clean_json = json.dumps(extracted)
                logger.info(f"✅ Aggressive cleanup extracted: {list(extracted.keys())}")
                return clean_json

            # 🎯 AGGRESSIVE FIX 3: Last resort - manual reconstruction
            # Try to find any JSON-like structure and rebuild it
            json_str = re.sub(r'[^\w\s:,{}".-]', '', json_str)  # Remove problematic chars
            json_str = re.sub(r'\s+', ' ', json_str)  # Normalize whitespace

            return json_str

        except Exception as e:
            logger.warning(f"Error in aggressive JSON cleanup: {e}")
            return json_str

    def extract_trading_decision_from_text(self, text: str) -> Optional[Dict[str, Any]]:
        """Extract trading decision from free-form text responses"""
        try:
            result = {}

            # Look for decision patterns
            decision_patterns = [
                r'DECISION:\s*([A-Z]+)',
                r'(?:I\s+)?(?:recommend|suggest|advise)(?:ing)?\s+(?:a\s+)?([A-Z]+)',
                r'(?:Go|Take|Enter)\s+([A-Z]+)',
                r'Signal:\s*([A-Z]+)',
                r'Action:\s*([A-Z]+)'
            ]

            for pattern in decision_patterns:
                match = re.search(pattern, text, re.IGNORECASE)
                if match:
                    decision = match.group(1).upper()
                    if decision in ['LONG', 'SHORT', 'WAIT', 'BUY', 'SELL', 'HOLD']:
                        # Normalize decision
                        if decision == 'BUY':
                            decision = 'LONG'
                        elif decision == 'SELL':
                            decision = 'SHORT'
                        elif decision == 'HOLD':
                            decision = 'WAIT'

                        result['DECISION'] = decision
                        break

            # Look for confidence patterns
            confidence_patterns = [
                r'CONFIDENCE:\s*(\d+(?:\.\d+)?)\s*%?',
                r'(\d+(?:\.\d+)?)\s*%\s*confidence',
                r'confidence\s*(?:of\s*)?(\d+(?:\.\d+)?)\s*%?',
                r'(\d+(?:\.\d+)?)\s*%\s*certain'
            ]

            for pattern in confidence_patterns:
                match = re.search(pattern, text, re.IGNORECASE)
                if match:
                    try:
                        confidence = float(match.group(1))
                        result['CONFIDENCE'] = max(0, min(100, confidence))
                        break
                    except ValueError:
                        continue

            # Look for explanation/reasoning
            explanation_patterns = [
                r'EXPLANATION:\s*([^\n\r]+)',
                r'REASONING:\s*([^\n\r]+)',
                r'(?:because|since|due to)\s+([^\n\r.!?]+)',
            ]

            for pattern in explanation_patterns:
                match = re.search(pattern, text, re.IGNORECASE)
                if match:
                    result['EXPLANATION'] = match.group(1).strip()
                    break

            # Set defaults if not found
            if 'CONFIDENCE' not in result:
                result['CONFIDENCE'] = 50.0

            return result if 'DECISION' in result else None

        except Exception as e:
            logger.error(f"Error extracting trading decision from text: {e}")
            return None
    
def parse_llm_entry_decision(raw_reply: str) -> dict:
        """
        Extracts:
          DECISION: BUY/SELL/WAIT
          CONFIDENCE: XX%
          RATIONALE: optional
        """
        result = {'decision': 'WAIT', 'confidence': 0.0, 'rationale': ''}
        try:
            lines = raw_reply.splitlines()
            for line in lines:
                if line.strip().upper().startswith('DECISION:'):
                    result['decision'] = line.split(':', 1)[1].strip().upper()
                elif line.strip().upper().startswith('CONFIDENCE:'):
                    val = line.split(':', 1)[1].strip().replace('%','')
                    try:
                        result['confidence'] = float(val)
                    except Exception:
                        result['confidence'] = 0.0
                elif line.strip().upper().startswith('RATIONALE:'):
                    result['rationale'] = line.split(':', 1)[1].strip()
        except Exception:
            pass
        return result

# Expose parse_llm_entry_decision at module level
parse_llm_entry_decision = parse_llm_entry_decision
