#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
MACD Indicator
-------------
Indicator module for Moving Average Convergence Divergence (MACD).
"""

import pandas as pd
import numpy as np
import logging
from strategies.signal_generator import SignalGenerator

logger = logging.getLogger('strategy.indicators.macd')

class MACDIndicator(SignalGenerator):
    """
    MACD Indicator implementation.
    
    This indicator uses the Moving Average Convergence Divergence (MACD) to identify trend changes.
    """
    
    def __init__(self, config=None):
        """
        Initialize the MACD Indicator.
        
        Args:
            config (dict): Configuration dictionary.
        """
        super().__init__(config, name="MACD")
        self._load_parameters()
        
    def _load_parameters(self):
        """Load indicator parameters from config."""
        # Get indicator-specific parameters from config
        indicator_config = self.config.get('strategies', {}).get('atr_ema_bands', {})
        
        # MACD parameters
        self.macd_fast_period = indicator_config.get('macd_fast_period', 12)
        self.macd_slow_period = indicator_config.get('macd_slow_period', 26)
        self.macd_signal_period = indicator_config.get('macd_signal_period', 9)
        
        # Weight for this indicator
        self.weight = indicator_config.get('macd_weight', 0.2)
        
    def generate_signal(self, df, **kwargs):
        """
        Generate trading signals based on MACD.
        
        Args:
            df (pd.DataFrame): OHLCV DataFrame.
            **kwargs: Additional arguments.
            
        Returns:
            dict: Signal dictionary with score, direction, and MACD values.
        """
        try:
            if len(df) < max(self.macd_fast_period, self.macd_slow_period, self.macd_signal_period) + 10:
                return {'score': 0.5, 'direction': 'neutral', 'weight': self.weight}
            
            # Calculate MACD
            macd_data = self.calculate_macd(df, self.macd_fast_period, self.macd_slow_period, self.macd_signal_period)
            
            # Get the latest values
            current_macd = macd_data['macd'].iloc[-1]
            current_signal = macd_data['signal'].iloc[-1]
            current_histogram = macd_data['histogram'].iloc[-1]
            
            # Initialize signal score (0.5 is neutral)
            signal_score = 0.5
            
            # MACD line crosses above signal line (bullish)
            if (len(macd_data['macd']) > 1 and 
                macd_data['macd'].iloc[-2] < macd_data['signal'].iloc[-2] and
                current_macd > current_signal):
                signal_score = 0.7  # Bullish signal
                logger.debug(f"MACD bullish crossover")
            
            # MACD line crosses below signal line (bearish)
            elif (len(macd_data['macd']) > 1 and 
                  macd_data['macd'].iloc[-2] > macd_data['signal'].iloc[-2] and
                  current_macd < current_signal):
                signal_score = 0.3  # Bearish signal
                logger.debug(f"MACD bearish crossover")
            
            # MACD histogram direction change (early signal)
            elif len(macd_data['histogram']) > 1:
                prev_histogram = macd_data['histogram'].iloc[-2]
                
                # Histogram turns positive (bullish)
                if prev_histogram < 0 and current_histogram > 0:
                    signal_score = 0.65  # Slightly bullish signal
                    logger.debug(f"MACD histogram turns positive")
                
                # Histogram turns negative (bearish)
                elif prev_histogram > 0 and current_histogram < 0:
                    signal_score = 0.35  # Slightly bearish signal
                    logger.debug(f"MACD histogram turns negative")
                
                # No crossover, use histogram strength
                else:
                    # Normalize histogram value to adjust signal score
                    # This creates a smooth transition between bullish and bearish signals
                    # based on the strength of the histogram
                    hist_max = max(abs(macd_data['histogram'].iloc[-20:].min()), 
                                  abs(macd_data['histogram'].iloc[-20:].max()))
                    if hist_max > 0:
                        normalized_hist = current_histogram / hist_max
                        # Scale to range [0.4, 0.6]
                        signal_score = 0.5 + (normalized_hist * 0.1)
            
            # Ensure score is between 0 and 1
            signal_score = self.normalize_score(signal_score)
            
            # Determine direction based on score
            direction = 'neutral'
            if signal_score >= 0.6:
                direction = 'buy'
            elif signal_score <= 0.4:
                direction = 'sell'
            
            return {
                'score': signal_score,
                'direction': direction,
                'macd': current_macd,
                'signal': current_signal,
                'histogram': current_histogram,
                'weight': self.weight
            }
            
        except Exception as e:
            logger.error(f"Error generating MACD signal: {e}", exc_info=True)
            return {'score': 0.5, 'direction': 'neutral', 'weight': self.weight}
