#!/usr/bin/env python3
"""
🎯 DYNAMIC RISK CONTROL WIDGET
GUI widget for real-time risk level management and parameter adjustment

FEATURES:
- Risk level dropdown with 5 levels
- Real-time parameter display
- Risk transition validation
- Parameter change history
- Performance-based recommendations
"""

import sys
import os
from typing import Dict, Any, Optional
from datetime import datetime

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

try:
    from PyQt5.QtWidgets import *
    from PyQt5.QtCore import *
    from PyQt5.QtGui import *
    PYQT_AVAILABLE = True
except ImportError:
    PYQT_AVAILABLE = False

if PYQT_AVAILABLE:
    from gui.matrix_theme import MatrixTheme
    from core.dynamic_risk_manager import dynamic_risk_manager, RiskLevel, RiskParameters
    
    import logging
    logger = logging.getLogger(__name__)


class DynamicRiskControlWidget(QWidget):
    """
    CRITICAL: Dynamic risk control widget for real-time risk management
    
    Provides GUI interface for:
    - Risk level selection and validation
    - Real-time parameter display and updates
    - Risk transition warnings and confirmations
    - Performance-based risk recommendations
    """
    
    # Signals
    risk_level_changed = pyqtSignal(str, str)  # new_level, reason
    parameters_updated = pyqtSignal(dict)  # new_parameters
    
    def __init__(self, parent=None):
        super().__init__(parent)
        
        # Current state
        self.current_risk_level = dynamic_risk_manager.current_risk_level
        self.updating_display = False
        
        # Setup UI
        self.setup_ui()
        self.apply_matrix_theme()
        self.setup_connections()
        
        # Register with risk manager
        dynamic_risk_manager.register_parameter_change_callback(self.on_parameters_changed)
        
        # Initial display update
        self.update_parameter_display()
        
        logger.info("Dynamic Risk Control Widget initialized")
    
    def setup_ui(self):
        """Setup the risk control UI"""
        main_layout = QVBoxLayout(self)
        
        # Header
        header_label = QLabel("🎯 DYNAMIC RISK MANAGEMENT")
        header_label.setProperty("class", "header")
        main_layout.addWidget(header_label)
        
        # Risk level selection
        self.create_risk_level_section(main_layout)
        
        # Current parameters display
        self.create_parameters_section(main_layout)
        
        # Risk transition validation
        self.create_validation_section(main_layout)
        
        # Performance recommendations
        self.create_recommendations_section(main_layout)
    
    def create_risk_level_section(self, parent_layout):
        """Create risk level selection section"""
        risk_group = QGroupBox("🎛️ RISK LEVEL CONTROL")
        risk_layout = QGridLayout(risk_group)
        
        # Current risk level display
        risk_layout.addWidget(QLabel("Current Risk Level:"), 0, 0)
        self.current_risk_label = QLabel(self.current_risk_level.value)
        self.current_risk_label.setStyleSheet(f"color: {MatrixTheme.GREEN}; font-weight: bold; font-size: 14px;")
        risk_layout.addWidget(self.current_risk_label, 0, 1)
        
        # Risk level dropdown
        risk_layout.addWidget(QLabel("Select New Risk Level:"), 1, 0)
        self.risk_level_combo = QComboBox()
        
        # Add all risk levels to dropdown
        for risk_level in RiskLevel:
            self.risk_level_combo.addItem(risk_level.value, risk_level)
        
        # Set current selection
        current_index = self.risk_level_combo.findData(self.current_risk_level)
        self.risk_level_combo.setCurrentIndex(current_index)
        
        risk_layout.addWidget(self.risk_level_combo, 1, 1)
        
        # Apply button
        self.apply_risk_btn = QPushButton("🔄 Apply Risk Level")
        self.apply_risk_btn.clicked.connect(self.apply_risk_level_change)
        risk_layout.addWidget(self.apply_risk_btn, 1, 2)
        
        # Auto-recommend button
        self.recommend_btn = QPushButton("🎯 Get Recommendation")
        self.recommend_btn.clicked.connect(self.get_risk_recommendation)
        risk_layout.addWidget(self.recommend_btn, 2, 0, 1, 3)
        
        parent_layout.addWidget(risk_group)
    
    def create_parameters_section(self, parent_layout):
        """Create parameters display section"""
        params_group = QGroupBox("📊 CURRENT RISK PARAMETERS")
        params_layout = QGridLayout(params_group)
        
        # Trading parameters
        params_layout.addWidget(QLabel("Max Trading Capital:"), 0, 0)
        self.max_capital_label = QLabel("$0.00")
        params_layout.addWidget(self.max_capital_label, 0, 1)
        
        params_layout.addWidget(QLabel("Position Size:"), 1, 0)
        self.position_size_label = QLabel("0.0%")
        params_layout.addWidget(self.position_size_label, 1, 1)
        
        params_layout.addWidget(QLabel("Portfolio Risk:"), 2, 0)
        self.portfolio_risk_label = QLabel("0.0%")
        params_layout.addWidget(self.portfolio_risk_label, 2, 1)
        
        params_layout.addWidget(QLabel("Max Leverage:"), 0, 2)
        self.max_leverage_label = QLabel("0.0x")
        params_layout.addWidget(self.max_leverage_label, 0, 3)
        
        params_layout.addWidget(QLabel("Max Positions:"), 1, 2)
        self.max_positions_label = QLabel("0")
        params_layout.addWidget(self.max_positions_label, 1, 3)
        
        params_layout.addWidget(QLabel("Daily Loss Limit:"), 2, 2)
        self.daily_loss_label = QLabel("0.0%")
        params_layout.addWidget(self.daily_loss_label, 2, 3)
        
        # Quality thresholds
        quality_separator = QLabel("🎯 QUALITY THRESHOLDS")
        quality_separator.setStyleSheet(f"color: {MatrixTheme.BRIGHT_GREEN}; font-weight: bold; margin-top: 10px;")
        params_layout.addWidget(quality_separator, 3, 0, 1, 4)
        
        params_layout.addWidget(QLabel("ScalperGPT Spread:"), 4, 0)
        self.scalper_spread_label = QLabel("0.0")
        params_layout.addWidget(self.scalper_spread_label, 4, 1)
        
        params_layout.addWidget(QLabel("ScalperGPT Decision:"), 4, 2)
        self.scalper_decision_label = QLabel("0.0")
        params_layout.addWidget(self.scalper_decision_label, 4, 3)
        
        params_layout.addWidget(QLabel("Symbol Quality:"), 5, 0)
        self.symbol_quality_label = QLabel("0.0")
        params_layout.addWidget(self.symbol_quality_label, 5, 1)
        
        # Timer intervals
        timer_separator = QLabel("⏰ TIMER INTERVALS")
        timer_separator.setStyleSheet(f"color: {MatrixTheme.BRIGHT_GREEN}; font-weight: bold; margin-top: 10px;")
        params_layout.addWidget(timer_separator, 6, 0, 1, 4)
        
        params_layout.addWidget(QLabel("Decision Loop:"), 7, 0)
        self.decision_interval_label = QLabel("0.0s")
        params_layout.addWidget(self.decision_interval_label, 7, 1)
        
        params_layout.addWidget(QLabel("ScalperGPT Analysis:"), 7, 2)
        self.scalper_interval_label = QLabel("0.0s")
        params_layout.addWidget(self.scalper_interval_label, 7, 3)
        
        params_layout.addWidget(QLabel("Symbol Scanner:"), 8, 0)
        self.scanner_interval_label = QLabel("0.0s")
        params_layout.addWidget(self.scanner_interval_label, 8, 1)
        
        parent_layout.addWidget(params_group)
    
    def create_validation_section(self, parent_layout):
        """Create risk transition validation section"""
        validation_group = QGroupBox("⚠️ RISK TRANSITION VALIDATION")
        validation_layout = QVBoxLayout(validation_group)
        
        self.validation_text = QTextEdit()
        self.validation_text.setMaximumHeight(100)
        self.validation_text.setReadOnly(True)
        self.validation_text.setText("Select a different risk level to see transition analysis...")
        validation_layout.addWidget(self.validation_text)
        
        parent_layout.addWidget(validation_group)
    
    def create_recommendations_section(self, parent_layout):
        """Create performance recommendations section"""
        recommendations_group = QGroupBox("🎯 PERFORMANCE-BASED RECOMMENDATIONS")
        recommendations_layout = QVBoxLayout(recommendations_group)
        
        self.recommendations_text = QTextEdit()
        self.recommendations_text.setMaximumHeight(80)
        self.recommendations_text.setReadOnly(True)
        self.recommendations_text.setText("Click 'Get Recommendation' to analyze optimal risk level...")
        recommendations_layout.addWidget(self.recommendations_text)
        
        parent_layout.addWidget(recommendations_group)
    
    def setup_connections(self):
        """Setup signal connections"""
        # Update validation when dropdown changes
        self.risk_level_combo.currentTextChanged.connect(self.update_transition_validation)
    
    def apply_matrix_theme(self):
        """Apply Matrix theme to the widget"""
        self.setStyleSheet(MatrixTheme.get_stylesheet())
    
    def update_parameter_display(self):
        """Update the parameter display with current values"""
        if self.updating_display:
            return
            
        try:
            self.updating_display = True
            
            current_params = dynamic_risk_manager.get_current_parameters()
            
            # Update current risk level
            self.current_risk_label.setText(dynamic_risk_manager.current_risk_level.value)
            
            # Update trading parameters
            self.max_capital_label.setText(f"${current_params.max_trading_capital:.2f}")
            self.position_size_label.setText(f"{current_params.position_size_pct:.1f}%")
            self.portfolio_risk_label.setText(f"{current_params.portfolio_risk_pct:.1f}%")
            self.max_leverage_label.setText(f"{current_params.max_leverage:.1f}x")
            self.max_positions_label.setText(str(current_params.max_concurrent_positions))
            self.daily_loss_label.setText(f"{current_params.max_daily_loss_pct:.1f}%")
            
            # Update quality thresholds
            self.scalper_spread_label.setText(f"{current_params.scalper_spread_quality:.1f}")
            self.scalper_decision_label.setText(f"{current_params.scalper_decision_quality:.1f}")
            self.symbol_quality_label.setText(f"{current_params.symbol_quality_threshold:.1f}")
            
            # Update timer intervals
            self.decision_interval_label.setText(f"{current_params.decision_loop_interval:.1f}s")
            self.scalper_interval_label.setText(f"{current_params.scalper_analysis_interval:.1f}s")
            self.scanner_interval_label.setText(f"{current_params.symbol_scanner_interval:.1f}s")
            
            # Color code based on risk level
            risk_color = self.get_risk_level_color(dynamic_risk_manager.current_risk_level)
            
            # Apply color to key metrics
            for label in [self.max_capital_label, self.position_size_label, self.portfolio_risk_label]:
                label.setStyleSheet(f"color: {risk_color}; font-weight: bold;")
            
        finally:
            self.updating_display = False
    
    def get_risk_level_color(self, risk_level: RiskLevel) -> str:
        """Get color for risk level"""
        color_map = {
            RiskLevel.ULTRA_CONSERVATIVE: MatrixTheme.GREEN,
            RiskLevel.CONSERVATIVE: MatrixTheme.LIGHT_GREEN,
            RiskLevel.MODERATE: MatrixTheme.YELLOW,
            RiskLevel.AGGRESSIVE: "#FF8800",  # Orange
            RiskLevel.HIGH_RISK: MatrixTheme.RED
        }
        return color_map.get(risk_level, MatrixTheme.GREEN)
    
    def update_transition_validation(self):
        """Update risk transition validation display"""
        try:
            selected_risk_level = self.risk_level_combo.currentData()
            
            if selected_risk_level == dynamic_risk_manager.current_risk_level:
                self.validation_text.setText("No change - current risk level selected.")
                return
            
            # Get transition validation
            validation = dynamic_risk_manager.validate_risk_level_transition(
                dynamic_risk_manager.current_risk_level,
                selected_risk_level
            )
            
            # Format validation message
            if validation['safe']:
                message = f"✅ Safe transition to {selected_risk_level.value}\n"
            else:
                message = f"⚠️ Caution required for transition to {selected_risk_level.value}\n"
            
            message += f"Capital change: {validation['capital_change']:+.1%}\n"
            message += f"Risk change: {validation['risk_change']:+.1%}\n"
            
            if validation['warnings']:
                message += "\nWarnings:\n"
                for warning in validation['warnings']:
                    message += f"• {warning}\n"
            
            self.validation_text.setText(message)
            
        except Exception as e:
            logger.error(f"Error updating transition validation: {e}")
    
    def apply_risk_level_change(self):
        """Apply the selected risk level change"""
        try:
            selected_risk_level = self.risk_level_combo.currentData()
            
            if selected_risk_level == dynamic_risk_manager.current_risk_level:
                QMessageBox.information(self, "No Change", "Selected risk level is already active.")
                return
            
            # Get transition validation
            validation = dynamic_risk_manager.validate_risk_level_transition(
                dynamic_risk_manager.current_risk_level,
                selected_risk_level
            )
            
            # Show confirmation dialog with warnings
            message = f"Change risk level to {selected_risk_level.value}?\n\n"
            message += f"Capital change: {validation['capital_change']:+.1%}\n"
            message += f"Risk change: {validation['risk_change']:+.1%}\n"
            
            if validation['warnings']:
                message += "\nWarnings:\n"
                for warning in validation['warnings']:
                    message += f"• {warning}\n"
            
            reply = QMessageBox.question(
                self,
                "Confirm Risk Level Change",
                message,
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )
            
            if reply == QMessageBox.Yes:
                # Apply the change
                success = dynamic_risk_manager.set_risk_level(
                    selected_risk_level,
                    "User GUI selection"
                )
                
                if success:
                    # Emit signal
                    self.risk_level_changed.emit(selected_risk_level.value, "User GUI selection")
                    
                    QMessageBox.information(
                        self,
                        "Risk Level Changed",
                        f"Risk level successfully changed to {selected_risk_level.value}"
                    )
                    
                    logger.info(f"Risk level changed to {selected_risk_level.value} via GUI")
                else:
                    QMessageBox.critical(self, "Error", "Failed to change risk level")
            
        except Exception as e:
            logger.error(f"Error applying risk level change: {e}")
            QMessageBox.critical(self, "Error", f"Failed to apply risk level change: {e}")
    
    def get_risk_recommendation(self):
        """Get performance-based risk recommendation"""
        try:
            # Mock performance data for demonstration
            # In real implementation, this would come from the trading system
            mock_performance = {
                'win_rate': 0.65,
                'sharpe_ratio': 1.2,
                'max_drawdown': 0.08,
                'total_trades': 150,
                'avg_return': 0.02
            }
            
            mock_balance = 1500.0  # Mock account balance
            
            # Get recommendation
            recommended_level = dynamic_risk_manager.get_recommended_risk_level(
                mock_balance,
                mock_performance
            )
            
            # Format recommendation message
            message = f"📊 Performance Analysis:\n"
            message += f"• Win Rate: {mock_performance['win_rate']:.1%}\n"
            message += f"• Sharpe Ratio: {mock_performance['sharpe_ratio']:.2f}\n"
            message += f"• Max Drawdown: {mock_performance['max_drawdown']:.1%}\n"
            message += f"• Account Balance: ${mock_balance:.2f}\n\n"
            message += f"🎯 Recommended Risk Level: {recommended_level.value}"
            
            if recommended_level != dynamic_risk_manager.current_risk_level:
                message += f"\n\n💡 Consider changing from {dynamic_risk_manager.current_risk_level.value}"
            else:
                message += f"\n\n✅ Current risk level is optimal"
            
            self.recommendations_text.setText(message)
            
        except Exception as e:
            logger.error(f"Error getting risk recommendation: {e}")
            self.recommendations_text.setText(f"Error getting recommendation: {e}")
    
    def on_parameters_changed(self, old_parameters: RiskParameters, new_parameters: RiskParameters):
        """Handle parameter changes from risk manager"""
        try:
            # Update display
            self.update_parameter_display()
            
            # Emit signal
            self.parameters_updated.emit(dynamic_risk_manager._parameters_to_dict(new_parameters))
            
            logger.info("Risk parameters updated in GUI")
            
        except Exception as e:
            logger.error(f"Error handling parameter changes: {e}")


if __name__ == "__main__" and PYQT_AVAILABLE:
    app = QApplication(sys.argv)
    app.setStyleSheet(MatrixTheme.get_stylesheet())
    
    widget = DynamicRiskControlWidget()
    widget.show()
    
    sys.exit(app.exec_())
