#!/usr/bin/env python3
"""
DIRECTORY CLEANUP IMPLEMENTATION
Organize Epinnox directory structure and remove redundant files
"""

import os
import sys
import shutil
from pathlib import Path
from datetime import datetime

def create_archive_directory():
    """Create archive directory for redundant scripts"""
    
    archive_dir = Path("archive")
    if not archive_dir.exists():
        archive_dir.mkdir()
        print(f"✅ Created archive directory: {archive_dir}")
    
    # Create subdirectories
    subdirs = [
        "archive/redundant_launchers",
        "archive/temporary_fixes", 
        "archive/duplicate_gui",
        "archive/old_configs"
    ]
    
    for subdir in subdirs:
        Path(subdir).mkdir(parents=True, exist_ok=True)
        print(f"✅ Created subdirectory: {subdir}")

def identify_redundant_files():
    """Identify files to be archived or removed"""
    
    redundant_launchers = [
        "deploy_live_validation.py",
        "deploy_ultra_conservative_live.py", 
        "deploy_live_production.py",
        "deploy_autonomous_trading.py",
        "start_autonomous_trading_robust.py",
        "start_autonomous_trading_direct.py", 
        "restart_autonomous_trading.py"
    ]
    
    temporary_fixes = [
        "fix_autonomous_trading_execution.py",
        "apply_final_unicode_fixes.py",
        "fix_unicode_logging.py",
        "fix_deployment_issues.py",
        "verify_system_status.py",
        "diagnose_live_trading.py"
    ]
    
    duplicate_gui = [
        "gui/epinnox_main_window.py"  # Will be merged into integrated_monitoring_dashboard.py
    ]
    
    old_configs = [
        # Will identify during cleanup
    ]
    
    return {
        "redundant_launchers": redundant_launchers,
        "temporary_fixes": temporary_fixes,
        "duplicate_gui": duplicate_gui,
        "old_configs": old_configs
    }

def archive_file(file_path: str, archive_subdir: str):
    """Archive a file to the specified subdirectory"""
    
    source = Path(file_path)
    if not source.exists():
        print(f"⚠️ File not found: {file_path}")
        return False
    
    # Create archive path with timestamp
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    archive_path = Path(f"archive/{archive_subdir}/{source.stem}_{timestamp}{source.suffix}")
    
    try:
        shutil.move(str(source), str(archive_path))
        print(f"📦 Archived: {file_path} → {archive_path}")
        return True
    except Exception as e:
        print(f"❌ Failed to archive {file_path}: {e}")
        return False

def create_cleanup_summary():
    """Create a summary of cleanup actions"""
    
    summary_content = f"""# EPINNOX DIRECTORY CLEANUP SUMMARY
Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

## ARCHIVED FILES

### Redundant Launchers
These scripts have been replaced by the unified launcher system:
- deploy_live_validation.py -> Functionality integrated into launch_epinnox_unified.py
- deploy_ultra_conservative_live.py -> Risk management integrated into unified system
- deploy_live_production.py -> Production deployment integrated
- deploy_autonomous_trading.py -> Autonomous trading integrated
- start_autonomous_trading_robust.py -> Core logic moved to unified system
- start_autonomous_trading_direct.py -> Redundant implementation
- restart_autonomous_trading.py -> Restart functionality in unified launcher

### Temporary Fix Scripts
These were temporary solutions that are no longer needed:
- fix_autonomous_trading_execution.py -> Issues resolved in unified system
- apply_final_unicode_fixes.py -> Unicode fixes applied permanently
- fix_unicode_logging.py -> Logging fixes integrated
- fix_deployment_issues.py -> Deployment issues resolved
- verify_system_status.py -> Status verification integrated into GUI
- diagnose_live_trading.py -> Diagnostics integrated into GUI

### Duplicate GUI Components
These have been consolidated:
- gui/epinnox_main_window.py -> Merged into integrated_monitoring_dashboard.py

## CURRENT STRUCTURE

### Core Components (KEPT)
- core/ -> All core trading components
- gui/integrated_monitoring_dashboard.py -> Enhanced main GUI
- validation/ -> Validation components
- trading/ -> Trading engine components

### New Unified System
- launch_epinnox_unified.py -> Single master launcher
- gui/autonomous_trading_controls.py -> Integrated trading controls
- gui/unified_status_widgets.py -> Unified status displays

## USAGE

### Before Cleanup
Multiple scripts required:
```bash
python deploy_live_validation.py
python start_autonomous_trading_robust.py
# Multiple terminal windows needed
```

### After Cleanup
Single unified launcher:
```bash
python launch_epinnox_unified.py --live --risk ultra-conservative
# Everything in one integrated GUI
```

## RECOVERY

If any archived file is needed:
1. Check archive/ directory
2. Files are timestamped for easy identification
3. Copy back to main directory if needed

## BENEFITS

1. **Simplified Usage**: Single entry point for all functionality
2. **Reduced Confusion**: No more multiple similar scripts
3. **Better Integration**: All components work together seamlessly
4. **Easier Maintenance**: Single codebase to maintain
5. **Professional Appearance**: Clean, organized directory structure
"""
    
    with open("CLEANUP_SUMMARY.md", "w", encoding='utf-8') as f:
        f.write(summary_content)
    
    print("📋 Created cleanup summary: CLEANUP_SUMMARY.md")

def perform_cleanup():
    """Perform the actual directory cleanup"""
    
    print("🧹 EPINNOX DIRECTORY CLEANUP")
    print("=" * 40)
    
    # Create archive directory
    create_archive_directory()
    
    # Identify files to archive
    files_to_archive = identify_redundant_files()
    
    archived_count = 0
    
    # Archive redundant launchers
    print("\n📦 Archiving redundant launcher scripts...")
    for file_path in files_to_archive["redundant_launchers"]:
        if archive_file(file_path, "redundant_launchers"):
            archived_count += 1
    
    # Archive temporary fixes
    print("\n🔧 Archiving temporary fix scripts...")
    for file_path in files_to_archive["temporary_fixes"]:
        if archive_file(file_path, "temporary_fixes"):
            archived_count += 1
    
    # Archive duplicate GUI components
    print("\n🖥️ Archiving duplicate GUI components...")
    for file_path in files_to_archive["duplicate_gui"]:
        if archive_file(file_path, "duplicate_gui"):
            archived_count += 1
    
    # Clean up log files (move old logs to logs/archive)
    print("\n📝 Organizing log files...")
    logs_archive = Path("logs/archive")
    logs_archive.mkdir(exist_ok=True)
    
    # Move old deployment logs
    for log_file in Path(".").glob("*deployment*.log"):
        if log_file.exists():
            shutil.move(str(log_file), f"logs/archive/{log_file.name}")
            print(f"📝 Moved log: {log_file.name} → logs/archive/")
            archived_count += 1
    
    # Clean up __pycache__ directories
    print("\n🗑️ Cleaning up cache files...")
    for pycache_dir in Path(".").rglob("__pycache__"):
        if pycache_dir.is_dir():
            shutil.rmtree(pycache_dir)
            print(f"🗑️ Removed cache: {pycache_dir}")
    
    # Create cleanup summary
    create_cleanup_summary()
    
    print(f"\n✅ CLEANUP COMPLETE")
    print(f"📦 Archived {archived_count} files")
    print(f"📋 Summary created: CLEANUP_SUMMARY.md")
    print(f"🎯 Directory structure organized")
    
    return archived_count

def show_new_directory_structure():
    """Show the new organized directory structure"""
    
    print("\n📁 NEW DIRECTORY STRUCTURE")
    print("=" * 30)
    
    structure = """
Epinnox_v6/
├── launch_epinnox_unified.py          # 🚀 SINGLE MASTER LAUNCHER
├── CLEANUP_SUMMARY.md                 # 📋 Cleanup documentation
├── UNIFIED_LAUNCHER_DESIGN.md         # 📖 Design documentation
├── 
├── core/                              # 🔧 Core trading components
│   ├── autonomous_trading_orchestrator.py
│   ├── unified_execution_engine.py
│   ├── dynamic_risk_manager.py
│   ├── emergency_stop_coordinator.py
│   ├── scalper_gpt.py
│   └── ... (other core components)
├── 
├── gui/                               # 🖥️ GUI components
│   ├── integrated_monitoring_dashboard.py  # 📊 MAIN GUI
│   ├── autonomous_trading_controls.py      # 🤖 Trading controls
│   ├── unified_status_widgets.py           # 📈 Status widgets
│   └── ... (other GUI components)
├── 
├── validation/                        # ✅ Validation components
├── trading/                           # 💱 Trading engine components
├── config/                            # ⚙️ Configuration files
├── logs/                              # 📝 Log files
│   └── archive/                       # 📦 Archived logs
├── 
└── archive/                           # 📦 ARCHIVED FILES
    ├── redundant_launchers/           # 🗂️ Old launcher scripts
    ├── temporary_fixes/               # 🔧 Temporary fix scripts
    ├── duplicate_gui/                 # 🖥️ Duplicate GUI components
    └── old_configs/                   # ⚙️ Old configuration files
"""
    
    print(structure)

def main():
    """Main cleanup function"""
    
    try:
        print("🧹 EPINNOX DIRECTORY CLEANUP TOOL")
        print("⚠️ This will archive redundant files and organize the directory")
        print("=" * 60)
        
        # Confirm cleanup
        response = input("\nProceed with cleanup? (y/N): ").strip().lower()
        if response != 'y':
            print("❌ Cleanup cancelled")
            return 1
        
        # Perform cleanup
        archived_count = perform_cleanup()
        
        # Show new structure
        show_new_directory_structure()
        
        print("\n🎉 DIRECTORY CLEANUP SUCCESSFUL")
        print("✅ Redundant files archived")
        print("✅ Directory structure organized") 
        print("✅ Ready for unified launcher implementation")
        
        print("\n🎯 NEXT STEPS:")
        print("1. Implement launch_epinnox_unified.py")
        print("2. Enhance integrated_monitoring_dashboard.py")
        print("3. Test unified system functionality")
        print("4. Update documentation")
        
        return 0
        
    except Exception as e:
        print(f"\n❌ CLEANUP ERROR: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
