#!/usr/bin/env python3
"""
SYSTEM INTEGRATION INITIALIZER
Initialize and register all system components for live deployment
"""

import sys
import os
import logging

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def initialize_system_integration():
    """Initialize and register all system components"""
    
    print("🔧 INITIALIZING SYSTEM INTEGRATION")
    print("=" * 40)
    
    try:
        # Import core components
        print("📦 Importing core components...")
        
        from core.dynamic_risk_integration import dynamic_risk_integration
        from core.autonomous_trading_orchestrator import AutonomousTradingOrchestrator, TradingMode
        from core.dynamic_risk_manager import dynamic_risk_manager, RiskLevel
        from core.emergency_stop_coordinator import emergency_coordinator
        from core.unified_execution_engine import unified_execution_engine
        
        print("✅ Core components imported successfully")
        
        # Set ultra-conservative risk level
        print("🎯 Setting ultra-conservative risk level...")
        success = dynamic_risk_manager.set_risk_level(
            RiskLevel.ULTRA_CONSERVATIVE,
            "System integration initialization"
        )
        
        if success:
            print("✅ Ultra-conservative risk level set")
        else:
            print("⚠️ Risk level setting failed")
        
        # Create test orchestrator for registration
        print("🤖 Creating test orchestrator...")
        test_config = {
            'trading_mode': TradingMode.PAPER,
            'max_trading_capital': 100.0,
            'position_size_pct': 1.0,
            'portfolio_risk_pct': 2.0
        }
        
        orchestrator = AutonomousTradingOrchestrator(TradingMode.PAPER, test_config)
        print("✅ Test orchestrator created")
        
        # Register components with integration system
        print("🔗 Registering components with integration system...")
        
        # Register orchestrator
        try:
            dynamic_risk_integration.register_orchestrator(orchestrator)
            print("  ✅ Orchestrator registered")
        except Exception as e:
            print(f"  ⚠️ Orchestrator registration failed: {e}")
        
        # Register dynamic risk manager
        try:
            dynamic_risk_integration.register_dynamic_risk_manager(dynamic_risk_manager)
            print("  ✅ Dynamic risk manager registered")
        except Exception as e:
            print(f"  ⚠️ Dynamic risk manager registration failed: {e}")
        
        # Register emergency coordinator
        try:
            dynamic_risk_integration.register_emergency_coordinator(emergency_coordinator)
            print("  ✅ Emergency coordinator registered")
        except Exception as e:
            print(f"  ⚠️ Emergency coordinator registration failed: {e}")
        
        # Register execution engine
        try:
            dynamic_risk_integration.register_execution_engine(unified_execution_engine)
            print("  ✅ Execution engine registered")
        except Exception as e:
            print(f"  ⚠️ Execution engine registration failed: {e}")
        
        # Verify integration status
        print("🔍 Verifying integration status...")
        integration_status = dynamic_risk_integration.get_integration_status()
        
        print("📊 Integration Status:")
        for component, status in integration_status.items():
            status_icon = "✅" if status else "❌"
            print(f"  {status_icon} {component}: {status}")
        
        # Test emergency systems
        print("🚨 Testing emergency systems...")
        if emergency_coordinator.is_initialized():
            print("  ✅ Emergency coordinator initialized")
            
            # Test emergency systems
            import asyncio
            test_result = asyncio.run(emergency_coordinator.test_emergency_systems())
            if test_result:
                print("  ✅ Emergency systems test passed")
            else:
                print("  ⚠️ Emergency systems test failed")
        else:
            print("  ❌ Emergency coordinator not initialized")
        
        # Test execution engine
        print("⚡ Testing execution engine...")
        execution_status = unified_execution_engine.get_execution_status()
        
        if execution_status['enforce_limit_orders_only']:
            print("  ✅ LIMIT orders enforcement enabled")
        else:
            print("  ❌ LIMIT orders enforcement disabled")
        
        print(f"  📊 Max position size: {execution_status['max_position_size'] * 100}%")
        print(f"  📊 Max leverage: {execution_status['max_leverage']}x")
        print(f"  📊 Max concurrent positions: {execution_status['max_concurrent_positions']}")
        
        # Final status - check only critical components
        critical_components = [
            'orchestrator_registered',
            'execution_engine_registered',
            'dynamic_risk_manager_registered',
            'emergency_coordinator_registered'
        ]

        critical_registered = all(integration_status.get(comp, False) for comp in critical_components)

        if critical_registered:
            print("\n🎉 SYSTEM INTEGRATION SUCCESSFUL")
            print("✅ All critical components registered and ready for deployment")
            print("⚠️ Optional components (ScalperGPT, Symbol Scanner) can be registered later")
            return True
        else:
            print("\n❌ SYSTEM INTEGRATION FAILED")
            print("Critical components missing:")
            for comp in critical_components:
                if not integration_status.get(comp, False):
                    print(f"  ❌ {comp}")
            return False
        
    except Exception as e:
        print(f"\n❌ SYSTEM INTEGRATION FAILED: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main initialization function"""
    
    print("🚀 STARTING SYSTEM INTEGRATION INITIALIZATION")
    print("=" * 50)
    
    success = initialize_system_integration()
    
    if success:
        print("\n✅ SYSTEM READY FOR LIVE DEPLOYMENT")
        print("🚀 You can now run: python deploy_live_validation.py")
        return 0
    else:
        print("\n❌ SYSTEM INTEGRATION INCOMPLETE")
        print("🔧 Check errors above and fix before deployment")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
