#!/usr/bin/env python3
"""
🚀 EPINNOX ULTRA-CONSERVATIVE LIVE DEPLOYMENT v2.0
Enhanced deployment script for ultra-conservative live trading with all integrated systems

CRITICAL SYSTEMS INTEGRATION:
✅ LIMIT Orders Enforcement
✅ Emergency Stop Mechanisms  
✅ WebSocket Stability
✅ Unified Execution Engine
✅ ScalperGPT Integration (spread_quality >= 7.0, decision_quality >= 8.0)
✅ Dynamic Symbol Scanner (scoring > 75.0)
✅ Timer Coordination (30-second decision loops)

ULTRA-CONSERVATIVE SETTINGS:
- Max $100 trading capital
- 1% position size
- 2% portfolio risk
- LIMIT orders only
- Emergency stops enabled
"""

import sys
import os
import json
import asyncio
import time
from datetime import datetime
import logging
from typing import Dict, List, Optional, Any

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Import all integrated systems
try:
    # Core trading systems
    from core.autonomous_trading_orchestrator import AutonomousTradingOrchestrator, TradingMode
    from core.autonomous_llm_integration import AutonomousLLMIntegration
    from core.unified_execution_engine import UnifiedExecutionEngine
    from core.emergency_stop_coordinator import emergency_coordinator, EmergencyType, EmergencyLevel
    from core.websocket_stability_manager import WebSocketStabilityManager
    from core.scalper_gpt import ScalperGPT
    from core.symbol_scanner import SymbolScanner, SymbolScannerConfig
    from core.timer_coordinator import timer_coordinator, TimerPriority, StandardIntervals
    
    # Credentials and configuration
    from credentials import CredentialsManager
    
    # Trading engine
    from trading.ccxt_trading_engine import CCXTTradingEngine
    
    print("✅ All critical systems imported successfully")
    
except ImportError as e:
    print(f"❌ Failed to import critical systems: {e}")
    print("⚠️  Please ensure all modules are properly installed")
    sys.exit(1)

# Configure comprehensive logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(name)s - %(message)s',
    handlers=[
        logging.FileHandler(f'ultra_conservative_deployment_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

# CRITICAL: Ultra-conservative live trading configuration
ULTRA_CONSERVATIVE_CONFIG = {
    'max_trading_capital': 100.0,        # Maximum $100 trading capital
    'position_size_pct': 1.0,            # 1% position size
    'portfolio_risk_pct': 2.0,           # 2% portfolio risk
    'max_daily_loss_pct': 5.0,           # 5% max daily loss
    'max_leverage': 2.0,                 # Maximum 2x leverage
    'max_concurrent_positions': 1,        # Only 1 position at a time
    'require_limit_orders_only': True,   # LIMIT orders only
    'emergency_stop_enabled': True,      # Emergency stops enabled
    'scalper_quality_thresholds': {
        'spread_quality': 7.0,           # ScalperGPT spread quality >= 7.0
        'decision_quality': 8.0,         # ScalperGPT decision quality >= 8.0
    },
    'symbol_quality_threshold': 75.0,    # Dynamic symbol scanner > 75.0
    'timer_coordination_enabled': True,  # 30-second decision loops
    'websocket_stability_required': True # WebSocket stability required
}


class UltraConservativeLiveDeployment:
    """
    CRITICAL: Ultra-conservative live trading deployment manager
    
    Integrates all 7 completed critical systems for safe live trading:
    1. LIMIT Orders Enforcement
    2. Emergency Stop Mechanisms
    3. WebSocket Stability
    4. Unified Execution Engine
    5. ScalperGPT Integration
    6. Dynamic Symbol Scanner
    7. Timer Coordination
    """
    
    def __init__(self):
        self.config = ULTRA_CONSERVATIVE_CONFIG
        self.credentials_manager = None
        self.exchange_engine = None
        self.orchestrator = None
        self.validation_results = {}
        self.deployment_start_time = None
        
        logger.info("[DEPLOYMENT] Ultra-conservative live deployment initialized")
    
    async def validate_all_systems(self) -> bool:
        """
        CRITICAL: Comprehensive validation of all integrated systems
        
        Returns:
            True if all systems pass validation
        """
        print("\n🔧 COMPREHENSIVE SYSTEM VALIDATION")
        print("=" * 60)
        
        validation_steps = [
            ("Credentials", self._validate_credentials),
            ("Exchange Connection", self._validate_exchange_connection),
            ("Emergency Stops", self._validate_emergency_stops),
            ("WebSocket Stability", self._validate_websocket_stability),
            ("ScalperGPT Integration", self._validate_scalper_gpt),
            ("Symbol Scanner", self._validate_symbol_scanner),
            ("Timer Coordination", self._validate_timer_coordination),
            ("Unified Execution", self._validate_unified_execution),
            ("Risk Limits", self._validate_risk_limits)
        ]
        
        for step_name, validation_func in validation_steps:
            print(f"\n🔍 Validating {step_name}...")
            try:
                result = await validation_func()
                self.validation_results[step_name] = result
                
                if result:
                    print(f"✅ {step_name} validation passed")
                else:
                    print(f"❌ {step_name} validation failed")
                    return False
                    
            except Exception as e:
                print(f"❌ {step_name} validation error: {e}")
                logger.error(f"Validation error for {step_name}: {e}")
                return False
        
        print(f"\n✅ ALL SYSTEM VALIDATIONS PASSED")
        return True
    
    async def _validate_credentials(self) -> bool:
        """Validate HTX exchange credentials"""
        try:
            self.credentials_manager = CredentialsManager()
            account_creds = self.credentials_manager.get_account_credentials()
            
            print(f"   Account: {account_creds['name']}")
            print(f"   Exchange: {account_creds['exchange']}")
            print(f"   API Key: {account_creds['api_key'][:8]}...")
            
            return True
            
        except Exception as e:
            logger.error(f"Credentials validation failed: {e}")
            return False
    
    async def _validate_exchange_connection(self) -> bool:
        """Validate HTX exchange connection and balance"""
        try:
            self.exchange_engine = CCXTTradingEngine('htx', demo_mode=False)
            
            if not self.exchange_engine.initialize_exchange():
                return False
            
            print("   HTX exchange connection established")
            
            # Check account balance
            balance = self.exchange_engine.exchange.fetch_balance()
            usdt_balance = balance.get('USDT', {}).get('free', 0)
            
            print(f"   Account balance: ${usdt_balance:.2f} USDT")
            
            # Validate balance for ultra-conservative trading
            min_balance = self.config['max_trading_capital']
            if usdt_balance >= min_balance:
                print(f"   Sufficient balance for trading (min: ${min_balance})")
            else:
                print(f"   Warning: Balance below recommended ${min_balance}")
                print(f"   Proceeding with available balance...")
            
            return True
            
        except Exception as e:
            logger.error(f"Exchange connection validation failed: {e}")
            return False
    
    async def _validate_emergency_stops(self) -> bool:
        """Validate emergency stop system"""
        try:
            if not emergency_coordinator.is_initialized():
                await emergency_coordinator.initialize()
            
            # Test emergency stop functionality
            test_result = await emergency_coordinator.test_emergency_systems()
            
            if test_result:
                print("   Emergency stop systems operational")
                return True
            else:
                print("   Emergency stop systems test failed")
                return False
                
        except Exception as e:
            logger.error(f"Emergency stop validation failed: {e}")
            return False
    
    async def _validate_websocket_stability(self) -> bool:
        """Validate WebSocket stability manager"""
        try:
            websocket_manager = WebSocketStabilityManager()
            
            # Test connection stability
            stability_test = await websocket_manager.test_connection_stability()
            
            if stability_test:
                print("   WebSocket stability manager operational")
                return True
            else:
                print("   WebSocket stability test failed")
                return False
                
        except Exception as e:
            logger.error(f"WebSocket stability validation failed: {e}")
            return False
    
    async def _validate_scalper_gpt(self) -> bool:
        """Validate ScalperGPT integration and quality thresholds"""
        try:
            scalper_gpt = ScalperGPT()
            quality_thresholds = scalper_gpt.quality_thresholds
            
            expected_spread = self.config['scalper_quality_thresholds']['spread_quality']
            expected_decision = self.config['scalper_quality_thresholds']['decision_quality']
            
            spread_ok = quality_thresholds['spread_quality'] >= expected_spread
            decision_ok = quality_thresholds['decision_quality'] >= expected_decision
            
            if spread_ok and decision_ok:
                print(f"   Quality thresholds: spread >= {expected_spread}, decision >= {expected_decision}")
                return True
            else:
                print(f"   Quality thresholds insufficient")
                return False
                
        except Exception as e:
            logger.error(f"ScalperGPT validation failed: {e}")
            return False
    
    async def _validate_symbol_scanner(self) -> bool:
        """Validate dynamic symbol scanner"""
        try:
            # Create mock API for testing
            class MockMarketAPI:
                def fetch_ticker(self, symbol):
                    return {'symbol': symbol, 'last': 0.35, 'bid': 0.349, 'ask': 0.351, 'volume': 100000}
                def fetch_order_book(self, symbol, limit=10):
                    return {'bids': [[0.349, 1000]], 'asks': [[0.351, 1000]]}
                def fetch_trades(self, symbol, limit=100):
                    return [{'price': 0.35, 'amount': 100}]
            
            scanner = SymbolScannerConfig.create_scanner(
                market_api=MockMarketAPI(),
                symbols=['DOGE/USDT', 'BTC/USDT'],
                mode='scalping'
            )
            
            # Test high-quality symbol detection
            high_quality_symbols = scanner.find_high_quality_symbols(
                min_score=self.config['symbol_quality_threshold']
            )
            
            print(f"   Symbol scanner operational (threshold: {self.config['symbol_quality_threshold']})")
            return True
            
        except Exception as e:
            logger.error(f"Symbol scanner validation failed: {e}")
            return False
    
    async def _validate_timer_coordination(self) -> bool:
        """Validate timer coordination system"""
        try:
            # Test timer coordinator functionality
            test_executed = {'count': 0}
            
            def test_timer():
                test_executed['count'] += 1
            
            # Register test timer
            success = timer_coordinator.register_timer(
                'validation_test',
                test_timer,
                interval=0.1,
                priority=TimerPriority.LOW
            )
            
            if success:
                print(f"   Timer coordination operational")
                print(f"   Standard intervals: {StandardIntervals.AUTONOMOUS_DECISION_LOOP}s decision loops")
                
                # Clean up test timer
                timer_coordinator.unregister_timer('validation_test')
                return True
            else:
                return False
                
        except Exception as e:
            logger.error(f"Timer coordination validation failed: {e}")
            return False
    
    async def _validate_unified_execution(self) -> bool:
        """Validate unified execution engine"""
        try:
            execution_engine = UnifiedExecutionEngine()
            
            # Test LIMIT orders only enforcement
            limit_orders_enforced = execution_engine.enforce_limit_orders_only
            
            if limit_orders_enforced:
                print("   Unified execution engine operational (LIMIT orders only)")
                return True
            else:
                print("   LIMIT orders enforcement not enabled")
                return False
                
        except Exception as e:
            logger.error(f"Unified execution validation failed: {e}")
            return False
    
    async def _validate_risk_limits(self) -> bool:
        """Validate ultra-conservative risk limits"""
        try:
            config = self.config
            
            # Validate all risk parameters are ultra-conservative
            validations = [
                (config['max_trading_capital'] <= 100.0, "Max trading capital <= $100"),
                (config['position_size_pct'] <= 1.0, "Position size <= 1%"),
                (config['portfolio_risk_pct'] <= 2.0, "Portfolio risk <= 2%"),
                (config['max_leverage'] <= 2.0, "Max leverage <= 2x"),
                (config['max_concurrent_positions'] <= 1, "Max positions <= 1"),
                (config['require_limit_orders_only'], "LIMIT orders only required"),
                (config['emergency_stop_enabled'], "Emergency stops enabled")
            ]
            
            for validation, description in validations:
                if not validation:
                    print(f"   Risk validation failed: {description}")
                    return False
            
            print("   Ultra-conservative risk limits validated")
            return True
            
        except Exception as e:
            logger.error(f"Risk limits validation failed: {e}")
            return False

    async def deploy_live_trading(self) -> bool:
        """
        CRITICAL: Deploy ultra-conservative live trading system

        Returns:
            True if deployment successful
        """
        try:
            print("\n🚀 DEPLOYING ULTRA-CONSERVATIVE LIVE TRADING SYSTEM")
            print("=" * 60)

            self.deployment_start_time = datetime.now()

            # Create autonomous trading orchestrator with ultra-conservative config
            orchestrator_config = {
                'trading_mode': TradingMode.LIVE,
                'max_trading_capital': self.config['max_trading_capital'],
                'position_size_pct': self.config['position_size_pct'],
                'portfolio_risk_pct': self.config['portfolio_risk_pct'],
                'max_leverage': self.config['max_leverage'],
                'max_concurrent_positions': self.config['max_concurrent_positions'],
                'require_limit_orders_only': self.config['require_limit_orders_only'],
                'emergency_stop_enabled': self.config['emergency_stop_enabled'],
                'dynamic_symbol_selection': True,
                'symbol_quality_threshold': self.config['symbol_quality_threshold'],
                'scalper_quality_thresholds': self.config['scalper_quality_thresholds'],
                'timer_coordination_enabled': self.config['timer_coordination_enabled'],
                'websocket_stability_required': self.config['websocket_stability_required'],
                'active_symbols': ['DOGE/USDT:USDT', 'BTC/USDT:USDT', 'ETH/USDT:USDT']
            }

            print("1️⃣ Initializing autonomous trading orchestrator...")
            self.orchestrator = AutonomousTradingOrchestrator(TradingMode.LIVE, orchestrator_config)

            # Initialize all systems
            print("2️⃣ Initializing integrated systems...")

            # Start timer coordinator
            timer_task = asyncio.create_task(timer_coordinator.start())
            print("   ✅ Timer coordinator started")

            # Initialize emergency stop coordinator
            await emergency_coordinator.initialize()
            print("   ✅ Emergency stop coordinator initialized")

            # Initialize orchestrator
            await self.orchestrator.initialize()
            print("   ✅ Autonomous trading orchestrator initialized")

            print("3️⃣ Starting autonomous trading operations...")

            # Start autonomous trading
            trading_task = asyncio.create_task(self.orchestrator.start_autonomous_trading())

            print("4️⃣ Starting monitoring systems...")

            # Start monitoring
            monitoring_task = asyncio.create_task(self._monitor_live_trading())

            print("\n✅ ULTRA-CONSERVATIVE LIVE TRADING DEPLOYED SUCCESSFULLY")
            print(f"🕐 Deployment time: {self.deployment_start_time.strftime('%Y-%m-%d %H:%M:%S')}")
            print(f"💰 Max trading capital: ${self.config['max_trading_capital']}")
            print(f"📊 Position size: {self.config['position_size_pct']}%")
            print(f"🛡️ Portfolio risk: {self.config['portfolio_risk_pct']}%")
            print(f"⚡ Decision loops: {StandardIntervals.AUTONOMOUS_DECISION_LOOP}s")
            print(f"🎯 Symbol quality threshold: {self.config['symbol_quality_threshold']}")
            print(f"🔬 ScalperGPT thresholds: spread >= {self.config['scalper_quality_thresholds']['spread_quality']}, decision >= {self.config['scalper_quality_thresholds']['decision_quality']}")

            # Wait for trading operations
            await asyncio.gather(trading_task, monitoring_task, timer_task)

            return True

        except Exception as e:
            logger.error(f"Live trading deployment failed: {e}")
            return False

    async def _monitor_live_trading(self):
        """Monitor live trading operations with comprehensive metrics"""
        print("\n📊 LIVE TRADING MONITORING STARTED")
        print("-" * 40)

        monitoring_interval = 60  # Monitor every minute

        while True:
            try:
                current_time = datetime.now()
                uptime = current_time - self.deployment_start_time if self.deployment_start_time else timedelta(0)

                print(f"\n🕐 {current_time.strftime('%H:%M:%S')} | Uptime: {uptime}")

                # Get system status
                if self.orchestrator:
                    orchestrator_status = self.orchestrator.get_system_status()
                    print(f"🤖 Orchestrator: {orchestrator_status.get('state', 'Unknown')}")
                    print(f"💹 Active positions: {orchestrator_status.get('active_positions', 0)}")
                    print(f"📈 Total trades: {orchestrator_status.get('total_trades', 0)}")

                # Get timer coordinator status
                timer_status = timer_coordinator.get_timer_status()
                print(f"⏰ Timer coordination: {timer_status['enabled_timers']}/{timer_status['total_timers']} timers active")

                # Get emergency stop status
                emergency_status = emergency_coordinator.get_system_status()
                print(f"🚨 Emergency stops: {'Active' if emergency_status.get('active', False) else 'Standby'}")

                # Get performance metrics
                if hasattr(self.orchestrator, 'get_performance_metrics'):
                    metrics = self.orchestrator.get_performance_metrics()
                    if metrics:
                        print(f"📊 Performance: {metrics.get('win_rate', 0):.1%} win rate")
                        print(f"💰 P&L: ${metrics.get('total_pnl', 0):.2f}")

                await asyncio.sleep(monitoring_interval)

            except Exception as e:
                logger.error(f"Monitoring error: {e}")
                await asyncio.sleep(monitoring_interval)

    async def shutdown(self):
        """Graceful shutdown of all systems"""
        print("\n🛑 INITIATING GRACEFUL SHUTDOWN")
        print("-" * 40)

        try:
            # Stop orchestrator
            if self.orchestrator:
                await self.orchestrator.stop_autonomous_trading()
                print("✅ Autonomous trading orchestrator stopped")

            # Stop timer coordinator
            timer_coordinator.stop()
            print("✅ Timer coordinator stopped")

            # Stop emergency coordinator
            await emergency_coordinator.shutdown()
            print("✅ Emergency stop coordinator shutdown")

            print("✅ All systems shutdown gracefully")

        except Exception as e:
            logger.error(f"Shutdown error: {e}")


async def main():
    """Main deployment function"""
    print("🚀" + "="*70 + "🚀")
    print("🚀" + " "*15 + "EPINNOX ULTRA-CONSERVATIVE LIVE DEPLOYMENT" + " "*11 + "🚀")
    print("🚀" + "="*70 + "🚀")
    print()

    # Final confirmation for live trading
    print("⚠️  WARNING: This will start LIVE trading with real money!")
    print(f"💰 Maximum trading capital: ${ULTRA_CONSERVATIVE_CONFIG['max_trading_capital']}")
    print(f"📊 Position size: {ULTRA_CONSERVATIVE_CONFIG['position_size_pct']}%")
    print(f"🛡️ Portfolio risk: {ULTRA_CONSERVATIVE_CONFIG['portfolio_risk_pct']}%")
    print()

    confirmation = input("Type 'DEPLOY LIVE' to confirm ultra-conservative live trading: ")
    if confirmation != 'DEPLOY LIVE':
        print("❌ Live trading deployment cancelled")
        return

    # Create deployment instance
    deployment = UltraConservativeLiveDeployment()

    try:
        # Validate all systems
        print("\n🔍 VALIDATING ALL INTEGRATED SYSTEMS...")
        if not await deployment.validate_all_systems():
            print("❌ System validation failed - deployment aborted")
            return

        # Deploy live trading
        print("\n🚀 DEPLOYING LIVE TRADING SYSTEM...")
        if await deployment.deploy_live_trading():
            print("✅ Live trading deployment successful")
        else:
            print("❌ Live trading deployment failed")

    except KeyboardInterrupt:
        print("\n🛑 Deployment interrupted by user")
    except Exception as e:
        logger.error(f"Deployment error: {e}")
        print(f"❌ Deployment failed: {e}")
    finally:
        await deployment.shutdown()


if __name__ == "__main__":
    asyncio.run(main())
