"""
🗳️ Unified Vote Aggregator System
Converts all LLM responses to numeric weights and resolves conflicts
Eliminates mixed signals like REJECTED + WAIT + LONG → confusion
"""

import logging
from typing import Dict, Any, List, Tuple, Optional
from dataclasses import dataclass
from enum import Enum

logger = logging.getLogger(__name__)

class VoteType(Enum):
    LONG = "LONG"
    SHORT = "SHORT"
    WAIT = "WAIT"
    CLOSE = "CLOSE"

@dataclass
class VoteWeight:
    """Individual vote with weight and confidence"""
    vote_type: VoteType
    weight: float
    confidence: float
    source: str
    reasoning: str = ""

@dataclass
class AggregatedDecision:
    """Final aggregated decision with metadata"""
    decision: VoteType
    confidence: float
    total_weight: float
    vote_breakdown: Dict[str, float]
    consensus_strength: float
    conflicting_signals: bool
    reasoning: str

class UnifiedVoteAggregator:
    """🗳️ Unified voting system that resolves LLM conflicts intelligently"""
    
    def __init__(self):
        # 🎯 Priority weights for different prompt types
        self.prompt_weights = {
            'risk_assessment': 3.0,      # Highest priority - can veto trades
            'entry_timing': 2.5,         # High priority - timing is critical
            'opportunity_scanner': 2.0,   # Medium-high priority - identifies setups
            'emergency_response': 5.0,    # Maximum priority - safety first
            'market_regime': 1.5,         # Medium priority - context
            'position_management': 2.0,   # Medium-high priority - existing positions
            'profit_optimization': 1.0,   # Lower priority - optimization
            'strategy_adaptation': 0.5    # Lowest priority - long-term adjustments
        }
        
        # 🎯 OPTIMIZED: Confidence thresholds for scalping strategies
        self.min_confidence_threshold = 55.0  # Reduced from 60.0 for more aggressive scalping
        self.high_confidence_threshold = 75.0  # Reduced from 80.0 for faster decisions
        self.consensus_threshold = 0.6  # Reduced from 0.7 for quicker consensus
        
    def aggregate_votes(self, llm_results: Dict[str, Any]) -> AggregatedDecision:
        """🗳️ Main aggregation method - converts LLM results to unified decision"""
        try:
            # Extract individual votes from LLM results
            votes = self._extract_votes_from_results(llm_results)
            
            if not votes:
                return self._create_default_decision("No valid votes extracted")
            
            # Calculate weighted vote totals
            vote_totals = self._calculate_weighted_totals(votes)
            
            # Apply priority rules and conflict resolution
            final_decision = self._resolve_conflicts_and_decide(vote_totals, votes)
            
            logger.info(f"🗳️ Vote aggregation: {final_decision.decision.value} ({final_decision.confidence:.1f}%) - {final_decision.reasoning}")
            
            return final_decision
            
        except Exception as e:
            logger.error(f"❌ Error in vote aggregation: {e}")
            return self._create_default_decision(f"Aggregation error: {e}")
    
    def _extract_votes_from_results(self, llm_results: Dict[str, Any]) -> List[VoteWeight]:
        """Extract and normalize votes from LLM results"""
        votes = []
        
        for prompt_type, result in llm_results.items():
            try:
                if not hasattr(result, 'response') or not result.success:
                    continue
                
                response = result.response
                prompt_name = prompt_type.value if hasattr(prompt_type, 'value') else str(prompt_type)
                
                # Extract vote based on prompt type
                vote_weight = self._extract_vote_from_response(prompt_name, response)
                if vote_weight:
                    votes.append(vote_weight)
                    
            except Exception as e:
                logger.warning(f"⚠️ Error extracting vote from {prompt_type}: {e}")
                continue
        
        return votes
    
    def _extract_vote_from_response(self, prompt_name: str, response: Dict[str, Any]) -> Optional[VoteWeight]:
        """Extract vote from individual prompt response"""
        try:
            # 🎯 RISK ASSESSMENT: APPROVED/REJECTED → WAIT/PROCEED
            if 'risk_assessment' in prompt_name.lower():
                approved = response.get('APPROVED', False)
                confidence = response.get('CONFIDENCE', 50)
                
                if approved:
                    # 🎯 OPTIMIZED: Risk approved - neutral vote (doesn't interfere with direction)
                    return VoteWeight(VoteType.WAIT, 0.0, confidence, prompt_name, "Risk approved - neutral")
                else:
                    # 🎯 OPTIMIZED: Risk rejected - moderate WAIT vote (reduced from 3.0 to 2.0)
                    return VoteWeight(VoteType.WAIT, 2.0, confidence, prompt_name, "Risk rejected - moderate WAIT")
            
            # 🎯 ENTRY TIMING: ACTION → Direct vote
            elif 'entry_timing' in prompt_name.lower():
                action = response.get('ACTION', 'WAIT').upper()
                confidence = response.get('CONFIDENCE', 50)
                
                if action in ['LONG', 'BUY']:
                    return VoteWeight(VoteType.LONG, 2.5, confidence, prompt_name, "Entry timing: LONG signal")
                elif action in ['SHORT', 'SELL']:
                    return VoteWeight(VoteType.SHORT, 2.5, confidence, prompt_name, "Entry timing: SHORT signal")
                else:
                    return VoteWeight(VoteType.WAIT, 1.0, confidence, prompt_name, "Entry timing: WAIT signal")
            
            # 🎯 OPPORTUNITY SCANNER: BEST_OPPORTUNITY → Directional vote
            elif 'opportunity_scanner' in prompt_name.lower():
                opportunity = response.get('BEST_OPPORTUNITY', 'NONE')
                decision = response.get('DECISION', 'WAIT').upper()
                confidence = response.get('CONFIDENCE', 50)
                
                if decision in ['LONG', 'BUY'] and opportunity != 'NONE':
                    return VoteWeight(VoteType.LONG, 2.0, confidence, prompt_name, f"Opportunity: {opportunity} → LONG")
                elif decision in ['SHORT', 'SELL'] and opportunity != 'NONE':
                    return VoteWeight(VoteType.SHORT, 2.0, confidence, prompt_name, f"Opportunity: {opportunity} → SHORT")
                else:
                    return VoteWeight(VoteType.WAIT, 0.5, confidence, prompt_name, "No clear opportunity")
            
            # 🎯 EMERGENCY RESPONSE: Highest priority override
            elif 'emergency' in prompt_name.lower():
                action = response.get('ACTION', 'MONITOR').upper()
                confidence = response.get('CONFIDENCE', 100)
                
                if action == 'CLOSE_ALL':
                    return VoteWeight(VoteType.CLOSE, 5.0, confidence, prompt_name, "Emergency: CLOSE ALL")
                elif action != 'MONITOR':
                    return VoteWeight(VoteType.WAIT, 5.0, confidence, prompt_name, f"Emergency: {action}")
                
            # 🎯 GENERIC: Try to extract any ACTION/DECISION
            else:
                action = (response.get('ACTION') or response.get('DECISION') or 'WAIT').upper()
                confidence = response.get('CONFIDENCE', 50)
                weight = self.prompt_weights.get(prompt_name.lower(), 1.0)
                
                if action in ['LONG', 'BUY']:
                    return VoteWeight(VoteType.LONG, weight, confidence, prompt_name, f"Generic: {action}")
                elif action in ['SHORT', 'SELL']:
                    return VoteWeight(VoteType.SHORT, weight, confidence, prompt_name, f"Generic: {action}")
                elif action in ['CLOSE', 'EXIT']:
                    return VoteWeight(VoteType.CLOSE, weight, confidence, prompt_name, f"Generic: {action}")
                else:
                    return VoteWeight(VoteType.WAIT, weight * 0.5, confidence, prompt_name, f"Generic: {action}")
            
            return None
            
        except Exception as e:
            logger.warning(f"⚠️ Error extracting vote from {prompt_name}: {e}")
            return None
    
    def _calculate_weighted_totals(self, votes: List[VoteWeight]) -> Dict[VoteType, float]:
        """Calculate weighted vote totals"""
        totals = {vote_type: 0.0 for vote_type in VoteType}
        
        for vote in votes:
            # Apply confidence weighting (low confidence votes get reduced weight)
            confidence_multiplier = max(0.1, vote.confidence / 100.0)
            weighted_vote = vote.weight * confidence_multiplier
            totals[vote.vote_type] += weighted_vote
            
        return totals
    
    def _resolve_conflicts_and_decide(self, vote_totals: Dict[VoteType, float], votes: List[VoteWeight]) -> AggregatedDecision:
        """Apply conflict resolution rules and make final decision"""
        
        # Find the winning vote
        max_vote_type = max(vote_totals, key=vote_totals.get)
        max_weight = vote_totals[max_vote_type]
        total_weight = sum(vote_totals.values())
        
        # Calculate consensus strength
        consensus_strength = max_weight / total_weight if total_weight > 0 else 0
        
        # Check for conflicting signals
        conflicting_signals = self._detect_conflicts(vote_totals)
        
        # Apply decision rules
        if conflicting_signals and consensus_strength < self.consensus_threshold:
            # High conflict, low consensus → WAIT
            decision = VoteType.WAIT
            confidence = 50.0
            reasoning = f"High conflict detected (consensus: {consensus_strength:.1%}) → forcing WAIT"
            
        elif max_weight < 1.0:
            # Very low total weight → WAIT
            decision = VoteType.WAIT
            confidence = 40.0
            reasoning = f"Insufficient vote weight ({max_weight:.1f}) → WAIT"
            
        elif vote_totals[VoteType.WAIT] > 1.5:
            # Strong WAIT signal → WAIT
            decision = VoteType.WAIT
            confidence = min(80.0, vote_totals[VoteType.WAIT] * 20)
            reasoning = f"Strong WAIT signal ({vote_totals[VoteType.WAIT]:.1f}) → WAIT"
            
        else:
            # Normal decision
            decision = max_vote_type
            confidence = min(95.0, max(50.0, consensus_strength * 100))
            reasoning = f"Clear {decision.value} signal (weight: {max_weight:.1f}, consensus: {consensus_strength:.1%})"
        
        return AggregatedDecision(
            decision=decision,
            confidence=confidence,
            total_weight=total_weight,
            vote_breakdown={vt.value: weight for vt, weight in vote_totals.items()},
            consensus_strength=consensus_strength,
            conflicting_signals=conflicting_signals,
            reasoning=reasoning
        )
    
    def _detect_conflicts(self, vote_totals: Dict[VoteType, float]) -> bool:
        """Detect conflicting signals (e.g., LONG vs SHORT both high)"""
        long_weight = vote_totals[VoteType.LONG]
        short_weight = vote_totals[VoteType.SHORT]
        
        # Conflict if both LONG and SHORT have significant weight
        if long_weight > 1.0 and short_weight > 1.0:
            return True
            
        # Conflict if any two non-WAIT votes are close
        non_wait_votes = [(vt, weight) for vt, weight in vote_totals.items() if vt != VoteType.WAIT and weight > 0.5]
        if len(non_wait_votes) >= 2:
            sorted_votes = sorted(non_wait_votes, key=lambda x: x[1], reverse=True)
            if len(sorted_votes) >= 2 and abs(sorted_votes[0][1] - sorted_votes[1][1]) < 0.5:
                return True
        
        return False
    
    def _create_default_decision(self, reason: str) -> AggregatedDecision:
        """Create default WAIT decision"""
        return AggregatedDecision(
            decision=VoteType.WAIT,
            confidence=50.0,
            total_weight=0.0,
            vote_breakdown={vt.value: 0.0 for vt in VoteType},
            consensus_strength=0.0,
            conflicting_signals=False,
            reasoning=reason
        )
