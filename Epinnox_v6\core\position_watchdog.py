"""
Position Watchdog System for Epinnox Trading System
Provides real-time position monitoring, health scoring, and orphaned position detection
"""

import asyncio
import logging
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, field
from enum import Enum
import numpy as np

logger = logging.getLogger(__name__)

class PositionHealth(Enum):
    HEALTHY = "healthy"
    WARNING = "warning"
    CRITICAL = "critical"
    ORPHANED = "orphaned"
    STALE = "stale"

class AlertLevel(Enum):
    INFO = "info"
    WARNING = "warning"
    CRITICAL = "critical"
    EMERGENCY = "emergency"

@dataclass
class PositionAlert:
    """Position alert data structure"""
    position_id: str
    symbol: str
    alert_type: str
    level: AlertLevel
    message: str
    timestamp: datetime
    data: Dict[str, Any] = field(default_factory=dict)

@dataclass
class PositionHealthScore:
    """Position health scoring data"""
    position_id: str
    overall_score: float  # 0-100
    risk_score: float     # 0-100 (higher = more risky)
    performance_score: float  # 0-100
    time_score: float     # 0-100
    health_status: PositionHealth
    alerts: List[PositionAlert] = field(default_factory=list)
    last_update: datetime = field(default_factory=datetime.now)

@dataclass
class WatchdogMetrics:
    """Watchdog system metrics"""
    total_positions_monitored: int = 0
    orphaned_positions_detected: int = 0
    critical_alerts_generated: int = 0
    positions_recovered: int = 0
    avg_health_score: float = 0.0
    monitoring_uptime: float = 0.0
    last_scan_duration: float = 0.0

class PositionWatchdog:
    """
    Position Watchdog System that provides:
    1. Real-time position health monitoring
    2. Orphaned position detection and recovery
    3. Position health scoring and alerts
    4. Automated position recovery mechanisms
    5. Performance tracking and analytics
    """
    
    def __init__(self, config: Dict[str, Any] = None):
        """Initialize the position watchdog"""
        self.config = config or {}
        
        # Monitoring configuration
        self.scan_interval = self.config.get('scan_interval', 10)  # 10 seconds
        self.health_check_interval = self.config.get('health_check_interval', 30)  # 30 seconds
        self.orphan_detection_threshold = self.config.get('orphan_detection_threshold', 300)  # 5 minutes
        self.stale_position_threshold = self.config.get('stale_position_threshold', 3600)  # 1 hour
        
        # Health scoring weights
        self.scoring_weights = {
            'risk_weight': self.config.get('risk_weight', 0.4),
            'performance_weight': self.config.get('performance_weight', 0.3),
            'time_weight': self.config.get('time_weight', 0.3)
        }
        
        # State tracking
        self.position_health_scores: Dict[str, PositionHealthScore] = {}
        self.active_alerts: Dict[str, List[PositionAlert]] = {}
        self.metrics = WatchdogMetrics()
        self.start_time = None
        
        # Monitoring control
        self.monitoring_enabled = False
        self.monitoring_task = None
        self.health_check_task = None
        
        # External interfaces
        self.position_manager = None
        self.execution_engine = None
        self.market_data_provider = None
        self.alert_handler = None
        
        logger.info("[WATCHDOG] Position Watchdog initialized")
    
    async def initialize(self, position_manager, execution_engine, market_data_provider, alert_handler=None):
        """Initialize watchdog with required components"""
        self.position_manager = position_manager
        self.execution_engine = execution_engine
        self.market_data_provider = market_data_provider
        self.alert_handler = alert_handler
        
        # Start monitoring
        await self.start_monitoring()
        
        logger.info("[WATCHDOG] Position Watchdog ready")
        return True
    
    async def start_monitoring(self):
        """Start the watchdog monitoring loops"""
        if self.monitoring_enabled:
            return
        
        self.monitoring_enabled = True
        self.start_time = datetime.now()
        
        # Start monitoring tasks
        self.monitoring_task = asyncio.create_task(self._monitoring_loop())
        self.health_check_task = asyncio.create_task(self._health_check_loop())
        
        logger.info(f"[WATCHDOG] Monitoring started (scan: {self.scan_interval}s, health: {self.health_check_interval}s)")
    
    async def stop_monitoring(self):
        """Stop the watchdog monitoring"""
        self.monitoring_enabled = False
        
        # Cancel tasks
        if self.monitoring_task:
            self.monitoring_task.cancel()
            try:
                await self.monitoring_task
            except asyncio.CancelledError:
                pass
        
        if self.health_check_task:
            self.health_check_task.cancel()
            try:
                await self.health_check_task
            except asyncio.CancelledError:
                pass
        
        logger.info("[WATCHDOG] Monitoring stopped")
    
    async def _monitoring_loop(self):
        """Main monitoring loop"""
        while self.monitoring_enabled:
            try:
                scan_start = time.time()
                
                # Scan all positions
                await self._scan_positions()
                
                # Update metrics
                scan_duration = time.time() - scan_start
                self.metrics.last_scan_duration = scan_duration
                
                # Update uptime
                if self.start_time:
                    uptime = (datetime.now() - self.start_time).total_seconds()
                    self.metrics.monitoring_uptime = uptime
                
                await asyncio.sleep(self.scan_interval)
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"[WATCHDOG] Monitoring loop error: {e}")
                await asyncio.sleep(self.scan_interval)
    
    async def _health_check_loop(self):
        """Health check and recovery loop"""
        while self.monitoring_enabled:
            try:
                await self._perform_health_checks()
                await self._check_for_orphaned_positions()
                await self._process_alerts()
                
                await asyncio.sleep(self.health_check_interval)
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"[WATCHDOG] Health check loop error: {e}")
                await asyncio.sleep(self.health_check_interval)
    
    async def _scan_positions(self):
        """Scan all positions and update health scores"""
        if not self.position_manager:
            return
        
        try:
            # Get all positions from position manager
            positions = self.position_manager.get_all_positions()
            
            self.metrics.total_positions_monitored = len(positions)
            
            # Calculate health scores for each position
            health_scores = []
            for position in positions:
                health_score = await self._calculate_position_health(position)
                if health_score:
                    self.position_health_scores[position.id] = health_score
                    health_scores.append(health_score.overall_score)
            
            # Update average health score
            if health_scores:
                self.metrics.avg_health_score = np.mean(health_scores)
            
        except Exception as e:
            logger.error(f"[WATCHDOG] Error scanning positions: {e}")
    
    async def _calculate_position_health(self, position) -> Optional[PositionHealthScore]:
        """Calculate comprehensive health score for a position"""
        try:
            # Calculate individual scores
            risk_score = self._calculate_risk_score(position)
            performance_score = self._calculate_performance_score(position)
            time_score = self._calculate_time_score(position)
            
            # Calculate weighted overall score
            overall_score = (
                risk_score * self.scoring_weights['risk_weight'] +
                performance_score * self.scoring_weights['performance_weight'] +
                time_score * self.scoring_weights['time_weight']
            )
            
            # Determine health status
            health_status = self._determine_health_status(overall_score, position)
            
            # Generate alerts if needed
            alerts = self._generate_position_alerts(position, overall_score, health_status)
            
            return PositionHealthScore(
                position_id=position.id,
                overall_score=overall_score,
                risk_score=risk_score,
                performance_score=performance_score,
                time_score=time_score,
                health_status=health_status,
                alerts=alerts
            )
            
        except Exception as e:
            logger.error(f"[WATCHDOG] Error calculating health for position {position.id}: {e}")
            return None
    
    def _calculate_risk_score(self, position) -> float:
        """Calculate risk score (0-100, higher = more risky)"""
        try:
            risk_factors = []
            
            # P&L risk factor
            if hasattr(position, 'unrealized_pnl_pct'):
                pnl_pct = position.unrealized_pnl_pct
                if pnl_pct < -0.05:  # More than 5% loss
                    risk_factors.append(80)
                elif pnl_pct < -0.02:  # More than 2% loss
                    risk_factors.append(60)
                elif pnl_pct < 0:  # Any loss
                    risk_factors.append(40)
                else:  # Profitable
                    risk_factors.append(20)
            
            # Drawdown risk factor
            if hasattr(position, 'max_drawdown') and position.max_drawdown > 0:
                if position.max_drawdown > position.size * 0.1:  # 10% drawdown
                    risk_factors.append(90)
                elif position.max_drawdown > position.size * 0.05:  # 5% drawdown
                    risk_factors.append(70)
                else:
                    risk_factors.append(30)
            
            # Time-based risk (positions held too long)
            if hasattr(position, 'entry_time'):
                hold_time = (datetime.now() - position.entry_time).total_seconds() / 3600  # hours
                if hold_time > 24:  # More than 24 hours
                    risk_factors.append(70)
                elif hold_time > 12:  # More than 12 hours
                    risk_factors.append(50)
                else:
                    risk_factors.append(20)
            
            # Stop loss distance risk
            if hasattr(position, 'levels') and hasattr(position, 'current_price'):
                if position.levels.stop_loss and position.current_price:
                    sl_distance = abs(position.current_price - position.levels.stop_loss) / position.current_price
                    if sl_distance > 0.1:  # Stop loss more than 10% away
                        risk_factors.append(80)
                    elif sl_distance > 0.05:  # Stop loss more than 5% away
                        risk_factors.append(60)
                    else:
                        risk_factors.append(30)
            
            return np.mean(risk_factors) if risk_factors else 50.0
            
        except Exception as e:
            logger.error(f"[WATCHDOG] Error calculating risk score: {e}")
            return 50.0
    
    def _calculate_performance_score(self, position) -> float:
        """Calculate performance score (0-100, higher = better performance)"""
        try:
            performance_factors = []
            
            # P&L performance
            if hasattr(position, 'unrealized_pnl_pct'):
                pnl_pct = position.unrealized_pnl_pct
                if pnl_pct > 0.05:  # More than 5% profit
                    performance_factors.append(90)
                elif pnl_pct > 0.02:  # More than 2% profit
                    performance_factors.append(75)
                elif pnl_pct > 0:  # Any profit
                    performance_factors.append(60)
                elif pnl_pct > -0.02:  # Small loss
                    performance_factors.append(40)
                else:  # Significant loss
                    performance_factors.append(20)
            
            # Max profit achievement
            if hasattr(position, 'max_profit') and hasattr(position, 'unrealized_pnl'):
                if position.max_profit > 0:
                    profit_retention = position.unrealized_pnl / position.max_profit
                    if profit_retention > 0.8:  # Retained 80% of max profit
                        performance_factors.append(85)
                    elif profit_retention > 0.5:  # Retained 50% of max profit
                        performance_factors.append(65)
                    else:
                        performance_factors.append(35)
            
            # Trailing stop effectiveness
            if hasattr(position, 'levels') and position.levels.trailing_stop:
                performance_factors.append(70)  # Bonus for having trailing stop
            
            return np.mean(performance_factors) if performance_factors else 50.0
            
        except Exception as e:
            logger.error(f"[WATCHDOG] Error calculating performance score: {e}")
            return 50.0
    
    def _calculate_time_score(self, position) -> float:
        """Calculate time-based score (0-100, considers position age and timing)"""
        try:
            if not hasattr(position, 'entry_time'):
                return 50.0
            
            hold_time = (datetime.now() - position.entry_time).total_seconds() / 3600  # hours
            
            # Optimal hold time scoring
            if hold_time < 0.5:  # Less than 30 minutes
                return 60  # Too early to judge
            elif hold_time < 2:  # 30 minutes to 2 hours
                return 85  # Good timing window
            elif hold_time < 6:  # 2 to 6 hours
                return 75  # Acceptable timing
            elif hold_time < 12:  # 6 to 12 hours
                return 60  # Getting long
            elif hold_time < 24:  # 12 to 24 hours
                return 40  # Too long
            else:  # More than 24 hours
                return 20  # Way too long
            
        except Exception as e:
            logger.error(f"[WATCHDOG] Error calculating time score: {e}")
            return 50.0

    def _determine_health_status(self, overall_score: float, position) -> PositionHealth:
        """Determine health status based on overall score and position state"""
        try:
            # Check for orphaned status first
            if hasattr(position, 'last_update'):
                time_since_update = (datetime.now() - position.last_update).total_seconds()
                if time_since_update > self.orphan_detection_threshold:
                    return PositionHealth.ORPHANED
                elif time_since_update > self.stale_position_threshold:
                    return PositionHealth.STALE

            # Check overall score
            if overall_score >= 80:
                return PositionHealth.HEALTHY
            elif overall_score >= 60:
                return PositionHealth.WARNING
            else:
                return PositionHealth.CRITICAL

        except Exception as e:
            logger.error(f"[WATCHDOG] Error determining health status: {e}")
            return PositionHealth.WARNING

    async def _perform_health_checks(self):
        """Perform comprehensive health checks"""
        try:
            critical_positions = []
            warning_positions = []

            for position_id, health_score in self.position_health_scores.items():
                if health_score.health_status == PositionHealth.CRITICAL:
                    critical_positions.append(position_id)
                elif health_score.health_status == PositionHealth.WARNING:
                    warning_positions.append(position_id)

            if critical_positions:
                logger.warning(f"[WATCHDOG] {len(critical_positions)} positions in critical state")

            if warning_positions:
                logger.info(f"[WATCHDOG] {len(warning_positions)} positions in warning state")

        except Exception as e:
            logger.error(f"[WATCHDOG] Error in health checks: {e}")

    async def _check_for_orphaned_positions(self):
        """Check for and attempt to recover orphaned positions"""
        try:
            orphaned_positions = []

            for position_id, health_score in self.position_health_scores.items():
                if health_score.health_status == PositionHealth.ORPHANED:
                    orphaned_positions.append(position_id)

            if orphaned_positions:
                logger.warning(f"[WATCHDOG] Detected {len(orphaned_positions)} orphaned positions")
                self.metrics.orphaned_positions_detected += len(orphaned_positions)

                # Attempt recovery
                for position_id in orphaned_positions:
                    await self._attempt_position_recovery(position_id)

        except Exception as e:
            logger.error(f"[WATCHDOG] Error checking for orphaned positions: {e}")

    def _generate_position_alerts(self, position, overall_score: float, health_status: PositionHealth) -> List[PositionAlert]:
        """Generate alerts based on position analysis"""
        alerts = []

        try:
            # Health status alerts
            if health_status == PositionHealth.CRITICAL:
                alerts.append(PositionAlert(
                    position_id=position.id,
                    symbol=position.symbol,
                    alert_type="health_critical",
                    level=AlertLevel.CRITICAL,
                    message=f"Position health critical (score: {overall_score:.1f})",
                    timestamp=datetime.now(),
                    data={'health_score': overall_score}
                ))
            elif health_status == PositionHealth.ORPHANED:
                alerts.append(PositionAlert(
                    position_id=position.id,
                    symbol=position.symbol,
                    alert_type="position_orphaned",
                    level=AlertLevel.EMERGENCY,
                    message="Position appears to be orphaned - no recent updates",
                    timestamp=datetime.now()
                ))

            # P&L alerts
            if hasattr(position, 'unrealized_pnl_pct'):
                if position.unrealized_pnl_pct < -0.05:  # More than 5% loss
                    alerts.append(PositionAlert(
                        position_id=position.id,
                        symbol=position.symbol,
                        alert_type="large_loss",
                        level=AlertLevel.CRITICAL,
                        message=f"Large unrealized loss: {position.unrealized_pnl_pct:.1%}",
                        timestamp=datetime.now(),
                        data={'pnl_pct': position.unrealized_pnl_pct}
                    ))

            return alerts

        except Exception as e:
            logger.error(f"[WATCHDOG] Error generating alerts: {e}")
            return []

    async def _process_alerts(self):
        """Process and handle generated alerts"""
        try:
            critical_alerts = []

            for health_score in self.position_health_scores.values():
                for alert in health_score.alerts:
                    if alert.level in [AlertLevel.CRITICAL, AlertLevel.EMERGENCY]:
                        critical_alerts.append(alert)

                        # Store in active alerts
                        if alert.position_id not in self.active_alerts:
                            self.active_alerts[alert.position_id] = []
                        self.active_alerts[alert.position_id].append(alert)

            if critical_alerts:
                self.metrics.critical_alerts_generated += len(critical_alerts)
                logger.warning(f"[WATCHDOG] Generated {len(critical_alerts)} critical alerts")

        except Exception as e:
            logger.error(f"[WATCHDOG] Error processing alerts: {e}")

    async def _attempt_position_recovery(self, position_id: str):
        """Attempt to recover an orphaned position"""
        try:
            logger.info(f"[WATCHDOG] Attempting recovery for orphaned position {position_id}")

            if self.position_manager:
                position = self.position_manager.get_position(position_id)
                if position:
                    # Try to update position with current market data
                    if self.market_data_provider:
                        current_price = await self._get_current_price(position.symbol)
                        if current_price:
                            # Update position manually
                            position.current_price = current_price
                            position.last_update = datetime.now()

                            logger.info(f"[WATCHDOG] Successfully recovered position {position_id}")
                            self.metrics.positions_recovered += 1
                            return True

            logger.warning(f"[WATCHDOG] Failed to recover position {position_id}")
            return False

        except Exception as e:
            logger.error(f"[WATCHDOG] Error recovering position {position_id}: {e}")
            return False

    async def _get_current_price(self, symbol: str) -> Optional[float]:
        """Get current market price for symbol"""
        try:
            if self.market_data_provider:
                ticker = await self.market_data_provider.get_ticker(symbol)
                return ticker.get('last') if ticker else None
            return None
        except Exception as e:
            logger.error(f"[WATCHDOG] Error getting price for {symbol}: {e}")
            return None

    def get_position_health(self, position_id: str) -> Optional[PositionHealthScore]:
        """Get health score for a specific position"""
        return self.position_health_scores.get(position_id)

    def get_critical_positions(self) -> List[str]:
        """Get list of positions in critical state"""
        return [
            pos_id for pos_id, health in self.position_health_scores.items()
            if health.health_status == PositionHealth.CRITICAL
        ]

    def get_status(self) -> Dict[str, Any]:
        """Get current watchdog status"""
        return {
            'monitoring_enabled': self.monitoring_enabled,
            'uptime_seconds': self.metrics.monitoring_uptime,
            'total_positions': self.metrics.total_positions_monitored,
            'avg_health_score': self.metrics.avg_health_score,
            'critical_positions': len(self.get_critical_positions()),
            'positions_recovered': self.metrics.positions_recovered,
            'last_scan_duration': self.metrics.last_scan_duration
        }

    async def force_health_check(self):
        """Force an immediate health check of all positions"""
        logger.info("[WATCHDOG] Forcing immediate health check")
        await self._scan_positions()
        await self._perform_health_checks()
        await self._check_for_orphaned_positions()
        await self._process_alerts()

    async def shutdown(self):
        """Shutdown the watchdog system"""
        await self.stop_monitoring()
        self.position_health_scores.clear()
        self.active_alerts.clear()
        logger.info("[WATCHDOG] Position Watchdog shutdown complete")
