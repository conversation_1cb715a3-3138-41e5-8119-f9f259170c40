2025-07-13 11:31:25,981 - __main__ - INFO - [PHASE2_TEST] Loaded configuration from config/phase2_autonomous_config.yaml
2025-07-13 11:31:25,981 - __main__ - INFO - [PHASE2_TEST] Phase 2 Integration Tester initialized
2025-07-13 11:31:25,982 - __main__ - INFO - [PHASE2_TEST] Starting comprehensive Phase 2 integration tests
2025-07-13 11:31:25,982 - __main__ - INFO - [PHASE2_TEST] Running Test 1: Unified LLM Integration...
2025-07-13 11:31:28,019 - core.unified_llm_manager - INFO - [UNIFIED_LLM] Unified LLM Manager initialized
2025-07-13 11:31:28,020 - core.unified_llm_manager - INFO - [UNIFIED_LLM] Initializing LLM providers...
2025-07-13 11:31:28,034 - config.autonomous_config - INFO - Configuration loaded from configs/autonomous_trading.yaml
2025-07-13 11:31:28,037 - llama.runner - INFO - Loaded OpenAI API key from models_config.yaml
2025-07-13 11:31:28,038 - llama.runner - INFO - Using model path: C:\Users\<USER>\.lmstudio\models\lmstudio-community\Phi-3.1-mini-128k-instruct-GGUF\Phi-3.1-mini-128k-instruct-Q4_K_M.gguf
2025-07-13 11:31:28,038 - llama.runner - INFO - Using guard path: C:\Users\<USER>\Documents\dev\Epinnox_v6\models\Llama-Guard-3-8B.gguf
2025-07-13 11:31:28,038 - llama.runner - INFO - Using binary path: C:\Users\<USER>\Documents\dev\llama.cpp\main
2025-07-13 11:31:28,043 - core.unified_llm_manager - INFO - [UNIFIED_LLM] ChatGPT provider initialized
2025-07-13 11:31:28,047 - llama.lmstudio_runner - INFO - Loaded LMStudio configuration from models_config.yaml
2025-07-13 11:31:30,122 - llama.lmstudio_runner - INFO - Discovered 8 models: ['phi-3.1-mini-128k-instruct', 'openthinker3-7b', 'mistral-7b-claude-chat', 'fine_tuned_qwen1.7b', 'tsfc-model-0.0.1', 'codestral-22b-v0.1', 'deepseek-r1-distill-qwen-7b', 'text-embedding-nomic-embed-text-v1.5']
2025-07-13 11:31:30,122 - llama.lmstudio_runner - INFO - Selected preferred model from config: phi-3.1-mini-128k-instruct
2025-07-13 11:31:30,122 - llama.lmstudio_runner - INFO - Initialized LMStudio with model: phi-3.1-mini-128k-instruct
2025-07-13 11:31:32,198 - core.unified_llm_manager - INFO - [UNIFIED_LLM] LMStudio provider initialized
2025-07-13 11:31:32,203 - core.unified_llm_manager - WARNING - [UNIFIED_LLM] Failed to initialize Transformers: __init__() missing 1 required positional argument: 'model_path'
2025-07-13 11:31:32,204 - core.unified_llm_manager - WARNING - [UNIFIED_LLM] Failed to initialize Llama: __init__() missing 1 required positional argument: 'bin_path'
2025-07-13 11:31:32,205 - llama.mock_runner - INFO - Initialized MockRunner with personality: {'caution': 0.627467914808283, 'optimism': 0.545894080649598, 'consistency': 0.8913989552915313, 'detail_level': 0.6019743341886148, 'confidence_bias': 1.4026243035728756}
2025-07-13 11:31:32,205 - core.unified_llm_manager - INFO - [UNIFIED_LLM] Mock provider initialized
2025-07-13 11:31:32,205 - core.unified_llm_manager - INFO - [UNIFIED_LLM] Selected provider: mock (score: 109.90)
2025-07-13 11:31:32,206 - core.unified_llm_manager - INFO - [UNIFIED_LLM] Health monitoring started
2025-07-13 11:31:32,206 - core.unified_llm_manager - INFO - [UNIFIED_LLM] Initialized with 3 providers
2025-07-13 11:31:32,206 - core.unified_llm_manager - INFO - [UNIFIED_LLM] Current provider: LLMProvider.MOCK
2025-07-13 11:31:32,206 - core.standardized_prompt_handler - INFO - [PROMPT_HANDLER] Standardized prompt handler initialized
2025-07-13 11:31:32,206 - llama.mock_runner - INFO - Running mock inference
2025-07-13 11:31:32,206 - core.unified_llm_manager - ERROR - [UNIFIED_LLM] Failed to parse response: 'LLMResponse' object has no attribute 'split'
2025-07-13 11:31:32,207 - core.unified_llm_manager - WARNING - [UNIFIED_LLM] mock response below confidence threshold
2025-07-13 11:31:32,207 - core.unified_llm_manager - WARNING - [UNIFIED_LLM] Failing over to chatgpt
2025-07-13 11:31:32,207 - llama.chatgpt_runner - ERROR - ChatGPT inference failed
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\dev\Epinnox_v6\llama\chatgpt_runner.py", line 34, in run_inference
    raise ImportError("OpenAI package not available or client not initialized")
ImportError: OpenAI package not available or client not initialized
2025-07-13 11:31:32,207 - core.unified_llm_manager - ERROR - [UNIFIED_LLM] Provider call failed: OpenAI package not available or client not initialized
2025-07-13 11:31:32,208 - core.unified_llm_manager - WARNING - [UNIFIED_LLM] Failing over to lmstudio
2025-07-13 11:31:37,063 - __main__ - INFO - [PHASE2_TEST] LLM response received from lmstudio
2025-07-13 11:31:37,063 - core.unified_llm_manager - INFO - [UNIFIED_LLM] Unified LLM Manager initialized
2025-07-13 11:31:37,063 - core.standardized_prompt_handler - INFO - [PROMPT_HANDLER] Standardized prompt handler initialized
2025-07-13 11:31:37,063 - core.autonomous_llm_integration - INFO - [AUTONOMOUS_LLM] Autonomous LLM Integration initialized
2025-07-13 11:31:37,063 - core.autonomous_llm_integration - INFO - [AUTONOMOUS_LLM] Initializing autonomous LLM integration...
2025-07-13 11:31:37,063 - core.unified_llm_manager - INFO - [UNIFIED_LLM] Initializing LLM providers...
2025-07-13 11:31:37,067 - core.unified_llm_manager - INFO - [UNIFIED_LLM] ChatGPT provider initialized
2025-07-13 11:31:37,071 - llama.lmstudio_runner - INFO - Loaded LMStudio configuration from models_config.yaml
2025-07-13 11:31:39,155 - llama.lmstudio_runner - INFO - Discovered 8 models: ['phi-3.1-mini-128k-instruct', 'openthinker3-7b', 'mistral-7b-claude-chat', 'fine_tuned_qwen1.7b', 'tsfc-model-0.0.1', 'codestral-22b-v0.1', 'deepseek-r1-distill-qwen-7b', 'text-embedding-nomic-embed-text-v1.5']
2025-07-13 11:31:39,156 - llama.lmstudio_runner - INFO - Selected preferred model from config: phi-3.1-mini-128k-instruct
2025-07-13 11:31:39,156 - llama.lmstudio_runner - INFO - Initialized LMStudio with model: phi-3.1-mini-128k-instruct
2025-07-13 11:31:41,201 - core.unified_llm_manager - INFO - [UNIFIED_LLM] LMStudio provider initialized
2025-07-13 11:31:41,201 - core.unified_llm_manager - WARNING - [UNIFIED_LLM] Failed to initialize Transformers: __init__() missing 1 required positional argument: 'model_path'
2025-07-13 11:31:41,201 - core.unified_llm_manager - WARNING - [UNIFIED_LLM] Failed to initialize Llama: __init__() missing 1 required positional argument: 'bin_path'
2025-07-13 11:31:41,201 - llama.mock_runner - INFO - Initialized MockRunner with personality: {'caution': 0.6406655336279142, 'optimism': 0.5928718335450451, 'consistency': 0.6383619694280274, 'detail_level': 0.7675754555746895, 'confidence_bias': 0.3605684729079073}
2025-07-13 11:31:41,201 - core.unified_llm_manager - INFO - [UNIFIED_LLM] Mock provider initialized
2025-07-13 11:31:41,201 - core.unified_llm_manager - INFO - [UNIFIED_LLM] Selected provider: mock (score: 109.90)
2025-07-13 11:31:41,201 - core.unified_llm_manager - INFO - [UNIFIED_LLM] Health monitoring started
2025-07-13 11:31:41,201 - core.unified_llm_manager - INFO - [UNIFIED_LLM] Initialized with 3 providers
2025-07-13 11:31:41,203 - core.unified_llm_manager - INFO - [UNIFIED_LLM] Current provider: LLMProvider.MOCK
2025-07-13 11:31:41,203 - core.autonomous_llm_integration - INFO - [AUTONOMOUS_LLM] Autonomous LLM integration initialized successfully
2025-07-13 11:31:41,203 - core.unified_llm_manager - INFO - [UNIFIED_LLM] Unified LLM Manager shutdown
2025-07-13 11:31:41,203 - __main__ - INFO - [PHASE2_TEST] Unified LLM integration test completed
2025-07-13 11:31:41,204 - __main__ - INFO - [PHASE2_TEST] Running Test 2: Autonomous Position Manager...
2025-07-13 11:31:41,208 - core.autonomous_position_manager - INFO - [POSITION_MANAGER] Autonomous Position Manager initialized
2025-07-13 11:31:41,209 - core.autonomous_position_manager - INFO - [POSITION_MANAGER] Position monitoring started (interval: 5s)
2025-07-13 11:31:41,209 - core.autonomous_position_manager - INFO - [POSITION_MANAGER] Autonomous Position Manager ready
2025-07-13 11:31:41,209 - core.autonomous_position_manager - INFO - [POSITION_MANAGER] Added position pos_1_1752424301: LONG 0.001 BTC/USDT:USDT @ 50000.0000
2025-07-13 11:31:41,209 - core.autonomous_position_manager - INFO - [POSITION_MANAGER] SL: 49000.0000, TP: 52000.0000
2025-07-13 11:31:41,209 - core.autonomous_position_manager - INFO - [POSITION_MANAGER] Updated SL for pos_1_1752424301: 49000.0000
2025-07-13 11:31:41,209 - core.autonomous_position_manager - INFO - [POSITION_MANAGER] Updated TP for pos_1_1752424301: 52000.0000
2025-07-13 11:31:41,209 - core.autonomous_position_manager - INFO - [POSITION_MANAGER] Exiting position pos_1_1752424301 - Reason: manual
2025-07-13 11:31:41,209 - core.autonomous_position_manager - INFO - [POSITION_MANAGER] Successfully exited position pos_1_1752424301
2025-07-13 11:31:41,210 - core.autonomous_position_manager - INFO - [POSITION_MANAGER] Position monitoring stopped
2025-07-13 11:31:41,210 - core.autonomous_position_manager - INFO - [POSITION_MANAGER] Autonomous Position Manager shutdown complete
2025-07-13 11:31:41,210 - __main__ - INFO - [PHASE2_TEST] Autonomous position manager test completed
2025-07-13 11:31:41,211 - __main__ - INFO - [PHASE2_TEST] Running Test 3: Position Watchdog System...
2025-07-13 11:31:41,215 - core.position_watchdog - INFO - [WATCHDOG] Position Watchdog initialized
2025-07-13 11:31:41,215 - core.position_watchdog - INFO - [WATCHDOG] Monitoring started (scan: 10s, health: 30s)
2025-07-13 11:31:41,215 - core.position_watchdog - INFO - [WATCHDOG] Position Watchdog ready
2025-07-13 11:31:41,217 - core.position_watchdog - ERROR - [WATCHDOG] Error calculating health for position pos_1_1752424301: 'PositionWatchdog' object has no attribute '_generate_position_alerts'
2025-07-13 11:31:41,217 - core.position_watchdog - ERROR - [WATCHDOG] Health check loop error: 'PositionWatchdog' object has no attribute '_process_alerts'
2025-07-13 11:31:43,220 - __main__ - ERROR - [PHASE2_TEST] Position watchdog test error: 'PositionWatchdog' object has no attribute 'force_health_check'
2025-07-13 11:31:43,222 - __main__ - INFO - [PHASE2_TEST] Running Test 4: Error Recovery System...
2025-07-13 11:31:43,222 - core.error_recovery_system - INFO - [ERROR_RECOVERY] Error Recovery System initialized
2025-07-13 11:31:43,222 - core.error_recovery_system - INFO - [ERROR_RECOVERY] Health monitoring started
2025-07-13 11:31:43,222 - core.error_recovery_system - INFO - [ERROR_RECOVERY] Error Recovery System ready
2025-07-13 11:31:43,228 - core.error_recovery_system - WARNING - [ERROR_RECOVERY] Retry 1/3 for test_function_with_retries in 0.90s: Test failure for retry testing
2025-07-13 11:31:44,144 - core.error_recovery_system - INFO - [ERROR_RECOVERY] Error Recovery System shutdown complete
2025-07-13 11:31:44,144 - __main__ - INFO - [PHASE2_TEST] Error recovery system test completed
2025-07-13 11:31:44,146 - __main__ - INFO - [PHASE2_TEST] Running Test 5: Component Integration...
2025-07-13 11:31:44,151 - core.autonomous_trading_orchestrator - INFO - Autonomous Trading Orchestrator initialized in paper mode
2025-07-13 11:31:44,151 - core.autonomous_trading_orchestrator - INFO - Initializing autonomous trading system...
2025-07-13 11:31:44,151 - core.error_recovery_system - INFO - [ERROR_RECOVERY] Error Recovery System initialized
2025-07-13 11:31:44,151 - core.error_recovery_system - INFO - [ERROR_RECOVERY] Health monitoring started
2025-07-13 11:31:44,151 - core.error_recovery_system - INFO - [ERROR_RECOVERY] Error Recovery System ready
2025-07-13 11:31:44,175 - core.websocket_manager - INFO - WebSocket manager initialized for htx_market: wss://api.huobi.pro/ws
2025-07-13 11:31:44,175 - core.websocket_manager - INFO - WebSocket manager initialized for htx_futures: wss://api.hbdm.com/swap-ws
2025-07-13 11:31:44,176 - core.websocket_manager - INFO - WebSocket manager initialized for htx_backup: wss://api-aws.huobi.pro/ws
2025-07-13 11:31:44,176 - data.market_data_manager - INFO - Initialized 3 WebSocket endpoints
2025-07-13 11:31:44,176 - data.market_data_manager - INFO - Market data manager initialized for htx
2025-07-13 11:31:44,176 - data.market_data_manager - INFO - Starting market data manager...
2025-07-13 11:31:44,176 - data.market_data_manager - INFO - Started WebSocket connection: market
2025-07-13 11:31:44,213 - data.market_data_manager - INFO - Started WebSocket connection: futures
2025-07-13 11:31:44,214 - data.market_data_manager - INFO - Started WebSocket connection: backup
2025-07-13 11:31:44,214 - core.websocket_manager - INFO - htx_market: Connecting to wss://api.huobi.pro/ws (attempt 1)
2025-07-13 11:31:44,240 - core.websocket_manager - INFO - htx_futures: Connecting to wss://api.hbdm.com/swap-ws (attempt 1)
2025-07-13 11:31:44,245 - core.websocket_manager - INFO - htx_backup: Connecting to wss://api-aws.huobi.pro/ws (attempt 1)
2025-07-13 11:31:46,228 - core.unified_llm_manager - INFO - [UNIFIED_LLM] Unified LLM Manager initialized
2025-07-13 11:31:46,228 - core.standardized_prompt_handler - INFO - [PROMPT_HANDLER] Standardized prompt handler initialized
2025-07-13 11:31:46,228 - core.autonomous_llm_integration - INFO - [AUTONOMOUS_LLM] Autonomous LLM Integration initialized
2025-07-13 11:31:46,228 - core.autonomous_llm_integration - INFO - [AUTONOMOUS_LLM] Initializing autonomous LLM integration...
2025-07-13 11:31:46,228 - core.unified_llm_manager - INFO - [UNIFIED_LLM] Initializing LLM providers...
2025-07-13 11:31:46,232 - core.unified_llm_manager - INFO - [UNIFIED_LLM] ChatGPT provider initialized
2025-07-13 11:31:46,235 - llama.lmstudio_runner - INFO - Loaded LMStudio configuration from models_config.yaml
2025-07-13 11:31:48,257 - llama.lmstudio_runner - INFO - Discovered 8 models: ['phi-3.1-mini-128k-instruct', 'openthinker3-7b', 'mistral-7b-claude-chat', 'fine_tuned_qwen1.7b', 'tsfc-model-0.0.1', 'codestral-22b-v0.1', 'deepseek-r1-distill-qwen-7b', 'text-embedding-nomic-embed-text-v1.5']
2025-07-13 11:31:48,258 - llama.lmstudio_runner - INFO - Selected preferred model from config: phi-3.1-mini-128k-instruct
2025-07-13 11:31:48,258 - llama.lmstudio_runner - INFO - Initialized LMStudio with model: phi-3.1-mini-128k-instruct
2025-07-13 11:31:50,287 - core.unified_llm_manager - INFO - [UNIFIED_LLM] LMStudio provider initialized
2025-07-13 11:31:50,288 - core.unified_llm_manager - WARNING - [UNIFIED_LLM] Failed to initialize Transformers: __init__() missing 1 required positional argument: 'model_path'
2025-07-13 11:31:50,288 - core.unified_llm_manager - WARNING - [UNIFIED_LLM] Failed to initialize Llama: __init__() missing 1 required positional argument: 'bin_path'
2025-07-13 11:31:50,288 - llama.mock_runner - INFO - Initialized MockRunner with personality: {'caution': 0.669281563065212, 'optimism': 0.544613846181655, 'consistency': 0.8567337649784836, 'detail_level': 0.8116275515661119, 'confidence_bias': 3.5897792565530615}
2025-07-13 11:31:50,289 - core.unified_llm_manager - INFO - [UNIFIED_LLM] Mock provider initialized
2025-07-13 11:31:50,289 - core.unified_llm_manager - INFO - [UNIFIED_LLM] Selected provider: mock (score: 109.90)
2025-07-13 11:31:50,289 - core.unified_llm_manager - INFO - [UNIFIED_LLM] Health monitoring started
2025-07-13 11:31:50,290 - core.unified_llm_manager - INFO - [UNIFIED_LLM] Initialized with 3 providers
2025-07-13 11:31:50,290 - core.unified_llm_manager - INFO - [UNIFIED_LLM] Current provider: LLMProvider.MOCK
2025-07-13 11:31:50,290 - core.autonomous_llm_integration - INFO - [AUTONOMOUS_LLM] Autonomous LLM integration initialized successfully
2025-07-13 11:31:50,304 - utils.cache_manager - INFO - Cache manager initialized: cache, max_age=24h, max_size=100MB
2025-07-13 11:31:50,304 - utils.cache_manager - INFO - Cache manager initialized: cache, max_age=24h, max_size=100MB
2025-07-13 11:31:50,304 - core.llm_orchestrator - INFO - LLM Prompt Orchestrator initialized
2025-07-13 11:31:53,276 - ml.models - INFO - TensorFlow available for LSTM models
2025-07-13 11:31:53,276 - ml.models - INFO - ML Model Manager initialized
2025-07-13 11:31:53,399 - absl - WARNING - Compiled the loaded model, but the compiled metrics have yet to be built. `model.compile_metrics` will be empty until you train or evaluate the model.
2025-07-13 11:31:53,436 - ml.models - ERROR - Error loading models: Could not locate function 'mse'. Make sure custom classes are decorated with `@keras.saving.register_keras_serializable()`. Full object config: {'module': 'keras.metrics', 'class_name': 'function', 'config': 'mse', 'registered_name': 'mse'}
2025-07-13 11:31:53,440 - core.autonomous_position_manager - INFO - [POSITION_MANAGER] Autonomous Position Manager initialized
2025-07-13 11:31:53,445 - core.adaptive_risk - INFO - TA-Lib not available, using pandas-ta fallback
2025-07-13 11:31:53,445 - core.adaptive_risk - INFO - Initialized adaptive risk manager
2025-07-13 11:31:53,451 - execution.unified_execution_engine - INFO - Unified execution engine initialized in paper mode
2025-07-13 11:31:53,451 - execution.execution_adapter - INFO - Execution adapter initialized in paper mode
2025-07-13 11:31:53,456 - core.autonomous_position_manager - INFO - [POSITION_MANAGER] Position monitoring started (interval: 5s)
2025-07-13 11:31:53,456 - core.autonomous_position_manager - INFO - [POSITION_MANAGER] Autonomous Position Manager ready
2025-07-13 11:31:53,459 - core.position_watchdog - INFO - [WATCHDOG] Position Watchdog initialized
2025-07-13 11:31:53,459 - core.position_watchdog - INFO - [WATCHDOG] Monitoring started (scan: 10s, health: 30s)
2025-07-13 11:31:53,459 - core.position_watchdog - INFO - [WATCHDOG] Position Watchdog ready
2025-07-13 11:31:53,463 - portfolio.portfolio_manager - INFO - Portfolio manager initialized with $10000.00 balance
2025-07-13 11:31:53,464 - core.autonomous_trading_orchestrator - ERROR - Failed to initialize autonomous trading system: System health check failed: ['No market data available']
2025-07-13 11:31:53,465 - __main__ - ERROR - [PHASE2_TEST] Orchestrator initialization failed
2025-07-13 11:31:53,466 - __main__ - INFO - [PHASE2_TEST] Running Test 6: End-to-End Workflow...
2025-07-13 11:31:53,466 - __main__ - INFO - [PHASE2_TEST] Testing end-to-end workflow simulation
2025-07-13 11:31:53,466 - __main__ - INFO - [PHASE2_TEST] Simulating: Market data collection
2025-07-13 11:31:53,467 - core.position_watchdog - ERROR - [WATCHDOG] Health check loop error: 'PositionWatchdog' object has no attribute '_process_alerts'
2025-07-13 11:31:53,468 - core.position_watchdog - ERROR - [WATCHDOG] Error calculating health for position pos_1_1752424301: 'PositionWatchdog' object has no attribute '_generate_position_alerts'
2025-07-13 11:31:53,468 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:31:53,468 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:31:53,468 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:31:53,468 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:31:53,468 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:31:53,468 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:31:53,468 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:31:53,470 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:31:53,470 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:31:53,470 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:31:53,470 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:31:53,470 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:31:53,470 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:31:53,470 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:31:53,470 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:31:53,470 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:31:53,470 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:31:53,470 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:31:53,470 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:31:53,470 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:31:53,470 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:31:53,470 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:31:53,470 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:31:53,470 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:31:53,470 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:31:53,470 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:31:53,470 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:31:53,470 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:31:53,471 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:31:53,471 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:31:53,471 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:31:53,471 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:31:53,471 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:31:53,471 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:31:53,471 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:31:53,471 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:31:53,471 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:31:53,471 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:31:53,472 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:31:53,472 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:31:53,472 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:31:53,472 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:31:53,472 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:31:53,472 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:31:53,472 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:31:53,472 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:31:53,472 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:31:53,473 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:31:53,473 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:31:53,473 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:31:53,473 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:31:53,473 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:31:53,473 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:31:53,473 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:31:53,473 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:31:53,473 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:31:53,474 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:31:53,474 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:31:53,474 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:31:53,474 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:31:53,474 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:31:53,474 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:31:53,474 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:31:53,474 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:31:53,474 - core.websocket_manager - ERROR - htx_futures: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:31:53,474 - core.websocket_manager - ERROR - htx_futures: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:31:53,475 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:31:53,475 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:31:53,475 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:31:53,475 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:31:53,475 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:31:53,475 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:31:53,475 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:31:53,475 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:31:53,475 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:31:53,563 - __main__ - INFO - [PHASE2_TEST] Simulating: LLM analysis and decision
2025-07-13 11:31:53,653 - __main__ - INFO - [PHASE2_TEST] Simulating: Position entry execution
2025-07-13 11:31:53,654 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:31:53,654 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:31:53,657 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:31:53,657 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:31:53,672 - core.websocket_manager - ERROR - htx_futures: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:31:53,771 - __main__ - INFO - [PHASE2_TEST] Simulating: Position monitoring
2025-07-13 11:31:53,820 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:31:53,822 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:31:53,886 - __main__ - INFO - [PHASE2_TEST] Simulating: Risk assessment
2025-07-13 11:31:53,922 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:31:53,922 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:31:53,999 - __main__ - INFO - [PHASE2_TEST] Simulating: Position exit execution
2025-07-13 11:31:54,019 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:31:54,021 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:31:54,092 - __main__ - INFO - [PHASE2_TEST] End-to-end workflow test completed
2025-07-13 11:31:54,095 - __main__ - INFO - [PHASE2_TEST] Running Test 7: Safety Systems Validation...
2025-07-13 11:31:54,095 - __main__ - INFO - [PHASE2_TEST] Testing safety systems
2025-07-13 11:31:54,095 - __main__ - INFO - [PHASE2_TEST] Safety systems test completed
2025-07-13 11:31:54,097 - __main__ - INFO - [PHASE2_TEST] Running Test 8: Performance Validation...
2025-07-13 11:31:54,097 - __main__ - INFO - [PHASE2_TEST] Testing performance validation
2025-07-13 11:31:54,097 - __main__ - INFO - [PHASE2_TEST] Performance validation test completed
2025-07-13 11:31:54,099 - __main__ - INFO - [PHASE2_TEST] Phase 2 integration tests completed - Overall: FAILED
