#!/usr/bin/env python3
"""
🔧 UNICODE-SAFE LOGGING CONFIGURATION
Robust logging system that handles Unicode characters on Windows

FEATURES:
- UTF-8 encoding enforcement for Windows
- Unicode character filtering and replacement
- Emoji-safe logging formatters
- Backward compatibility with existing loggers
"""

import logging
import sys
import os
import re
from typing import Dict, Any
from datetime import datetime


class UnicodeFilterFormatter(logging.Formatter):
    """
    Custom formatter that safely handles Unicode characters
    Replaces problematic Unicode with ASCII equivalents
    """
    
    # Emoji to ASCII mapping
    EMOJI_REPLACEMENTS = {
        '🎯': '[TARGET]',
        '✅': '[OK]',
        '❌': '[ERROR]',
        '⚠️': '[WARNING]',
        '🚀': '[LAUNCH]',
        '📊': '[CHART]',
        '🔧': '[TOOL]',
        '🛡️': '[SHIELD]',
        '💰': '[MONEY]',
        '🔍': '[SEARCH]',
        '📈': '[UP]',
        '📉': '[DOWN]',
        '🎉': '[CELEBRATE]',
        '🚨': '[ALERT]',
        '⏰': '[TIME]',
        '🔗': '[LINK]',
        '📝': '[NOTE]',
        '🏆': '[TROPHY]',
        '⚡': '[LIGHTNING]',
        '🎭': '[MASK]',
        '🔑': '[KEY]',
        '📋': '[CLIPBOARD]',
        '🖥️': '[COMPUTER]',
        '📱': '[PHONE]',
        '🌐': '[GLOBE]',
        '🔒': '[LOCK]',
        '🔓': '[UNLOCK]',
        '📦': '[PACKAGE]',
        '🎪': '[CIRCUS]',
        '🎨': '[ART]',
        '🎵': '[MUSIC]',
        '🎮': '[GAME]',
        '🎲': '[DICE]',
        '🎳': '[BOWLING]',
        '🎸': '[GUITAR]',
        '🎺': '[TRUMPET]',
        '🎻': '[VIOLIN]',
        '🥁': '[DRUM]',
        '🎤': '[MIC]',
        '🎧': '[HEADPHONES]',
        '📻': '[RADIO]',
        '📺': '[TV]',
        '📷': '[CAMERA]',
        '📹': '[VIDEO]',
        '💻': '[LAPTOP]',
        '🖨️': '[PRINTER]',
        '⌨️': '[KEYBOARD]',
        '🖱️': '[MOUSE]',
        '💾': '[DISK]',
        '💿': '[CD]',
        '📀': '[DVD]',
        '🧮': '[ABACUS]',
        '🔋': '[BATTERY]',
        '🔌': '[PLUG]',
        '💡': '[BULB]',
        '🔦': '[FLASHLIGHT]',
        '🕯️': '[CANDLE]',
        '🪔': '[LAMP]',
        '🔥': '[FIRE]',
        '💧': '[WATER]',
        '🌊': '[WAVE]',
        '❄️': '[SNOW]',
        '⭐': '[STAR]',
        '🌟': '[SPARKLE]',
        '💫': '[DIZZY]',
        '⚡': '[ZAP]',
        '☀️': '[SUN]',
        '🌙': '[MOON]',
        '🌈': '[RAINBOW]',
        '☁️': '[CLOUD]',
        '⛅': '[PARTLY_CLOUDY]',
        '🌤️': '[SUN_CLOUD]',
        '🌦️': '[RAIN_SUN]',
        '🌧️': '[RAIN]',
        '⛈️': '[STORM]',
        '🌩️': '[LIGHTNING]',
        '❄️': '[SNOW]',
        '☃️': '[SNOWMAN]',
        '⛄': '[SNOWMAN2]',
        '🌨️': '[SNOW_CLOUD]',
        '💨': '[WIND]',
        '🌪️': '[TORNADO]',
        '🌫️': '[FOG]',
        '🌊': '[OCEAN]',
        '💦': '[SWEAT]',
        '☔': '[UMBRELLA]',
        '⛱️': '[BEACH_UMBRELLA]',
        '⚓': '[ANCHOR]',
        '🚢': '[SHIP]',
        '⛵': '[SAILBOAT]',
        '🚤': '[SPEEDBOAT]',
        '🛥️': '[MOTOR_BOAT]',
        '🚁': '[HELICOPTER]',
        '✈️': '[AIRPLANE]',
        '🛩️': '[SMALL_PLANE]',
        '🚀': '[ROCKET]',
        '🛸': '[UFO]',
        '🚂': '[TRAIN]',
        '🚃': '[TRAIN_CAR]',
        '🚄': '[BULLET_TRAIN]',
        '🚅': '[BULLET_TRAIN2]',
        '🚆': '[TRAIN2]',
        '🚇': '[METRO]',
        '🚈': '[LIGHT_RAIL]',
        '🚉': '[STATION]',
        '🚊': '[TRAM]',
        '🚝': '[MONORAIL]',
        '🚞': '[MOUNTAIN_RAILWAY]',
        '🚋': '[TRAM2]',
        '🚌': '[BUS]',
        '🚍': '[BUS2]',
        '🚎': '[TROLLEYBUS]',
        '🚐': '[MINIBUS]',
        '🚑': '[AMBULANCE]',
        '🚒': '[FIRE_ENGINE]',
        '🚓': '[POLICE_CAR]',
        '🚔': '[POLICE_CAR2]',
        '🚕': '[TAXI]',
        '🚖': '[TAXI2]',
        '🚗': '[CAR]',
        '🚘': '[CAR2]',
        '🚙': '[SUV]',
        '🚚': '[TRUCK]',
        '🚛': '[TRUCK2]',
        '🚜': '[TRACTOR]',
        '🏎️': '[RACE_CAR]',
        '🏍️': '[MOTORCYCLE]',
        '🛵': '[SCOOTER]',
        '🚲': '[BICYCLE]',
        '🛴': '[KICK_SCOOTER]',
        '🛹': '[SKATEBOARD]',
        '🛼': '[ROLLER_SKATE]',
        '🚁': '[HELICOPTER]',
        '🚟': '[SUSPENSION_RAILWAY]',
        '🚠': '[MOUNTAIN_CABLEWAY]',
        '🚡': '[AERIAL_TRAMWAY]',
        '🛰️': '[SATELLITE]',
        '🚀': '[ROCKET]'
    }
    
    def __init__(self, fmt=None, datefmt=None, style='%', validate=True):
        super().__init__(fmt, datefmt, style, validate)
    
    def format(self, record):
        """Format log record with Unicode safety"""
        try:
            # Get the original formatted message
            formatted = super().format(record)
            
            # Replace Unicode emojis with ASCII equivalents
            for emoji, replacement in self.EMOJI_REPLACEMENTS.items():
                formatted = formatted.replace(emoji, replacement)
            
            # Remove any remaining problematic Unicode characters
            formatted = self._sanitize_unicode(formatted)
            
            return formatted
            
        except UnicodeEncodeError:
            # Fallback: create a safe ASCII-only message
            safe_message = self._create_safe_message(record)
            return safe_message
    
    def _sanitize_unicode(self, text: str) -> str:
        """Remove or replace problematic Unicode characters"""
        try:
            # Try to encode as cp1252 (Windows default)
            text.encode('cp1252')
            return text
        except UnicodeEncodeError:
            # Replace problematic characters with ASCII equivalents
            # Remove high Unicode characters that can't be encoded
            sanitized = re.sub(r'[^\x00-\x7F]+', '[UNICODE]', text)
            return sanitized
    
    def _create_safe_message(self, record) -> str:
        """Create a safe ASCII-only log message"""
        try:
            timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            level = record.levelname
            name = record.name
            message = str(record.getMessage()).encode('ascii', 'replace').decode('ascii')
            
            return f"{timestamp} - {level} - {name} - {message}"
        except Exception:
            return f"LOG_ERROR - Unable to format message safely"


class UnicodeStreamHandler(logging.StreamHandler):
    """
    Stream handler that forces UTF-8 encoding on Windows
    """
    
    def __init__(self, stream=None):
        super().__init__(stream)
        
        # Force UTF-8 encoding on Windows
        if sys.platform.startswith('win') and hasattr(self.stream, 'reconfigure'):
            try:
                self.stream.reconfigure(encoding='utf-8', errors='replace')
            except Exception:
                pass
    
    def emit(self, record):
        """Emit log record with Unicode safety"""
        try:
            super().emit(record)
        except UnicodeEncodeError:
            # Fallback: write a safe message
            try:
                safe_msg = f"[UNICODE_ERROR] {record.levelname}: {record.name}\n"
                self.stream.write(safe_msg)
                self.stream.flush()
            except Exception:
                pass


def configure_unicode_safe_logging():
    """
    Configure Unicode-safe logging for the entire application
    """
    
    # Set environment variables for UTF-8 encoding
    os.environ['PYTHONIOENCODING'] = 'utf-8'
    
    # Configure root logger
    root_logger = logging.getLogger()
    
    # Remove existing handlers to avoid duplicates
    for handler in root_logger.handlers[:]:
        root_logger.removeHandler(handler)
    
    # Create Unicode-safe formatter
    formatter = UnicodeFilterFormatter(
        fmt='%(asctime)s - %(levelname)s - %(name)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    
    # Create Unicode-safe console handler
    console_handler = UnicodeStreamHandler(sys.stdout)
    console_handler.setFormatter(formatter)
    console_handler.setLevel(logging.INFO)
    
    # Create Unicode-safe file handler
    try:
        log_file = f"logs/unicode_safe_{datetime.now().strftime('%Y%m%d')}.log"
        os.makedirs(os.path.dirname(log_file), exist_ok=True)
        
        file_handler = logging.FileHandler(log_file, encoding='utf-8', errors='replace')
        file_handler.setFormatter(formatter)
        file_handler.setLevel(logging.DEBUG)
        
        root_logger.addHandler(file_handler)
    except Exception as e:
        print(f"Warning: Could not create file handler: {e}")
    
    # Add console handler
    root_logger.addHandler(console_handler)
    root_logger.setLevel(logging.DEBUG)
    
    # Configure specific loggers that are causing issues
    problematic_loggers = [
        'core.scalper_gpt',
        'core.autonomous_trading_orchestrator',
        'validation.live_trading_validator',
        'core.dynamic_risk_manager',
        'core.emergency_stop_coordinator',
        'core.unified_execution_engine'
    ]
    
    for logger_name in problematic_loggers:
        logger = logging.getLogger(logger_name)
        logger.handlers = []  # Remove existing handlers
        logger.propagate = True  # Use root logger handlers
    
    print("Unicode-safe logging configured successfully")
    return True


def test_unicode_logging():
    """Test Unicode logging configuration"""
    
    configure_unicode_safe_logging()
    
    logger = logging.getLogger('test_unicode')
    
    # Test various Unicode characters
    test_messages = [
        "🎯 Testing target emoji",
        "✅ Testing checkmark emoji", 
        "❌ Testing X emoji",
        "⚠️ Testing warning emoji",
        "🚀 Testing rocket emoji",
        "Regular ASCII message",
        "Mixed: 🎯 ASCII and Unicode ✅ content",
        "Special chars: àáâãäåæçèéêë",
        "Math symbols: ∑∏∆∇∂∫√∞"
    ]
    
    print("Testing Unicode logging...")
    for msg in test_messages:
        try:
            logger.info(msg)
            print(f"✓ Successfully logged: {msg[:20]}...")
        except Exception as e:
            print(f"✗ Failed to log: {msg[:20]}... Error: {e}")
    
    print("Unicode logging test completed")


if __name__ == "__main__":
    test_unicode_logging()
