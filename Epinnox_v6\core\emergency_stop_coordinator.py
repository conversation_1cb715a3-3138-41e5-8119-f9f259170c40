"""
Emergency Stop Coordinator
Centralized emergency stop management for autonomous trading system
"""

import asyncio
import time
import logging
from typing import Dict, List, Optional, Callable, Any
from dataclasses import dataclass
from enum import Enum
from datetime import datetime

logger = logging.getLogger(__name__)

class EmergencyLevel(Enum):
    """Emergency severity levels"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"

class EmergencyType(Enum):
    """Types of emergency conditions"""
    MANUAL = "manual"
    RISK_LIMIT = "risk_limit"
    SYSTEM_ERROR = "system_error"
    MARKET_CONDITION = "market_condition"
    CONNECTIVITY = "connectivity"
    PERFORMANCE = "performance"

@dataclass
class EmergencyEvent:
    """Emergency event data structure"""
    event_id: str
    emergency_type: EmergencyType
    level: EmergencyLevel
    reason: str
    timestamp: datetime
    source_module: str
    data: Dict[str, Any] = None

class EmergencyStopCoordinator:
    """
    Centralized emergency stop coordinator
    Manages emergency stops across all trading modules
    """
    
    def __init__(self):
        self.emergency_active = False
        self.emergency_events: List[EmergencyEvent] = []
        self.registered_modules: Dict[str, Any] = {}
        self.emergency_callbacks: List[Callable] = []
        
        # Emergency stop configuration
        self.auto_stop_levels = [EmergencyLevel.HIGH, EmergencyLevel.CRITICAL]
        self.propagation_timeout = 30.0  # seconds
        
        logger.info("Emergency Stop Coordinator initialized")
        self.initialized = True
    
    def register_module(self, module_name: str, module_instance: Any) -> bool:
        """
        Register a module for emergency stop propagation
        
        Args:
            module_name: Name of the module
            module_instance: Module instance with emergency_stop method
            
        Returns:
            bool: True if registration successful
        """
        try:
            # Verify module has emergency_stop method
            if not hasattr(module_instance, 'emergency_stop'):
                logger.error(f"Module {module_name} does not have emergency_stop method")
                return False
            
            self.registered_modules[module_name] = module_instance
            logger.info(f"[OK] Registered module for emergency stop: {module_name}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to register module {module_name}: {e}")
            return False

    def is_initialized(self) -> bool:
        """Check if emergency coordinator is initialized"""
        return getattr(self, 'initialized', False)

    async def test_emergency_systems(self) -> bool:
        """Test emergency systems functionality"""
        try:
            logger.info("Testing emergency systems...")

            # Test emergency stop mechanism
            test_event = EmergencyEvent(
                event_id="test_emergency",
                emergency_type=EmergencyType.MANUAL,
                level=EmergencyLevel.LOW,
                reason="Emergency systems test",
                timestamp=datetime.now(),
                source_module="test"
            )

            # Test callback system
            test_callbacks_working = len(self.emergency_callbacks) >= 0

            # Test module registration system
            test_modules_working = isinstance(self.registered_modules, dict)

            logger.info("Emergency systems test completed successfully")
            return test_callbacks_working and test_modules_working

        except Exception as e:
            logger.error(f"Emergency systems test failed: {e}")
            return False

    def get_system_status(self) -> Dict[str, Any]:
        """Get emergency system status"""
        return {
            'active': self.emergency_active,
            'initialized': self.is_initialized(),
            'registered_modules': len(self.registered_modules),
            'emergency_count': len(self.emergency_events),
            'callbacks_registered': len(self.emergency_callbacks)
        }

    def register_callback(self, callback: Callable) -> bool:
        """
        Register callback for emergency events
        
        Args:
            callback: Callback function to call on emergency
            
        Returns:
            bool: True if registration successful
        """
        try:
            self.emergency_callbacks.append(callback)
            logger.info("[OK] Emergency callback registered")
            return True
            
        except Exception as e:
            logger.error(f"Failed to register emergency callback: {e}")
            return False
    
    async def trigger_emergency_stop(self, emergency_type: EmergencyType, 
                                   level: EmergencyLevel, reason: str,
                                   source_module: str = "unknown",
                                   data: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        Trigger emergency stop across all registered modules
        
        Args:
            emergency_type: Type of emergency
            level: Severity level
            reason: Reason for emergency stop
            source_module: Module that triggered the emergency
            data: Additional emergency data
            
        Returns:
            Dict with emergency stop results
        """
        try:
            event_id = f"emergency_{int(time.time() * 1000)}"
            
            # Create emergency event
            event = EmergencyEvent(
                event_id=event_id,
                emergency_type=emergency_type,
                level=level,
                reason=reason,
                timestamp=datetime.now(),
                source_module=source_module,
                data=data or {}
            )
            
            self.emergency_events.append(event)
            
            logger.critical(f"[ALERT] EMERGENCY STOP TRIGGERED: {level.value.upper()} - {reason}")
            
            # Check if this level requires auto-stop
            if level in self.auto_stop_levels:
                self.emergency_active = True
                
                # Propagate to all registered modules
                propagation_results = await self._propagate_emergency_stop(event)
                
                # Call emergency callbacks
                await self._call_emergency_callbacks(event)
                
                logger.critical(f"🛑 EMERGENCY STOP PROPAGATED to {len(propagation_results)} modules")
                
                return {
                    'success': True,
                    'event_id': event_id,
                    'emergency_active': self.emergency_active,
                    'propagation_results': propagation_results,
                    'timestamp': time.time()
                }
            else:
                logger.warning(f"[WARNING] Emergency event logged but auto-stop not triggered for level: {level.value}")
                return {
                    'success': True,
                    'event_id': event_id,
                    'emergency_active': False,
                    'reason': f'Level {level.value} does not trigger auto-stop',
                    'timestamp': time.time()
                }
                
        except Exception as e:
            logger.error(f"CRITICAL ERROR in emergency stop trigger: {e}")
            return {
                'success': False,
                'error': str(e),
                'timestamp': time.time()
            }
    
    async def _propagate_emergency_stop(self, event: EmergencyEvent) -> Dict[str, Any]:
        """Propagate emergency stop to all registered modules"""
        results = {}
        
        # Create tasks for parallel execution
        tasks = []
        for module_name, module_instance in self.registered_modules.items():
            task = asyncio.create_task(
                self._call_module_emergency_stop(module_name, module_instance, event)
            )
            tasks.append((module_name, task))
        
        # Wait for all tasks with timeout
        try:
            await asyncio.wait_for(
                asyncio.gather(*[task for _, task in tasks], return_exceptions=True),
                timeout=self.propagation_timeout
            )
        except asyncio.TimeoutError:
            logger.error(f"Emergency stop propagation timeout after {self.propagation_timeout}s")
        
        # Collect results
        for module_name, task in tasks:
            try:
                if task.done():
                    results[module_name] = task.result()
                else:
                    results[module_name] = {'success': False, 'error': 'Timeout'}
            except Exception as e:
                results[module_name] = {'success': False, 'error': str(e)}
        
        return results
    
    async def _call_module_emergency_stop(self, module_name: str, 
                                        module_instance: Any, 
                                        event: EmergencyEvent) -> Dict[str, Any]:
        """Call emergency stop on a specific module"""
        try:
            logger.info(f"[ALERT] Calling emergency stop on {module_name}")
            
            # Call emergency stop method
            if asyncio.iscoroutinefunction(module_instance.emergency_stop):
                result = await module_instance.emergency_stop(event.reason)
            else:
                result = module_instance.emergency_stop(event.reason)
            
            logger.info(f"[OK] Emergency stop completed for {module_name}")
            return {'success': True, 'result': result}
            
        except Exception as e:
            logger.error(f"[ERROR] Emergency stop failed for {module_name}: {e}")
            return {'success': False, 'error': str(e)}
    
    async def _call_emergency_callbacks(self, event: EmergencyEvent):
        """Call all registered emergency callbacks"""
        for callback in self.emergency_callbacks:
            try:
                if asyncio.iscoroutinefunction(callback):
                    await callback(event)
                else:
                    callback(event)
            except Exception as e:
                logger.error(f"Emergency callback failed: {e}")
    
    def reset_emergency_stop(self, authorization_code: str = None) -> bool:
        """
        Reset emergency stop state
        
        Args:
            authorization_code: Authorization code for reset
            
        Returns:
            bool: True if reset successful
        """
        try:
            # Simple authorization check
            if authorization_code != "RESET_EMERGENCY_2024":
                logger.error("[ALERT] UNAUTHORIZED emergency stop reset attempt")
                return False
            
            self.emergency_active = False
            logger.warning("[WARNING] Emergency stop coordinator reset - system can resume")
            return True
            
        except Exception as e:
            logger.error(f"Error resetting emergency stop coordinator: {e}")
            return False
    
    def get_emergency_status(self) -> Dict[str, Any]:
        """Get current emergency status"""
        return {
            'emergency_active': self.emergency_active,
            'total_events': len(self.emergency_events),
            'registered_modules': list(self.registered_modules.keys()),
            'recent_events': [
                {
                    'event_id': event.event_id,
                    'type': event.emergency_type.value,
                    'level': event.level.value,
                    'reason': event.reason,
                    'timestamp': event.timestamp.isoformat(),
                    'source': event.source_module
                }
                for event in self.emergency_events[-5:]  # Last 5 events
            ]
        }

# Global emergency stop coordinator instance
emergency_coordinator = EmergencyStopCoordinator()
