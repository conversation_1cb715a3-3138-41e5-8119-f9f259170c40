"""
Autonomous Trading Rules and Enforcement
Critical safety rules for autonomous trading operations
"""

import logging
from typing import Dict, Any, List
from enum import Enum

logger = logging.getLogger(__name__)

class TradingRule(Enum):
    """Critical trading rules that must be enforced"""
    LIMIT_ORDERS_ONLY = "limit_orders_only"
    MAX_POSITION_SIZE = "max_position_size"
    MAX_LEVERAGE = "max_leverage"
    MAX_DAILY_LOSS = "max_daily_loss"
    MIN_CONFIDENCE = "min_confidence"
    MAX_CONCURRENT_POSITIONS = "max_concurrent_positions"

class AutonomousTradingRules:
    """
    Enforces critical safety rules for autonomous trading
    These rules cannot be overridden and are hardcoded for safety
    """
    
    # CRITICAL SAFETY RULES - DO NOT MODIFY
    RULES = {
        TradingRule.LIMIT_ORDERS_ONLY: {
            'enabled': True,
            'description': 'Only LIMIT orders allowed - NO market orders',
            'enforcement': 'STRICT',
            'violation_action': 'REJECT_ORDER'
        },
        TradingRule.MAX_POSITION_SIZE: {
            'enabled': True,
            'value': 0.30,  # 30% of balance max
            'description': 'Maximum position size as percentage of balance',
            'enforcement': 'STRICT',
            'violation_action': 'REJECT_ORDER'
        },
        TradingRule.MAX_LEVERAGE: {
            'enabled': True,
            'value': 3.0,  # 3x maximum leverage
            'description': 'Maximum leverage allowed',
            'enforcement': 'STRICT',
            'violation_action': 'REJECT_ORDER'
        },
        TradingRule.MAX_DAILY_LOSS: {
            'enabled': True,
            'value': 0.20,  # 20% of balance max daily loss
            'description': 'Maximum daily loss as percentage of balance',
            'enforcement': 'STRICT',
            'violation_action': 'EMERGENCY_STOP'
        },
        TradingRule.MIN_CONFIDENCE: {
            'enabled': True,
            'value': 0.55,  # 55% minimum confidence
            'description': 'Minimum confidence threshold for trades',
            'enforcement': 'STRICT',
            'violation_action': 'REJECT_ORDER'
        },
        TradingRule.MAX_CONCURRENT_POSITIONS: {
            'enabled': True,
            'value': 2,  # Maximum 2 concurrent positions
            'description': 'Maximum number of concurrent open positions',
            'enforcement': 'STRICT',
            'violation_action': 'REJECT_ORDER'
        }
    }
    
    @classmethod
    def validate_order_type(cls, order_type: str) -> Dict[str, Any]:
        """
        Validate that order type is LIMIT only
        
        Args:
            order_type: Order type to validate
            
        Returns:
            Dict with validation result
        """
        rule = cls.RULES[TradingRule.LIMIT_ORDERS_ONLY]
        
        if not rule['enabled']:
            return {'valid': True, 'reason': 'Rule disabled'}
        
        if order_type.lower() != 'limit':
            return {
                'valid': False,
                'rule': TradingRule.LIMIT_ORDERS_ONLY,
                'reason': f'Order type "{order_type}" violates LIMIT_ORDERS_ONLY rule',
                'action': rule['violation_action'],
                'severity': 'CRITICAL'
            }
        
        return {'valid': True, 'reason': 'Order type validation passed'}
    
    @classmethod
    def validate_position_size(cls, position_value: float, balance: float) -> Dict[str, Any]:
        """
        Validate position size against maximum allowed
        
        Args:
            position_value: Value of the position
            balance: Current account balance
            
        Returns:
            Dict with validation result
        """
        rule = cls.RULES[TradingRule.MAX_POSITION_SIZE]
        
        if not rule['enabled']:
            return {'valid': True, 'reason': 'Rule disabled'}
        
        max_position_value = balance * rule['value']
        
        if position_value > max_position_value:
            return {
                'valid': False,
                'rule': TradingRule.MAX_POSITION_SIZE,
                'reason': f'Position value ${position_value:.2f} exceeds maximum ${max_position_value:.2f} ({rule["value"]:.1%} of balance)',
                'action': rule['violation_action'],
                'severity': 'HIGH'
            }
        
        return {'valid': True, 'reason': 'Position size validation passed'}
    
    @classmethod
    def validate_leverage(cls, leverage: float) -> Dict[str, Any]:
        """
        Validate leverage against maximum allowed
        
        Args:
            leverage: Leverage to validate
            
        Returns:
            Dict with validation result
        """
        rule = cls.RULES[TradingRule.MAX_LEVERAGE]
        
        if not rule['enabled']:
            return {'valid': True, 'reason': 'Rule disabled'}
        
        if leverage > rule['value']:
            return {
                'valid': False,
                'rule': TradingRule.MAX_LEVERAGE,
                'reason': f'Leverage {leverage}x exceeds maximum {rule["value"]}x',
                'action': rule['violation_action'],
                'severity': 'HIGH'
            }
        
        return {'valid': True, 'reason': 'Leverage validation passed'}
    
    @classmethod
    def validate_daily_loss(cls, daily_pnl: float, balance: float) -> Dict[str, Any]:
        """
        Validate daily loss against maximum allowed
        
        Args:
            daily_pnl: Current daily P&L
            balance: Current account balance
            
        Returns:
            Dict with validation result
        """
        rule = cls.RULES[TradingRule.MAX_DAILY_LOSS]
        
        if not rule['enabled']:
            return {'valid': True, 'reason': 'Rule disabled'}
        
        max_daily_loss = balance * rule['value']
        
        if abs(daily_pnl) > max_daily_loss and daily_pnl < 0:
            return {
                'valid': False,
                'rule': TradingRule.MAX_DAILY_LOSS,
                'reason': f'Daily loss ${abs(daily_pnl):.2f} exceeds maximum ${max_daily_loss:.2f} ({rule["value"]:.1%} of balance)',
                'action': rule['violation_action'],
                'severity': 'CRITICAL'
            }
        
        return {'valid': True, 'reason': 'Daily loss validation passed'}
    
    @classmethod
    def validate_confidence(cls, confidence: float) -> Dict[str, Any]:
        """
        Validate confidence against minimum required
        
        Args:
            confidence: Confidence level (0.0 to 1.0)
            
        Returns:
            Dict with validation result
        """
        rule = cls.RULES[TradingRule.MIN_CONFIDENCE]
        
        if not rule['enabled']:
            return {'valid': True, 'reason': 'Rule disabled'}
        
        if confidence < rule['value']:
            return {
                'valid': False,
                'rule': TradingRule.MIN_CONFIDENCE,
                'reason': f'Confidence {confidence:.1%} below minimum {rule["value"]:.1%}',
                'action': rule['violation_action'],
                'severity': 'MEDIUM'
            }
        
        return {'valid': True, 'reason': 'Confidence validation passed'}
    
    @classmethod
    def validate_concurrent_positions(cls, current_positions: int) -> Dict[str, Any]:
        """
        Validate number of concurrent positions
        
        Args:
            current_positions: Number of current open positions
            
        Returns:
            Dict with validation result
        """
        rule = cls.RULES[TradingRule.MAX_CONCURRENT_POSITIONS]
        
        if not rule['enabled']:
            return {'valid': True, 'reason': 'Rule disabled'}
        
        if current_positions >= rule['value']:
            return {
                'valid': False,
                'rule': TradingRule.MAX_CONCURRENT_POSITIONS,
                'reason': f'Current positions {current_positions} at/exceeds maximum {rule["value"]}',
                'action': rule['violation_action'],
                'severity': 'MEDIUM'
            }
        
        return {'valid': True, 'reason': 'Concurrent positions validation passed'}
    
    @classmethod
    def validate_all_rules(cls, order_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Validate all applicable rules for an order
        
        Args:
            order_data: Dict containing order information
            
        Returns:
            Dict with comprehensive validation result
        """
        violations = []
        
        # Validate order type
        if 'order_type' in order_data:
            result = cls.validate_order_type(order_data['order_type'])
            if not result['valid']:
                violations.append(result)
        
        # Validate position size
        if 'position_value' in order_data and 'balance' in order_data:
            result = cls.validate_position_size(order_data['position_value'], order_data['balance'])
            if not result['valid']:
                violations.append(result)
        
        # Validate leverage
        if 'leverage' in order_data:
            result = cls.validate_leverage(order_data['leverage'])
            if not result['valid']:
                violations.append(result)
        
        # Validate confidence
        if 'confidence' in order_data:
            result = cls.validate_confidence(order_data['confidence'])
            if not result['valid']:
                violations.append(result)
        
        # Validate concurrent positions
        if 'current_positions' in order_data:
            result = cls.validate_concurrent_positions(order_data['current_positions'])
            if not result['valid']:
                violations.append(result)
        
        # Validate daily loss
        if 'daily_pnl' in order_data and 'balance' in order_data:
            result = cls.validate_daily_loss(order_data['daily_pnl'], order_data['balance'])
            if not result['valid']:
                violations.append(result)
        
        if violations:
            # Find the most severe violation
            critical_violations = [v for v in violations if v['severity'] == 'CRITICAL']
            if critical_violations:
                return critical_violations[0]  # Return first critical violation
            else:
                return violations[0]  # Return first violation
        
        return {'valid': True, 'reason': 'All rule validations passed'}
    
    @classmethod
    def get_rule_summary(cls) -> Dict[str, Any]:
        """Get summary of all trading rules"""
        return {
            'total_rules': len(cls.RULES),
            'enabled_rules': len([r for r in cls.RULES.values() if r['enabled']]),
            'rules': cls.RULES
        }
