# Unified LLM Configuration for Epinnox Autonomous Trading System
# This configuration standardizes all LLM providers under a single interface

# LLM Manager Configuration
llm_manager:
  max_response_time: 30.0          # Maximum response time in seconds
  max_consecutive_failures: 3      # Max failures before marking provider as failed
  health_check_interval: 60        # Health check interval in seconds
  min_confidence_threshold: 0.5    # Minimum confidence for responses

# Autonomous Decision Loop Configuration
decision_loop:
  decision_interval: 30            # Decision interval in seconds (30-second loops)
  max_decisions_per_minute: 3      # Rate limiting
  min_confidence_for_action: 0.70  # Minimum confidence to execute trades

# Provider-Specific Configurations
providers:
  # ChatGPT Configuration
  chatgpt:
    enabled: true
    model: "gpt-3.5-turbo"
    temperature: 0.7
    max_tokens: 1024
    # API key loaded from models_config.yaml or environment
    
  # LMStudio Configuration  
  lmstudio:
    enabled: true
    api_url: "http://localhost:1234/v1"
    temperature: 0.1
    max_tokens: 512
    
  # Transformers Configuration
  transformers:
    enabled: true
    model_name: "microsoft/DialoGPT-medium"
    temperature: 0.7
    max_length: 512
    
  # Llama Configuration
  llama:
    enabled: true
    model_path: null  # Auto-detect or specify path
    temperature: 0.7
    max_tokens: 512
    
  # Mock Provider (for testing)
  mock:
    enabled: true
    response_time: 0.1
    success_rate: 0.95

# Failover Order (most reliable first)
failover_order:
  - "chatgpt"
  - "lmstudio" 
  - "llama"
  - "transformers"
  - "mock"

# Prompt Templates Configuration
prompt_templates:
  market_analysis:
    max_length: 2000
    include_technical_indicators: true
    include_market_context: true
    include_risk_metrics: true
    
  entry_timing:
    max_length: 1500
    focus_on_timing: true
    include_volatility: true
    
  position_management:
    max_length: 1800
    include_performance_metrics: true
    focus_on_risk: true
    
  emergency_response:
    max_length: 1000
    priority: "capital_preservation"
    response_urgency: "immediate"

# Response Parsing Configuration
response_parsing:
  confidence_patterns:
    - "CONFIDENCE:\\s*(\\d+(?:\\.\\d+)?)%?"
    - "confidence:\\s*(\\d+(?:\\.\\d+)?)"
  
  decision_patterns:
    - "DECISION:\\s*([A-Z_]+)"
    - "ACTION:\\s*([A-Z_]+)"
  
  price_patterns:
    - "PRICE:\\s*\\$?(\\d+(?:\\.\\d+)?)"
    - "OPTIMAL_PRICE:\\s*\\$?(\\d+(?:\\.\\d+)?)"

# Performance Monitoring
monitoring:
  track_response_times: true
  track_success_rates: true
  track_confidence_scores: true
  log_all_decisions: true
  
  # Performance thresholds
  thresholds:
    max_avg_response_time: 10.0
    min_success_rate: 0.80
    min_avg_confidence: 0.60

# Safety Configuration
safety:
  enforce_limit_orders_only: true
  validate_all_decisions: true
  require_minimum_confidence: true
  emergency_stop_on_failures: true
  
  # Emergency thresholds
  emergency_thresholds:
    consecutive_failures: 5
    low_confidence_streak: 10
    response_time_exceeded: 60.0

# Integration Settings
integration:
  # Backward compatibility with existing LLM orchestrator
  maintain_legacy_compatibility: true
  
  # Integration with trading orchestrator
  auto_start_decision_loop: true
  integrate_with_risk_manager: true
  integrate_with_execution_engine: true
  
  # Data sources
  use_real_market_data: true
  include_technical_analysis: true
  include_sentiment_analysis: false

# Logging Configuration
logging:
  log_level: "INFO"
  log_all_requests: true
  log_all_responses: true
  log_performance_metrics: true
  
  # Log file settings
  log_file_prefix: "unified_llm"
  max_log_files: 10
  log_rotation_size: "10MB"

# Development and Testing
development:
  enable_mock_mode: false
  simulate_failures: false
  test_all_providers: true
  validate_configurations: true
  
  # Testing parameters
  test_prompts:
    - "Analyze BTC/USDT market conditions"
    - "Evaluate entry timing for DOGE/USDT"
    - "Assess current position risk"

# Production Settings
production:
  strict_validation: true
  require_all_providers: false
  minimum_providers: 1
  enable_health_monitoring: true
  
  # Production safety
  production_safety:
    double_check_decisions: true
    require_high_confidence: true
    limit_decision_frequency: true
