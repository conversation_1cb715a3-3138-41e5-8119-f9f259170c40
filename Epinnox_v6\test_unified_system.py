#!/usr/bin/env python3
"""
UNIFIED SYSTEM TESTING AND VALIDATION
Comprehensive testing for the Epinnox unified launcher system
"""

import sys
import os
import asyncio
import logging
from datetime import datetime
import subprocess
import time

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Configure Unicode-safe logging
try:
    from core.unicode_safe_logging import configure_unicode_safe_logging
    configure_unicode_safe_logging()
except ImportError:
    pass

logger = logging.getLogger(__name__)

class UnifiedSystemTester:
    """
    Comprehensive tester for the unified launcher system
    """
    
    def __init__(self):
        self.test_results = {}
        self.total_tests = 0
        self.passed_tests = 0
        self.failed_tests = 0
        
        logger.info("Unified System Tester initialized")
    
    def run_test(self, test_name: str, test_func):
        """Run a single test and record results"""
        
        self.total_tests += 1
        
        try:
            print(f"\n🧪 TESTING: {test_name}")
            print("-" * 50)
            
            result = test_func()
            
            if result:
                print(f"✅ PASSED: {test_name}")
                self.passed_tests += 1
                self.test_results[test_name] = "PASSED"
            else:
                print(f"❌ FAILED: {test_name}")
                self.failed_tests += 1
                self.test_results[test_name] = "FAILED"
                
            return result
            
        except Exception as e:
            print(f"❌ ERROR in {test_name}: {e}")
            self.failed_tests += 1
            self.test_results[test_name] = f"ERROR: {e}"
            return False
    
    async def run_async_test(self, test_name: str, test_func):
        """Run an async test and record results"""
        
        self.total_tests += 1
        
        try:
            print(f"\n🧪 TESTING: {test_name}")
            print("-" * 50)
            
            result = await test_func()
            
            if result:
                print(f"✅ PASSED: {test_name}")
                self.passed_tests += 1
                self.test_results[test_name] = "PASSED"
            else:
                print(f"❌ FAILED: {test_name}")
                self.failed_tests += 1
                self.test_results[test_name] = "FAILED"
                
            return result
            
        except Exception as e:
            print(f"❌ ERROR in {test_name}: {e}")
            self.failed_tests += 1
            self.test_results[test_name] = f"ERROR: {e}"
            return False
    
    def test_directory_structure(self):
        """Test that directory cleanup was successful"""
        
        # Check that redundant files are archived
        redundant_files = [
            "deploy_live_validation.py",
            "deploy_ultra_conservative_live.py",
            "start_autonomous_trading_robust.py",
            "fix_autonomous_trading_execution.py"
        ]
        
        for file_path in redundant_files:
            if os.path.exists(file_path):
                print(f"❌ Redundant file still exists: {file_path}")
                return False
        
        # Check that archive directory exists
        if not os.path.exists("archive"):
            print("❌ Archive directory not found")
            return False
        
        # Check that core files exist
        core_files = [
            "launch_epinnox_unified.py",
            "core/autonomous_trading_orchestrator.py",
            "gui/integrated_monitoring_dashboard.py",
            "gui/autonomous_trading_controls.py"
        ]
        
        for file_path in core_files:
            if not os.path.exists(file_path):
                print(f"❌ Core file missing: {file_path}")
                return False
        
        print("✅ Directory structure is clean and organized")
        return True
    
    def test_unified_launcher_syntax(self):
        """Test that the unified launcher has valid syntax"""
        
        try:
            # Try to compile the launcher script
            with open("launch_epinnox_unified.py", "r", encoding='utf-8') as f:
                code = f.read()
            
            compile(code, "launch_epinnox_unified.py", "exec")
            print("✅ Unified launcher syntax is valid")
            return True
            
        except SyntaxError as e:
            print(f"❌ Syntax error in unified launcher: {e}")
            return False
    
    def test_gui_components_syntax(self):
        """Test that GUI components have valid syntax"""
        
        gui_files = [
            "gui/autonomous_trading_controls.py",
            "gui/integrated_monitoring_dashboard.py"
        ]
        
        for file_path in gui_files:
            try:
                with open(file_path, "r", encoding='utf-8') as f:
                    code = f.read()
                
                compile(code, file_path, "exec")
                print(f"✅ {file_path} syntax is valid")
                
            except SyntaxError as e:
                print(f"❌ Syntax error in {file_path}: {e}")
                return False
            except FileNotFoundError:
                print(f"❌ File not found: {file_path}")
                return False
        
        return True
    
    async def test_core_component_imports(self):
        """Test that core components can be imported"""
        
        components = [
            ("core.autonomous_trading_orchestrator", "AutonomousTradingOrchestrator"),
            ("core.unified_execution_engine", "unified_execution_engine"),
            ("core.dynamic_risk_manager", "dynamic_risk_manager"),
            ("core.emergency_stop_coordinator", "emergency_coordinator"),
            ("core.scalper_gpt", "ScalperGPT")
        ]
        
        for module_name, component_name in components:
            try:
                module = __import__(module_name, fromlist=[component_name])
                component = getattr(module, component_name)
                print(f"✅ Successfully imported {module_name}.{component_name}")
                
            except ImportError as e:
                print(f"❌ Failed to import {module_name}.{component_name}: {e}")
                return False
            except AttributeError as e:
                print(f"❌ Component not found {module_name}.{component_name}: {e}")
                return False
        
        return True
    
    def test_launcher_help_output(self):
        """Test that the launcher shows help correctly"""
        
        try:
            result = subprocess.run([
                sys.executable, "launch_epinnox_unified.py", "--help"
            ], capture_output=True, text=True, timeout=10)
            
            if result.returncode == 0:
                output = result.stdout
                
                # Check for key help elements
                required_elements = [
                    "Epinnox Unified Autonomous Trading Launcher",
                    "--mode",
                    "--risk", 
                    "--capital",
                    "--headless",
                    "Examples:"
                ]
                
                for element in required_elements:
                    if element not in output:
                        print(f"❌ Missing help element: {element}")
                        return False
                
                print("✅ Launcher help output is complete")
                return True
            else:
                print(f"❌ Launcher help failed with return code: {result.returncode}")
                return False
                
        except subprocess.TimeoutExpired:
            print("❌ Launcher help command timed out")
            return False
        except Exception as e:
            print(f"❌ Error testing launcher help: {e}")
            return False
    
    async def test_launcher_initialization(self):
        """Test that the launcher can initialize without errors"""
        
        try:
            # Import the launcher
            from launch_epinnox_unified import EpinnoxUnifiedLauncher, parse_arguments
            
            # Test argument parsing
            test_args = ["--paper", "--risk", "ultra-conservative", "--capital", "100"]
            
            # Mock sys.argv for testing
            original_argv = sys.argv
            sys.argv = ["launch_epinnox_unified.py"] + test_args
            
            try:
                args = parse_arguments()
                launcher = EpinnoxUnifiedLauncher(args)
                
                print("✅ Launcher initialization successful")
                return True
                
            finally:
                sys.argv = original_argv
                
        except Exception as e:
            print(f"❌ Launcher initialization failed: {e}")
            return False
    
    def test_pyqt_availability(self):
        """Test PyQt5 availability for GUI functionality"""
        
        try:
            from PyQt5.QtWidgets import QApplication
            from PyQt5.QtCore import QTimer
            print("✅ PyQt5 is available for GUI functionality")
            return True
            
        except ImportError:
            print("⚠️ PyQt5 not available - GUI functionality will be limited")
            return True  # Not a failure, just a limitation
    
    async def test_safety_systems(self):
        """Test that safety systems are operational"""
        
        try:
            # Test emergency coordinator
            from core.emergency_stop_coordinator import emergency_coordinator
            
            if emergency_coordinator.is_initialized():
                print("✅ Emergency coordinator is initialized")
            else:
                print("❌ Emergency coordinator not initialized")
                return False
            
            # Test execution engine
            from core.unified_execution_engine import unified_execution_engine
            
            status = unified_execution_engine.get_execution_status()
            
            if status['enforce_limit_orders_only']:
                print("✅ LIMIT orders enforcement is active")
            else:
                print("❌ LIMIT orders enforcement is disabled")
                return False
            
            # Test risk management
            from core.dynamic_risk_manager import dynamic_risk_manager
            
            current_params = dynamic_risk_manager.get_current_parameters()
            
            if current_params.max_trading_capital <= 1000:
                print(f"✅ Trading capital limit: ${current_params.max_trading_capital}")
            else:
                print(f"⚠️ High trading capital limit: ${current_params.max_trading_capital}")
            
            print("✅ Safety systems are operational")
            return True
            
        except Exception as e:
            print(f"❌ Safety systems test failed: {e}")
            return False
    
    def test_configuration_files(self):
        """Test that configuration files are accessible"""
        
        config_files = [
            "credentials.py",
            "config/trading_config.yaml"
        ]
        
        for config_file in config_files:
            if os.path.exists(config_file):
                print(f"✅ Configuration file found: {config_file}")
            else:
                print(f"⚠️ Configuration file missing: {config_file}")
        
        return True
    
    def generate_test_report(self):
        """Generate comprehensive test report"""
        
        print("\n" + "=" * 60)
        print("UNIFIED SYSTEM TEST REPORT")
        print("=" * 60)
        
        print(f"📊 SUMMARY:")
        print(f"   Total Tests: {self.total_tests}")
        print(f"   Passed: {self.passed_tests}")
        print(f"   Failed: {self.failed_tests}")
        print(f"   Success Rate: {(self.passed_tests/self.total_tests)*100:.1f}%")
        
        print(f"\n📋 DETAILED RESULTS:")
        for test_name, result in self.test_results.items():
            status_icon = "✅" if result == "PASSED" else "❌"
            print(f"   {status_icon} {test_name}: {result}")
        
        # Overall assessment
        success_rate = (self.passed_tests / self.total_tests) * 100
        
        print(f"\n🎯 OVERALL ASSESSMENT:")
        if success_rate >= 90:
            print("🎉 EXCELLENT - System is ready for deployment")
        elif success_rate >= 75:
            print("✅ GOOD - System is mostly ready with minor issues")
        elif success_rate >= 50:
            print("⚠️ FAIR - System needs attention before deployment")
        else:
            print("❌ POOR - System requires significant fixes")
        
        print(f"\n📅 Test completed: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        return success_rate >= 75
    
    async def run_all_tests(self):
        """Run all tests"""
        
        print("🧪 EPINNOX UNIFIED SYSTEM TESTING")
        print("=" * 50)
        print("Testing all components of the unified launcher system...")
        
        # Directory and file structure tests
        self.run_test("Directory Structure Cleanup", self.test_directory_structure)
        self.run_test("Unified Launcher Syntax", self.test_unified_launcher_syntax)
        self.run_test("GUI Components Syntax", self.test_gui_components_syntax)
        self.run_test("Configuration Files", self.test_configuration_files)
        
        # Component import tests
        await self.run_async_test("Core Component Imports", self.test_core_component_imports)
        
        # Launcher functionality tests
        self.run_test("Launcher Help Output", self.test_launcher_help_output)
        await self.run_async_test("Launcher Initialization", self.test_launcher_initialization)
        
        # System functionality tests
        self.run_test("PyQt5 Availability", self.test_pyqt_availability)
        await self.run_async_test("Safety Systems", self.test_safety_systems)
        
        # Generate report
        return self.generate_test_report()

async def main():
    """Main testing function"""
    
    try:
        print("🚀 STARTING UNIFIED SYSTEM VALIDATION")
        print("⚠️ This will test all components of the unified launcher")
        print("=" * 60)
        
        # Create tester
        tester = UnifiedSystemTester()
        
        # Run all tests
        success = await tester.run_all_tests()
        
        if success:
            print("\n🎉 UNIFIED SYSTEM VALIDATION SUCCESSFUL")
            print("✅ System is ready for use")
            return 0
        else:
            print("\n⚠️ UNIFIED SYSTEM VALIDATION ISSUES DETECTED")
            print("🔧 Review failed tests and fix issues")
            return 1
        
    except Exception as e:
        print(f"\n❌ TESTING ERROR: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
