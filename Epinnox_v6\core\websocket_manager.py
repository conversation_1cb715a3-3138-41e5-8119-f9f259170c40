#!/usr/bin/env python3
"""
WEBSOCKET MANAGER
Robust WebSocket connection management for real-time market data

FEATURES:
- Automatic reconnection with exponential backoff
- Connection health monitoring
- Graceful fallback to REST API when WebSocket unavailable
- Thread-safe operation
- Comprehensive error handling
"""

import asyncio
import websockets
import json
import logging
import time
import threading
from typing import Dict, Any, Optional, Callable, List
from datetime import datetime, timedelta
from enum import Enum
import ssl


logger = logging.getLogger(__name__)


class ConnectionState(Enum):
    """WebSocket connection states"""
    DISCONNECTED = "disconnected"
    CONNECTING = "connecting"
    CONNECTED = "connected"
    RECONNECTING = "reconnecting"
    FAILED = "failed"


class WebSocketManager:
    """
    CRITICAL: WebSocket connection manager for real-time data
    
    Manages WebSocket connections with automatic reconnection,
    health monitoring, and fallback mechanisms.
    """
    
    def __init__(self, exchange_name: str = "htx"):
        self.exchange_name = exchange_name
        self.connection_state = ConnectionState.DISCONNECTED
        self.websocket = None
        self.connection_url = None
        
        # Connection management
        self.max_reconnect_attempts = 10
        self.reconnect_delay = 1.0
        self.max_reconnect_delay = 60.0
        self.ping_interval = 30
        self.ping_timeout = 10
        
        # Subscriptions and callbacks
        self.subscriptions = {}
        self.message_callbacks = {}
        self.connection_callbacks = []
        
        # Threading
        self.connection_lock = threading.Lock()
        self.running = False
        self.connection_task = None
        
        # Statistics
        self.connection_attempts = 0
        self.successful_connections = 0
        self.last_connection_time = None
        self.last_disconnect_time = None
        self.total_messages_received = 0
        
        # Configure exchange-specific settings
        self._configure_exchange_settings()
        
        logger.info(f"WebSocket Manager initialized for {exchange_name}")
    
    def _configure_exchange_settings(self):
        """Configure exchange-specific WebSocket settings"""
        
        if self.exchange_name.lower() == "htx":
            self.connection_url = "wss://api.hbdm.com/ws"
            self.ping_interval = 20  # HTX requires frequent pings
        elif self.exchange_name.lower() == "binance":
            self.connection_url = "wss://fstream.binance.com/ws"
            self.ping_interval = 30
        else:
            # Generic settings
            self.connection_url = None
            logger.warning(f"Unknown exchange {self.exchange_name}, WebSocket disabled")
    
    async def connect(self) -> bool:
        """
        Establish WebSocket connection
        
        Returns:
            True if connection successful
        """
        
        if not self.connection_url:
            logger.warning("No WebSocket URL configured - WebSocket disabled")
            return False
        
        with self.connection_lock:
            if self.connection_state == ConnectionState.CONNECTED:
                return True
            
            self.connection_state = ConnectionState.CONNECTING
            self.connection_attempts += 1
        
        try:
            logger.info(f"Connecting to WebSocket: {self.connection_url}")
            
            # Create SSL context for secure connections
            ssl_context = ssl.create_default_context()
            ssl_context.check_hostname = False
            ssl_context.verify_mode = ssl.CERT_NONE
            
            # Establish WebSocket connection
            self.websocket = await websockets.connect(
                self.connection_url,
                ssl=ssl_context,
                ping_interval=self.ping_interval,
                ping_timeout=self.ping_timeout,
                close_timeout=10
            )
            
            with self.connection_lock:
                self.connection_state = ConnectionState.CONNECTED
                self.successful_connections += 1
                self.last_connection_time = datetime.now()
            
            logger.info("WebSocket connection established successfully")
            
            # Notify connection callbacks
            for callback in self.connection_callbacks:
                try:
                    await callback(True)
                except Exception as e:
                    logger.error(f"Error in connection callback: {e}")
            
            # Start message handling
            if not self.connection_task:
                self.connection_task = asyncio.create_task(self._handle_messages())
            
            return True
            
        except Exception as e:
            logger.error(f"WebSocket connection failed: {e}")
            
            with self.connection_lock:
                self.connection_state = ConnectionState.FAILED
                self.last_disconnect_time = datetime.now()
            
            return False
    
    async def disconnect(self):
        """Gracefully disconnect WebSocket"""
        
        self.running = False
        
        with self.connection_lock:
            if self.websocket and not self.websocket.closed:
                try:
                    await self.websocket.close()
                    logger.info("WebSocket disconnected gracefully")
                except Exception as e:
                    logger.error(f"Error during WebSocket disconnect: {e}")
            
            self.connection_state = ConnectionState.DISCONNECTED
            self.websocket = None
            self.last_disconnect_time = datetime.now()
        
        # Cancel connection task
        if self.connection_task:
            self.connection_task.cancel()
            self.connection_task = None
        
        # Notify connection callbacks
        for callback in self.connection_callbacks:
            try:
                await callback(False)
            except Exception as e:
                logger.error(f"Error in disconnection callback: {e}")
    
    async def _handle_messages(self):
        """Handle incoming WebSocket messages"""
        
        self.running = True
        
        try:
            while self.running and self.websocket and not self.websocket.closed:
                try:
                    # Receive message with timeout
                    message = await asyncio.wait_for(
                        self.websocket.recv(),
                        timeout=self.ping_timeout + 5
                    )
                    
                    self.total_messages_received += 1
                    
                    # Process message
                    await self._process_message(message)
                    
                except asyncio.TimeoutError:
                    logger.warning("WebSocket message timeout - checking connection")
                    if not await self._check_connection_health():
                        break
                
                except websockets.exceptions.ConnectionClosed:
                    logger.warning("WebSocket connection closed")
                    break
                
                except Exception as e:
                    logger.error(f"Error handling WebSocket message: {e}")
                    await asyncio.sleep(1)
        
        except Exception as e:
            logger.error(f"Error in message handler: {e}")
        
        finally:
            # Connection lost - attempt reconnection
            if self.running:
                await self._handle_connection_loss()
    
    async def _process_message(self, message: str):
        """Process incoming WebSocket message"""
        
        try:
            # Parse JSON message
            data = json.loads(message)
            
            # Handle ping/pong for HTX
            if self.exchange_name.lower() == "htx" and "ping" in data:
                pong_message = {"pong": data["ping"]}
                await self.websocket.send(json.dumps(pong_message))
                return
            
            # Route message to appropriate callback
            message_type = self._identify_message_type(data)
            
            if message_type in self.message_callbacks:
                for callback in self.message_callbacks[message_type]:
                    try:
                        await callback(data)
                    except Exception as e:
                        logger.error(f"Error in message callback: {e}")
        
        except json.JSONDecodeError:
            logger.warning(f"Invalid JSON message received: {message[:100]}...")
        except Exception as e:
            logger.error(f"Error processing message: {e}")
    
    def _identify_message_type(self, data: Dict[str, Any]) -> str:
        """Identify the type of WebSocket message"""
        
        # HTX message identification
        if "ch" in data:
            return "market_data"
        elif "op" in data:
            return "operation_response"
        elif "tick" in data:
            return "ticker"
        
        # Generic identification
        if "symbol" in data and "price" in data:
            return "price_update"
        elif "type" in data:
            return data["type"]
        
        return "unknown"
    
    async def _check_connection_health(self) -> bool:
        """Check WebSocket connection health"""
        
        try:
            if not self.websocket or self.websocket.closed:
                return False
            
            # Send ping to check connection
            await self.websocket.ping()
            return True
            
        except Exception:
            return False
    
    async def _handle_connection_loss(self):
        """Handle WebSocket connection loss with reconnection"""
        
        with self.connection_lock:
            self.connection_state = ConnectionState.RECONNECTING
            self.last_disconnect_time = datetime.now()
        
        logger.warning("WebSocket connection lost - attempting reconnection")
        
        # Exponential backoff reconnection
        delay = self.reconnect_delay
        attempts = 0
        
        while self.running and attempts < self.max_reconnect_attempts:
            attempts += 1
            
            logger.info(f"Reconnection attempt {attempts}/{self.max_reconnect_attempts}")
            
            await asyncio.sleep(delay)
            
            if await self.connect():
                logger.info("WebSocket reconnection successful")
                return
            
            # Exponential backoff
            delay = min(delay * 2, self.max_reconnect_delay)
        
        logger.error("WebSocket reconnection failed - giving up")
        
        with self.connection_lock:
            self.connection_state = ConnectionState.FAILED
    
    async def subscribe(self, subscription: Dict[str, Any]) -> bool:
        """
        Subscribe to WebSocket data stream
        
        Args:
            subscription: Subscription parameters
            
        Returns:
            True if subscription successful
        """
        
        if self.connection_state != ConnectionState.CONNECTED:
            logger.warning("Cannot subscribe - WebSocket not connected")
            return False
        
        try:
            # Store subscription for reconnection
            sub_id = subscription.get("id", str(len(self.subscriptions)))
            self.subscriptions[sub_id] = subscription
            
            # Send subscription message
            await self.websocket.send(json.dumps(subscription))
            
            logger.info(f"WebSocket subscription sent: {sub_id}")
            return True
            
        except Exception as e:
            logger.error(f"WebSocket subscription failed: {e}")
            return False
    
    def add_message_callback(self, message_type: str, callback: Callable):
        """Add callback for specific message type"""
        
        if message_type not in self.message_callbacks:
            self.message_callbacks[message_type] = []
        
        self.message_callbacks[message_type].append(callback)
        logger.info(f"Message callback added for type: {message_type}")
    
    def add_connection_callback(self, callback: Callable):
        """Add callback for connection state changes"""
        
        self.connection_callbacks.append(callback)
        logger.info("Connection callback added")
    
    def get_connection_status(self) -> Dict[str, Any]:
        """Get WebSocket connection status"""
        
        return {
            "state": self.connection_state.value,
            "connected": self.connection_state == ConnectionState.CONNECTED,
            "connection_attempts": self.connection_attempts,
            "successful_connections": self.successful_connections,
            "last_connection_time": self.last_connection_time,
            "last_disconnect_time": self.last_disconnect_time,
            "total_messages_received": self.total_messages_received,
            "active_subscriptions": len(self.subscriptions),
            "url": self.connection_url
        }
    
    def is_connected(self) -> bool:
        """Check if WebSocket is connected"""
        return self.connection_state == ConnectionState.CONNECTED
    
    async def start(self) -> bool:
        """Start WebSocket manager"""
        
        logger.info("Starting WebSocket manager...")
        
        if not self.connection_url:
            logger.warning("WebSocket disabled - no URL configured")
            return False
        
        success = await self.connect()
        
        if success:
            logger.info("WebSocket manager started successfully")
        else:
            logger.warning("WebSocket manager failed to start")
        
        return success
    
    async def stop(self):
        """Stop WebSocket manager"""
        
        logger.info("Stopping WebSocket manager...")
        await self.disconnect()
        logger.info("WebSocket manager stopped")


# Global WebSocket manager instance
websocket_manager = WebSocketManager()


# Fallback functions for when WebSocket is not available
def get_websocket_status() -> Dict[str, Any]:
    """Get WebSocket status (fallback function)"""
    return {
        "available": False,
        "state": "disabled",
        "reason": "WebSocket manager not initialized"
    }


async def ensure_websocket_connection() -> bool:
    """Ensure WebSocket connection is established (fallback function)"""
    try:
        return await websocket_manager.start()
    except Exception as e:
        logger.warning(f"WebSocket connection failed: {e}")
        return False


def is_websocket_available() -> bool:
    """Check if WebSocket is available"""
    return websocket_manager.is_connected()


# Export main components
__all__ = [
    'WebSocketManager',
    'websocket_manager',
    'ConnectionState',
    'get_websocket_status',
    'ensure_websocket_connection',
    'is_websocket_available'
]
