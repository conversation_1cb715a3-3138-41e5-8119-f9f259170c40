2025-07-13 14:12:55,422 - core.autonomous_trading_orchestrator - INFO - Autonomous Trading Orchestrator initialized in paper mode
2025-07-13 14:13:35,532 - core.autonomous_trading_orchestrator - INFO - Autonomous Trading Orchestrator initialized in paper mode
2025-07-13 14:13:35,532 - core.autonomous_trading_orchestrator - INFO - Initializing autonomous trading system...
2025-07-13 14:13:35,532 - core.error_recovery_system - INFO - [ERROR_RECOVERY] Error Recovery System initialized
2025-07-13 14:13:35,532 - core.error_recovery_system - INFO - [ERROR_RECOVERY] Health monitoring started
2025-07-13 14:13:35,533 - core.error_recovery_system - INFO - [ERROR_RECOVERY] Error Recovery System ready
2025-07-13 14:13:35,567 - core.websocket_manager - INFO - WebSocket manager initialized for htx_market: wss://api.huobi.pro/ws
2025-07-13 14:13:35,567 - core.websocket_manager - INFO - WebSocket manager initialized for htx_futures: wss://api.hbdm.com/swap-ws
2025-07-13 14:13:35,568 - core.websocket_manager - INFO - WebSocket manager initialized for htx_backup: wss://api-aws.huobi.pro/ws
2025-07-13 14:13:35,568 - data.market_data_manager - INFO - Initialized 3 WebSocket endpoints
2025-07-13 14:13:35,568 - data.market_data_manager - INFO - Market data manager initialized for htx
2025-07-13 14:13:35,568 - data.market_data_manager - INFO - Starting market data manager...
2025-07-13 14:13:35,568 - data.market_data_manager - INFO - Started WebSocket connection: market
2025-07-13 14:13:35,568 - data.market_data_manager - INFO - Started WebSocket connection: futures
2025-07-13 14:13:35,568 - data.market_data_manager - INFO - Started WebSocket connection: backup
2025-07-13 14:13:35,568 - core.websocket_manager - INFO - htx_market: Connecting to wss://api.huobi.pro/ws (attempt 1)
2025-07-13 14:13:35,618 - core.websocket_manager - INFO - htx_futures: Connecting to wss://api.hbdm.com/swap-ws (attempt 1)
2025-07-13 14:13:35,623 - core.websocket_manager - INFO - htx_backup: Connecting to wss://api-aws.huobi.pro/ws (attempt 1)
2025-07-13 14:13:37,601 - core.unified_llm_manager - INFO - [UNIFIED_LLM] Unified LLM Manager initialized
2025-07-13 14:13:37,601 - core.standardized_prompt_handler - INFO - [PROMPT_HANDLER] Standardized prompt handler initialized
2025-07-13 14:13:37,602 - core.autonomous_llm_integration - INFO - [AUTONOMOUS_LLM] Autonomous LLM Integration initialized
2025-07-13 14:13:37,602 - core.autonomous_llm_integration - INFO - [AUTONOMOUS_LLM] Initializing autonomous LLM integration...
2025-07-13 14:13:37,602 - core.unified_llm_manager - INFO - [UNIFIED_LLM] Initializing LLM providers...
2025-07-13 14:13:37,621 - config.autonomous_config - INFO - Configuration loaded from configs/autonomous_trading.yaml
2025-07-13 14:13:37,632 - llama.runner - INFO - Loaded OpenAI API key from models_config.yaml
2025-07-13 14:13:37,632 - llama.runner - INFO - Using model path: C:\Users\<USER>\.lmstudio\models\lmstudio-community\Phi-3.1-mini-128k-instruct-GGUF\Phi-3.1-mini-128k-instruct-Q4_K_M.gguf
2025-07-13 14:13:37,632 - llama.runner - INFO - Using guard path: C:\Users\<USER>\Documents\dev\Epinnox_v6\models\Llama-Guard-3-8B.gguf
2025-07-13 14:13:37,633 - llama.runner - INFO - Using binary path: C:\Users\<USER>\Documents\dev\llama.cpp\main
2025-07-13 14:13:37,639 - core.unified_llm_manager - INFO - [UNIFIED_LLM] ChatGPT provider initialized
2025-07-13 14:13:37,682 - llama.lmstudio_runner - INFO - Loaded LMStudio configuration from models_config.yaml
2025-07-13 14:13:39,773 - llama.lmstudio_runner - INFO - Discovered 8 models: ['openthinker3-7b', 'mistral-7b-claude-chat', 'fine_tuned_qwen1.7b', 'tsfc-model-0.0.1', 'codestral-22b-v0.1', 'phi-3.1-mini-128k-instruct', 'deepseek-r1-distill-qwen-7b', 'text-embedding-nomic-embed-text-v1.5']
2025-07-13 14:13:39,778 - llama.lmstudio_runner - INFO - Selected preferred model from config: phi-3.1-mini-128k-instruct
2025-07-13 14:13:39,778 - llama.lmstudio_runner - INFO - Initialized LMStudio with model: phi-3.1-mini-128k-instruct
2025-07-13 14:13:41,834 - core.unified_llm_manager - INFO - [UNIFIED_LLM] LMStudio provider initialized
2025-07-13 14:13:41,840 - core.unified_llm_manager - WARNING - [UNIFIED_LLM] Failed to initialize Transformers: __init__() missing 1 required positional argument: 'model_path'
2025-07-13 14:13:41,841 - core.unified_llm_manager - WARNING - [UNIFIED_LLM] Failed to initialize Llama: __init__() missing 1 required positional argument: 'bin_path'
2025-07-13 14:13:41,843 - llama.mock_runner - INFO - Initialized MockRunner with personality: {'caution': 0.5954731772878594, 'optimism': 0.44913799570978485, 'consistency': 0.8847057833001477, 'detail_level': 0.8926203758126756, 'confidence_bias': -0.47767021795988374}
2025-07-13 14:13:41,843 - core.unified_llm_manager - INFO - [UNIFIED_LLM] Mock provider initialized
2025-07-13 14:13:41,843 - core.unified_llm_manager - INFO - [UNIFIED_LLM] Selected provider: mock (score: 109.90)
2025-07-13 14:13:41,843 - core.unified_llm_manager - INFO - [UNIFIED_LLM] Health monitoring started
2025-07-13 14:13:41,843 - core.unified_llm_manager - INFO - [UNIFIED_LLM] Initialized with 3 providers
2025-07-13 14:13:41,843 - core.unified_llm_manager - INFO - [UNIFIED_LLM] Current provider: LLMProvider.MOCK
2025-07-13 14:13:41,843 - core.autonomous_llm_integration - INFO - [AUTONOMOUS_LLM] Autonomous LLM integration initialized successfully
2025-07-13 14:13:41,863 - utils.cache_manager - INFO - Cache manager initialized: cache, max_age=24h, max_size=100MB
2025-07-13 14:13:41,863 - utils.cache_manager - INFO - Cache manager initialized: cache, max_age=24h, max_size=100MB
2025-07-13 14:13:41,863 - core.llm_orchestrator - INFO - LLM Prompt Orchestrator initialized
2025-07-13 14:13:48,400 - ml.models - INFO - TensorFlow available for LSTM models
2025-07-13 14:13:48,400 - ml.models - INFO - ML Model Manager initialized
2025-07-13 14:13:48,721 - absl - WARNING - Compiled the loaded model, but the compiled metrics have yet to be built. `model.compile_metrics` will be empty until you train or evaluate the model.
2025-07-13 14:13:48,768 - ml.models - ERROR - Error loading models: Could not locate function 'mse'. Make sure custom classes are decorated with `@keras.saving.register_keras_serializable()`. Full object config: {'module': 'keras.metrics', 'class_name': 'function', 'config': 'mse', 'registered_name': 'mse'}
2025-07-13 14:13:48,772 - core.autonomous_position_manager - INFO - [POSITION_MANAGER] Autonomous Position Manager initialized
2025-07-13 14:13:48,777 - core.adaptive_risk - INFO - TA-Lib not available, using pandas-ta fallback
2025-07-13 14:13:48,777 - core.adaptive_risk - INFO - Initialized adaptive risk manager
2025-07-13 14:13:48,784 - execution.unified_execution_engine - INFO - Unified execution engine initialized in paper mode
2025-07-13 14:13:48,785 - execution.execution_adapter - INFO - Execution adapter initialized in paper mode
2025-07-13 14:13:48,789 - core.autonomous_position_manager - INFO - [POSITION_MANAGER] Position monitoring started (interval: 5s)
2025-07-13 14:13:48,789 - core.autonomous_position_manager - INFO - [POSITION_MANAGER] Autonomous Position Manager ready
2025-07-13 14:13:48,792 - core.position_watchdog - INFO - [WATCHDOG] Position Watchdog initialized
2025-07-13 14:13:48,792 - core.position_watchdog - INFO - [WATCHDOG] Monitoring started (scan: 10s, health: 30s)
2025-07-13 14:13:48,792 - core.position_watchdog - INFO - [WATCHDOG] Position Watchdog ready
2025-07-13 14:13:48,797 - portfolio.portfolio_manager - INFO - Portfolio manager initialized with $10000.00 balance
2025-07-13 14:13:48,799 - core.autonomous_trading_orchestrator - WARNING - No market data available - this is expected during testing
2025-07-13 14:13:48,812 - core.autonomous_trading_orchestrator - WARNING - No market data available
2025-07-13 14:13:48,814 - portfolio.portfolio_manager - WARNING - [PORTFOLIO] Closing all positions: Emergency stop
2025-07-13 14:13:48,814 - portfolio.portfolio_manager - WARNING - [PORTFOLIO] Closed 0 positions due to: Emergency stop
2025-07-13 14:13:48,814 - execution.unified_execution_engine - INFO - Cancelled 0 orders. Reason: Emergency stop
2025-07-13 14:13:48,814 - core.autonomous_trading_orchestrator - CRITICAL - Emergency stop procedure completed
2025-07-13 14:13:48,814 - core.autonomous_trading_orchestrator - INFO - Autonomous trading stopped
2025-07-13 14:13:48,821 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:13:48,821 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:13:48,821 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:13:48,822 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:13:48,822 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:13:48,822 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:13:48,822 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:13:48,822 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:13:48,822 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:13:48,822 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:13:48,822 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:13:48,822 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:13:48,822 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:13:48,822 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:13:48,822 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:13:48,822 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:13:48,822 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:13:48,822 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:13:48,822 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:13:48,822 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:13:48,824 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:13:48,824 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:13:48,824 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:13:48,824 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:13:48,824 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:13:48,824 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:13:48,824 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:13:48,825 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:13:48,825 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:13:48,825 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:13:48,825 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:13:48,825 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:13:48,825 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:13:48,825 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:13:48,825 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:13:48,825 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:13:48,825 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:13:48,825 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:13:48,826 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:13:48,826 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:13:48,826 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:13:48,826 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:13:48,826 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:13:48,826 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:13:48,826 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:13:48,826 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:13:48,826 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:13:48,826 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:13:48,826 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:13:48,826 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:13:48,826 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:13:48,827 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:13:48,827 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:13:48,827 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:13:48,827 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:13:48,827 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:13:48,827 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:13:48,827 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:13:48,827 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:13:48,827 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:13:48,827 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:13:48,827 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:13:48,827 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:13:48,828 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:13:48,828 - core.websocket_manager - ERROR - htx_futures: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:13:48,828 - core.websocket_manager - ERROR - htx_futures: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:13:48,828 - core.websocket_manager - ERROR - htx_futures: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:13:48,828 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:13:48,828 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:13:48,829 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:13:48,829 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:13:48,829 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:13:48,829 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:13:48,829 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:13:48,829 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:13:48,829 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:13:48,829 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:13:48,829 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:13:48,829 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:13:48,830 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:13:48,830 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:13:48,830 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:13:48,830 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:13:49,002 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:13:49,003 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:13:49,007 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:13:49,007 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:13:49,044 - core.websocket_manager - ERROR - htx_futures: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:13:49,268 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:13:49,269 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:13:49,870 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:13:49,871 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:13:49,970 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:13:49,970 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:13:49,971 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:13:49,973 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:13:50,070 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:13:50,070 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:13:50,169 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:13:50,170 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:13:50,258 - core.websocket_manager - ERROR - htx_futures: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:13:50,267 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:13:50,268 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:13:50,368 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:13:50,369 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:13:50,468 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:13:50,469 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:13:50,969 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:13:50,971 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:13:51,067 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:13:51,068 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:13:51,167 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:13:51,168 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:13:51,267 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:13:51,268 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:13:51,368 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:13:51,368 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:13:51,568 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:13:51,569 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:13:51,667 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:13:51,669 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:13:52,108 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:13:52,260 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:13:53,667 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:13:53,667 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:13:54,150 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:13:54,297 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:13:55,157 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:13:55,169 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:13:55,170 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:13:55,260 - core.websocket_manager - ERROR - htx_futures: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:13:55,267 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:13:55,268 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:13:55,303 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:13:55,367 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:13:55,368 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:13:55,767 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:13:55,768 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:13:55,867 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:13:55,868 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:13:56,182 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:13:56,318 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:13:56,367 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:13:56,368 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:13:57,067 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:13:57,068 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:13:57,167 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:13:57,168 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:13:57,200 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:13:57,336 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:13:57,468 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:13:57,469 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:13:57,568 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:13:57,569 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:13:57,669 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:13:57,670 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:13:57,766 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:13:57,767 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:13:57,969 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:13:57,970 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:13:58,217 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:13:58,355 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:13:58,367 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:13:58,368 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:13:58,469 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:13:58,470 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:13:58,567 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:13:58,568 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:13:58,668 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:13:58,669 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:13:58,869 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:13:58,870 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:13:59,231 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:13:59,382 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:14:00,168 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:14:00,169 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:14:00,239 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:14:00,264 - core.websocket_manager - ERROR - htx_futures: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:14:00,268 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:14:00,269 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:14:00,269 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:14:00,270 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:14:00,367 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:14:00,368 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:14:00,369 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:14:00,369 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:14:00,389 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:14:00,468 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:14:00,469 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:14:00,667 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:14:00,668 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:14:00,767 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:14:00,769 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:14:01,067 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:14:01,068 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:14:01,167 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:14:01,168 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:14:01,255 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:14:01,380 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:14:01,381 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:14:01,384 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:14:01,385 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:14:01,396 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:14:01,770 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:14:01,771 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:14:01,773 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:14:01,778 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:14:01,872 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:14:01,872 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:14:01,873 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:14:01,874 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:14:01,969 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:14:01,970 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:14:01,970 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:14:01,971 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:14:02,070 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:14:02,071 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:14:02,072 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:14:02,072 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:14:02,171 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:14:02,172 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:14:02,173 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:14:02,173 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:14:02,272 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:14:02,274 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:14:02,296 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:14:02,371 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:14:02,372 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:14:02,406 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:14:02,471 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:14:02,471 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:14:02,471 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:14:02,472 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:14:02,570 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:14:02,570 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:14:02,571 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:14:02,571 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:14:02,769 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:14:02,770 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:14:02,972 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:14:02,973 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:14:03,170 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:14:03,171 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:14:03,270 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:14:03,272 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:14:03,325 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:14:03,420 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:14:03,470 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:14:03,470 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:14:03,567 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:14:03,569 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:14:04,168 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:14:04,169 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:14:04,270 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:14:04,270 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:14:04,345 - core.websocket_manager - WARNING - htx_market: Connection closed
2025-07-13 14:14:04,346 - core.websocket_manager - INFO - htx_market: Reconnecting in 5 seconds (attempt 1)
2025-07-13 14:14:04,369 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:14:04,426 - core.websocket_manager - WARNING - htx_backup: Connection closed
2025-07-13 14:14:04,426 - core.websocket_manager - INFO - htx_backup: Reconnecting in 5 seconds (attempt 1)
2025-07-13 14:14:05,282 - core.websocket_manager - ERROR - htx_futures: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:14:09,296 - core.websocket_manager - WARNING - htx_futures: Connection closed
2025-07-13 14:14:09,296 - core.websocket_manager - INFO - htx_futures: Reconnecting in 5 seconds (attempt 1)
2025-07-13 14:14:09,349 - core.websocket_manager - INFO - htx_market: Connecting to wss://api.huobi.pro/ws (attempt 1)
2025-07-13 14:14:09,413 - core.websocket_manager - INFO - htx_backup: Connecting to wss://api-aws.huobi.pro/ws (attempt 1)
2025-07-13 14:14:11,032 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:14:12,893 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:14:14,311 - core.websocket_manager - INFO - htx_futures: Connecting to wss://api.hbdm.com/swap-ws (attempt 1)
2025-07-13 14:14:15,948 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:14:16,138 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:14:16,287 - core.websocket_manager - ERROR - htx_futures: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:14:16,968 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:14:17,152 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:14:17,985 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:14:18,183 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:14:19,003 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:14:19,196 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:14:20,013 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:14:20,211 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:14:21,032 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:14:21,231 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:14:21,290 - core.websocket_manager - ERROR - htx_futures: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:14:22,058 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:14:22,269 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:14:23,110 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:14:23,289 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:14:24,134 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:14:24,348 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:14:25,147 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:14:25,362 - core.websocket_manager - WARNING - htx_market: Connection closed
2025-07-13 14:14:25,363 - core.websocket_manager - INFO - htx_market: Reconnecting in 5 seconds (attempt 1)
2025-07-13 14:14:26,166 - core.websocket_manager - WARNING - htx_backup: Connection closed
2025-07-13 14:14:26,166 - core.websocket_manager - INFO - htx_backup: Reconnecting in 5 seconds (attempt 1)
2025-07-13 14:14:26,296 - core.websocket_manager - ERROR - htx_futures: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:14:30,368 - core.websocket_manager - INFO - htx_market: Connecting to wss://api.huobi.pro/ws (attempt 1)
2025-07-13 14:14:31,178 - core.websocket_manager - INFO - htx_backup: Connecting to wss://api-aws.huobi.pro/ws (attempt 1)
2025-07-13 14:14:31,299 - core.websocket_manager - ERROR - htx_futures: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:14:34,977 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:14:35,303 - core.websocket_manager - WARNING - htx_futures: Connection closed
2025-07-13 14:14:35,304 - core.websocket_manager - INFO - htx_futures: Reconnecting in 5 seconds (attempt 1)
2025-07-13 14:14:35,642 - data.market_data_manager - WARNING - Stale data detected for symbols: ['DOGE/USDT:USDT', 'BTC/USDT:USDT']
2025-07-13 14:14:35,827 - data.market_data_manager - WARNING - Unhealthy WebSocket connection: futures
2025-07-13 14:14:35,954 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:14:36,970 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:14:37,023 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:14:37,990 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:14:38,046 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:14:39,014 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:14:39,078 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:14:40,049 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:14:40,105 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:14:40,316 - core.websocket_manager - INFO - htx_futures: Connecting to wss://api.hbdm.com/swap-ws (attempt 1)
2025-07-13 14:14:41,068 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:14:41,149 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:14:42,091 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:14:42,163 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:14:43,092 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:14:43,166 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:14:44,094 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:14:44,178 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:14:45,100 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:14:45,186 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:14:45,311 - core.websocket_manager - ERROR - htx_futures: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:14:46,127 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:14:46,212 - core.websocket_manager - WARNING - htx_market: Connection closed
2025-07-13 14:14:46,212 - core.websocket_manager - INFO - htx_market: Reconnecting in 5 seconds (attempt 1)
2025-07-13 14:14:47,132 - core.websocket_manager - WARNING - htx_backup: Connection closed
2025-07-13 14:14:47,132 - core.websocket_manager - INFO - htx_backup: Reconnecting in 5 seconds (attempt 1)
2025-07-13 14:14:48,832 - llama.chatgpt_runner - ERROR - ChatGPT inference failed
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\dev\Epinnox_v6\llama\chatgpt_runner.py", line 34, in run_inference
    raise ImportError("OpenAI package not available or client not initialized")
ImportError: OpenAI package not available or client not initialized
2025-07-13 14:14:48,832 - core.unified_llm_manager - ERROR - [UNIFIED_LLM] Provider call failed: OpenAI package not available or client not initialized
2025-07-13 14:15:04,599 - llama.mock_runner - INFO - Running mock inference
2025-07-13 14:15:04,599 - core.websocket_manager - INFO - htx_market: Connecting to wss://api.huobi.pro/ws (attempt 1)
2025-07-13 14:15:04,605 - core.websocket_manager - INFO - htx_backup: Connecting to wss://api-aws.huobi.pro/ws (attempt 1)
2025-07-13 14:15:04,611 - core.websocket_manager - ERROR - htx_futures: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:15:04,612 - core.websocket_manager - ERROR - htx_futures: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:15:04,612 - core.websocket_manager - ERROR - htx_futures: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:15:04,612 - core.websocket_manager - WARNING - htx_futures: Connection closed
2025-07-13 14:15:04,612 - core.websocket_manager - INFO - htx_futures: Reconnecting in 5 seconds (attempt 1)
2025-07-13 14:15:07,010 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:15:09,604 - core.websocket_manager - INFO - htx_futures: Connecting to wss://api.hbdm.com/swap-ws (attempt 1)
2025-07-13 14:15:10,530 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:15:11,079 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:15:11,555 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:15:12,103 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:15:12,121 - core.websocket_manager - ERROR - htx_futures: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:15:12,575 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:15:13,134 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:15:13,594 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:15:14,142 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:15:14,619 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:15:15,155 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:15:15,642 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:15:16,186 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:15:16,654 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:15:17,121 - core.websocket_manager - ERROR - htx_futures: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:15:17,228 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:15:17,689 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:15:18,278 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:15:18,730 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:15:19,321 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:15:19,772 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:15:20,355 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:15:20,826 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:15:21,355 - core.websocket_manager - WARNING - htx_backup: Connection closed
2025-07-13 14:15:21,356 - core.websocket_manager - INFO - htx_backup: Reconnecting in 5 seconds (attempt 1)
2025-07-13 14:15:21,833 - core.websocket_manager - WARNING - htx_market: Connection closed
2025-07-13 14:15:21,833 - core.websocket_manager - INFO - htx_market: Reconnecting in 5 seconds (attempt 1)
2025-07-13 14:15:22,127 - core.websocket_manager - ERROR - htx_futures: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:15:26,362 - core.websocket_manager - INFO - htx_backup: Connecting to wss://api-aws.huobi.pro/ws (attempt 1)
2025-07-13 14:15:26,845 - core.websocket_manager - INFO - htx_market: Connecting to wss://api.huobi.pro/ws (attempt 1)
2025-07-13 14:15:27,132 - core.websocket_manager - ERROR - htx_futures: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:15:30,011 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:15:31,139 - core.websocket_manager - WARNING - htx_futures: Connection closed
2025-07-13 14:15:31,140 - core.websocket_manager - INFO - htx_futures: Reconnecting in 5 seconds (attempt 1)
2025-07-13 14:15:31,959 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:15:32,980 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:15:33,089 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:15:34,001 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:15:34,108 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:15:35,021 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:15:35,131 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:15:36,019 - data.market_data_manager - WARNING - Stale data detected for symbols: ['DOGE/USDT:USDT', 'BTC/USDT:USDT']
2025-07-13 14:15:36,033 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:15:36,145 - core.websocket_manager - INFO - htx_futures: Connecting to wss://api.hbdm.com/swap-ws (attempt 1)
2025-07-13 14:15:36,176 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:15:36,208 - data.market_data_manager - WARNING - Unhealthy WebSocket connection: futures
2025-07-13 14:15:37,049 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:15:37,184 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:15:38,058 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:15:38,210 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:15:39,101 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:15:39,263 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:15:40,116 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:15:40,263 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:15:41,129 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:15:41,285 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:15:41,349 - core.websocket_manager - ERROR - htx_futures: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:15:42,143 - core.websocket_manager - WARNING - htx_backup: Connection closed
2025-07-13 14:15:42,143 - core.websocket_manager - INFO - htx_backup: Reconnecting in 5 seconds (attempt 1)
2025-07-13 14:15:42,306 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:15:43,313 - core.websocket_manager - WARNING - htx_market: Connection closed
2025-07-13 14:15:43,313 - core.websocket_manager - INFO - htx_market: Reconnecting in 5 seconds (attempt 1)
2025-07-13 14:15:46,356 - core.websocket_manager - ERROR - htx_futures: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:15:47,148 - core.websocket_manager - INFO - htx_backup: Connecting to wss://api-aws.huobi.pro/ws (attempt 1)
2025-07-13 14:15:48,311 - core.websocket_manager - INFO - htx_market: Connecting to wss://api.huobi.pro/ws (attempt 1)
2025-07-13 14:15:50,404 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:15:51,358 - core.websocket_manager - ERROR - htx_futures: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:15:53,311 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:15:53,445 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:15:54,358 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:15:54,459 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:15:55,389 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:15:55,498 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:15:56,362 - core.websocket_manager - ERROR - htx_futures: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:15:56,398 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:15:56,521 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:15:57,363 - core.websocket_manager - WARNING - htx_futures: Connection closed
2025-07-13 14:15:57,363 - core.websocket_manager - INFO - htx_futures: Reconnecting in 5 seconds (attempt 1)
2025-07-13 14:15:57,415 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:15:57,540 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:15:58,448 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:15:58,567 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:15:59,477 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:15:59,580 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:16:00,497 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:16:00,599 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:16:01,511 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:16:01,613 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:16:02,349 - core.websocket_manager - INFO - htx_futures: Connecting to wss://api.hbdm.com/swap-ws (attempt 1)
2025-07-13 14:16:02,526 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:16:02,635 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:16:03,376 - core.websocket_manager - ERROR - htx_futures: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:16:03,527 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:16:03,648 - core.websocket_manager - WARNING - htx_backup: Connection closed
2025-07-13 14:16:03,648 - core.websocket_manager - INFO - htx_backup: Reconnecting in 5 seconds (attempt 1)
2025-07-13 14:16:04,527 - core.websocket_manager - WARNING - htx_market: Connection closed
2025-07-13 14:16:04,528 - core.websocket_manager - INFO - htx_market: Reconnecting in 5 seconds (attempt 1)
2025-07-13 14:16:04,615 - llama.chatgpt_runner - ERROR - ChatGPT inference failed
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\dev\Epinnox_v6\llama\chatgpt_runner.py", line 34, in run_inference
    raise ImportError("OpenAI package not available or client not initialized")
ImportError: OpenAI package not available or client not initialized
2025-07-13 14:16:04,616 - core.unified_llm_manager - ERROR - [UNIFIED_LLM] Provider call failed: OpenAI package not available or client not initialized
2025-07-13 14:16:08,414 - llama.mock_runner - INFO - Running mock inference
2025-07-13 14:16:08,414 - core.websocket_manager - ERROR - htx_futures: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:16:08,656 - core.websocket_manager - INFO - htx_backup: Connecting to wss://api-aws.huobi.pro/ws (attempt 1)
2025-07-13 14:16:09,526 - core.websocket_manager - INFO - htx_market: Connecting to wss://api.huobi.pro/ws (attempt 1)
2025-07-13 14:16:11,316 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:16:13,382 - core.websocket_manager - ERROR - htx_futures: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:16:14,354 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:16:15,361 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:16:16,387 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:16:16,392 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:16:17,413 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:16:17,418 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:16:18,389 - core.websocket_manager - ERROR - htx_futures: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:16:18,431 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:16:18,444 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:16:19,438 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:16:19,467 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:16:20,445 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:16:20,491 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:16:21,469 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:16:21,499 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:16:22,494 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:16:22,516 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:16:23,394 - core.websocket_manager - WARNING - htx_futures: Connection closed
2025-07-13 14:16:23,395 - core.websocket_manager - INFO - htx_futures: Reconnecting in 5 seconds (attempt 1)
2025-07-13 14:16:23,514 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:16:23,540 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:16:24,522 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:16:24,572 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:16:25,536 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:16:25,581 - core.websocket_manager - WARNING - htx_backup: Connection closed
2025-07-13 14:16:25,581 - core.websocket_manager - INFO - htx_backup: Reconnecting in 5 seconds (attempt 1)
2025-07-13 14:16:26,543 - core.websocket_manager - WARNING - htx_market: Connection closed
2025-07-13 14:16:26,543 - core.websocket_manager - INFO - htx_market: Reconnecting in 5 seconds (attempt 1)
2025-07-13 14:16:28,391 - core.websocket_manager - INFO - htx_futures: Connecting to wss://api.hbdm.com/swap-ws (attempt 1)
2025-07-13 14:16:30,591 - core.websocket_manager - INFO - htx_backup: Connecting to wss://api-aws.huobi.pro/ws (attempt 1)
2025-07-13 14:16:31,547 - core.websocket_manager - INFO - htx_market: Connecting to wss://api.huobi.pro/ws (attempt 1)
2025-07-13 14:16:33,401 - core.websocket_manager - ERROR - htx_futures: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:16:33,531 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:16:35,098 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:16:36,399 - data.market_data_manager - WARNING - Stale data detected for symbols: ['DOGE/USDT:USDT', 'BTC/USDT:USDT']
2025-07-13 14:16:37,145 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:16:37,597 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:16:38,159 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:16:38,405 - core.websocket_manager - ERROR - htx_futures: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:16:38,608 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:16:39,167 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:16:39,615 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:16:40,189 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:16:40,630 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:16:41,203 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:16:41,660 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:16:42,228 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:16:42,680 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:16:43,241 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:16:43,409 - core.websocket_manager - ERROR - htx_futures: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:16:43,706 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:16:44,248 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:16:44,707 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:16:45,258 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:16:45,721 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:16:46,274 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:16:46,741 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:16:47,279 - core.websocket_manager - WARNING - htx_backup: Connection closed
2025-07-13 14:16:47,279 - core.websocket_manager - INFO - htx_backup: Reconnecting in 5 seconds (attempt 1)
2025-07-13 14:16:47,742 - core.websocket_manager - WARNING - htx_market: Connection closed
2025-07-13 14:16:47,742 - core.websocket_manager - INFO - htx_market: Reconnecting in 5 seconds (attempt 1)
2025-07-13 14:16:48,414 - core.websocket_manager - ERROR - htx_futures: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:16:49,420 - core.websocket_manager - WARNING - htx_futures: Connection closed
2025-07-13 14:16:49,420 - core.websocket_manager - INFO - htx_futures: Reconnecting in 5 seconds (attempt 1)
2025-07-13 14:16:52,294 - core.websocket_manager - INFO - htx_backup: Connecting to wss://api-aws.huobi.pro/ws (attempt 1)
2025-07-13 14:16:52,748 - core.websocket_manager - INFO - htx_market: Connecting to wss://api.huobi.pro/ws (attempt 1)
2025-07-13 14:16:54,416 - core.websocket_manager - INFO - htx_futures: Connecting to wss://api.hbdm.com/swap-ws (attempt 1)
2025-07-13 14:16:55,473 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:16:57,198 - core.websocket_manager - ERROR - htx_futures: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:16:57,769 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:16:58,508 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:16:58,784 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:16:59,529 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:16:59,798 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:17:00,545 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:17:00,805 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:17:01,575 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:17:01,818 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:17:02,202 - core.websocket_manager - ERROR - htx_futures: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:17:02,593 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:17:02,831 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:17:03,601 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:17:03,845 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:17:04,632 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:17:04,860 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:17:05,651 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:17:05,873 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:17:06,665 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:17:06,888 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:17:07,206 - core.websocket_manager - ERROR - htx_futures: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:17:07,684 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:17:07,901 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:17:08,430 - llama.chatgpt_runner - ERROR - ChatGPT inference failed
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\dev\Epinnox_v6\llama\chatgpt_runner.py", line 34, in run_inference
    raise ImportError("OpenAI package not available or client not initialized")
ImportError: OpenAI package not available or client not initialized
2025-07-13 14:17:08,430 - core.unified_llm_manager - ERROR - [UNIFIED_LLM] Provider call failed: OpenAI package not available or client not initialized
2025-07-13 14:17:08,430 - core.unified_llm_manager - WARNING - [UNIFIED_LLM] chatgpt marked as failed
2025-07-13 14:17:12,001 - llama.mock_runner - INFO - Running mock inference
2025-07-13 14:17:12,002 - core.websocket_manager - WARNING - htx_backup: Connection closed
2025-07-13 14:17:12,002 - core.websocket_manager - INFO - htx_backup: Reconnecting in 5 seconds (attempt 1)
2025-07-13 14:17:12,002 - core.websocket_manager - WARNING - htx_market: Connection closed
2025-07-13 14:17:12,002 - core.websocket_manager - INFO - htx_market: Reconnecting in 5 seconds (attempt 1)
2025-07-13 14:17:12,212 - core.websocket_manager - ERROR - htx_futures: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:17:15,216 - core.websocket_manager - WARNING - htx_futures: Connection closed
2025-07-13 14:17:15,216 - core.websocket_manager - INFO - htx_futures: Reconnecting in 5 seconds (attempt 1)
2025-07-13 14:17:17,003 - core.websocket_manager - INFO - htx_backup: Connecting to wss://api-aws.huobi.pro/ws (attempt 1)
2025-07-13 14:17:17,008 - core.websocket_manager - INFO - htx_market: Connecting to wss://api.huobi.pro/ws (attempt 1)
2025-07-13 14:17:18,788 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:17:20,165 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:17:20,212 - core.websocket_manager - INFO - htx_futures: Connecting to wss://api.hbdm.com/swap-ws (attempt 1)
2025-07-13 14:17:22,451 - core.websocket_manager - ERROR - htx_futures: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:17:23,264 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:17:23,857 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:17:24,292 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:17:24,873 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:17:25,307 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:17:25,895 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:17:26,330 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:17:26,925 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:17:27,348 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:17:27,943 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:17:28,363 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:17:28,944 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:17:29,372 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:17:29,951 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:17:30,394 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:17:30,970 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:17:31,410 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:17:31,994 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:17:32,452 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:17:33,001 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:17:33,451 - core.websocket_manager - WARNING - htx_backup: Connection closed
2025-07-13 14:17:33,452 - core.websocket_manager - INFO - htx_backup: Reconnecting in 5 seconds (attempt 1)
2025-07-13 14:17:34,013 - core.websocket_manager - WARNING - htx_market: Connection closed
2025-07-13 14:17:34,013 - core.websocket_manager - INFO - htx_market: Reconnecting in 5 seconds (attempt 1)
2025-07-13 14:17:36,995 - data.market_data_manager - WARNING - Stale data detected for symbols: ['DOGE/USDT:USDT', 'BTC/USDT:USDT']
2025-07-13 14:17:36,996 - data.market_data_manager - WARNING - Unhealthy WebSocket connection: market
2025-07-13 14:17:38,447 - core.websocket_manager - INFO - htx_backup: Connecting to wss://api-aws.huobi.pro/ws (attempt 1)
2025-07-13 14:17:39,023 - core.websocket_manager - INFO - htx_market: Connecting to wss://api.huobi.pro/ws (attempt 1)
2025-07-13 14:17:41,315 - core.websocket_manager - ERROR - htx_futures: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:17:41,316 - core.websocket_manager - ERROR - htx_futures: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:17:41,317 - core.websocket_manager - ERROR - htx_futures: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:17:41,477 - core.websocket_manager - WARNING - htx_futures: Connection closed
2025-07-13 14:17:41,478 - core.websocket_manager - INFO - htx_futures: Reconnecting in 5 seconds (attempt 1)
2025-07-13 14:17:41,507 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:17:44,100 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:17:44,660 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:17:45,113 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:17:45,661 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:17:46,131 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:17:46,480 - core.websocket_manager - INFO - htx_futures: Connecting to wss://api.hbdm.com/swap-ws (attempt 1)
2025-07-13 14:17:46,675 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:17:47,147 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:17:47,696 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:17:48,147 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:17:48,715 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:17:49,172 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:17:49,740 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:17:50,193 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:17:50,463 - core.websocket_manager - ERROR - htx_futures: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:17:50,741 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:17:51,208 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:17:51,742 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:17:52,215 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:17:52,756 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:17:53,229 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:17:53,776 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:17:54,244 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:17:54,784 - core.websocket_manager - WARNING - htx_backup: Connection closed
2025-07-13 14:17:54,785 - core.websocket_manager - INFO - htx_backup: Reconnecting in 5 seconds (attempt 1)
2025-07-13 14:17:55,245 - core.websocket_manager - WARNING - htx_market: Connection closed
2025-07-13 14:17:55,246 - core.websocket_manager - INFO - htx_market: Reconnecting in 5 seconds (attempt 1)
2025-07-13 14:17:55,467 - core.websocket_manager - ERROR - htx_futures: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:17:59,789 - core.websocket_manager - INFO - htx_backup: Connecting to wss://api-aws.huobi.pro/ws (attempt 1)
2025-07-13 14:18:00,254 - core.websocket_manager - INFO - htx_market: Connecting to wss://api.huobi.pro/ws (attempt 1)
2025-07-13 14:18:00,471 - core.websocket_manager - ERROR - htx_futures: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:18:02,322 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:18:05,481 - core.websocket_manager - ERROR - htx_futures: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:18:06,154 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:18:06,369 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:18:07,168 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:18:07,377 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:18:07,482 - core.websocket_manager - WARNING - htx_futures: Connection closed
2025-07-13 14:18:07,482 - core.websocket_manager - INFO - htx_futures: Reconnecting in 5 seconds (attempt 1)
2025-07-13 14:18:08,199 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:18:08,390 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:18:09,221 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:18:09,404 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:18:10,229 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:18:10,417 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:18:11,244 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:18:11,442 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:18:11,994 - llama.chatgpt_runner - ERROR - ChatGPT inference failed
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\dev\Epinnox_v6\llama\chatgpt_runner.py", line 34, in run_inference
    raise ImportError("OpenAI package not available or client not initialized")
ImportError: OpenAI package not available or client not initialized
2025-07-13 14:18:11,995 - core.unified_llm_manager - ERROR - [UNIFIED_LLM] Provider call failed: OpenAI package not available or client not initialized
2025-07-13 14:18:11,995 - core.unified_llm_manager - WARNING - [UNIFIED_LLM] chatgpt marked as failed
2025-07-13 14:18:15,574 - llama.mock_runner - INFO - Running mock inference
2025-07-13 14:18:15,574 - core.websocket_manager - INFO - htx_futures: Connecting to wss://api.hbdm.com/swap-ws (attempt 1)
2025-07-13 14:18:15,579 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:18:15,579 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:18:15,580 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:18:15,580 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:18:15,580 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:18:15,580 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:18:15,580 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:18:15,580 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:18:16,272 - core.websocket_manager - WARNING - htx_market: Connection closed
2025-07-13 14:18:16,272 - core.websocket_manager - INFO - htx_market: Reconnecting in 5 seconds (attempt 1)
2025-07-13 14:18:16,529 - core.websocket_manager - WARNING - htx_backup: Connection closed
2025-07-13 14:18:16,529 - core.websocket_manager - INFO - htx_backup: Reconnecting in 5 seconds (attempt 1)
2025-07-13 14:18:19,502 - core.websocket_manager - ERROR - htx_futures: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:18:21,295 - core.websocket_manager - INFO - htx_market: Connecting to wss://api.huobi.pro/ws (attempt 1)
2025-07-13 14:18:21,545 - core.websocket_manager - INFO - htx_backup: Connecting to wss://api-aws.huobi.pro/ws (attempt 1)
2025-07-13 14:18:23,706 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:18:24,309 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:18:24,495 - core.websocket_manager - ERROR - htx_futures: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:18:27,799 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:18:28,384 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:18:28,822 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:18:29,392 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:18:29,499 - core.websocket_manager - ERROR - htx_futures: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:18:29,838 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:18:30,419 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:18:30,846 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:18:31,451 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:18:31,867 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:18:32,480 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:18:32,896 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:18:33,495 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:18:33,929 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:18:34,502 - core.websocket_manager - ERROR - htx_futures: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:18:34,509 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:18:34,965 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:18:35,532 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:18:35,978 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:18:36,515 - core.websocket_manager - WARNING - htx_futures: Connection closed
2025-07-13 14:18:36,515 - core.websocket_manager - INFO - htx_futures: Reconnecting in 5 seconds (attempt 1)
2025-07-13 14:18:36,559 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:18:36,986 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:18:37,574 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:18:37,993 - core.websocket_manager - WARNING - htx_market: Connection closed
2025-07-13 14:18:37,993 - core.websocket_manager - INFO - htx_market: Reconnecting in 5 seconds (attempt 1)
2025-07-13 14:18:38,582 - core.websocket_manager - WARNING - htx_backup: Connection closed
2025-07-13 14:18:38,582 - core.websocket_manager - INFO - htx_backup: Reconnecting in 5 seconds (attempt 1)
2025-07-13 14:18:41,517 - data.market_data_manager - WARNING - Stale data detected for symbols: ['DOGE/USDT:USDT', 'BTC/USDT:USDT']
2025-07-13 14:18:41,517 - data.market_data_manager - WARNING - Unhealthy WebSocket connection: market
2025-07-13 14:18:41,517 - data.market_data_manager - WARNING - Unhealthy WebSocket connection: futures
2025-07-13 14:18:41,518 - data.market_data_manager - WARNING - Unhealthy WebSocket connection: backup
2025-07-13 14:18:41,518 - core.websocket_manager - INFO - htx_futures: Connecting to wss://api.hbdm.com/swap-ws (attempt 1)
2025-07-13 14:18:42,522 - core.websocket_manager - ERROR - htx_futures: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:18:42,996 - core.websocket_manager - INFO - htx_market: Connecting to wss://api.huobi.pro/ws (attempt 1)
2025-07-13 14:18:43,592 - core.websocket_manager - INFO - htx_backup: Connecting to wss://api-aws.huobi.pro/ws (attempt 1)
2025-07-13 14:18:45,126 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:18:47,527 - core.websocket_manager - ERROR - htx_futures: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:18:49,166 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:18:49,295 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:18:50,172 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:18:50,296 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:18:51,190 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:18:51,309 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:18:52,203 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:18:52,334 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:18:52,560 - core.websocket_manager - ERROR - htx_futures: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:18:53,223 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:18:53,352 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:18:54,235 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:18:54,359 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:18:55,243 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:18:55,362 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:18:56,263 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:18:56,379 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:18:57,297 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:18:57,380 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:18:57,535 - core.websocket_manager - ERROR - htx_futures: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:18:58,325 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:18:58,387 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:18:59,330 - core.websocket_manager - WARNING - htx_market: Connection closed
2025-07-13 14:18:59,330 - core.websocket_manager - INFO - htx_market: Reconnecting in 5 seconds (attempt 1)
2025-07-13 14:18:59,394 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:19:00,400 - core.websocket_manager - WARNING - htx_backup: Connection closed
2025-07-13 14:19:00,402 - core.websocket_manager - INFO - htx_backup: Reconnecting in 5 seconds (attempt 1)
2025-07-13 14:19:02,549 - core.websocket_manager - WARNING - htx_futures: Connection closed
2025-07-13 14:19:02,550 - core.websocket_manager - INFO - htx_futures: Reconnecting in 5 seconds (attempt 1)
2025-07-13 14:19:04,343 - core.websocket_manager - INFO - htx_market: Connecting to wss://api.huobi.pro/ws (attempt 1)
2025-07-13 14:19:05,414 - core.websocket_manager - INFO - htx_backup: Connecting to wss://api-aws.huobi.pro/ws (attempt 1)
2025-07-13 14:19:07,548 - core.websocket_manager - INFO - htx_futures: Connecting to wss://api.hbdm.com/swap-ws (attempt 1)
2025-07-13 14:19:08,545 - core.websocket_manager - ERROR - htx_futures: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:19:08,808 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:19:09,108 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:19:11,136 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:19:11,867 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:19:12,144 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:19:12,872 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:19:13,161 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:19:13,551 - core.websocket_manager - ERROR - htx_futures: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:19:13,873 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:19:14,182 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:19:14,874 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:19:15,213 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:19:15,586 - llama.chatgpt_runner - ERROR - ChatGPT inference failed
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\dev\Epinnox_v6\llama\chatgpt_runner.py", line 34, in run_inference
    raise ImportError("OpenAI package not available or client not initialized")
ImportError: OpenAI package not available or client not initialized
2025-07-13 14:19:15,587 - core.unified_llm_manager - ERROR - [UNIFIED_LLM] Provider call failed: OpenAI package not available or client not initialized
2025-07-13 14:19:15,587 - core.unified_llm_manager - WARNING - [UNIFIED_LLM] chatgpt marked as failed
2025-07-13 14:19:19,384 - llama.mock_runner - INFO - Running mock inference
2025-07-13 14:19:19,385 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:19:19,385 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:19:19,385 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:19:19,385 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:19:19,385 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:19:19,385 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:19:19,385 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:19:19,385 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:19:19,386 - core.websocket_manager - ERROR - htx_futures: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:19:19,978 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:19:20,307 - core.websocket_manager - WARNING - htx_market: Connection closed
2025-07-13 14:19:20,307 - core.websocket_manager - INFO - htx_market: Reconnecting in 5 seconds (attempt 1)
2025-07-13 14:19:21,018 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:19:22,018 - core.websocket_manager - WARNING - htx_backup: Connection closed
2025-07-13 14:19:22,018 - core.websocket_manager - INFO - htx_backup: Reconnecting in 5 seconds (attempt 1)
2025-07-13 14:19:23,560 - core.websocket_manager - ERROR - htx_futures: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:19:25,310 - core.websocket_manager - INFO - htx_market: Connecting to wss://api.huobi.pro/ws (attempt 1)
2025-07-13 14:19:27,024 - core.websocket_manager - INFO - htx_backup: Connecting to wss://api-aws.huobi.pro/ws (attempt 1)
2025-07-13 14:19:28,541 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:19:28,570 - core.websocket_manager - WARNING - htx_futures: Connection closed
2025-07-13 14:19:28,570 - core.websocket_manager - INFO - htx_futures: Reconnecting in 5 seconds (attempt 1)
2025-07-13 14:19:29,897 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:19:31,931 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:19:32,937 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:19:33,595 - core.websocket_manager - INFO - htx_futures: Connecting to wss://api.hbdm.com/swap-ws (attempt 1)
2025-07-13 14:19:33,608 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:19:33,949 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:19:34,622 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:19:34,980 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:19:35,675 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:19:35,988 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:19:36,703 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:19:36,991 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:19:37,358 - core.websocket_manager - ERROR - htx_futures: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:19:37,735 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:19:38,017 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:19:38,736 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 14:19:39,043 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 17:24:11,248 - gui_integration - INFO - Enhanced GUI Integration (Phase 3) initialized
2025-07-13 17:24:11,248 - core.autonomous_trading_orchestrator - INFO - Autonomous Trading Orchestrator initialized in paper mode
2025-07-13 17:24:11,250 - gui_integration - INFO - Phase 3 components initialized successfully
2025-07-13 17:25:12,790 - gui_integration - INFO - Enhanced GUI Integration (Phase 3) initialized
2025-07-13 17:25:12,790 - core.autonomous_trading_orchestrator - INFO - Autonomous Trading Orchestrator initialized in paper mode
2025-07-13 17:25:12,791 - gui_integration - INFO - Phase 3 components initialized successfully
2025-07-13 17:25:12,792 - gui.main_window - INFO - Phase 3 GUI integration initialized successfully
2025-07-13 17:25:12,792 - gui.main_window - INFO - Autonomous tab connected to GUI integration
2025-07-13 17:25:12,792 - gui.main_window - INFO - Monitoring dashboard connected to GUI integration
2025-07-13 17:25:12,792 - gui.main_window - INFO - Trading System GUI initialized
2025-07-13 17:25:12,806 - gui.safety_controls_widget - INFO - Safety Controls Widget initialized
2025-07-13 17:25:12,811 - gui.autonomous_trading_tab - INFO - Autonomous Trading Tab initialized
2025-07-13 17:25:12,818 - gui.phase3_monitoring_dashboard - INFO - Phase 3 Monitoring Dashboard initialized
2025-07-13 17:25:12,822 - gui.safety_controls_widget - INFO - Safety Controls Widget initialized
2025-07-13 17:25:12,823 - gui_integration - INFO - Enhanced GUI Integration (Phase 3) initialized
2025-07-13 17:25:12,823 - core.autonomous_trading_orchestrator - INFO - Autonomous Trading Orchestrator initialized in paper mode
2025-07-13 17:25:12,823 - gui_integration - INFO - Phase 3 components initialized successfully
2025-07-13 17:26:06,057 - gui.safety_controls_widget - INFO - Safety Controls Widget initialized
2025-07-13 17:26:06,068 - gui.autonomous_trading_tab - INFO - Autonomous Trading Tab initialized
2025-07-13 17:26:06,068 - gui_integration - INFO - Enhanced GUI Integration (Phase 3) initialized
2025-07-13 17:26:06,068 - core.autonomous_trading_orchestrator - INFO - Autonomous Trading Orchestrator initialized in paper mode
2025-07-13 17:26:06,068 - gui_integration - INFO - Phase 3 components initialized successfully
2025-07-13 17:27:34,634 - gui.safety_controls_widget - INFO - Safety Controls Widget initialized
2025-07-13 17:27:34,760 - gui.autonomous_trading_tab - INFO - Autonomous Trading Tab initialized
2025-07-13 17:27:34,760 - gui_integration - INFO - Enhanced GUI Integration (Phase 3) initialized
2025-07-13 17:27:34,760 - core.autonomous_trading_orchestrator - INFO - Autonomous Trading Orchestrator initialized in paper mode
2025-07-13 17:27:34,760 - gui_integration - INFO - Phase 3 components initialized successfully
2025-07-13 17:27:35,763 - gui.safety_controls_widget - INFO - Safety Controls Widget initialized
2025-07-13 17:27:35,763 - gui_integration - INFO - Enhanced GUI Integration (Phase 3) initialized
2025-07-13 17:27:35,763 - core.autonomous_trading_orchestrator - INFO - Autonomous Trading Orchestrator initialized in paper mode
2025-07-13 17:27:35,763 - gui_integration - INFO - Phase 3 components initialized successfully
2025-07-13 17:27:36,845 - gui.safety_controls_widget - INFO - Safety Controls Widget initialized
2025-07-13 17:27:37,859 - gui.phase3_monitoring_dashboard - INFO - Phase 3 Monitoring Dashboard initialized
2025-07-13 17:27:37,859 - gui_integration - INFO - Enhanced GUI Integration (Phase 3) initialized
2025-07-13 17:27:37,859 - core.autonomous_trading_orchestrator - INFO - Autonomous Trading Orchestrator initialized in paper mode
2025-07-13 17:27:37,860 - gui_integration - INFO - Phase 3 components initialized successfully
2025-07-13 17:27:37,882 - gui_integration - INFO - Enhanced GUI Integration (Phase 3) initialized
2025-07-13 17:27:37,882 - core.autonomous_trading_orchestrator - INFO - Autonomous Trading Orchestrator initialized in paper mode
2025-07-13 17:27:37,882 - gui_integration - INFO - Phase 3 components initialized successfully
2025-07-13 17:27:37,884 - gui_integration - INFO - Enhanced GUI Integration (Phase 3) initialized
2025-07-13 17:27:37,884 - core.autonomous_trading_orchestrator - INFO - Autonomous Trading Orchestrator initialized in paper mode
2025-07-13 17:27:37,884 - gui_integration - INFO - Phase 3 components initialized successfully
2025-07-13 17:27:39,012 - gui.safety_controls_widget - INFO - Safety Controls Widget initialized
2025-07-13 17:27:39,054 - gui.autonomous_trading_tab - INFO - Autonomous Trading Tab initialized
2025-07-13 17:27:39,057 - gui.main_window - INFO - Autonomous Trading tab added successfully
2025-07-13 17:27:39,082 - gui.phase3_monitoring_dashboard - INFO - Phase 3 Monitoring Dashboard initialized
2025-07-13 17:27:39,082 - gui.main_window - INFO - Phase 3 Monitoring Dashboard tab added successfully
2025-07-13 17:27:39,084 - gui_integration - INFO - Enhanced GUI Integration (Phase 3) initialized
2025-07-13 17:27:39,084 - core.autonomous_trading_orchestrator - INFO - Autonomous Trading Orchestrator initialized in paper mode
2025-07-13 17:27:39,086 - gui_integration - INFO - Phase 3 components initialized successfully
2025-07-13 17:27:39,086 - gui.main_window - INFO - Phase 3 GUI integration initialized successfully
2025-07-13 17:27:39,086 - gui.main_window - INFO - Autonomous tab connected to GUI integration
2025-07-13 17:27:39,088 - gui.main_window - INFO - Monitoring dashboard connected to GUI integration
2025-07-13 17:27:39,089 - gui.main_window - INFO - Trading System GUI initialized
