2025-07-13 11:33:10,121 - __main__ - INFO - [PHASE2_TEST] Loaded configuration from config/phase2_autonomous_config.yaml
2025-07-13 11:33:10,121 - __main__ - INFO - [PHASE2_TEST] Phase 2 Integration Tester initialized
2025-07-13 11:33:10,122 - __main__ - INFO - [PHASE2_TEST] Starting comprehensive Phase 2 integration tests
2025-07-13 11:33:10,122 - __main__ - INFO - [PHASE2_TEST] Running Test 1: Unified LLM Integration...
2025-07-13 11:33:12,050 - core.unified_llm_manager - INFO - [UNIFIED_LLM] Unified LLM Manager initialized
2025-07-13 11:33:12,050 - core.unified_llm_manager - INFO - [UNIFIED_LLM] Initializing LLM providers...
2025-07-13 11:33:12,065 - config.autonomous_config - INFO - Configuration loaded from configs/autonomous_trading.yaml
2025-07-13 11:33:12,069 - llama.runner - INFO - Loaded OpenAI API key from models_config.yaml
2025-07-13 11:33:12,069 - llama.runner - INFO - Using model path: C:\Users\<USER>\.lmstudio\models\lmstudio-community\Phi-3.1-mini-128k-instruct-GGUF\Phi-3.1-mini-128k-instruct-Q4_K_M.gguf
2025-07-13 11:33:12,069 - llama.runner - INFO - Using guard path: C:\Users\<USER>\Documents\dev\Epinnox_v6\models\Llama-Guard-3-8B.gguf
2025-07-13 11:33:12,069 - llama.runner - INFO - Using binary path: C:\Users\<USER>\Documents\dev\llama.cpp\main
2025-07-13 11:33:12,075 - core.unified_llm_manager - INFO - [UNIFIED_LLM] ChatGPT provider initialized
2025-07-13 11:33:12,080 - llama.lmstudio_runner - INFO - Loaded LMStudio configuration from models_config.yaml
2025-07-13 11:33:14,117 - llama.lmstudio_runner - INFO - Discovered 8 models: ['phi-3.1-mini-128k-instruct', 'openthinker3-7b', 'mistral-7b-claude-chat', 'fine_tuned_qwen1.7b', 'tsfc-model-0.0.1', 'codestral-22b-v0.1', 'deepseek-r1-distill-qwen-7b', 'text-embedding-nomic-embed-text-v1.5']
2025-07-13 11:33:14,117 - llama.lmstudio_runner - INFO - Selected preferred model from config: phi-3.1-mini-128k-instruct
2025-07-13 11:33:14,118 - llama.lmstudio_runner - INFO - Initialized LMStudio with model: phi-3.1-mini-128k-instruct
2025-07-13 11:33:16,172 - core.unified_llm_manager - INFO - [UNIFIED_LLM] LMStudio provider initialized
2025-07-13 11:33:16,176 - core.unified_llm_manager - WARNING - [UNIFIED_LLM] Failed to initialize Transformers: __init__() missing 1 required positional argument: 'model_path'
2025-07-13 11:33:16,177 - core.unified_llm_manager - WARNING - [UNIFIED_LLM] Failed to initialize Llama: __init__() missing 1 required positional argument: 'bin_path'
2025-07-13 11:33:16,178 - llama.mock_runner - INFO - Initialized MockRunner with personality: {'caution': 0.82795528915905, 'optimism': 0.480897228051285, 'consistency': 0.8130696026649382, 'detail_level': 0.6538009898557421, 'confidence_bias': 1.5132465642911974}
2025-07-13 11:33:16,178 - core.unified_llm_manager - INFO - [UNIFIED_LLM] Mock provider initialized
2025-07-13 11:33:16,178 - core.unified_llm_manager - INFO - [UNIFIED_LLM] Selected provider: mock (score: 109.90)
2025-07-13 11:33:16,178 - core.unified_llm_manager - INFO - [UNIFIED_LLM] Health monitoring started
2025-07-13 11:33:16,178 - core.unified_llm_manager - INFO - [UNIFIED_LLM] Initialized with 3 providers
2025-07-13 11:33:16,178 - core.unified_llm_manager - INFO - [UNIFIED_LLM] Current provider: LLMProvider.MOCK
2025-07-13 11:33:16,178 - core.standardized_prompt_handler - INFO - [PROMPT_HANDLER] Standardized prompt handler initialized
2025-07-13 11:33:16,178 - llama.mock_runner - INFO - Running mock inference
2025-07-13 11:33:16,179 - core.unified_llm_manager - ERROR - [UNIFIED_LLM] Failed to parse response: 'LLMResponse' object has no attribute 'split'
2025-07-13 11:33:16,179 - core.unified_llm_manager - WARNING - [UNIFIED_LLM] mock response below confidence threshold
2025-07-13 11:33:16,179 - core.unified_llm_manager - WARNING - [UNIFIED_LLM] Failing over to chatgpt
2025-07-13 11:33:16,179 - llama.chatgpt_runner - ERROR - ChatGPT inference failed
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\dev\Epinnox_v6\llama\chatgpt_runner.py", line 34, in run_inference
    raise ImportError("OpenAI package not available or client not initialized")
ImportError: OpenAI package not available or client not initialized
2025-07-13 11:33:16,180 - core.unified_llm_manager - ERROR - [UNIFIED_LLM] Provider call failed: OpenAI package not available or client not initialized
2025-07-13 11:33:16,180 - core.unified_llm_manager - WARNING - [UNIFIED_LLM] Failing over to lmstudio
2025-07-13 11:33:20,791 - __main__ - INFO - [PHASE2_TEST] LLM response received from lmstudio
2025-07-13 11:33:20,791 - core.unified_llm_manager - INFO - [UNIFIED_LLM] Unified LLM Manager initialized
2025-07-13 11:33:20,791 - core.standardized_prompt_handler - INFO - [PROMPT_HANDLER] Standardized prompt handler initialized
2025-07-13 11:33:20,791 - core.autonomous_llm_integration - INFO - [AUTONOMOUS_LLM] Autonomous LLM Integration initialized
2025-07-13 11:33:20,791 - core.autonomous_llm_integration - INFO - [AUTONOMOUS_LLM] Initializing autonomous LLM integration...
2025-07-13 11:33:20,791 - core.unified_llm_manager - INFO - [UNIFIED_LLM] Initializing LLM providers...
2025-07-13 11:33:20,795 - core.unified_llm_manager - INFO - [UNIFIED_LLM] ChatGPT provider initialized
2025-07-13 11:33:20,800 - llama.lmstudio_runner - INFO - Loaded LMStudio configuration from models_config.yaml
2025-07-13 11:33:22,855 - llama.lmstudio_runner - INFO - Discovered 8 models: ['phi-3.1-mini-128k-instruct', 'openthinker3-7b', 'mistral-7b-claude-chat', 'fine_tuned_qwen1.7b', 'tsfc-model-0.0.1', 'codestral-22b-v0.1', 'deepseek-r1-distill-qwen-7b', 'text-embedding-nomic-embed-text-v1.5']
2025-07-13 11:33:22,855 - llama.lmstudio_runner - INFO - Selected preferred model from config: phi-3.1-mini-128k-instruct
2025-07-13 11:33:22,855 - llama.lmstudio_runner - INFO - Initialized LMStudio with model: phi-3.1-mini-128k-instruct
2025-07-13 11:33:24,905 - core.unified_llm_manager - INFO - [UNIFIED_LLM] LMStudio provider initialized
2025-07-13 11:33:24,905 - core.unified_llm_manager - WARNING - [UNIFIED_LLM] Failed to initialize Transformers: __init__() missing 1 required positional argument: 'model_path'
2025-07-13 11:33:24,905 - core.unified_llm_manager - WARNING - [UNIFIED_LLM] Failed to initialize Llama: __init__() missing 1 required positional argument: 'bin_path'
2025-07-13 11:33:24,905 - llama.mock_runner - INFO - Initialized MockRunner with personality: {'caution': 0.7145161743264306, 'optimism': 0.4890448807218176, 'consistency': 0.6461219715106501, 'detail_level': 0.6665954611779829, 'confidence_bias': 1.4829957494307244}
2025-07-13 11:33:24,906 - core.unified_llm_manager - INFO - [UNIFIED_LLM] Mock provider initialized
2025-07-13 11:33:24,906 - core.unified_llm_manager - INFO - [UNIFIED_LLM] Selected provider: mock (score: 109.90)
2025-07-13 11:33:24,906 - core.unified_llm_manager - INFO - [UNIFIED_LLM] Health monitoring started
2025-07-13 11:33:24,906 - core.unified_llm_manager - INFO - [UNIFIED_LLM] Initialized with 3 providers
2025-07-13 11:33:24,906 - core.unified_llm_manager - INFO - [UNIFIED_LLM] Current provider: LLMProvider.MOCK
2025-07-13 11:33:24,906 - core.autonomous_llm_integration - INFO - [AUTONOMOUS_LLM] Autonomous LLM integration initialized successfully
2025-07-13 11:33:24,906 - core.unified_llm_manager - INFO - [UNIFIED_LLM] Unified LLM Manager shutdown
2025-07-13 11:33:24,906 - __main__ - INFO - [PHASE2_TEST] Unified LLM integration test completed
2025-07-13 11:33:24,908 - __main__ - INFO - [PHASE2_TEST] Running Test 2: Autonomous Position Manager...
2025-07-13 11:33:24,910 - core.autonomous_position_manager - INFO - [POSITION_MANAGER] Autonomous Position Manager initialized
2025-07-13 11:33:24,910 - core.autonomous_position_manager - INFO - [POSITION_MANAGER] Position monitoring started (interval: 5s)
2025-07-13 11:33:24,910 - core.autonomous_position_manager - INFO - [POSITION_MANAGER] Autonomous Position Manager ready
2025-07-13 11:33:24,910 - core.autonomous_position_manager - INFO - [POSITION_MANAGER] Added position pos_1_1752424404: LONG 0.001 BTC/USDT:USDT @ 50000.0000
2025-07-13 11:33:24,910 - core.autonomous_position_manager - INFO - [POSITION_MANAGER] SL: 49000.0000, TP: 52000.0000
2025-07-13 11:33:24,910 - core.autonomous_position_manager - INFO - [POSITION_MANAGER] Updated SL for pos_1_1752424404: 49000.0000
2025-07-13 11:33:24,910 - core.autonomous_position_manager - INFO - [POSITION_MANAGER] Updated TP for pos_1_1752424404: 52000.0000
2025-07-13 11:33:24,910 - core.autonomous_position_manager - INFO - [POSITION_MANAGER] Exiting position pos_1_1752424404 - Reason: manual
2025-07-13 11:33:24,911 - core.autonomous_position_manager - INFO - [POSITION_MANAGER] Successfully exited position pos_1_1752424404
2025-07-13 11:33:24,911 - core.autonomous_position_manager - INFO - [POSITION_MANAGER] Position monitoring stopped
2025-07-13 11:33:24,911 - core.autonomous_position_manager - INFO - [POSITION_MANAGER] Autonomous Position Manager shutdown complete
2025-07-13 11:33:24,911 - __main__ - INFO - [PHASE2_TEST] Autonomous position manager test completed
2025-07-13 11:33:24,912 - __main__ - INFO - [PHASE2_TEST] Running Test 3: Position Watchdog System...
2025-07-13 11:33:24,917 - core.position_watchdog - INFO - [WATCHDOG] Position Watchdog initialized
2025-07-13 11:33:24,918 - core.position_watchdog - INFO - [WATCHDOG] Monitoring started (scan: 10s, health: 30s)
2025-07-13 11:33:24,918 - core.position_watchdog - INFO - [WATCHDOG] Position Watchdog ready
2025-07-13 11:33:24,918 - core.position_watchdog - WARNING - [WATCHDOG] 1 positions in critical state
2025-07-13 11:33:24,918 - core.position_watchdog - WARNING - [WATCHDOG] Generated 1 critical alerts
2025-07-13 11:33:26,930 - __main__ - ERROR - [PHASE2_TEST] Position watchdog test error: 'PositionWatchdog' object has no attribute 'force_health_check'
2025-07-13 11:33:26,933 - __main__ - INFO - [PHASE2_TEST] Running Test 4: Error Recovery System...
2025-07-13 11:33:26,934 - core.error_recovery_system - INFO - [ERROR_RECOVERY] Error Recovery System initialized
2025-07-13 11:33:26,934 - core.error_recovery_system - INFO - [ERROR_RECOVERY] Health monitoring started
2025-07-13 11:33:26,935 - core.error_recovery_system - INFO - [ERROR_RECOVERY] Error Recovery System ready
2025-07-13 11:33:26,935 - core.error_recovery_system - WARNING - [ERROR_RECOVERY] Retry 1/3 for test_function_with_retries in 0.62s: Test failure for retry testing
2025-07-13 11:33:27,568 - core.error_recovery_system - INFO - [ERROR_RECOVERY] Error Recovery System shutdown complete
2025-07-13 11:33:27,568 - __main__ - INFO - [PHASE2_TEST] Error recovery system test completed
2025-07-13 11:33:27,572 - __main__ - INFO - [PHASE2_TEST] Running Test 5: Component Integration...
2025-07-13 11:33:27,577 - core.autonomous_trading_orchestrator - INFO - Autonomous Trading Orchestrator initialized in paper mode
2025-07-13 11:33:27,577 - core.autonomous_trading_orchestrator - INFO - Initializing autonomous trading system...
2025-07-13 11:33:27,577 - core.error_recovery_system - INFO - [ERROR_RECOVERY] Error Recovery System initialized
2025-07-13 11:33:27,577 - core.error_recovery_system - INFO - [ERROR_RECOVERY] Health monitoring started
2025-07-13 11:33:27,577 - core.error_recovery_system - INFO - [ERROR_RECOVERY] Error Recovery System ready
2025-07-13 11:33:27,626 - core.websocket_manager - INFO - WebSocket manager initialized for htx_market: wss://api.huobi.pro/ws
2025-07-13 11:33:27,626 - core.websocket_manager - INFO - WebSocket manager initialized for htx_futures: wss://api.hbdm.com/swap-ws
2025-07-13 11:33:27,626 - core.websocket_manager - INFO - WebSocket manager initialized for htx_backup: wss://api-aws.huobi.pro/ws
2025-07-13 11:33:27,626 - data.market_data_manager - INFO - Initialized 3 WebSocket endpoints
2025-07-13 11:33:27,626 - data.market_data_manager - INFO - Market data manager initialized for htx
2025-07-13 11:33:27,626 - data.market_data_manager - INFO - Starting market data manager...
2025-07-13 11:33:27,626 - data.market_data_manager - INFO - Started WebSocket connection: market
2025-07-13 11:33:27,626 - data.market_data_manager - INFO - Started WebSocket connection: futures
2025-07-13 11:33:27,626 - data.market_data_manager - INFO - Started WebSocket connection: backup
2025-07-13 11:33:27,626 - core.websocket_manager - INFO - htx_market: Connecting to wss://api.huobi.pro/ws (attempt 1)
2025-07-13 11:33:27,656 - core.websocket_manager - INFO - htx_futures: Connecting to wss://api.hbdm.com/swap-ws (attempt 1)
2025-07-13 11:33:27,660 - core.websocket_manager - INFO - htx_backup: Connecting to wss://api-aws.huobi.pro/ws (attempt 1)
2025-07-13 11:33:29,649 - core.unified_llm_manager - INFO - [UNIFIED_LLM] Unified LLM Manager initialized
2025-07-13 11:33:29,650 - core.standardized_prompt_handler - INFO - [PROMPT_HANDLER] Standardized prompt handler initialized
2025-07-13 11:33:29,650 - core.autonomous_llm_integration - INFO - [AUTONOMOUS_LLM] Autonomous LLM Integration initialized
2025-07-13 11:33:29,650 - core.autonomous_llm_integration - INFO - [AUTONOMOUS_LLM] Initializing autonomous LLM integration...
2025-07-13 11:33:29,650 - core.unified_llm_manager - INFO - [UNIFIED_LLM] Initializing LLM providers...
2025-07-13 11:33:29,653 - core.unified_llm_manager - INFO - [UNIFIED_LLM] ChatGPT provider initialized
2025-07-13 11:33:29,657 - llama.lmstudio_runner - INFO - Loaded LMStudio configuration from models_config.yaml
2025-07-13 11:33:31,721 - llama.lmstudio_runner - INFO - Discovered 8 models: ['phi-3.1-mini-128k-instruct', 'openthinker3-7b', 'mistral-7b-claude-chat', 'fine_tuned_qwen1.7b', 'tsfc-model-0.0.1', 'codestral-22b-v0.1', 'deepseek-r1-distill-qwen-7b', 'text-embedding-nomic-embed-text-v1.5']
2025-07-13 11:33:31,722 - llama.lmstudio_runner - INFO - Selected preferred model from config: phi-3.1-mini-128k-instruct
2025-07-13 11:33:31,722 - llama.lmstudio_runner - INFO - Initialized LMStudio with model: phi-3.1-mini-128k-instruct
2025-07-13 11:33:33,785 - core.unified_llm_manager - INFO - [UNIFIED_LLM] LMStudio provider initialized
2025-07-13 11:33:33,786 - core.unified_llm_manager - WARNING - [UNIFIED_LLM] Failed to initialize Transformers: __init__() missing 1 required positional argument: 'model_path'
2025-07-13 11:33:33,786 - core.unified_llm_manager - WARNING - [UNIFIED_LLM] Failed to initialize Llama: __init__() missing 1 required positional argument: 'bin_path'
2025-07-13 11:33:33,786 - llama.mock_runner - INFO - Initialized MockRunner with personality: {'caution': 0.7912088626084387, 'optimism': 0.48417453662463183, 'consistency': 0.7493575395622551, 'detail_level': 0.8864756129652727, 'confidence_bias': 0.3055756915135843}
2025-07-13 11:33:33,786 - core.unified_llm_manager - INFO - [UNIFIED_LLM] Mock provider initialized
2025-07-13 11:33:33,787 - core.unified_llm_manager - INFO - [UNIFIED_LLM] Selected provider: mock (score: 109.90)
2025-07-13 11:33:33,787 - core.unified_llm_manager - INFO - [UNIFIED_LLM] Health monitoring started
2025-07-13 11:33:33,787 - core.unified_llm_manager - INFO - [UNIFIED_LLM] Initialized with 3 providers
2025-07-13 11:33:33,787 - core.unified_llm_manager - INFO - [UNIFIED_LLM] Current provider: LLMProvider.MOCK
2025-07-13 11:33:33,787 - core.autonomous_llm_integration - INFO - [AUTONOMOUS_LLM] Autonomous LLM integration initialized successfully
2025-07-13 11:33:33,797 - utils.cache_manager - INFO - Cache manager initialized: cache, max_age=24h, max_size=100MB
2025-07-13 11:33:33,797 - utils.cache_manager - INFO - Cache manager initialized: cache, max_age=24h, max_size=100MB
2025-07-13 11:33:33,798 - core.llm_orchestrator - INFO - LLM Prompt Orchestrator initialized
2025-07-13 11:33:36,720 - ml.models - INFO - TensorFlow available for LSTM models
2025-07-13 11:33:36,720 - ml.models - INFO - ML Model Manager initialized
2025-07-13 11:33:36,849 - absl - WARNING - Compiled the loaded model, but the compiled metrics have yet to be built. `model.compile_metrics` will be empty until you train or evaluate the model.
2025-07-13 11:33:36,890 - ml.models - ERROR - Error loading models: Could not locate function 'mse'. Make sure custom classes are decorated with `@keras.saving.register_keras_serializable()`. Full object config: {'module': 'keras.metrics', 'class_name': 'function', 'config': 'mse', 'registered_name': 'mse'}
2025-07-13 11:33:36,892 - core.autonomous_position_manager - INFO - [POSITION_MANAGER] Autonomous Position Manager initialized
2025-07-13 11:33:36,898 - core.adaptive_risk - INFO - TA-Lib not available, using pandas-ta fallback
2025-07-13 11:33:36,898 - core.adaptive_risk - INFO - Initialized adaptive risk manager
2025-07-13 11:33:36,904 - execution.unified_execution_engine - INFO - Unified execution engine initialized in paper mode
2025-07-13 11:33:36,904 - execution.execution_adapter - INFO - Execution adapter initialized in paper mode
2025-07-13 11:33:36,911 - core.autonomous_position_manager - INFO - [POSITION_MANAGER] Position monitoring started (interval: 5s)
2025-07-13 11:33:36,911 - core.autonomous_position_manager - INFO - [POSITION_MANAGER] Autonomous Position Manager ready
2025-07-13 11:33:36,913 - core.position_watchdog - INFO - [WATCHDOG] Position Watchdog initialized
2025-07-13 11:33:36,913 - core.position_watchdog - INFO - [WATCHDOG] Monitoring started (scan: 10s, health: 30s)
2025-07-13 11:33:36,913 - core.position_watchdog - INFO - [WATCHDOG] Position Watchdog ready
2025-07-13 11:33:36,922 - portfolio.portfolio_manager - INFO - Portfolio manager initialized with $10000.00 balance
2025-07-13 11:33:36,924 - core.autonomous_trading_orchestrator - WARNING - No market data available - this is expected during testing
2025-07-13 11:33:36,926 - __main__ - INFO - [PHASE2_TEST] Component unified_llm_integration successfully initialized
2025-07-13 11:33:36,926 - __main__ - INFO - [PHASE2_TEST] Component autonomous_position_manager successfully initialized
2025-07-13 11:33:36,926 - __main__ - INFO - [PHASE2_TEST] Component position_watchdog successfully initialized
2025-07-13 11:33:36,926 - __main__ - INFO - [PHASE2_TEST] Component error_recovery_system successfully initialized
2025-07-13 11:33:36,927 - __main__ - ERROR - [PHASE2_TEST] Component integration test error: 'AutonomousPositionManager' object has no attribute 'get_status'
2025-07-13 11:33:36,930 - __main__ - INFO - [PHASE2_TEST] Running Test 6: End-to-End Workflow...
2025-07-13 11:33:36,930 - __main__ - INFO - [PHASE2_TEST] Testing end-to-end workflow simulation
2025-07-13 11:33:36,930 - __main__ - INFO - [PHASE2_TEST] Simulating: Market data collection
2025-07-13 11:33:36,932 - core.websocket_manager - ERROR - htx_futures: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:33:36,933 - core.websocket_manager - ERROR - htx_futures: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:33:36,933 - core.websocket_manager - ERROR - htx_futures: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:33:36,933 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:33:36,933 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:33:36,933 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:33:36,933 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:33:36,933 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:33:36,933 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:33:36,934 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:33:36,934 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:33:36,934 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:33:36,934 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:33:36,934 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:33:36,934 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:33:36,934 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:33:36,934 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:33:36,934 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:33:36,935 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:33:36,935 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:33:36,935 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:33:36,935 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:33:36,935 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:33:36,935 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:33:36,935 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:33:36,935 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:33:36,936 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:33:36,936 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:33:36,936 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:33:36,936 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:33:36,936 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:33:36,936 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:33:36,936 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:33:36,936 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:33:36,937 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:33:36,937 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:33:36,937 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:33:36,937 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:33:36,937 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:33:36,937 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:33:36,937 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:33:36,937 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:33:36,937 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:33:36,937 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:33:36,938 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:33:36,938 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:33:36,939 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:33:36,939 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:33:36,939 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:33:36,939 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:33:36,939 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:33:36,939 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:33:36,939 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:33:36,939 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:33:36,940 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:33:36,940 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:33:36,940 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:33:36,940 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:33:36,940 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:33:36,940 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:33:36,940 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:33:36,940 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:33:36,940 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:33:36,941 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:33:36,941 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:33:36,941 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:33:36,941 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:33:36,942 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:33:36,942 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:33:36,942 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:33:36,942 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:33:36,942 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:33:36,942 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:33:36,942 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:33:36,942 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:33:36,942 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:33:36,943 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:33:36,944 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:33:36,944 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:33:36,944 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:33:36,944 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:33:36,944 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:33:36,944 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:33:36,944 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:33:37,012 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:33:37,034 - __main__ - INFO - [PHASE2_TEST] Simulating: LLM analysis and decision
2025-07-13 11:33:37,034 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:33:37,112 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:33:37,113 - core.websocket_manager - ERROR - htx_futures: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:33:37,121 - __main__ - INFO - [PHASE2_TEST] Simulating: Position entry execution
2025-07-13 11:33:37,121 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:33:37,122 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:33:37,122 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:33:37,122 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:33:37,133 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:33:37,202 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:33:37,222 - __main__ - INFO - [PHASE2_TEST] Simulating: Position monitoring
2025-07-13 11:33:37,222 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:33:37,313 - __main__ - INFO - [PHASE2_TEST] Simulating: Risk assessment
2025-07-13 11:33:37,314 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:33:37,412 - __main__ - INFO - [PHASE2_TEST] Simulating: Position exit execution
2025-07-13 11:33:37,413 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:33:37,423 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:33:37,510 - __main__ - INFO - [PHASE2_TEST] End-to-end workflow test completed
2025-07-13 11:33:37,512 - __main__ - INFO - [PHASE2_TEST] Running Test 7: Safety Systems Validation...
2025-07-13 11:33:37,512 - __main__ - INFO - [PHASE2_TEST] Testing safety systems
2025-07-13 11:33:37,512 - __main__ - INFO - [PHASE2_TEST] Safety systems test completed
2025-07-13 11:33:37,515 - __main__ - INFO - [PHASE2_TEST] Running Test 8: Performance Validation...
2025-07-13 11:33:37,515 - __main__ - INFO - [PHASE2_TEST] Testing performance validation
2025-07-13 11:33:37,515 - __main__ - INFO - [PHASE2_TEST] Performance validation test completed
2025-07-13 11:33:37,517 - __main__ - INFO - [PHASE2_TEST] Phase 2 integration tests completed - Overall: FAILED
