#!/usr/bin/env python3
"""
🧪 LLM ORCHESTRATOR GUI INTEGRATION TEST
Tests the enhanced data flow from LLM orchestrator to GUI display
"""

import sys
import time
import json
from datetime import datetime
from typing import Dict, Any

def test_llm_gui_integration():
    """Test LLM orchestrator results processing and GUI storage"""
    
    print("=" * 70)
    print("🧪 LLM ORCHESTRATOR GUI INTEGRATION TEST")
    print("=" * 70)
    
    # Test 1: Simulate PromptResult objects
    print("\n🔬 TEST 1: PromptResult Processing")
    
    # Mock PromptResult structure
    class MockPromptResult:
        def __init__(self, prompt_type, response, confidence, success=True, execution_time=1.5):
            self.prompt_type = prompt_type
            self.response = response
            self.confidence = confidence
            self.success = success
            self.execution_time = execution_time
            self.timestamp = datetime.now()
            self.error_message = None if success else "Mock error"
    
    # Create mock LLM orchestrator results
    mock_results = {
        'risk_assessment': MockPromptResult(
            'risk_assessment',
            {'ACTION': 'LONG', 'CONFIDENCE': 85, 'REASONING': 'Strong bullish signals detected'},
            85.0
        ),
        'entry_timing': MockPromptResult(
            'entry_timing', 
            {'ACTION': 'ENTER_NOW', 'CONFIDENCE': 90, 'REASONING': 'Optimal entry conditions'},
            90.0
        ),
        'opportunity_scanner': MockPromptResult(
            'opportunity_scanner',
            {'DECISION': 'LONG', 'CONFIDENCE': 80, 'EXPLANATION': 'High-quality setup identified'},
            80.0
        ),
        'market_regime': MockPromptResult(
            'market_regime',
            {'REGIME': 'TRENDING_BULL', 'CONFIDENCE': 75, 'SCALP_SUITABILITY': 'HIGH'},
            75.0
        ),
        'strategy_adaptation': MockPromptResult(
            'strategy_adaptation',
            {'RISK_ADJUSTMENT': 1.2, 'CONFIDENCE': 70, 'REASONING': 'Increase position size'},
            70.0
        )
    }
    
    print(f"✅ Created {len(mock_results)} mock PromptResult objects")
    
    # Test 2: Process results like the enhanced on_llm_orchestrator_cycle_complete method
    print("\n🔬 TEST 2: Results Processing Logic")
    
    individual_results = {}
    ensemble_analysis = {}
    
    for prompt_type, result in mock_results.items():
        if result.success and result.response:
            response_data = result.response
            
            # Extract action/decision
            action = (response_data.get('ACTION') or
                     response_data.get('DECISION') or
                     response_data.get('action') or 'WAIT')
            
            # Extract confidence
            confidence = (response_data.get('CONFIDENCE') or
                         response_data.get('confidence') or
                         result.confidence or 50.0)
            
            # Extract reasoning
            reasoning = (response_data.get('REASONING') or
                        response_data.get('EXPLANATION') or
                        response_data.get('reasoning') or
                        f'Analysis from {prompt_type}')
            
            # Store individual result
            individual_results[prompt_type] = {
                'action': str(action).upper(),
                'confidence': float(confidence),
                'reasoning': reasoning,
                'execution_time': result.execution_time,
                'timestamp': result.timestamp,
                'success': result.success,
                'raw_response': response_data
            }
            
            # Store ensemble data
            ensemble_analysis[prompt_type] = {
                'decision': str(action).upper(),
                'confidence': float(confidence),
                'response': response_data,
                'execution_time': result.execution_time,
                'success': result.success
            }
    
    print(f"✅ Processed {len(individual_results)} individual results")
    print(f"✅ Created ensemble analysis with {len(ensemble_analysis)} components")
    
    # Test 3: Create comprehensive ensemble data structure
    print("\n🔬 TEST 3: Ensemble Data Structure Creation")
    
    if ensemble_analysis:
        # Calculate aggregate metrics
        total_confidence = sum(data['confidence'] for data in ensemble_analysis.values() if data['success'])
        active_count = sum(1 for data in ensemble_analysis.values() if data['success'])
        avg_confidence = total_confidence / active_count if active_count > 0 else 50.0
        
        # Determine majority vote
        vote_counts = {}
        for data in ensemble_analysis.values():
            if data['success']:
                decision = data['decision']
                vote_counts[decision] = vote_counts.get(decision, 0) + 1
        
        majority_vote = max(vote_counts, key=vote_counts.get) if vote_counts else 'WAIT'
        consensus_strength = (vote_counts.get(majority_vote, 0) / active_count * 100) if active_count > 0 else 0
        
        ensemble_data = {
            'majority_vote': majority_vote,
            'avg_confidence': avg_confidence,
            'consensus_strength': consensus_strength,
            'active_models': active_count,
            'total_models': len(ensemble_analysis),
            'model_breakdown': [
                {
                    'name': prompt_type.replace('_', ' ').title(),
                    'decision': data['decision'],
                    'confidence': data['confidence'],
                    'weight': 1.0,
                    'accuracy': 85.0,
                    'status': 'ACTIVE' if data['success'] else 'ERROR',
                    'execution_time': data.get('execution_time', 0.0)
                }
                for prompt_type, data in ensemble_analysis.items()
            ],
            'vote_breakdown': vote_counts
        }
        
        print(f"✅ Ensemble Data Created:")
        print(f"   Majority Vote: {ensemble_data['majority_vote']}")
        print(f"   Average Confidence: {ensemble_data['avg_confidence']:.1f}%")
        print(f"   Consensus Strength: {ensemble_data['consensus_strength']:.1f}%")
        print(f"   Active Models: {ensemble_data['active_models']}/{ensemble_data['total_models']}")
    
    # Test 4: Create trade instruction
    print("\n🔬 TEST 4: Trade Instruction Creation")
    
    if individual_results:
        strongest_result = max(individual_results.values(), key=lambda x: x['confidence'])
        trade_instruction = {
            'ACTION': strongest_result['action'],
            'confidence': strongest_result['confidence'],
            'reasoning': strongest_result['reasoning'],
            'source': 'strongest_individual_signal'
        }
        
        print(f"✅ Trade Instruction Created:")
        print(f"   Action: {trade_instruction['ACTION']}")
        print(f"   Confidence: {trade_instruction['confidence']:.1f}%")
        print(f"   Source: {trade_instruction['source']}")
    
    # Test 5: Create stored_analysis_results structure
    print("\n🔬 TEST 5: Stored Analysis Results Structure")
    
    stored_analysis_results = {
        'llm_results': mock_results,
        'individual_results': individual_results,
        'ensemble_analysis': ensemble_data,
        'trade_instruction': trade_instruction,
        'last_llm_update': datetime.now(),
        'cycle_metadata': {
            'total_prompts': len(mock_results),
            'successful_prompts': len([r for r in mock_results.values() if r.success]),
            'failed_prompts': len([r for r in mock_results.values() if not r.success]),
            'avg_execution_time': sum(r.execution_time for r in mock_results.values()) / len(mock_results)
        }
    }
    
    print(f"✅ Stored Analysis Results Created:")
    print(f"   Keys: {list(stored_analysis_results.keys())}")
    print(f"   Individual Results: {len(stored_analysis_results['individual_results'])} items")
    print(f"   Ensemble Models: {len(stored_analysis_results['ensemble_analysis']['model_breakdown'])} models")
    print(f"   Cycle Metadata: {stored_analysis_results['cycle_metadata']}")
    
    # Test 6: Validate GUI update data
    print("\n🔬 TEST 6: GUI Update Data Validation")
    
    gui_update_tests = {
        'ensemble_display': stored_analysis_results.get('ensemble_analysis', {}).get('model_breakdown', []),
        'trade_instruction': stored_analysis_results.get('trade_instruction', {}),
        'individual_results': stored_analysis_results.get('individual_results', {}),
        'vote_breakdown': stored_analysis_results.get('ensemble_analysis', {}).get('vote_breakdown', {}),
        'cycle_stats': stored_analysis_results.get('cycle_metadata', {})
    }
    
    validation_results = {}
    for test_name, test_data in gui_update_tests.items():
        if test_data:
            validation_results[test_name] = "✅ VALID"
        else:
            validation_results[test_name] = "❌ EMPTY"
    
    print("GUI Update Data Validation:")
    for test_name, result in validation_results.items():
        print(f"   {test_name}: {result}")
    
    # Calculate overall success rate
    valid_count = sum(1 for result in validation_results.values() if "✅" in result)
    total_count = len(validation_results)
    success_rate = (valid_count / total_count) * 100
    
    print(f"\n🎯 OVERALL SUCCESS RATE: {valid_count}/{total_count} ({success_rate:.1f}%)")
    
    if success_rate >= 90:
        print("🎉 EXCELLENT: LLM-GUI integration ready for production!")
    elif success_rate >= 75:
        print("✅ GOOD: LLM-GUI integration working well")
    else:
        print("⚠️ NEEDS IMPROVEMENT: Some integration issues detected")
    
    return stored_analysis_results, success_rate

if __name__ == "__main__":
    print("🚀 Starting LLM-GUI Integration Test...")
    results, success_rate = test_llm_gui_integration()
    print(f"\n🏁 TEST COMPLETE - Success Rate: {success_rate:.1f}%")
    
    # Save test results for inspection
    with open('test_llm_gui_results.json', 'w') as f:
        # Convert datetime objects to strings for JSON serialization
        serializable_results = {}
        for key, value in results.items():
            if key == 'last_llm_update':
                serializable_results[key] = value.isoformat()
            elif key == 'individual_results':
                serializable_results[key] = {}
                for k, v in value.items():
                    serializable_results[key][k] = {**v, 'timestamp': v['timestamp'].isoformat()}
            else:
                serializable_results[key] = value
        
        json.dump(serializable_results, f, indent=2)
    
    print("📄 Test results saved to test_llm_gui_results.json")
