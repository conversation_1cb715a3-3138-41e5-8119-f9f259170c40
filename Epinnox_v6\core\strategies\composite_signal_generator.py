#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Composite Signal Generator
------------------------
Combines signals from multiple indicators to generate a composite signal.
"""

import pandas as pd
import numpy as np
import logging
from strategies.signal_generator import SignalGenerator

logger = logging.getLogger('strategy.composite_signal_generator')

class CompositeSignalGenerator(SignalGenerator):
    """
    Composite Signal Generator implementation.
    
    This class combines signals from multiple indicators to generate a composite signal.
    """
    
    def __init__(self, config=None, name="CompositeSignalGenerator"):
        """
        Initialize the Composite Signal Generator.
        
        Args:
            config (dict): Configuration dictionary.
            name (str): Name of the signal generator.
        """
        super().__init__(config, name=name)
        self.indicators = []
        
    def add_indicator(self, indicator):
        """
        Add an indicator to the composite signal generator.
        
        Args:
            indicator (SignalGenerator): Indicator to add.
        """
        self.indicators.append(indicator)
        logger.debug(f"Added indicator: {indicator.name} with weight {indicator.weight}")
        
    def generate_signal(self, df, **kwargs):
        """
        Generate a composite signal by combining signals from all indicators.
        
        Args:
            df (pd.DataFrame): OHLCV DataFrame.
            **kwargs: Additional arguments.
            
        Returns:
            dict: Composite signal dictionary.
        """
        try:
            if not self.indicators:
                logger.warning("No indicators added to composite signal generator")
                return {'score': 0.5, 'direction': 'neutral'}
            
            # Generate signals from all indicators
            signals = []
            for indicator in self.indicators:
                signal = indicator.generate_signal(df, **kwargs)
                signals.append(signal)
                logger.debug(f"Indicator {indicator.name} generated signal: score={signal['score']:.2f}, direction={signal['direction']}")
            
            # Calculate weighted average score
            total_weight = sum(signal.get('weight', 1.0) for signal in signals)
            if total_weight == 0:
                total_weight = 1.0  # Avoid division by zero
                
            weighted_score = sum(signal['score'] * signal.get('weight', 1.0) for signal in signals) / total_weight
            
            # Ensure score is between 0 and 1
            weighted_score = self.normalize_score(weighted_score)
            
            # Determine direction based on weighted score
            direction = 'neutral'
            if weighted_score >= 0.6:
                direction = 'buy'
            elif weighted_score <= 0.4:
                direction = 'sell'
            
            # Create composite result
            result = {
                'score': weighted_score,
                'direction': direction,
                'indicators': {indicator.name: signals[i] for i, indicator in enumerate(self.indicators)},
                'indicator_scores': {indicator.name: signals[i]['score'] for i, indicator in enumerate(self.indicators)},
                'indicator_directions': {indicator.name: signals[i]['direction'] for i, indicator in enumerate(self.indicators)}
            }
            
            # Add any additional data from indicators
            for i, indicator in enumerate(self.indicators):
                if indicator.name == "EMA Bands" and 'bands' in signals[i]:
                    result['bands'] = signals[i]['bands']
                    
                if indicator.name == "RSI" and 'rsi' in signals[i]:
                    result['rsi'] = signals[i]['rsi']
                    
                if indicator.name == "MACD" and 'macd' in signals[i]:
                    result['macd'] = signals[i]['macd']
                    result['macd_signal'] = signals[i]['signal']
                    result['macd_histogram'] = signals[i]['histogram']
            
            return result
            
        except Exception as e:
            logger.error(f"Error generating composite signal: {e}", exc_info=True)
            return {'score': 0.5, 'direction': 'neutral'}
