"""
Error Recovery System for Epinnox Trading System
Provides comprehensive error handling, retry mechanisms, and system recovery
"""

import asyncio
import logging
import time
import traceback
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Callable, Union
from dataclasses import dataclass, field
from enum import Enum
import functools

logger = logging.getLogger(__name__)

class ErrorSeverity(Enum):
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"

class ErrorCategory(Enum):
    NETWORK = "network"
    API = "api"
    EXECUTION = "execution"
    DATA = "data"
    SYSTEM = "system"
    VALIDATION = "validation"
    TIMEOUT = "timeout"

class RecoveryAction(Enum):
    RETRY = "retry"
    FALLBACK = "fallback"
    SKIP = "skip"
    EMERGENCY_STOP = "emergency_stop"
    RESTART_COMPONENT = "restart_component"

@dataclass
class ErrorRecord:
    """Error record for tracking and analysis"""
    timestamp: datetime
    error_type: str
    error_message: str
    severity: ErrorSeverity
    category: ErrorCategory
    component: str
    function_name: str
    traceback_info: str
    context: Dict[str, Any] = field(default_factory=dict)
    recovery_action: Optional[RecoveryAction] = None
    recovery_successful: bool = False
    retry_count: int = 0

@dataclass
class RetryConfig:
    """Configuration for retry mechanisms"""
    max_retries: int = 3
    base_delay: float = 1.0
    max_delay: float = 60.0
    exponential_base: float = 2.0
    jitter: bool = True
    retry_on_exceptions: List[type] = field(default_factory=lambda: [Exception])

class ErrorRecoverySystem:
    """
    Comprehensive Error Recovery System that provides:
    1. Graceful exception handling with context preservation
    2. Intelligent retry mechanisms with exponential backoff
    3. Error classification and severity assessment
    4. Automatic recovery strategies
    5. System health monitoring and recovery
    6. Error analytics and pattern detection
    """
    
    def __init__(self, config: Dict[str, Any] = None):
        """Initialize the error recovery system"""
        self.config = config or {}
        
        # Error tracking
        self.error_history: List[ErrorRecord] = []
        self.error_patterns: Dict[str, int] = {}
        self.component_health: Dict[str, float] = {}
        
        # Recovery configuration
        self.default_retry_config = RetryConfig(
            max_retries=self.config.get('default_max_retries', 3),
            base_delay=self.config.get('default_base_delay', 1.0),
            max_delay=self.config.get('default_max_delay', 60.0),
            exponential_base=self.config.get('default_exponential_base', 2.0),
            jitter=self.config.get('default_jitter', True)
        )
        
        # Component-specific configurations
        self.component_configs: Dict[str, RetryConfig] = {}
        
        # Recovery strategies
        self.recovery_strategies: Dict[str, Callable] = {}
        self.fallback_handlers: Dict[str, Callable] = {}
        
        # System state
        self.system_health_score = 100.0
        self.emergency_mode = False
        self.recovery_in_progress = False
        
        # Monitoring
        self.monitoring_enabled = True
        self.health_check_interval = self.config.get('health_check_interval', 60)
        self.health_check_task = None
        
        logger.info("[ERROR_RECOVERY] Error Recovery System initialized")
    
    async def initialize(self):
        """Initialize the error recovery system"""
        # Load component-specific configurations
        self._load_component_configs()
        
        # Register default recovery strategies
        self._register_default_strategies()
        
        # Start health monitoring
        await self._start_health_monitoring()
        
        logger.info("[ERROR_RECOVERY] Error Recovery System ready")
        return True
    
    def _load_component_configs(self):
        """Load component-specific retry configurations"""
        component_configs = self.config.get('component_configs', {})
        
        for component, config in component_configs.items():
            self.component_configs[component] = RetryConfig(
                max_retries=config.get('max_retries', 3),
                base_delay=config.get('base_delay', 1.0),
                max_delay=config.get('max_delay', 60.0),
                exponential_base=config.get('exponential_base', 2.0),
                jitter=config.get('jitter', True),
                retry_on_exceptions=config.get('retry_on_exceptions', [Exception])
            )
    
    def _register_default_strategies(self):
        """Register default recovery strategies"""
        self.recovery_strategies.update({
            'network_error': self._handle_network_error,
            'api_error': self._handle_api_error,
            'execution_error': self._handle_execution_error,
            'data_error': self._handle_data_error,
            'timeout_error': self._handle_timeout_error,
            'validation_error': self._handle_validation_error
        })
    
    async def _start_health_monitoring(self):
        """Start system health monitoring"""
        if self.health_check_task:
            return
        
        self.health_check_task = asyncio.create_task(self._health_monitoring_loop())
        logger.info("[ERROR_RECOVERY] Health monitoring started")
    
    async def _health_monitoring_loop(self):
        """Health monitoring loop"""
        while self.monitoring_enabled:
            try:
                await self._update_system_health()
                await self._check_error_patterns()
                await asyncio.sleep(self.health_check_interval)
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"[ERROR_RECOVERY] Health monitoring error: {e}")
                await asyncio.sleep(self.health_check_interval)
    
    def with_retry(self, component: str = None, config: RetryConfig = None):
        """Decorator for adding retry functionality to functions"""
        def decorator(func):
            @functools.wraps(func)
            async def async_wrapper(*args, **kwargs):
                return await self._execute_with_retry(func, args, kwargs, component, config)
            
            @functools.wraps(func)
            def sync_wrapper(*args, **kwargs):
                return asyncio.run(self._execute_with_retry(func, args, kwargs, component, config))
            
            # Return appropriate wrapper based on function type
            if asyncio.iscoroutinefunction(func):
                return async_wrapper
            else:
                return sync_wrapper
        
        return decorator
    
    async def _execute_with_retry(self, func, args, kwargs, component: str = None, config: RetryConfig = None):
        """Execute function with retry logic"""
        # Get retry configuration
        retry_config = config or self.component_configs.get(component, self.default_retry_config)
        
        last_exception = None
        
        for attempt in range(retry_config.max_retries + 1):
            try:
                # Execute function
                if asyncio.iscoroutinefunction(func):
                    result = await func(*args, **kwargs)
                else:
                    result = func(*args, **kwargs)
                
                # Success - reset component health if it was degraded
                if component and self.component_health.get(component, 100) < 100:
                    self.component_health[component] = min(100, self.component_health.get(component, 0) + 20)
                
                return result
                
            except Exception as e:
                last_exception = e
                
                # Check if we should retry this exception
                if not any(isinstance(e, exc_type) for exc_type in retry_config.retry_on_exceptions):
                    break
                
                # Record error
                error_record = self._create_error_record(e, component, func.__name__, attempt)
                self._record_error(error_record)
                
                # Check if we've exhausted retries
                if attempt >= retry_config.max_retries:
                    break
                
                # Calculate delay with exponential backoff and jitter
                delay = min(
                    retry_config.base_delay * (retry_config.exponential_base ** attempt),
                    retry_config.max_delay
                )
                
                if retry_config.jitter:
                    import random
                    delay *= (0.5 + random.random() * 0.5)  # Add 0-50% jitter
                
                logger.warning(f"[ERROR_RECOVERY] Retry {attempt + 1}/{retry_config.max_retries} "
                             f"for {func.__name__} in {delay:.2f}s: {str(e)}")
                
                await asyncio.sleep(delay)
        
        # All retries exhausted - attempt recovery
        if last_exception:
            await self._attempt_recovery(last_exception, component, func.__name__)
            raise last_exception
    
    def _create_error_record(self, exception: Exception, component: str, function_name: str, retry_count: int) -> ErrorRecord:
        """Create error record from exception"""
        # Classify error
        severity = self._classify_error_severity(exception)
        category = self._classify_error_category(exception)
        
        return ErrorRecord(
            timestamp=datetime.now(),
            error_type=type(exception).__name__,
            error_message=str(exception),
            severity=severity,
            category=category,
            component=component or "unknown",
            function_name=function_name,
            traceback_info=traceback.format_exc(),
            retry_count=retry_count
        )
    
    def _classify_error_severity(self, exception: Exception) -> ErrorSeverity:
        """Classify error severity based on exception type and message"""
        error_type = type(exception).__name__
        error_message = str(exception).lower()
        
        # Critical errors
        if any(keyword in error_message for keyword in ['emergency', 'critical', 'fatal', 'system failure']):
            return ErrorSeverity.CRITICAL
        
        # High severity errors
        if any(keyword in error_message for keyword in ['connection', 'authentication', 'authorization', 'timeout']):
            return ErrorSeverity.HIGH
        
        # Medium severity errors
        if any(keyword in error_message for keyword in ['validation', 'format', 'parsing', 'data']):
            return ErrorSeverity.MEDIUM
        
        # Default to low severity
        return ErrorSeverity.LOW
    
    def _classify_error_category(self, exception: Exception) -> ErrorCategory:
        """Classify error category based on exception type and message"""
        error_type = type(exception).__name__
        error_message = str(exception).lower()
        
        # Network errors
        if any(keyword in error_message for keyword in ['connection', 'network', 'socket', 'dns']):
            return ErrorCategory.NETWORK
        
        # API errors
        if any(keyword in error_message for keyword in ['api', 'http', 'request', 'response', 'status']):
            return ErrorCategory.API
        
        # Execution errors
        if any(keyword in error_message for keyword in ['execution', 'order', 'trade', 'position']):
            return ErrorCategory.EXECUTION
        
        # Data errors
        if any(keyword in error_message for keyword in ['data', 'parsing', 'format', 'json', 'xml']):
            return ErrorCategory.DATA
        
        # Timeout errors
        if any(keyword in error_message for keyword in ['timeout', 'deadline', 'expired']):
            return ErrorCategory.TIMEOUT
        
        # Validation errors
        if any(keyword in error_message for keyword in ['validation', 'invalid', 'missing', 'required']):
            return ErrorCategory.VALIDATION
        
        # Default to system
        return ErrorCategory.SYSTEM
    
    def _record_error(self, error_record: ErrorRecord):
        """Record error for tracking and analysis"""
        # Add to history
        self.error_history.append(error_record)
        
        # Limit history size
        max_history = self.config.get('max_error_history', 1000)
        if len(self.error_history) > max_history:
            self.error_history = self.error_history[-max_history:]
        
        # Update error patterns
        pattern_key = f"{error_record.component}:{error_record.error_type}"
        self.error_patterns[pattern_key] = self.error_patterns.get(pattern_key, 0) + 1
        
        # Update component health
        if error_record.component:
            current_health = self.component_health.get(error_record.component, 100)
            severity_impact = {
                ErrorSeverity.LOW: 5,
                ErrorSeverity.MEDIUM: 10,
                ErrorSeverity.HIGH: 20,
                ErrorSeverity.CRITICAL: 40
            }
            
            impact = severity_impact.get(error_record.severity, 10)
            self.component_health[error_record.component] = max(0, current_health - impact)
    
    async def _attempt_recovery(self, exception: Exception, component: str, function_name: str):
        """Attempt to recover from error"""
        try:
            self.recovery_in_progress = True
            
            # Get error category for recovery strategy
            category = self._classify_error_category(exception)
            strategy_key = f"{category.value}_error"
            
            # Try component-specific recovery first
            if component and component in self.recovery_strategies:
                await self.recovery_strategies[component](exception, component, function_name)
            # Try category-specific recovery
            elif strategy_key in self.recovery_strategies:
                await self.recovery_strategies[strategy_key](exception, component, function_name)
            # Try fallback handler
            elif component and component in self.fallback_handlers:
                await self.fallback_handlers[component](exception, component, function_name)
            
            logger.info(f"[ERROR_RECOVERY] Recovery attempted for {component}:{function_name}")
            
        except Exception as recovery_error:
            logger.error(f"[ERROR_RECOVERY] Recovery failed: {recovery_error}")
        finally:
            self.recovery_in_progress = False

    # Default recovery strategy implementations
    async def _handle_network_error(self, exception: Exception, component: str, function_name: str):
        """Handle network-related errors"""
        logger.info(f"[ERROR_RECOVERY] Handling network error for {component}")

        # Wait for network to stabilize
        await asyncio.sleep(5)

        # Could implement network connectivity checks here
        # For now, just log the recovery attempt
        logger.info(f"[ERROR_RECOVERY] Network error recovery completed for {component}")

    async def _handle_api_error(self, exception: Exception, component: str, function_name: str):
        """Handle API-related errors"""
        logger.info(f"[ERROR_RECOVERY] Handling API error for {component}")

        # Check if it's a rate limit error
        if 'rate limit' in str(exception).lower():
            # Wait longer for rate limit recovery
            await asyncio.sleep(30)
        else:
            # Standard API error recovery
            await asyncio.sleep(10)

        logger.info(f"[ERROR_RECOVERY] API error recovery completed for {component}")

    async def _handle_execution_error(self, exception: Exception, component: str, function_name: str):
        """Handle execution-related errors"""
        logger.warning(f"[ERROR_RECOVERY] Handling execution error for {component}")

        # Execution errors are serious - might need to check system state
        # For now, just log and wait
        await asyncio.sleep(15)

        logger.info(f"[ERROR_RECOVERY] Execution error recovery completed for {component}")

    async def _handle_data_error(self, exception: Exception, component: str, function_name: str):
        """Handle data-related errors"""
        logger.info(f"[ERROR_RECOVERY] Handling data error for {component}")

        # Data errors might be temporary - short wait
        await asyncio.sleep(2)

        logger.info(f"[ERROR_RECOVERY] Data error recovery completed for {component}")

    async def _handle_timeout_error(self, exception: Exception, component: str, function_name: str):
        """Handle timeout-related errors"""
        logger.info(f"[ERROR_RECOVERY] Handling timeout error for {component}")

        # Timeout errors - wait and hope for better conditions
        await asyncio.sleep(10)

        logger.info(f"[ERROR_RECOVERY] Timeout error recovery completed for {component}")

    async def _handle_validation_error(self, exception: Exception, component: str, function_name: str):
        """Handle validation-related errors"""
        logger.info(f"[ERROR_RECOVERY] Handling validation error for {component}")

        # Validation errors usually don't need recovery time
        # Just log the attempt
        logger.info(f"[ERROR_RECOVERY] Validation error recovery completed for {component}")

    async def _update_system_health(self):
        """Update overall system health score"""
        if not self.component_health:
            self.system_health_score = 100.0
            return

        # Calculate weighted average of component health
        total_health = sum(self.component_health.values())
        component_count = len(self.component_health)

        if component_count > 0:
            self.system_health_score = total_health / component_count
        else:
            self.system_health_score = 100.0

        # Check for emergency mode
        if self.system_health_score < 30 and not self.emergency_mode:
            self.emergency_mode = True
            logger.critical(f"[ERROR_RECOVERY] System health critical: {self.system_health_score:.1f}% - entering emergency mode")
        elif self.system_health_score > 70 and self.emergency_mode:
            self.emergency_mode = False
            logger.info(f"[ERROR_RECOVERY] System health recovered: {self.system_health_score:.1f}% - exiting emergency mode")

    async def _check_error_patterns(self):
        """Check for error patterns that might indicate systemic issues"""
        # Check for repeated errors in short time periods
        recent_errors = [
            error for error in self.error_history
            if (datetime.now() - error.timestamp).total_seconds() < 300  # Last 5 minutes
        ]

        if len(recent_errors) > 10:
            logger.warning(f"[ERROR_RECOVERY] High error rate detected: {len(recent_errors)} errors in 5 minutes")

        # Check for component-specific error patterns
        component_error_counts = {}
        for error in recent_errors:
            component_error_counts[error.component] = component_error_counts.get(error.component, 0) + 1

        for component, count in component_error_counts.items():
            if count > 5:
                logger.warning(f"[ERROR_RECOVERY] Component {component} has {count} errors in 5 minutes")

    # Public interface methods
    def register_recovery_strategy(self, key: str, strategy: Callable):
        """Register a custom recovery strategy"""
        self.recovery_strategies[key] = strategy
        logger.info(f"[ERROR_RECOVERY] Registered recovery strategy: {key}")

    def register_fallback_handler(self, component: str, handler: Callable):
        """Register a fallback handler for a component"""
        self.fallback_handlers[component] = handler
        logger.info(f"[ERROR_RECOVERY] Registered fallback handler for: {component}")

    def get_component_health(self, component: str) -> float:
        """Get health score for a specific component"""
        return self.component_health.get(component, 100.0)

    def get_system_health(self) -> float:
        """Get overall system health score"""
        return self.system_health_score

    def get_error_statistics(self) -> Dict[str, Any]:
        """Get error statistics and analytics"""
        if not self.error_history:
            return {
                'total_errors': 0,
                'error_rate_per_hour': 0,
                'most_common_errors': [],
                'component_health': self.component_health.copy(),
                'system_health': self.system_health_score
            }

        # Calculate error rate
        recent_errors = [
            error for error in self.error_history
            if (datetime.now() - error.timestamp).total_seconds() < 3600  # Last hour
        ]

        # Most common error types
        error_type_counts = {}
        for error in self.error_history[-100:]:  # Last 100 errors
            error_type_counts[error.error_type] = error_type_counts.get(error.error_type, 0) + 1

        most_common = sorted(error_type_counts.items(), key=lambda x: x[1], reverse=True)[:5]

        return {
            'total_errors': len(self.error_history),
            'recent_errors_1h': len(recent_errors),
            'error_rate_per_hour': len(recent_errors),
            'most_common_errors': most_common,
            'component_health': self.component_health.copy(),
            'system_health': self.system_health_score,
            'emergency_mode': self.emergency_mode,
            'recovery_in_progress': self.recovery_in_progress
        }

    def get_recent_errors(self, limit: int = 10) -> List[ErrorRecord]:
        """Get recent error records"""
        return self.error_history[-limit:] if self.error_history else []

    def clear_error_history(self):
        """Clear error history (use with caution)"""
        self.error_history.clear()
        self.error_patterns.clear()
        logger.info("[ERROR_RECOVERY] Error history cleared")

    def reset_component_health(self, component: str = None):
        """Reset component health scores"""
        if component:
            self.component_health[component] = 100.0
            logger.info(f"[ERROR_RECOVERY] Reset health for component: {component}")
        else:
            self.component_health.clear()
            self.system_health_score = 100.0
            logger.info("[ERROR_RECOVERY] Reset all component health scores")

    async def force_recovery(self, component: str):
        """Force recovery attempt for a specific component"""
        logger.info(f"[ERROR_RECOVERY] Forcing recovery for component: {component}")

        try:
            if component in self.recovery_strategies:
                await self.recovery_strategies[component](None, component, "force_recovery")
            elif component in self.fallback_handlers:
                await self.fallback_handlers[component](None, component, "force_recovery")
            else:
                # Generic recovery
                await asyncio.sleep(5)

            # Reset component health
            self.component_health[component] = 100.0
            logger.info(f"[ERROR_RECOVERY] Forced recovery completed for: {component}")

        except Exception as e:
            logger.error(f"[ERROR_RECOVERY] Forced recovery failed for {component}: {e}")

    def is_emergency_mode(self) -> bool:
        """Check if system is in emergency mode"""
        return self.emergency_mode

    def get_status(self) -> Dict[str, Any]:
        """Get current status of error recovery system"""
        return {
            'system_health': self.system_health_score,
            'emergency_mode': self.emergency_mode,
            'recovery_in_progress': self.recovery_in_progress,
            'monitoring_enabled': self.monitoring_enabled,
            'total_errors': len(self.error_history),
            'component_count': len(self.component_health),
            'registered_strategies': len(self.recovery_strategies),
            'registered_fallbacks': len(self.fallback_handlers)
        }

    async def shutdown(self):
        """Shutdown the error recovery system"""
        self.monitoring_enabled = False

        if self.health_check_task:
            self.health_check_task.cancel()
            try:
                await self.health_check_task
            except asyncio.CancelledError:
                pass

        logger.info("[ERROR_RECOVERY] Error Recovery System shutdown complete")

# Convenience function for creating retry decorator
def retry_on_error(component: str = None, max_retries: int = 3, base_delay: float = 1.0):
    """Convenience function for creating retry decorator"""
    config = RetryConfig(max_retries=max_retries, base_delay=base_delay)

    # This would need to be connected to a global error recovery system instance
    # For now, create a simple retry decorator
    def decorator(func):
        @functools.wraps(func)
        async def wrapper(*args, **kwargs):
            last_exception = None

            for attempt in range(max_retries + 1):
                try:
                    if asyncio.iscoroutinefunction(func):
                        return await func(*args, **kwargs)
                    else:
                        return func(*args, **kwargs)
                except Exception as e:
                    last_exception = e

                    if attempt >= max_retries:
                        break

                    delay = base_delay * (2 ** attempt)
                    logger.warning(f"Retry {attempt + 1}/{max_retries} for {func.__name__} in {delay:.2f}s: {str(e)}")
                    await asyncio.sleep(delay)

            if last_exception:
                raise last_exception

        return wrapper
    return decorator
