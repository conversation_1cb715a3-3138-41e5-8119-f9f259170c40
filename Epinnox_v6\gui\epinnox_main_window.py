#!/usr/bin/env python3
"""
Epinnox Main Window
Modular main window class extracted from launch_epinnox.py
"""

import sys
import os
from PyQt5.QtWidgets import *
from PyQt5.QtCore import *
from PyQt5.QtGui import *

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

class EpinnoxMainWindow(QMainWindow):
    """Main window for Epinnox v6 Trading System"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Epinnox v6 Trading System")
        self.setGeometry(100, 100, 1600, 1000)
        
        # Initialize core components
        self.setup_core_components()
        
        # Setup UI
        self.setup_ui()
        
        # Setup connections
        self.setup_connections()
        
        print("✅ Epinnox Main Window initialized")
    
    def setup_core_components(self):
        """Initialize core trading components"""
        try:
            # Import and initialize autonomous trading orchestrator
            from core.autonomous_trading_orchestrator import AutonomousTradingOrchestrator
            self.orchestrator = AutonomousTradingOrchestrator()
            
            # Import and initialize other core components
            from execution.autonomous_executor import AutonomousTradeExecutor
            from data.live_data_manager import LiveDataManager
            
            self.executor = None  # Will be initialized with exchange
            self.data_manager = LiveDataManager()
            
            print("✅ Core components initialized")
            
        except Exception as e:
            print(f"⚠️ Error initializing core components: {e}")
    
    def setup_ui(self):
        """Setup the user interface"""
        try:
            # Create central widget
            central_widget = QWidget()
            self.setCentralWidget(central_widget)
            
            # Create main layout
            main_layout = QHBoxLayout(central_widget)
            
            # Create tabs
            self.tab_widget = QTabWidget()
            main_layout.addWidget(self.tab_widget)
            
            # Add tabs
            self.setup_tabs()
            
            # Setup menu bar
            self.setup_menu_bar()
            
            # Setup status bar
            self.setup_status_bar()
            
            print("✅ UI setup complete")
            
        except Exception as e:
            print(f"❌ Error setting up UI: {e}")
    
    def setup_tabs(self):
        """Setup trading tabs"""
        try:
            # Import tab classes
            from gui.autonomous_trading_tab import AutonomousTradingTab
            from gui.live_trading_tab import LiveTradingTab
            from gui.performance_dashboard_tab import PerformanceDashboardTab
            
            # Create and add tabs
            self.autonomous_tab = AutonomousTradingTab()
            self.live_trading_tab = LiveTradingTab()
            self.performance_tab = PerformanceDashboardTab()
            
            self.tab_widget.addTab(self.autonomous_tab, "🤖 Autonomous Trading")
            self.tab_widget.addTab(self.live_trading_tab, "📈 Live Trading")
            self.tab_widget.addTab(self.performance_tab, "📊 Performance")
            
            print("✅ Tabs setup complete")
            
        except Exception as e:
            print(f"⚠️ Error setting up tabs: {e}")
            # Create fallback simple tab
            fallback_tab = QWidget()
            fallback_layout = QVBoxLayout(fallback_tab)
            fallback_layout.addWidget(QLabel("Epinnox v6 Trading System"))
            fallback_layout.addWidget(QLabel("Loading components..."))
            self.tab_widget.addTab(fallback_tab, "Main")
    
    def setup_menu_bar(self):
        """Setup menu bar"""
        try:
            menubar = self.menuBar()
            
            # File menu
            file_menu = menubar.addMenu('File')
            
            # Add actions
            exit_action = QAction('Exit', self)
            exit_action.setShortcut('Ctrl+Q')
            exit_action.triggered.connect(self.close)
            file_menu.addAction(exit_action)
            
            # Trading menu
            trading_menu = menubar.addMenu('Trading')
            
            start_autonomous_action = QAction('Start Autonomous Trading', self)
            start_autonomous_action.triggered.connect(self.start_autonomous_trading)
            trading_menu.addAction(start_autonomous_action)
            
            stop_autonomous_action = QAction('Stop Autonomous Trading', self)
            stop_autonomous_action.triggered.connect(self.stop_autonomous_trading)
            trading_menu.addAction(stop_autonomous_action)
            
            # Help menu
            help_menu = menubar.addMenu('Help')
            
            about_action = QAction('About', self)
            about_action.triggered.connect(self.show_about)
            help_menu.addAction(about_action)
            
            print("✅ Menu bar setup complete")
            
        except Exception as e:
            print(f"❌ Error setting up menu bar: {e}")
    
    def setup_status_bar(self):
        """Setup status bar"""
        try:
            self.status_bar = self.statusBar()
            self.status_bar.showMessage("Epinnox v6 Ready")
            
            # Add permanent widgets
            self.connection_status = QLabel("🔴 Disconnected")
            self.status_bar.addPermanentWidget(self.connection_status)
            
            self.balance_status = QLabel("Balance: $0.00")
            self.status_bar.addPermanentWidget(self.balance_status)
            
            print("✅ Status bar setup complete")
            
        except Exception as e:
            print(f"❌ Error setting up status bar: {e}")
    
    def setup_connections(self):
        """Setup signal connections"""
        try:
            # Connect orchestrator signals if available
            if hasattr(self.orchestrator, 'status_updated'):
                self.orchestrator.status_updated.connect(self.update_status)
            
            print("✅ Connections setup complete")
            
        except Exception as e:
            print(f"⚠️ Error setting up connections: {e}")
    
    def start_autonomous_trading(self):
        """Start autonomous trading"""
        try:
            if self.orchestrator:
                # Start orchestrator
                self.orchestrator.start_autonomous_trading()
                self.status_bar.showMessage("🤖 Autonomous trading started")
                self.connection_status.setText("🟢 Active")
                print("✅ Autonomous trading started")
            else:
                self.status_bar.showMessage("❌ Orchestrator not available")
                
        except Exception as e:
            print(f"❌ Error starting autonomous trading: {e}")
            self.status_bar.showMessage(f"❌ Error: {e}")
    
    def stop_autonomous_trading(self):
        """Stop autonomous trading"""
        try:
            if self.orchestrator:
                # Stop orchestrator
                self.orchestrator.stop_autonomous_trading()
                self.status_bar.showMessage("🛑 Autonomous trading stopped")
                self.connection_status.setText("🔴 Stopped")
                print("✅ Autonomous trading stopped")
            else:
                self.status_bar.showMessage("❌ Orchestrator not available")
                
        except Exception as e:
            print(f"❌ Error stopping autonomous trading: {e}")
            self.status_bar.showMessage(f"❌ Error: {e}")
    
    def update_status(self, message):
        """Update status bar message"""
        self.status_bar.showMessage(message)
    
    def show_about(self):
        """Show about dialog"""
        QMessageBox.about(self, "About Epinnox v6", 
                         "Epinnox v6 Trading System\n\n"
                         "Autonomous futures trading with AI decision making\n"
                         "Version 6.0\n\n"
                         "Features:\n"
                         "• Autonomous trading orchestrator\n"
                         "• LLM integration for decision making\n"
                         "• Real-time market data\n"
                         "• Risk management\n"
                         "• Performance tracking")
    
    def closeEvent(self, event):
        """Handle window close event"""
        try:
            # Stop autonomous trading if running
            if self.orchestrator:
                self.orchestrator.stop_autonomous_trading()
            
            # Accept the close event
            event.accept()
            print("✅ Epinnox Main Window closed gracefully")
            
        except Exception as e:
            print(f"⚠️ Error during close: {e}")
            event.accept()

def run_epinnox_main_window():
    """Run the main window"""
    try:
        # Create QApplication
        app = QApplication(sys.argv)
        app.setApplicationName("Epinnox v6 Trading System")
        app.setApplicationVersion("6.0")
        
        # Create and show main window
        window = EpinnoxMainWindow()
        window.show()
        
        print("✅ Epinnox v6 Main Window started")
        
        # Run application
        return app.exec_()
        
    except Exception as e:
        print(f"❌ Error running main window: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(run_epinnox_main_window())
