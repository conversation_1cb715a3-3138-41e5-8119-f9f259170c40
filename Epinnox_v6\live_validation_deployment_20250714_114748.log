2025-07-14 11:47:48,248 - INFO - __main__ - Live Validation Deployment initialized
2025-07-14 11:47:48,268 - INFO - validation.live_trading_validator - Live Trading Validator initialized
2025-07-14 11:47:50,319 - INFO - core.timer_coordinator - [TIMER_COORDINATOR] Initialized unified timer coordinator
2025-07-14 11:47:50,325 - INFO - core.emergency_stop_coordinator - Emergency Stop Coordinator initialized
2025-07-14 11:47:50,329 - INFO - core.dynamic_risk_manager - Dynamic Risk Manager initialized
2025-07-14 11:47:50,329 - INFO - core.autonomous_trading_orchestrator - Autonomous Trading Orchestrator initialized in paper mode
2025-07-14 11:47:50,331 - INFO - validation.live_trading_validator - Orchestrator registered with live trading validator
2025-07-14 11:47:50,331 - INFO - validation.live_trading_validator - Dynamic risk manager registered with live trading validator
2025-07-14 11:47:50,331 - INFO - validation.live_trading_validator - Emergency coordinator registered with live trading validator
2025-07-14 11:47:50,331 - INFO - validation.live_trading_validator - Running pre-deployment validation...
2025-07-14 11:47:50,331 - INFO - core.dynamic_risk_manager - Registered parameter change callback: on_risk_parameters_changed
2025-07-14 11:47:50,331 - INFO - core.dynamic_risk_integration - Dynamic Risk Integration initialized
2025-07-14 11:47:50,335 - INFO - core.dynamic_risk_integration - Orchestrator registered with dynamic risk integration
2025-07-14 11:47:50,335 - INFO - core.dynamic_risk_integration - Dynamic risk manager registered with dynamic risk integration
2025-07-14 11:47:50,335 - INFO - core.dynamic_risk_integration - Emergency coordinator registered with dynamic risk integration
2025-07-14 11:47:50,335 - INFO - validation.live_trading_validator - System integration validation passed
2025-07-14 11:47:50,335 - INFO - validation.live_trading_validator - Risk management validation passed
2025-07-14 11:47:50,335 - INFO - core.emergency_stop_coordinator - Testing emergency systems...
2025-07-14 11:47:50,336 - INFO - core.emergency_stop_coordinator - Emergency systems test completed successfully
2025-07-14 11:47:50,336 - INFO - core.unified_execution_engine - Unified Execution Engine initialized with LIMIT orders enforcement
2025-07-14 11:47:50,337 - INFO - core.unified_execution_engine - Unified Execution Engine initialized with LIMIT orders enforcement
2025-07-14 11:47:50,337 - INFO - validation.live_trading_validator - Safety systems validation passed
2025-07-14 11:47:50,342 - INFO - validation.live_trading_validator - Quality thresholds validation passed
2025-07-14 11:47:50,344 - INFO - validation.live_trading_validator - Credentials validation passed
2025-07-14 11:47:59,153 - INFO - validation.live_trading_validator - Exchange connectivity validation passed (Balance: $116.59)
2025-07-14 11:47:59,159 - INFO - core.dynamic_risk_integration - Updating all systems with new risk parameters
2025-07-14 11:47:59,162 - INFO - core.dynamic_risk_integration - Orchestrator attributes updated
2025-07-14 11:47:59,162 - INFO - core.dynamic_risk_integration - Individual timer intervals updated
2025-07-14 11:47:59,162 - INFO - core.dynamic_risk_integration - All systems updated with new risk parameters
2025-07-14 11:47:59,162 - INFO - core.dynamic_risk_manager - Risk level changed from Ultra-Conservative to Ultra-Conservative: Live validation deployment
2025-07-14 11:48:09,401 - INFO - core.autonomous_trading_orchestrator - Autonomous Trading Orchestrator initialized in live mode
2025-07-14 11:48:09,401 - INFO - core.autonomous_trading_orchestrator - Initializing autonomous trading system...
2025-07-14 11:48:09,401 - INFO - core.error_recovery_system - [ERROR_RECOVERY] Error Recovery System initialized
2025-07-14 11:48:09,401 - INFO - core.error_recovery_system - [ERROR_RECOVERY] Health monitoring started
2025-07-14 11:48:09,401 - INFO - core.error_recovery_system - [ERROR_RECOVERY] Error Recovery System ready
2025-07-14 11:48:09,434 - ERROR - core.autonomous_trading_orchestrator - Failed to initialize data manager: No module named 'core.websocket_manager'
2025-07-14 11:48:09,434 - ERROR - core.autonomous_trading_orchestrator - Failed to initialize autonomous trading system: No module named 'core.websocket_manager'
2025-07-14 11:48:09,438 - INFO - validation.live_trading_validator - Orchestrator registered with live trading validator
2025-07-14 11:48:11,728 - INFO - core.dynamic_risk_manager - Registered parameter change callback: on_parameters_changed
2025-07-14 11:48:11,728 - INFO - gui.dynamic_risk_control_widget - Dynamic Risk Control Widget initialized
2025-07-14 11:48:11,747 - INFO - gui.live_validation_monitor - Live Validation Monitor initialized
2025-07-14 11:48:11,762 - INFO - gui.integrated_monitoring_dashboard - Integrated Monitoring Dashboard initialized
2025-07-14 11:48:11,762 - INFO - gui.integrated_monitoring_dashboard - Orchestrator connected to monitoring dashboard
