2025-07-14 12:12:28,756 - INFO - validation.live_trading_validator - Live Trading Validator initialized
2025-07-14 12:12:31,235 - INFO - core.dynamic_risk_manager - Dynamic Risk Manager initialized
2025-07-14 12:12:31,235 - INFO - core.dynamic_risk_manager - Registered parameter change callback: on_risk_parameters_changed
2025-07-14 12:12:31,235 - INFO - core.dynamic_risk_integration - Dynamic Risk Integration initialized
2025-07-14 12:12:31,242 - INFO - core.timer_coordinator - [TIMER_COORDINATOR] Initialized unified timer coordinator
2025-07-14 12:12:31,248 - INFO - core.emergency_stop_coordinator - Emergency Stop Coordinator initialized
2025-07-14 12:12:31,255 - INFO - core.unified_execution_engine - Unified Execution Engine initialized with LIMIT orders enforcement
2025-07-14 12:12:31,255 - INFO - core.dynamic_risk_integration - Updating all systems with new risk parameters
2025-07-14 12:12:31,255 - INFO - core.dynamic_risk_integration - Individual timer intervals updated
2025-07-14 12:12:31,255 - INFO - core.dynamic_risk_integration - All systems updated with new risk parameters
2025-07-14 12:12:31,256 - INFO - core.dynamic_risk_manager - Risk level changed from Ultra-Conservative to Ultra-Conservative: System integration initialization
2025-07-14 12:12:31,256 - INFO - core.autonomous_trading_orchestrator - Autonomous Trading Orchestrator initialized in paper mode
2025-07-14 12:12:31,256 - INFO - core.dynamic_risk_integration - Orchestrator registered with dynamic risk integration
2025-07-14 12:12:31,257 - INFO - core.dynamic_risk_integration - Dynamic risk manager registered with dynamic risk integration
2025-07-14 12:12:31,257 - INFO - core.dynamic_risk_integration - Emergency coordinator registered with dynamic risk integration
2025-07-14 12:12:31,257 - INFO - core.dynamic_risk_integration - Execution engine registered with dynamic risk integration
2025-07-14 12:12:31,259 - INFO - core.emergency_stop_coordinator - Testing emergency systems...
2025-07-14 12:12:31,259 - INFO - core.emergency_stop_coordinator - Emergency systems test completed successfully
