#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
RSI Indicator
------------
Indicator module for Relative Strength Index (RSI).
"""

import pandas as pd
import numpy as np
import logging
from strategies.signal_generator import SignalGenerator

logger = logging.getLogger('strategy.indicators.rsi')

class RSIIndicator(SignalGenerator):
    """
    RSI Indicator implementation.
    
    This indicator uses the Relative Strength Index (RSI) to identify overbought and oversold conditions.
    """
    
    def __init__(self, config=None):
        """
        Initialize the RSI Indicator.
        
        Args:
            config (dict): Configuration dictionary.
        """
        super().__init__(config, name="RSI")
        self._load_parameters()
        
    def _load_parameters(self):
        """Load indicator parameters from config."""
        # Get indicator-specific parameters from config
        indicator_config = self.config.get('strategies', {}).get('atr_ema_bands', {})
        
        # RSI parameters
        self.rsi_period = indicator_config.get('rsi_period', 14)
        self.rsi_overbought = indicator_config.get('rsi_overbought', 70)
        self.rsi_oversold = indicator_config.get('rsi_oversold', 30)
        
        # Weight for this indicator
        self.weight = indicator_config.get('rsi_weight', 0.3)
        
    def generate_signal(self, df, **kwargs):
        """
        Generate trading signals based on RSI.
        
        Args:
            df (pd.DataFrame): OHLCV DataFrame.
            **kwargs: Additional arguments.
            
        Returns:
            dict: Signal dictionary with score, direction, and RSI value.
        """
        try:
            if len(df) < self.rsi_period + 10:
                return {'score': 0.5, 'direction': 'neutral', 'weight': self.weight}
            
            # Calculate RSI
            rsi = self.calculate_rsi(df, self.rsi_period)
            
            # Get the latest value
            current_rsi = rsi.iloc[-1]
            
            # Initialize signal score (0.5 is neutral)
            signal_score = 0.5
            
            # Determine signal based on RSI value
            if current_rsi <= self.rsi_oversold:
                # Oversold condition (bullish)
                signal_score = 0.7  # Bullish signal
                logger.debug(f"RSI oversold: {current_rsi:.2f} < {self.rsi_oversold}")
            elif current_rsi >= self.rsi_overbought:
                # Overbought condition (bearish)
                signal_score = 0.3  # Bearish signal
                logger.debug(f"RSI overbought: {current_rsi:.2f} > {self.rsi_overbought}")
            else:
                # RSI in neutral zone
                # Scale the RSI value from the range [oversold, overbought] to [0.6, 0.4]
                # This creates a smooth transition between bullish and bearish signals
                normalized_rsi = (current_rsi - self.rsi_oversold) / (self.rsi_overbought - self.rsi_oversold)
                signal_score = 0.6 - (normalized_rsi * 0.2)
            
            # Ensure score is between 0 and 1
            signal_score = self.normalize_score(signal_score)
            
            # Determine direction based on score
            direction = 'neutral'
            if signal_score >= 0.6:
                direction = 'buy'
            elif signal_score <= 0.4:
                direction = 'sell'
            
            return {
                'score': signal_score,
                'direction': direction,
                'rsi': current_rsi,
                'weight': self.weight
            }
            
        except Exception as e:
            logger.error(f"Error generating RSI signal: {e}", exc_info=True)
            return {'score': 0.5, 'direction': 'neutral', 'weight': self.weight}
