2025-07-13 11:34:17,849 - __main__ - INFO - [PHASE2_TEST] Loaded configuration from config/phase2_autonomous_config.yaml
2025-07-13 11:34:17,849 - __main__ - INFO - [PHASE2_TEST] Phase 2 Integration Tester initialized
2025-07-13 11:34:17,849 - __main__ - INFO - [PHASE2_TEST] Starting comprehensive Phase 2 integration tests
2025-07-13 11:34:17,849 - __main__ - INFO - [PHASE2_TEST] Running Test 1: Unified LLM Integration...
2025-07-13 11:34:19,803 - core.unified_llm_manager - INFO - [UNIFIED_LLM] Unified LLM Manager initialized
2025-07-13 11:34:19,803 - core.unified_llm_manager - INFO - [UNIFIED_LLM] Initializing LLM providers...
2025-07-13 11:34:19,816 - config.autonomous_config - INFO - Configuration loaded from configs/autonomous_trading.yaml
2025-07-13 11:34:19,820 - llama.runner - INFO - Loaded OpenAI API key from models_config.yaml
2025-07-13 11:34:19,821 - llama.runner - INFO - Using model path: C:\Users\<USER>\.lmstudio\models\lmstudio-community\Phi-3.1-mini-128k-instruct-GGUF\Phi-3.1-mini-128k-instruct-Q4_K_M.gguf
2025-07-13 11:34:19,821 - llama.runner - INFO - Using guard path: C:\Users\<USER>\Documents\dev\Epinnox_v6\models\Llama-Guard-3-8B.gguf
2025-07-13 11:34:19,821 - llama.runner - INFO - Using binary path: C:\Users\<USER>\Documents\dev\llama.cpp\main
2025-07-13 11:34:19,826 - core.unified_llm_manager - INFO - [UNIFIED_LLM] ChatGPT provider initialized
2025-07-13 11:34:19,831 - llama.lmstudio_runner - INFO - Loaded LMStudio configuration from models_config.yaml
2025-07-13 11:34:21,884 - llama.lmstudio_runner - INFO - Discovered 8 models: ['phi-3.1-mini-128k-instruct', 'openthinker3-7b', 'mistral-7b-claude-chat', 'fine_tuned_qwen1.7b', 'tsfc-model-0.0.1', 'codestral-22b-v0.1', 'deepseek-r1-distill-qwen-7b', 'text-embedding-nomic-embed-text-v1.5']
2025-07-13 11:34:21,885 - llama.lmstudio_runner - INFO - Selected preferred model from config: phi-3.1-mini-128k-instruct
2025-07-13 11:34:21,885 - llama.lmstudio_runner - INFO - Initialized LMStudio with model: phi-3.1-mini-128k-instruct
2025-07-13 11:34:23,940 - core.unified_llm_manager - INFO - [UNIFIED_LLM] LMStudio provider initialized
2025-07-13 11:34:23,943 - core.unified_llm_manager - WARNING - [UNIFIED_LLM] Failed to initialize Transformers: __init__() missing 1 required positional argument: 'model_path'
2025-07-13 11:34:23,943 - core.unified_llm_manager - WARNING - [UNIFIED_LLM] Failed to initialize Llama: __init__() missing 1 required positional argument: 'bin_path'
2025-07-13 11:34:23,944 - llama.mock_runner - INFO - Initialized MockRunner with personality: {'caution': 0.6195160614770305, 'optimism': 0.5265837049181081, 'consistency': 0.7094551593170637, 'detail_level': 0.7381806864721536, 'confidence_bias': 2.8974638171556775}
2025-07-13 11:34:23,944 - core.unified_llm_manager - INFO - [UNIFIED_LLM] Mock provider initialized
2025-07-13 11:34:23,944 - core.unified_llm_manager - INFO - [UNIFIED_LLM] Selected provider: mock (score: 109.90)
2025-07-13 11:34:23,944 - core.unified_llm_manager - INFO - [UNIFIED_LLM] Health monitoring started
2025-07-13 11:34:23,945 - core.unified_llm_manager - INFO - [UNIFIED_LLM] Initialized with 3 providers
2025-07-13 11:34:23,945 - core.unified_llm_manager - INFO - [UNIFIED_LLM] Current provider: LLMProvider.MOCK
2025-07-13 11:34:23,945 - core.standardized_prompt_handler - INFO - [PROMPT_HANDLER] Standardized prompt handler initialized
2025-07-13 11:34:23,945 - llama.mock_runner - INFO - Running mock inference
2025-07-13 11:34:23,945 - core.unified_llm_manager - ERROR - [UNIFIED_LLM] Failed to parse response: 'LLMResponse' object has no attribute 'split'
2025-07-13 11:34:23,945 - core.unified_llm_manager - WARNING - [UNIFIED_LLM] mock response below confidence threshold
2025-07-13 11:34:23,945 - core.unified_llm_manager - WARNING - [UNIFIED_LLM] Failing over to chatgpt
2025-07-13 11:34:23,945 - llama.chatgpt_runner - ERROR - ChatGPT inference failed
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\dev\Epinnox_v6\llama\chatgpt_runner.py", line 34, in run_inference
    raise ImportError("OpenAI package not available or client not initialized")
ImportError: OpenAI package not available or client not initialized
2025-07-13 11:34:23,946 - core.unified_llm_manager - ERROR - [UNIFIED_LLM] Provider call failed: OpenAI package not available or client not initialized
2025-07-13 11:34:23,946 - core.unified_llm_manager - WARNING - [UNIFIED_LLM] Failing over to lmstudio
2025-07-13 11:34:27,748 - __main__ - INFO - [PHASE2_TEST] LLM response received from lmstudio
2025-07-13 11:34:27,748 - core.unified_llm_manager - INFO - [UNIFIED_LLM] Unified LLM Manager initialized
2025-07-13 11:34:27,748 - core.standardized_prompt_handler - INFO - [PROMPT_HANDLER] Standardized prompt handler initialized
2025-07-13 11:34:27,748 - core.autonomous_llm_integration - INFO - [AUTONOMOUS_LLM] Autonomous LLM Integration initialized
2025-07-13 11:34:27,748 - core.autonomous_llm_integration - INFO - [AUTONOMOUS_LLM] Initializing autonomous LLM integration...
2025-07-13 11:34:27,748 - core.unified_llm_manager - INFO - [UNIFIED_LLM] Initializing LLM providers...
2025-07-13 11:34:27,752 - core.unified_llm_manager - INFO - [UNIFIED_LLM] ChatGPT provider initialized
2025-07-13 11:34:27,756 - llama.lmstudio_runner - INFO - Loaded LMStudio configuration from models_config.yaml
2025-07-13 11:34:29,824 - llama.lmstudio_runner - INFO - Discovered 8 models: ['phi-3.1-mini-128k-instruct', 'openthinker3-7b', 'mistral-7b-claude-chat', 'fine_tuned_qwen1.7b', 'tsfc-model-0.0.1', 'codestral-22b-v0.1', 'deepseek-r1-distill-qwen-7b', 'text-embedding-nomic-embed-text-v1.5']
2025-07-13 11:34:29,824 - llama.lmstudio_runner - INFO - Selected preferred model from config: phi-3.1-mini-128k-instruct
2025-07-13 11:34:29,824 - llama.lmstudio_runner - INFO - Initialized LMStudio with model: phi-3.1-mini-128k-instruct
2025-07-13 11:34:31,864 - core.unified_llm_manager - INFO - [UNIFIED_LLM] LMStudio provider initialized
2025-07-13 11:34:31,865 - core.unified_llm_manager - WARNING - [UNIFIED_LLM] Failed to initialize Transformers: __init__() missing 1 required positional argument: 'model_path'
2025-07-13 11:34:31,865 - core.unified_llm_manager - WARNING - [UNIFIED_LLM] Failed to initialize Llama: __init__() missing 1 required positional argument: 'bin_path'
2025-07-13 11:34:31,865 - llama.mock_runner - INFO - Initialized MockRunner with personality: {'caution': 0.8679942587577918, 'optimism': 0.5041376201316687, 'consistency': 0.6313222466261288, 'detail_level': 0.8212331494332867, 'confidence_bias': 1.451291096639082}
2025-07-13 11:34:31,866 - core.unified_llm_manager - INFO - [UNIFIED_LLM] Mock provider initialized
2025-07-13 11:34:31,866 - core.unified_llm_manager - INFO - [UNIFIED_LLM] Selected provider: mock (score: 109.90)
2025-07-13 11:34:31,866 - core.unified_llm_manager - INFO - [UNIFIED_LLM] Health monitoring started
2025-07-13 11:34:31,866 - core.unified_llm_manager - INFO - [UNIFIED_LLM] Initialized with 3 providers
2025-07-13 11:34:31,867 - core.unified_llm_manager - INFO - [UNIFIED_LLM] Current provider: LLMProvider.MOCK
2025-07-13 11:34:31,867 - core.autonomous_llm_integration - INFO - [AUTONOMOUS_LLM] Autonomous LLM integration initialized successfully
2025-07-13 11:34:31,867 - core.unified_llm_manager - INFO - [UNIFIED_LLM] Unified LLM Manager shutdown
2025-07-13 11:34:31,867 - __main__ - INFO - [PHASE2_TEST] Unified LLM integration test completed
2025-07-13 11:34:31,869 - __main__ - INFO - [PHASE2_TEST] Running Test 2: Autonomous Position Manager...
2025-07-13 11:34:31,874 - core.autonomous_position_manager - INFO - [POSITION_MANAGER] Autonomous Position Manager initialized
2025-07-13 11:34:31,874 - core.autonomous_position_manager - INFO - [POSITION_MANAGER] Position monitoring started (interval: 5s)
2025-07-13 11:34:31,874 - core.autonomous_position_manager - INFO - [POSITION_MANAGER] Autonomous Position Manager ready
2025-07-13 11:34:31,875 - core.autonomous_position_manager - INFO - [POSITION_MANAGER] Added position pos_1_1752424471: LONG 0.001 BTC/USDT:USDT @ 50000.0000
2025-07-13 11:34:31,875 - core.autonomous_position_manager - INFO - [POSITION_MANAGER] SL: 49000.0000, TP: 52000.0000
2025-07-13 11:34:31,875 - core.autonomous_position_manager - INFO - [POSITION_MANAGER] Updated SL for pos_1_1752424471: 49000.0000
2025-07-13 11:34:31,875 - core.autonomous_position_manager - INFO - [POSITION_MANAGER] Updated TP for pos_1_1752424471: 52000.0000
2025-07-13 11:34:31,875 - core.autonomous_position_manager - INFO - [POSITION_MANAGER] Exiting position pos_1_1752424471 - Reason: manual
2025-07-13 11:34:31,875 - core.autonomous_position_manager - INFO - [POSITION_MANAGER] Successfully exited position pos_1_1752424471
2025-07-13 11:34:31,875 - core.autonomous_position_manager - INFO - [POSITION_MANAGER] Position monitoring stopped
2025-07-13 11:34:31,875 - core.autonomous_position_manager - INFO - [POSITION_MANAGER] Autonomous Position Manager shutdown complete
2025-07-13 11:34:31,875 - __main__ - INFO - [PHASE2_TEST] Autonomous position manager test completed
2025-07-13 11:34:31,876 - __main__ - INFO - [PHASE2_TEST] Running Test 3: Position Watchdog System...
2025-07-13 11:34:31,877 - core.position_watchdog - INFO - [WATCHDOG] Position Watchdog initialized
2025-07-13 11:34:31,880 - core.position_watchdog - INFO - [WATCHDOG] Monitoring started (scan: 10s, health: 30s)
2025-07-13 11:34:31,880 - core.position_watchdog - INFO - [WATCHDOG] Position Watchdog ready
2025-07-13 11:34:31,880 - core.position_watchdog - WARNING - [WATCHDOG] 1 positions in critical state
2025-07-13 11:34:31,880 - core.position_watchdog - WARNING - [WATCHDOG] Generated 1 critical alerts
2025-07-13 11:34:33,885 - __main__ - ERROR - [PHASE2_TEST] Position watchdog test error: 'PositionWatchdog' object has no attribute 'force_health_check'
2025-07-13 11:34:33,889 - __main__ - INFO - [PHASE2_TEST] Running Test 4: Error Recovery System...
2025-07-13 11:34:33,893 - core.error_recovery_system - INFO - [ERROR_RECOVERY] Error Recovery System initialized
2025-07-13 11:34:33,893 - core.error_recovery_system - INFO - [ERROR_RECOVERY] Health monitoring started
2025-07-13 11:34:33,894 - core.error_recovery_system - INFO - [ERROR_RECOVERY] Error Recovery System ready
2025-07-13 11:34:33,894 - core.error_recovery_system - WARNING - [ERROR_RECOVERY] Retry 1/3 for test_function_with_retries in 0.96s: Test failure for retry testing
2025-07-13 11:34:34,857 - core.error_recovery_system - INFO - [ERROR_RECOVERY] Error Recovery System shutdown complete
2025-07-13 11:34:34,857 - __main__ - INFO - [PHASE2_TEST] Error recovery system test completed
2025-07-13 11:34:34,859 - __main__ - INFO - [PHASE2_TEST] Running Test 5: Component Integration...
2025-07-13 11:34:34,860 - core.autonomous_trading_orchestrator - INFO - Autonomous Trading Orchestrator initialized in paper mode
2025-07-13 11:34:34,861 - core.autonomous_trading_orchestrator - INFO - Initializing autonomous trading system...
2025-07-13 11:34:34,861 - core.error_recovery_system - INFO - [ERROR_RECOVERY] Error Recovery System initialized
2025-07-13 11:34:34,861 - core.error_recovery_system - INFO - [ERROR_RECOVERY] Health monitoring started
2025-07-13 11:34:34,861 - core.error_recovery_system - INFO - [ERROR_RECOVERY] Error Recovery System ready
2025-07-13 11:34:34,885 - core.websocket_manager - INFO - WebSocket manager initialized for htx_market: wss://api.huobi.pro/ws
2025-07-13 11:34:34,885 - core.websocket_manager - INFO - WebSocket manager initialized for htx_futures: wss://api.hbdm.com/swap-ws
2025-07-13 11:34:34,885 - core.websocket_manager - INFO - WebSocket manager initialized for htx_backup: wss://api-aws.huobi.pro/ws
2025-07-13 11:34:34,886 - data.market_data_manager - INFO - Initialized 3 WebSocket endpoints
2025-07-13 11:34:34,886 - data.market_data_manager - INFO - Market data manager initialized for htx
2025-07-13 11:34:34,886 - data.market_data_manager - INFO - Starting market data manager...
2025-07-13 11:34:34,886 - data.market_data_manager - INFO - Started WebSocket connection: market
2025-07-13 11:34:34,886 - data.market_data_manager - INFO - Started WebSocket connection: futures
2025-07-13 11:34:34,886 - data.market_data_manager - INFO - Started WebSocket connection: backup
2025-07-13 11:34:34,886 - core.websocket_manager - INFO - htx_market: Connecting to wss://api.huobi.pro/ws (attempt 1)
2025-07-13 11:34:34,943 - core.websocket_manager - INFO - htx_futures: Connecting to wss://api.hbdm.com/swap-ws (attempt 1)
2025-07-13 11:34:34,947 - core.websocket_manager - INFO - htx_backup: Connecting to wss://api-aws.huobi.pro/ws (attempt 1)
2025-07-13 11:34:36,899 - core.unified_llm_manager - INFO - [UNIFIED_LLM] Unified LLM Manager initialized
2025-07-13 11:34:36,899 - core.standardized_prompt_handler - INFO - [PROMPT_HANDLER] Standardized prompt handler initialized
2025-07-13 11:34:36,899 - core.autonomous_llm_integration - INFO - [AUTONOMOUS_LLM] Autonomous LLM Integration initialized
2025-07-13 11:34:36,899 - core.autonomous_llm_integration - INFO - [AUTONOMOUS_LLM] Initializing autonomous LLM integration...
2025-07-13 11:34:36,899 - core.unified_llm_manager - INFO - [UNIFIED_LLM] Initializing LLM providers...
2025-07-13 11:34:36,902 - core.unified_llm_manager - INFO - [UNIFIED_LLM] ChatGPT provider initialized
2025-07-13 11:34:36,908 - llama.lmstudio_runner - INFO - Loaded LMStudio configuration from models_config.yaml
2025-07-13 11:34:38,968 - llama.lmstudio_runner - INFO - Discovered 8 models: ['phi-3.1-mini-128k-instruct', 'openthinker3-7b', 'mistral-7b-claude-chat', 'fine_tuned_qwen1.7b', 'tsfc-model-0.0.1', 'codestral-22b-v0.1', 'deepseek-r1-distill-qwen-7b', 'text-embedding-nomic-embed-text-v1.5']
2025-07-13 11:34:38,968 - llama.lmstudio_runner - INFO - Selected preferred model from config: phi-3.1-mini-128k-instruct
2025-07-13 11:34:38,968 - llama.lmstudio_runner - INFO - Initialized LMStudio with model: phi-3.1-mini-128k-instruct
2025-07-13 11:34:41,018 - core.unified_llm_manager - INFO - [UNIFIED_LLM] LMStudio provider initialized
2025-07-13 11:34:41,018 - core.unified_llm_manager - WARNING - [UNIFIED_LLM] Failed to initialize Transformers: __init__() missing 1 required positional argument: 'model_path'
2025-07-13 11:34:41,018 - core.unified_llm_manager - WARNING - [UNIFIED_LLM] Failed to initialize Llama: __init__() missing 1 required positional argument: 'bin_path'
2025-07-13 11:34:41,019 - llama.mock_runner - INFO - Initialized MockRunner with personality: {'caution': 0.5705022518163555, 'optimism': 0.59460132741303, 'consistency': 0.8760326281016952, 'detail_level': 0.6353388104889619, 'confidence_bias': -1.5228497421674305}
2025-07-13 11:34:41,019 - core.unified_llm_manager - INFO - [UNIFIED_LLM] Mock provider initialized
2025-07-13 11:34:41,019 - core.unified_llm_manager - INFO - [UNIFIED_LLM] Selected provider: mock (score: 109.90)
2025-07-13 11:34:41,019 - core.unified_llm_manager - INFO - [UNIFIED_LLM] Health monitoring started
2025-07-13 11:34:41,019 - core.unified_llm_manager - INFO - [UNIFIED_LLM] Initialized with 3 providers
2025-07-13 11:34:41,020 - core.unified_llm_manager - INFO - [UNIFIED_LLM] Current provider: LLMProvider.MOCK
2025-07-13 11:34:41,020 - core.autonomous_llm_integration - INFO - [AUTONOMOUS_LLM] Autonomous LLM integration initialized successfully
2025-07-13 11:34:41,030 - utils.cache_manager - INFO - Cache manager initialized: cache, max_age=24h, max_size=100MB
2025-07-13 11:34:41,030 - utils.cache_manager - INFO - Cache manager initialized: cache, max_age=24h, max_size=100MB
2025-07-13 11:34:41,031 - core.llm_orchestrator - INFO - LLM Prompt Orchestrator initialized
2025-07-13 11:34:43,976 - ml.models - INFO - TensorFlow available for LSTM models
2025-07-13 11:34:43,977 - ml.models - INFO - ML Model Manager initialized
2025-07-13 11:34:44,096 - absl - WARNING - Compiled the loaded model, but the compiled metrics have yet to be built. `model.compile_metrics` will be empty until you train or evaluate the model.
2025-07-13 11:34:44,134 - ml.models - ERROR - Error loading models: Could not locate function 'mse'. Make sure custom classes are decorated with `@keras.saving.register_keras_serializable()`. Full object config: {'module': 'keras.metrics', 'class_name': 'function', 'config': 'mse', 'registered_name': 'mse'}
2025-07-13 11:34:44,139 - core.autonomous_position_manager - INFO - [POSITION_MANAGER] Autonomous Position Manager initialized
2025-07-13 11:34:44,144 - core.adaptive_risk - INFO - TA-Lib not available, using pandas-ta fallback
2025-07-13 11:34:44,145 - core.adaptive_risk - INFO - Initialized adaptive risk manager
2025-07-13 11:34:44,151 - execution.unified_execution_engine - INFO - Unified execution engine initialized in paper mode
2025-07-13 11:34:44,151 - execution.execution_adapter - INFO - Execution adapter initialized in paper mode
2025-07-13 11:34:44,156 - core.autonomous_position_manager - INFO - [POSITION_MANAGER] Position monitoring started (interval: 5s)
2025-07-13 11:34:44,156 - core.autonomous_position_manager - INFO - [POSITION_MANAGER] Autonomous Position Manager ready
2025-07-13 11:34:44,158 - core.position_watchdog - INFO - [WATCHDOG] Position Watchdog initialized
2025-07-13 11:34:44,158 - core.position_watchdog - INFO - [WATCHDOG] Monitoring started (scan: 10s, health: 30s)
2025-07-13 11:34:44,158 - core.position_watchdog - INFO - [WATCHDOG] Position Watchdog ready
2025-07-13 11:34:44,162 - portfolio.portfolio_manager - INFO - Portfolio manager initialized with $10000.00 balance
2025-07-13 11:34:44,164 - core.autonomous_trading_orchestrator - WARNING - No market data available - this is expected during testing
2025-07-13 11:34:44,166 - __main__ - INFO - [PHASE2_TEST] Component unified_llm_integration successfully initialized
2025-07-13 11:34:44,166 - __main__ - INFO - [PHASE2_TEST] Component autonomous_position_manager successfully initialized
2025-07-13 11:34:44,166 - __main__ - INFO - [PHASE2_TEST] Component position_watchdog successfully initialized
2025-07-13 11:34:44,166 - __main__ - INFO - [PHASE2_TEST] Component error_recovery_system successfully initialized
2025-07-13 11:34:44,170 - __main__ - INFO - [PHASE2_TEST] System health check: False
2025-07-13 11:34:44,170 - core.autonomous_trading_orchestrator - INFO - Stopping autonomous trading...
2025-07-13 11:34:44,171 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:34:44,171 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:34:44,172 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:34:44,172 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:34:44,172 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:34:44,172 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:34:44,172 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:34:44,172 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:34:44,172 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:34:44,172 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:34:44,172 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:34:44,172 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:34:44,172 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:34:44,172 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:34:44,173 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:34:44,173 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:34:44,173 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:34:44,173 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:34:44,173 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:34:44,173 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:34:44,173 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:34:44,173 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:34:44,173 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:34:44,173 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:34:44,173 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:34:44,173 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:34:44,173 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:34:44,173 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:34:44,174 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:34:44,174 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:34:44,174 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:34:44,174 - core.websocket_manager - ERROR - htx_futures: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:34:44,174 - core.websocket_manager - ERROR - htx_futures: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:34:44,353 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:34:44,354 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:34:44,355 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:34:44,356 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:34:44,366 - core.websocket_manager - ERROR - htx_futures: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:34:44,458 - core.websocket_manager - ERROR - htx_futures: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:34:45,311 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:34:45,327 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:34:45,397 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:34:45,423 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:34:46,175 - core.autonomous_trading_orchestrator - INFO - Autonomous trading stopped gracefully
2025-07-13 11:34:46,176 - __main__ - INFO - [PHASE2_TEST] Component integration test completed
2025-07-13 11:34:46,179 - __main__ - INFO - [PHASE2_TEST] Running Test 6: End-to-End Workflow...
2025-07-13 11:34:46,179 - __main__ - INFO - [PHASE2_TEST] Testing end-to-end workflow simulation
2025-07-13 11:34:46,179 - __main__ - INFO - [PHASE2_TEST] Simulating: Market data collection
2025-07-13 11:34:46,212 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:34:46,223 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:34:46,304 - __main__ - INFO - [PHASE2_TEST] Simulating: LLM analysis and decision
2025-07-13 11:34:46,311 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:34:46,322 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:34:46,418 - __main__ - INFO - [PHASE2_TEST] Simulating: Position entry execution
2025-07-13 11:34:46,533 - __main__ - INFO - [PHASE2_TEST] Simulating: Position monitoring
2025-07-13 11:34:46,649 - __main__ - INFO - [PHASE2_TEST] Simulating: Risk assessment
2025-07-13 11:34:46,750 - __main__ - INFO - [PHASE2_TEST] Simulating: Position exit execution
2025-07-13 11:34:46,858 - __main__ - INFO - [PHASE2_TEST] End-to-end workflow test completed
2025-07-13 11:34:46,861 - __main__ - INFO - [PHASE2_TEST] Running Test 7: Safety Systems Validation...
2025-07-13 11:34:46,861 - __main__ - INFO - [PHASE2_TEST] Testing safety systems
2025-07-13 11:34:46,861 - __main__ - INFO - [PHASE2_TEST] Safety systems test completed
2025-07-13 11:34:46,863 - __main__ - INFO - [PHASE2_TEST] Running Test 8: Performance Validation...
2025-07-13 11:34:46,863 - __main__ - INFO - [PHASE2_TEST] Testing performance validation
2025-07-13 11:34:46,863 - __main__ - INFO - [PHASE2_TEST] Performance validation test completed
2025-07-13 11:34:46,864 - __main__ - INFO - [PHASE2_TEST] Phase 2 integration tests completed - Overall: FAILED
