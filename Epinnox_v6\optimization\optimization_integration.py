#!/usr/bin/env python3
"""
🔗 OPTIMIZATION INTEGRATION MODULE
Connects hyperparameter optimization with all trading system components

INTEGRATION POINTS:
- ScalperGPT parameter updates
- Symbol Scanner configuration updates
- Timer Coordination interval adjustments
- LLM Integration parameter tuning
- Risk Management optimization
- Dynamic Risk Level parameter scaling
"""

import logging
from typing import Dict, Any, Optional, List
from datetime import datetime
import asyncio

logger = logging.getLogger(__name__)


class OptimizationIntegration:
    """
    CRITICAL: Integration layer for hyperparameter optimization
    
    Connects optimization results with all trading system components
    for seamless parameter updates and performance improvements.
    """
    
    def __init__(self):
        # Component references
        self.scalper_gpt = None
        self.symbol_scanner = None
        self.llm_integration = None
        self.orchestrator = None
        self.dynamic_risk_manager = None
        
        # Optimization tracking
        self.applied_optimizations = {}
        self.optimization_history = []
        
        logger.info("Optimization Integration initialized")
    
    def register_scalper_gpt(self, scalper_gpt):
        """Register ScalperGPT instance"""
        self.scalper_gpt = scalper_gpt
        logger.info("ScalperGPT registered with optimization integration")
    
    def register_symbol_scanner(self, symbol_scanner):
        """Register Symbol Scanner instance"""
        self.symbol_scanner = symbol_scanner
        logger.info("Symbol Scanner registered with optimization integration")
    
    def register_llm_integration(self, llm_integration):
        """Register LLM Integration instance"""
        self.llm_integration = llm_integration
        logger.info("LLM Integration registered with optimization integration")
    
    def register_orchestrator(self, orchestrator):
        """Register Autonomous Trading Orchestrator"""
        self.orchestrator = orchestrator
        logger.info("Orchestrator registered with optimization integration")
    
    def register_dynamic_risk_manager(self, dynamic_risk_manager):
        """Register Dynamic Risk Manager"""
        self.dynamic_risk_manager = dynamic_risk_manager
        logger.info("Dynamic Risk Manager registered with optimization integration")
    
    def apply_scalper_gpt_optimization(self, optimized_params: Dict[str, Any]) -> bool:
        """Apply optimized parameters to ScalperGPT"""
        try:
            if not self.scalper_gpt:
                logger.error("ScalperGPT not registered")
                return False
            
            # Update quality thresholds
            if 'spread_quality_threshold' in optimized_params:
                self.scalper_gpt.quality_thresholds['spread_quality'] = optimized_params['spread_quality_threshold']
            
            if 'decision_quality_threshold' in optimized_params:
                self.scalper_gpt.quality_thresholds['decision_quality'] = optimized_params['decision_quality_threshold']
            
            # Update analysis weights
            if hasattr(self.scalper_gpt, 'analysis_weights'):
                weight_updates = {}
                for param, value in optimized_params.items():
                    if param.endswith('_weight'):
                        weight_name = param.replace('_weight', '')
                        weight_updates[weight_name] = value
                
                if weight_updates:
                    self.scalper_gpt.analysis_weights.update(weight_updates)
            
            # Update analysis parameters
            if 'analysis_lookback_periods' in optimized_params:
                if hasattr(self.scalper_gpt, 'lookback_periods'):
                    self.scalper_gpt.lookback_periods = optimized_params['analysis_lookback_periods']
            
            # Record optimization application
            self.applied_optimizations['scalper_gpt'] = {
                'timestamp': datetime.now(),
                'parameters': optimized_params.copy()
            }
            
            logger.info("ScalperGPT optimization parameters applied successfully")
            return True
            
        except Exception as e:
            logger.error(f"Error applying ScalperGPT optimization: {e}")
            return False
    
    def apply_symbol_scanner_optimization(self, optimized_params: Dict[str, Any]) -> bool:
        """Apply optimized parameters to Symbol Scanner"""
        try:
            if not self.symbol_scanner:
                logger.error("Symbol Scanner not registered")
                return False
            
            # Update quality threshold
            if 'quality_threshold' in optimized_params:
                self.symbol_scanner.quality_threshold = optimized_params['quality_threshold']
            
            # Update scoring weights
            if hasattr(self.symbol_scanner, 'metrics_weights'):
                weight_mapping = {
                    'spread_score_weight': 'spread_score',
                    'tick_atr_score_weight': 'tick_atr_score',
                    'flow_score_weight': 'flow_score',
                    'depth_score_weight': 'depth_score',
                    'volume_score_weight': 'volume_score'
                }
                
                for param_name, weight_key in weight_mapping.items():
                    if param_name in optimized_params:
                        self.symbol_scanner.metrics_weights[weight_key] = optimized_params[param_name]
            
            # Update filtering thresholds
            if 'min_volume_threshold' in optimized_params:
                if hasattr(self.symbol_scanner, 'min_volume_threshold'):
                    self.symbol_scanner.min_volume_threshold = optimized_params['min_volume_threshold']
            
            if 'max_spread_threshold' in optimized_params:
                if hasattr(self.symbol_scanner, 'max_spread_threshold'):
                    self.symbol_scanner.max_spread_threshold = optimized_params['max_spread_threshold']
            
            # Record optimization application
            self.applied_optimizations['symbol_scanner'] = {
                'timestamp': datetime.now(),
                'parameters': optimized_params.copy()
            }
            
            logger.info("Symbol Scanner optimization parameters applied successfully")
            return True
            
        except Exception as e:
            logger.error(f"Error applying Symbol Scanner optimization: {e}")
            return False
    
    def apply_timer_coordination_optimization(self, optimized_params: Dict[str, Any]) -> bool:
        """Apply optimized parameters to Timer Coordination"""
        try:
            # Import timer coordinator
            from core.timer_coordinator import timer_coordinator
            
            # Update timer intervals
            interval_updates = {}
            
            if 'decision_loop_interval' in optimized_params:
                interval_updates['autonomous_decision_loop'] = optimized_params['decision_loop_interval']
            
            if 'scalper_analysis_interval' in optimized_params:
                interval_updates['scalper_gpt_analysis'] = optimized_params['scalper_analysis_interval']
            
            if 'symbol_scanner_interval' in optimized_params:
                interval_updates['symbol_scanner_update'] = optimized_params['symbol_scanner_interval']
            
            if 'market_data_refresh_interval' in optimized_params:
                interval_updates['market_data_refresh'] = optimized_params['market_data_refresh_interval']
            
            if 'performance_update_interval' in optimized_params:
                interval_updates['performance_update'] = optimized_params['performance_update_interval']
            
            # Apply interval updates
            if interval_updates:
                if hasattr(timer_coordinator, 'update_intervals'):
                    timer_coordinator.update_intervals(interval_updates)
                else:
                    # Fallback: update individual timers
                    for timer_name, interval in interval_updates.items():
                        if hasattr(timer_coordinator, 'update_timer_interval'):
                            timer_coordinator.update_timer_interval(timer_name, interval)
            
            # Record optimization application
            self.applied_optimizations['timer_coordination'] = {
                'timestamp': datetime.now(),
                'parameters': optimized_params.copy()
            }
            
            logger.info("Timer Coordination optimization parameters applied successfully")
            return True
            
        except Exception as e:
            logger.error(f"Error applying Timer Coordination optimization: {e}")
            return False
    
    def apply_llm_integration_optimization(self, optimized_params: Dict[str, Any]) -> bool:
        """Apply optimized parameters to LLM Integration"""
        try:
            if not self.llm_integration:
                logger.error("LLM Integration not registered")
                return False
            
            # Update confidence threshold
            if 'confidence_threshold' in optimized_params:
                if hasattr(self.llm_integration, 'confidence_threshold'):
                    self.llm_integration.confidence_threshold = optimized_params['confidence_threshold']
            
            # Update decision timeout
            if 'decision_timeout' in optimized_params:
                if hasattr(self.llm_integration, 'decision_timeout'):
                    self.llm_integration.decision_timeout = optimized_params['decision_timeout']
            
            # Update LLM parameters
            llm_params = {}
            if 'temperature' in optimized_params:
                llm_params['temperature'] = optimized_params['temperature']
            
            if 'max_tokens' in optimized_params:
                llm_params['max_tokens'] = optimized_params['max_tokens']
            
            if 'context_window_size' in optimized_params:
                llm_params['context_window_size'] = optimized_params['context_window_size']
            
            if llm_params and hasattr(self.llm_integration, 'update_llm_parameters'):
                self.llm_integration.update_llm_parameters(llm_params)
            
            # Update analysis weights
            if 'market_analysis_weight' in optimized_params:
                if hasattr(self.llm_integration, 'analysis_weights'):
                    self.llm_integration.analysis_weights['market_analysis'] = optimized_params['market_analysis_weight']
            
            if 'risk_analysis_weight' in optimized_params:
                if hasattr(self.llm_integration, 'analysis_weights'):
                    self.llm_integration.analysis_weights['risk_analysis'] = optimized_params['risk_analysis_weight']
            
            # Record optimization application
            self.applied_optimizations['llm_integration'] = {
                'timestamp': datetime.now(),
                'parameters': optimized_params.copy()
            }
            
            logger.info("LLM Integration optimization parameters applied successfully")
            return True
            
        except Exception as e:
            logger.error(f"Error applying LLM Integration optimization: {e}")
            return False
    
    def apply_risk_management_optimization(self, optimized_params: Dict[str, Any]) -> bool:
        """Apply optimized parameters to Risk Management"""
        try:
            if not self.dynamic_risk_manager:
                logger.error("Dynamic Risk Manager not registered")
                return False
            
            # Get current risk level parameters
            current_level = self.dynamic_risk_manager.current_risk_level
            current_params = self.dynamic_risk_manager.get_current_parameters()
            
            # Apply optimization multipliers
            if 'position_size_multiplier' in optimized_params:
                multiplier = optimized_params['position_size_multiplier']
                current_params.position_size_pct *= multiplier
            
            if 'stop_loss_multiplier' in optimized_params:
                multiplier = optimized_params['stop_loss_multiplier']
                current_params.emergency_loss_threshold *= multiplier
            
            if 'take_profit_multiplier' in optimized_params:
                # This would be applied to take profit logic if implemented
                pass
            
            if 'volatility_adjustment_factor' in optimized_params:
                # This would be applied to volatility-based adjustments
                pass
            
            # Update risk parameters
            self.dynamic_risk_manager.risk_parameters[current_level] = current_params
            
            # Record optimization application
            self.applied_optimizations['risk_management'] = {
                'timestamp': datetime.now(),
                'parameters': optimized_params.copy()
            }
            
            logger.info("Risk Management optimization parameters applied successfully")
            return True
            
        except Exception as e:
            logger.error(f"Error applying Risk Management optimization: {e}")
            return False
    
    def apply_optimization_result(self, component: str, optimization_result: Dict[str, Any]) -> bool:
        """Apply optimization result for specified component"""
        try:
            optimized_params = optimization_result.get('best_params', {})
            
            if component.lower() == 'scalpergpt':
                return self.apply_scalper_gpt_optimization(optimized_params)
            elif component.lower() == 'symbol scanner':
                return self.apply_symbol_scanner_optimization(optimized_params)
            elif component.lower() == 'timer coordination':
                return self.apply_timer_coordination_optimization(optimized_params)
            elif component.lower() == 'llm integration':
                return self.apply_llm_integration_optimization(optimized_params)
            elif component.lower() == 'risk management':
                return self.apply_risk_management_optimization(optimized_params)
            else:
                logger.error(f"Unknown component for optimization: {component}")
                return False
                
        except Exception as e:
            logger.error(f"Error applying optimization result for {component}: {e}")
            return False
    
    def get_applied_optimizations_summary(self) -> Dict[str, Any]:
        """Get summary of applied optimizations"""
        summary = {
            'total_optimizations': len(self.applied_optimizations),
            'components_optimized': list(self.applied_optimizations.keys()),
            'last_optimization': None,
            'optimization_details': {}
        }
        
        # Find most recent optimization
        latest_time = None
        latest_component = None
        
        for component, details in self.applied_optimizations.items():
            timestamp = details['timestamp']
            if latest_time is None or timestamp > latest_time:
                latest_time = timestamp
                latest_component = component
            
            summary['optimization_details'][component] = {
                'timestamp': timestamp.isoformat(),
                'parameter_count': len(details['parameters'])
            }
        
        if latest_component:
            summary['last_optimization'] = {
                'component': latest_component,
                'timestamp': latest_time.isoformat()
            }
        
        return summary
    
    def validate_optimization_integration(self) -> Dict[str, bool]:
        """Validate optimization integration with all components"""
        validation_results = {
            'scalper_gpt': self.scalper_gpt is not None,
            'symbol_scanner': self.symbol_scanner is not None,
            'llm_integration': self.llm_integration is not None,
            'orchestrator': self.orchestrator is not None,
            'dynamic_risk_manager': self.dynamic_risk_manager is not None
        }
        
        return validation_results


# Global instance
optimization_integration = OptimizationIntegration()
