#!/usr/bin/env python3
"""
LIVE TRADING DIAGNOSTIC TOOL
Diagnose and fix issues preventing live trading execution
"""

import sys
import os
import traceback

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def diagnose_live_trading_issues():
    """Diagnose what's preventing live trading from working"""
    
    print("LIVE TRADING DIAGNOSTIC TOOL")
    print("=" * 40)
    
    issues_found = []
    fixes_available = []
    
    # Test 1: Core imports
    print("\n1. Testing core imports...")
    try:
        from deploy_live_validation import LiveValidationDeployment
        from validation.live_trading_validator import live_trading_validator
        print("   ✅ Core deployment components imported")
    except Exception as e:
        issues_found.append(f"Core import failed: {e}")
        print(f"   ❌ Core import failed: {e}")
    
    # Test 2: System integration
    print("\n2. Testing system integration...")
    try:
        from initialize_system_integration import initialize_system_integration
        success = initialize_system_integration()
        if success:
            print("   ✅ System integration successful")
        else:
            issues_found.append("System integration failed")
            print("   ❌ System integration failed")
    except Exception as e:
        issues_found.append(f"System integration error: {e}")
        print(f"   ❌ System integration error: {e}")
    
    # Test 3: Exchange connectivity
    print("\n3. Testing exchange connectivity...")
    try:
        from trading.ccxt_trading_engine import CCXTTradingEngine
        exchange_engine = CCXTTradingEngine('htx', demo_mode=False)
        
        if exchange_engine.initialize_exchange():
            balance = exchange_engine.exchange.fetch_balance()
            usdt_balance = balance.get('USDT', {}).get('free', 0)
            print(f"   ✅ Exchange connected (Balance: ${usdt_balance:.2f})")
        else:
            issues_found.append("Exchange initialization failed")
            print("   ❌ Exchange initialization failed")
    except Exception as e:
        issues_found.append(f"Exchange connectivity error: {e}")
        print(f"   ❌ Exchange connectivity error: {e}")
    
    # Test 4: GUI availability
    print("\n4. Testing GUI availability...")
    try:
        from PyQt5.QtWidgets import QApplication
        print("   ✅ PyQt5 available for GUI monitoring")
    except ImportError:
        issues_found.append("PyQt5 not available - GUI monitoring disabled")
        print("   ⚠️ PyQt5 not available - GUI monitoring disabled")
    
    # Test 5: Risk management
    print("\n5. Testing risk management...")
    try:
        from core.dynamic_risk_manager import dynamic_risk_manager, RiskLevel
        current_level = dynamic_risk_manager.current_risk_level
        current_params = dynamic_risk_manager.get_current_parameters()
        
        print(f"   ✅ Risk level: {current_level.value}")
        print(f"   ✅ Max capital: ${current_params.max_trading_capital}")
        print(f"   ✅ Position size: {current_params.position_size_pct}%")
        
        if current_params.max_trading_capital > 100:
            issues_found.append("Trading capital exceeds safe limit")
            fixes_available.append("Set ultra-conservative risk level")
        
    except Exception as e:
        issues_found.append(f"Risk management error: {e}")
        print(f"   ❌ Risk management error: {e}")
    
    # Summary
    print("\n" + "=" * 40)
    print("DIAGNOSTIC SUMMARY")
    print("=" * 40)
    
    if not issues_found:
        print("✅ NO ISSUES FOUND - System ready for live trading")
        print("\n🚀 TO START LIVE TRADING:")
        print("1. Run: python deploy_live_validation.py")
        print("2. Follow prompts and type 'DEPLOY LIVE VALIDATION' to confirm")
        print("3. Monitor through GUI dashboard")
        print("\n⚠️ IMPORTANT: This will use real money!")
        return True
    else:
        print(f"❌ ISSUES FOUND: {len(issues_found)}")
        for i, issue in enumerate(issues_found, 1):
            print(f"  {i}. {issue}")
        
        if fixes_available:
            print(f"\n🔧 AVAILABLE FIXES: {len(fixes_available)}")
            for i, fix in enumerate(fixes_available, 1):
                print(f"  {i}. {fix}")
        
        print("\n🔧 RECOMMENDED ACTIONS:")
        print("1. Fix the issues listed above")
        print("2. Re-run this diagnostic: python diagnose_live_trading.py")
        print("3. Once all issues resolved, run: python deploy_live_validation.py")
        
        return False

def main():
    """Main diagnostic function"""
    try:
        success = diagnose_live_trading_issues()
        
        if success:
            print("\n✅ SYSTEM READY FOR LIVE TRADING")
            
            # Ask if user wants to proceed
            print("\n⚠️ FINAL WARNING: Live trading uses real money!")
            response = input("Do you want to start live trading now? (yes/no): ").strip().lower()
            
            if response == 'yes':
                print("\n🚀 Starting live trading deployment...")
                print("Run: python deploy_live_validation.py")
                print("(You must run this command manually for safety)")
            else:
                print("\n✅ Diagnostic complete - run when ready")
        else:
            print("\n❌ ISSUES MUST BE FIXED BEFORE LIVE TRADING")
        
        return 0 if success else 1
        
    except Exception as e:
        print(f"\n❌ DIAGNOSTIC ERROR: {e}")
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
