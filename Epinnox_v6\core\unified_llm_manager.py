"""
Unified LLM Manager for Epinnox Autonomous Trading System
Standardizes all LLM integrations under a single interface with health monitoring and failover
"""

import asyncio
import logging
import time
import json
from typing import Dict, List, Optional, Any, Union
from dataclasses import dataclass
from enum import Enum
from datetime import datetime, timedelta

logger = logging.getLogger(__name__)

class LLMProvider(Enum):
    """Supported LLM providers"""
    CHATGPT = "chatgpt"
    LMSTUDIO = "lmstudio"
    TRANSFORMERS = "transformers"
    LLAMA = "llama"
    MOCK = "mock"

class LLMStatus(Enum):
    """LLM health status"""
    HEALTHY = "healthy"
    DEGRADED = "degraded"
    FAILED = "failed"
    UNKNOWN = "unknown"

@dataclass
class LLMResponse:
    """Standardized LLM response format"""
    content: str
    confidence: float
    provider: LLMProvider
    response_time: float
    timestamp: datetime
    metadata: Dict[str, Any] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization"""
        return {
            'content': self.content,
            'confidence': self.confidence,
            'provider': self.provider.value,
            'response_time': self.response_time,
            'timestamp': self.timestamp.isoformat(),
            'metadata': self.metadata or {}
        }

@dataclass
class LLMHealthMetrics:
    """Health metrics for LLM providers"""
    provider: LLMProvider
    status: LLMStatus
    last_success: Optional[datetime]
    last_failure: Optional[datetime]
    success_rate: float
    avg_response_time: float
    total_requests: int
    failed_requests: int
    consecutive_failures: int
    
class UnifiedLLMManager:
    """
    Unified LLM Manager that provides:
    1. Single interface for all LLM providers
    2. Health monitoring and metrics
    3. Automatic failover between providers
    4. Response standardization and validation
    5. Performance optimization
    """
    
    def __init__(self, config: Dict[str, Any] = None):
        """Initialize the unified LLM manager"""
        self.config = config or {}
        self.providers = {}
        self.health_metrics = {}
        self.failover_order = []
        self.current_provider = None
        
        # Configuration
        self.max_response_time = self.config.get('max_response_time', 30.0)
        self.max_consecutive_failures = self.config.get('max_consecutive_failures', 3)
        self.health_check_interval = self.config.get('health_check_interval', 60)
        self.min_confidence_threshold = self.config.get('min_confidence_threshold', 0.5)
        
        # Health monitoring
        self.last_health_check = None
        self.health_check_task = None
        
        logger.info("[UNIFIED_LLM] Unified LLM Manager initialized")
    
    async def initialize(self) -> bool:
        """Initialize all available LLM providers"""
        try:
            logger.info("[UNIFIED_LLM] Initializing LLM providers...")
            
            # Initialize providers in order of preference
            await self._initialize_chatgpt()
            await self._initialize_lmstudio()
            await self._initialize_transformers()
            await self._initialize_llama()
            await self._initialize_mock()
            
            # Set failover order (most reliable first)
            self.failover_order = [
                LLMProvider.CHATGPT,
                LLMProvider.LMSTUDIO,
                LLMProvider.LLAMA,
                LLMProvider.TRANSFORMERS,
                LLMProvider.MOCK
            ]
            
            # Select initial provider
            await self._select_best_provider()
            
            # Start health monitoring
            await self._start_health_monitoring()
            
            logger.info(f"[UNIFIED_LLM] Initialized with {len(self.providers)} providers")
            logger.info(f"[UNIFIED_LLM] Current provider: {self.current_provider}")
            
            return len(self.providers) > 0
            
        except Exception as e:
            logger.error(f"[UNIFIED_LLM] Failed to initialize: {e}")
            return False
    
    async def _initialize_chatgpt(self):
        """Initialize ChatGPT provider"""
        try:
            from llama.chatgpt_runner import ChatGPTRunner
            
            # Get API key from config
            api_key = self.config.get('chatgpt', {}).get('api_key')
            if not api_key:
                # Try to load from models_config.yaml
                import yaml
                import os
                config_path = os.path.join('config', 'models_config.yaml')
                if os.path.exists(config_path):
                    with open(config_path, 'r') as f:
                        models_config = yaml.safe_load(f)
                        api_key = models_config.get('api_keys', {}).get('openai')
            
            if api_key:
                chatgpt = ChatGPTRunner(
                    api_key=api_key,
                    model=self.config.get('chatgpt', {}).get('model', 'gpt-3.5-turbo'),
                    temperature=self.config.get('chatgpt', {}).get('temperature', 0.7)
                )
                
                self.providers[LLMProvider.CHATGPT] = chatgpt
                self.health_metrics[LLMProvider.CHATGPT] = LLMHealthMetrics(
                    provider=LLMProvider.CHATGPT,
                    status=LLMStatus.UNKNOWN,
                    last_success=None,
                    last_failure=None,
                    success_rate=1.0,
                    avg_response_time=0.0,
                    total_requests=0,
                    failed_requests=0,
                    consecutive_failures=0
                )
                
                logger.info("[UNIFIED_LLM] ChatGPT provider initialized")
            else:
                logger.warning("[UNIFIED_LLM] ChatGPT API key not found")
                
        except Exception as e:
            logger.warning(f"[UNIFIED_LLM] Failed to initialize ChatGPT: {e}")
    
    async def _initialize_lmstudio(self):
        """Initialize LMStudio provider"""
        try:
            from llama.lmstudio_runner import LMStudioRunner
            
            lmstudio = LMStudioRunner(
                api_url=self.config.get('lmstudio', {}).get('api_url', 'http://localhost:1234/v1')
            )
            
            # Test connection
            if lmstudio.test_connection():
                self.providers[LLMProvider.LMSTUDIO] = lmstudio
                self.health_metrics[LLMProvider.LMSTUDIO] = LLMHealthMetrics(
                    provider=LLMProvider.LMSTUDIO,
                    status=LLMStatus.HEALTHY,
                    last_success=datetime.now(),
                    last_failure=None,
                    success_rate=1.0,
                    avg_response_time=0.0,
                    total_requests=0,
                    failed_requests=0,
                    consecutive_failures=0
                )
                
                logger.info("[UNIFIED_LLM] LMStudio provider initialized")
            else:
                logger.warning("[UNIFIED_LLM] LMStudio connection test failed")
                
        except Exception as e:
            logger.warning(f"[UNIFIED_LLM] Failed to initialize LMStudio: {e}")
    
    async def _initialize_transformers(self):
        """Initialize Transformers provider"""
        try:
            from llama.transformers_runner import TransformersRunner
            
            transformers = TransformersRunner(
                model_name=self.config.get('transformers', {}).get('model_name', 'microsoft/DialoGPT-medium')
            )
            
            self.providers[LLMProvider.TRANSFORMERS] = transformers
            self.health_metrics[LLMProvider.TRANSFORMERS] = LLMHealthMetrics(
                provider=LLMProvider.TRANSFORMERS,
                status=LLMStatus.UNKNOWN,
                last_success=None,
                last_failure=None,
                success_rate=1.0,
                avg_response_time=0.0,
                total_requests=0,
                failed_requests=0,
                consecutive_failures=0
            )
            
            logger.info("[UNIFIED_LLM] Transformers provider initialized")
            
        except Exception as e:
            logger.warning(f"[UNIFIED_LLM] Failed to initialize Transformers: {e}")
    
    async def _initialize_llama(self):
        """Initialize Llama provider"""
        try:
            from llama.llama_runner import LlamaRunner
            
            llama = LlamaRunner(
                model_path=self.config.get('llama', {}).get('model_path')
            )
            
            self.providers[LLMProvider.LLAMA] = llama
            self.health_metrics[LLMProvider.LLAMA] = LLMHealthMetrics(
                provider=LLMProvider.LLAMA,
                status=LLMStatus.UNKNOWN,
                last_success=None,
                last_failure=None,
                success_rate=1.0,
                avg_response_time=0.0,
                total_requests=0,
                failed_requests=0,
                consecutive_failures=0
            )
            
            logger.info("[UNIFIED_LLM] Llama provider initialized")
            
        except Exception as e:
            logger.warning(f"[UNIFIED_LLM] Failed to initialize Llama: {e}")
    
    async def _initialize_mock(self):
        """Initialize Mock provider for testing"""
        try:
            from llama.mock_runner import MockRunner
            
            mock = MockRunner()
            
            self.providers[LLMProvider.MOCK] = mock
            self.health_metrics[LLMProvider.MOCK] = LLMHealthMetrics(
                provider=LLMProvider.MOCK,
                status=LLMStatus.HEALTHY,
                last_success=datetime.now(),
                last_failure=None,
                success_rate=1.0,
                avg_response_time=0.1,
                total_requests=0,
                failed_requests=0,
                consecutive_failures=0
            )
            
            logger.info("[UNIFIED_LLM] Mock provider initialized")
            
        except Exception as e:
            logger.warning(f"[UNIFIED_LLM] Failed to initialize Mock: {e}")
    
    async def _select_best_provider(self):
        """Select the best available provider based on health metrics"""
        best_provider = None
        best_score = -1
        
        for provider in self.failover_order:
            if provider not in self.providers:
                continue
                
            metrics = self.health_metrics[provider]
            
            # Calculate health score
            score = self._calculate_health_score(metrics)
            
            if score > best_score:
                best_score = score
                best_provider = provider
        
        if best_provider:
            self.current_provider = best_provider
            logger.info(f"[UNIFIED_LLM] Selected provider: {best_provider.value} (score: {best_score:.2f})")
        else:
            logger.error("[UNIFIED_LLM] No healthy providers available")
    
    def _calculate_health_score(self, metrics: LLMHealthMetrics) -> float:
        """Calculate health score for a provider"""
        if metrics.status == LLMStatus.FAILED:
            return 0.0
        
        score = metrics.success_rate * 100
        
        # Penalty for consecutive failures
        score -= metrics.consecutive_failures * 10
        
        # Bonus for fast response times
        if metrics.avg_response_time > 0:
            score += max(0, 10 - metrics.avg_response_time)
        
        # Penalty for degraded status
        if metrics.status == LLMStatus.DEGRADED:
            score *= 0.5
        
        return max(0, score)

    async def generate_response(self, prompt: str, context: Dict[str, Any] = None) -> Optional[LLMResponse]:
        """
        Generate response using the best available provider with automatic failover

        Args:
            prompt: The prompt to send to the LLM
            context: Additional context for the request

        Returns:
            LLMResponse if successful, None otherwise
        """
        if not self.current_provider or self.current_provider not in self.providers:
            await self._select_best_provider()

        if not self.current_provider:
            logger.error("[UNIFIED_LLM] No providers available")
            return None

        # Try current provider first
        response = await self._try_provider(self.current_provider, prompt, context)
        if response:
            return response

        # Failover to other providers
        for provider in self.failover_order:
            if provider == self.current_provider or provider not in self.providers:
                continue

            logger.warning(f"[UNIFIED_LLM] Failing over to {provider.value}")
            response = await self._try_provider(provider, prompt, context)
            if response:
                self.current_provider = provider
                return response

        logger.error("[UNIFIED_LLM] All providers failed")
        return None

    async def _try_provider(self, provider: LLMProvider, prompt: str, context: Dict[str, Any] = None) -> Optional[LLMResponse]:
        """Try to get response from a specific provider"""
        start_time = time.time()
        metrics = self.health_metrics[provider]

        try:
            # Get the provider instance
            provider_instance = self.providers[provider]

            # Call the provider with standardized interface
            raw_response = await self._call_provider(provider_instance, prompt, context)

            if raw_response:
                response_time = time.time() - start_time

                # Parse and validate response
                parsed_response = self._parse_response(raw_response, provider, response_time)

                if parsed_response and parsed_response.confidence >= self.min_confidence_threshold:
                    # Update success metrics
                    self._update_success_metrics(metrics, response_time)
                    return parsed_response
                else:
                    logger.warning(f"[UNIFIED_LLM] {provider.value} response below confidence threshold")

            # Update failure metrics
            self._update_failure_metrics(metrics)
            return None

        except Exception as e:
            response_time = time.time() - start_time
            logger.error(f"[UNIFIED_LLM] {provider.value} failed: {e}")
            self._update_failure_metrics(metrics)
            return None

    async def _call_provider(self, provider_instance, prompt: str, context: Dict[str, Any] = None) -> Optional[str]:
        """Call provider with standardized interface"""
        try:
            # Determine the method to call based on provider type
            if hasattr(provider_instance, 'run_inference'):
                # Standard interface (LMStudio, Llama, etc.)
                return provider_instance.run_inference(prompt, temperature=0.7)
            elif hasattr(provider_instance, 'generate'):
                # Transformers interface
                return provider_instance.generate(prompt)
            elif hasattr(provider_instance, 'chat_completion'):
                # ChatGPT interface
                return await provider_instance.chat_completion(prompt)
            else:
                logger.error(f"[UNIFIED_LLM] Unknown provider interface: {type(provider_instance)}")
                return None

        except Exception as e:
            logger.error(f"[UNIFIED_LLM] Provider call failed: {e}")
            return None

    def _parse_response(self, raw_response: str, provider: LLMProvider, response_time: float) -> Optional[LLMResponse]:
        """Parse and standardize response from any provider"""
        try:
            # Extract confidence from response
            confidence = self._extract_confidence(raw_response)

            # Clean and validate content
            content = self._clean_content(raw_response)

            if not content:
                return None

            return LLMResponse(
                content=content,
                confidence=confidence,
                provider=provider,
                response_time=response_time,
                timestamp=datetime.now(),
                metadata={'raw_response': raw_response}
            )

        except Exception as e:
            logger.error(f"[UNIFIED_LLM] Failed to parse response: {e}")
            return None

    def _extract_confidence(self, response: str) -> float:
        """Extract confidence score from response"""
        try:
            # Look for confidence patterns
            import re

            # Pattern 1: CONFIDENCE: XX%
            match = re.search(r'CONFIDENCE:\s*(\d+(?:\.\d+)?)%?', response, re.IGNORECASE)
            if match:
                return float(match.group(1)) / 100.0

            # Pattern 2: confidence: XX
            match = re.search(r'confidence:\s*(\d+(?:\.\d+)?)', response, re.IGNORECASE)
            if match:
                confidence = float(match.group(1))
                return confidence / 100.0 if confidence > 1.0 else confidence

            # Default confidence based on response quality
            if len(response) > 100 and any(word in response.lower() for word in ['buy', 'sell', 'long', 'short', 'wait']):
                return 0.7
            else:
                return 0.5

        except Exception:
            return 0.5

    def _clean_content(self, response: str) -> str:
        """Clean and validate response content"""
        if not response:
            return ""

        # Remove excessive whitespace
        content = ' '.join(response.split())

        # Ensure minimum content length
        if len(content) < 10:
            return ""

        return content

    def _update_success_metrics(self, metrics: LLMHealthMetrics, response_time: float):
        """Update metrics after successful request"""
        metrics.total_requests += 1
        metrics.last_success = datetime.now()
        metrics.consecutive_failures = 0

        # Update average response time
        if metrics.avg_response_time == 0:
            metrics.avg_response_time = response_time
        else:
            metrics.avg_response_time = (metrics.avg_response_time * 0.9) + (response_time * 0.1)

        # Update success rate
        metrics.success_rate = (metrics.total_requests - metrics.failed_requests) / metrics.total_requests

        # Update status
        if response_time > self.max_response_time:
            metrics.status = LLMStatus.DEGRADED
        else:
            metrics.status = LLMStatus.HEALTHY

    def _update_failure_metrics(self, metrics: LLMHealthMetrics):
        """Update metrics after failed request"""
        metrics.total_requests += 1
        metrics.failed_requests += 1
        metrics.consecutive_failures += 1
        metrics.last_failure = datetime.now()

        # Update success rate
        metrics.success_rate = (metrics.total_requests - metrics.failed_requests) / metrics.total_requests

        # Update status based on consecutive failures
        if metrics.consecutive_failures >= self.max_consecutive_failures:
            metrics.status = LLMStatus.FAILED
        else:
            metrics.status = LLMStatus.DEGRADED

    async def _start_health_monitoring(self):
        """Start background health monitoring"""
        if self.health_check_task:
            self.health_check_task.cancel()

        self.health_check_task = asyncio.create_task(self._health_monitor_loop())
        logger.info("[UNIFIED_LLM] Health monitoring started")

    async def _health_monitor_loop(self):
        """Background health monitoring loop"""
        while True:
            try:
                await asyncio.sleep(self.health_check_interval)
                await self._perform_health_checks()

            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"[UNIFIED_LLM] Health monitor error: {e}")

    async def _perform_health_checks(self):
        """Perform health checks on all providers"""
        logger.debug("[UNIFIED_LLM] Performing health checks...")

        for provider, instance in self.providers.items():
            try:
                # Simple health check with minimal prompt
                start_time = time.time()
                response = await self._call_provider(instance, "Health check", {})
                response_time = time.time() - start_time

                metrics = self.health_metrics[provider]

                if response and len(response) > 0:
                    # Provider is responsive
                    if metrics.status == LLMStatus.FAILED:
                        metrics.status = LLMStatus.DEGRADED
                        metrics.consecutive_failures = 0
                        logger.info(f"[UNIFIED_LLM] {provider.value} recovered from failure")
                else:
                    # Provider not responding
                    metrics.consecutive_failures += 1
                    if metrics.consecutive_failures >= self.max_consecutive_failures:
                        metrics.status = LLMStatus.FAILED
                        logger.warning(f"[UNIFIED_LLM] {provider.value} marked as failed")

            except Exception as e:
                logger.debug(f"[UNIFIED_LLM] Health check failed for {provider.value}: {e}")

        # Re-select best provider if current one is unhealthy
        current_metrics = self.health_metrics.get(self.current_provider)
        if current_metrics and current_metrics.status == LLMStatus.FAILED:
            await self._select_best_provider()

    def get_health_status(self) -> Dict[str, Any]:
        """Get current health status of all providers"""
        status = {
            'current_provider': self.current_provider.value if self.current_provider else None,
            'providers': {}
        }

        for provider, metrics in self.health_metrics.items():
            status['providers'][provider.value] = {
                'status': metrics.status.value,
                'success_rate': metrics.success_rate,
                'avg_response_time': metrics.avg_response_time,
                'total_requests': metrics.total_requests,
                'failed_requests': metrics.failed_requests,
                'consecutive_failures': metrics.consecutive_failures,
                'last_success': metrics.last_success.isoformat() if metrics.last_success else None,
                'last_failure': metrics.last_failure.isoformat() if metrics.last_failure else None
            }

        return status

    async def shutdown(self):
        """Shutdown the LLM manager"""
        if self.health_check_task:
            self.health_check_task.cancel()

        logger.info("[UNIFIED_LLM] Unified LLM Manager shutdown")
