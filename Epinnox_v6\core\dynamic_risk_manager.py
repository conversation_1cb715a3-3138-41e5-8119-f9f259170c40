#!/usr/bin/env python3
"""
[TARGET] DYNAMIC RISK MANAGEMENT SYSTEM
Advanced risk level control with real-time parameter adjustment

RISK LEVELS:
- Ultra-Conservative: $100 max, 1% position, 2% portfolio risk
- Conservative: $500 max, 2% position, 5% portfolio risk  
- Moderate: $1000 max, 3% position, 8% portfolio risk
- Aggressive: $2000 max, 5% position, 12% portfolio risk
- High-Risk: $5000 max, 8% position, 20% portfolio risk

FEATURES:
- Real-time parameter adjustment
- Quality threshold scaling
- Emergency stop threshold adaptation
- System-wide configuration updates
"""

import logging
from typing import Dict, Any, Optional, Callable
from enum import Enum
from dataclasses import dataclass
from datetime import datetime
import json

logger = logging.getLogger(__name__)


class RiskLevel(Enum):
    """Risk level enumeration"""
    ULTRA_CONSERVATIVE = "Ultra-Conservative"
    CONSERVATIVE = "Conservative"
    MODERATE = "Moderate"
    AGGRESSIVE = "Aggressive"
    HIGH_RISK = "High-Risk"


@dataclass
class RiskParameters:
    """Risk parameters for each risk level"""
    max_trading_capital: float
    position_size_pct: float
    portfolio_risk_pct: float
    max_leverage: float
    max_concurrent_positions: int
    max_daily_loss_pct: float
    
    # Quality thresholds (stricter for higher risk)
    scalper_spread_quality: float
    scalper_decision_quality: float
    symbol_quality_threshold: float
    
    # Emergency thresholds
    emergency_loss_threshold: float
    emergency_drawdown_threshold: float
    
    # Timer intervals (faster for higher risk)
    decision_loop_interval: float
    scalper_analysis_interval: float
    symbol_scanner_interval: float


class DynamicRiskManager:
    """
    CRITICAL: Dynamic risk management system with real-time parameter adjustment
    
    Manages 5 risk levels with automatic parameter scaling and system-wide updates.
    Integrates with all existing systems for seamless risk level transitions.
    """
    
    def __init__(self):
        self.current_risk_level = RiskLevel.ULTRA_CONSERVATIVE
        self.risk_parameters = self._initialize_risk_parameters()
        self.parameter_change_callbacks = []
        self.risk_change_history = []
        
        logger.info("Dynamic Risk Manager initialized")
    
    def _initialize_risk_parameters(self) -> Dict[RiskLevel, RiskParameters]:
        """Initialize risk parameters for all risk levels"""
        return {
            RiskLevel.ULTRA_CONSERVATIVE: RiskParameters(
                max_trading_capital=100.0,
                position_size_pct=1.0,
                portfolio_risk_pct=2.0,
                max_leverage=2.0,
                max_concurrent_positions=1,
                max_daily_loss_pct=5.0,
                scalper_spread_quality=8.0,  # Stricter for ultra-conservative
                scalper_decision_quality=9.0,
                symbol_quality_threshold=80.0,  # Higher threshold
                emergency_loss_threshold=3.0,
                emergency_drawdown_threshold=5.0,
                decision_loop_interval=30.0,
                scalper_analysis_interval=5.0,
                symbol_scanner_interval=30.0
            ),
            
            RiskLevel.CONSERVATIVE: RiskParameters(
                max_trading_capital=500.0,
                position_size_pct=2.0,
                portfolio_risk_pct=5.0,
                max_leverage=3.0,
                max_concurrent_positions=2,
                max_daily_loss_pct=8.0,
                scalper_spread_quality=7.5,
                scalper_decision_quality=8.5,
                symbol_quality_threshold=75.0,
                emergency_loss_threshold=5.0,
                emergency_drawdown_threshold=8.0,
                decision_loop_interval=25.0,
                scalper_analysis_interval=4.0,
                symbol_scanner_interval=25.0
            ),
            
            RiskLevel.MODERATE: RiskParameters(
                max_trading_capital=1000.0,
                position_size_pct=3.0,
                portfolio_risk_pct=8.0,
                max_leverage=4.0,
                max_concurrent_positions=3,
                max_daily_loss_pct=12.0,
                scalper_spread_quality=7.0,
                scalper_decision_quality=8.0,
                symbol_quality_threshold=70.0,
                emergency_loss_threshold=8.0,
                emergency_drawdown_threshold=12.0,
                decision_loop_interval=20.0,
                scalper_analysis_interval=3.0,
                symbol_scanner_interval=20.0
            ),
            
            RiskLevel.AGGRESSIVE: RiskParameters(
                max_trading_capital=2000.0,
                position_size_pct=5.0,
                portfolio_risk_pct=12.0,
                max_leverage=5.0,
                max_concurrent_positions=4,
                max_daily_loss_pct=18.0,
                scalper_spread_quality=6.5,
                scalper_decision_quality=7.5,
                symbol_quality_threshold=65.0,
                emergency_loss_threshold=12.0,
                emergency_drawdown_threshold=18.0,
                decision_loop_interval=15.0,
                scalper_analysis_interval=2.0,
                symbol_scanner_interval=15.0
            ),
            
            RiskLevel.HIGH_RISK: RiskParameters(
                max_trading_capital=5000.0,
                position_size_pct=8.0,
                portfolio_risk_pct=20.0,
                max_leverage=6.0,
                max_concurrent_positions=5,
                max_daily_loss_pct=25.0,
                scalper_spread_quality=6.0,
                scalper_decision_quality=7.0,
                symbol_quality_threshold=60.0,
                emergency_loss_threshold=18.0,
                emergency_drawdown_threshold=25.0,
                decision_loop_interval=10.0,
                scalper_analysis_interval=1.0,
                symbol_scanner_interval=10.0
            )
        }
    
    def get_current_parameters(self) -> RiskParameters:
        """Get current risk parameters"""
        return self.risk_parameters[self.current_risk_level]
    
    def get_risk_level_parameters(self, risk_level: RiskLevel) -> RiskParameters:
        """Get parameters for specific risk level"""
        return self.risk_parameters[risk_level]
    
    def set_risk_level(self, new_risk_level: RiskLevel, reason: str = "Manual change") -> bool:
        """
        Set new risk level with real-time parameter updates
        
        Args:
            new_risk_level: New risk level to set
            reason: Reason for the change
            
        Returns:
            True if successful
        """
        try:
            old_risk_level = self.current_risk_level
            old_parameters = self.get_current_parameters()
            
            # Update current risk level
            self.current_risk_level = new_risk_level
            new_parameters = self.get_current_parameters()
            
            # Record change in history
            change_record = {
                'timestamp': datetime.now(),
                'old_level': old_risk_level.value,
                'new_level': new_risk_level.value,
                'reason': reason,
                'old_parameters': self._parameters_to_dict(old_parameters),
                'new_parameters': self._parameters_to_dict(new_parameters)
            }
            self.risk_change_history.append(change_record)
            
            # Notify all registered callbacks
            self._notify_parameter_change(old_parameters, new_parameters)
            
            logger.info(f"Risk level changed from {old_risk_level.value} to {new_risk_level.value}: {reason}")
            
            return True
            
        except Exception as e:
            logger.error(f"Error setting risk level: {e}")
            return False
    
    def register_parameter_change_callback(self, callback: Callable[[RiskParameters, RiskParameters], None]):
        """Register callback for parameter changes"""
        self.parameter_change_callbacks.append(callback)
        logger.info(f"Registered parameter change callback: {callback.__name__}")
    
    def _notify_parameter_change(self, old_parameters: RiskParameters, new_parameters: RiskParameters):
        """Notify all callbacks of parameter changes"""
        for callback in self.parameter_change_callbacks:
            try:
                callback(old_parameters, new_parameters)
            except Exception as e:
                logger.error(f"Error in parameter change callback {callback.__name__}: {e}")
    
    def _parameters_to_dict(self, parameters: RiskParameters) -> Dict[str, Any]:
        """Convert RiskParameters to dictionary"""
        return {
            'max_trading_capital': parameters.max_trading_capital,
            'position_size_pct': parameters.position_size_pct,
            'portfolio_risk_pct': parameters.portfolio_risk_pct,
            'max_leverage': parameters.max_leverage,
            'max_concurrent_positions': parameters.max_concurrent_positions,
            'max_daily_loss_pct': parameters.max_daily_loss_pct,
            'scalper_spread_quality': parameters.scalper_spread_quality,
            'scalper_decision_quality': parameters.scalper_decision_quality,
            'symbol_quality_threshold': parameters.symbol_quality_threshold,
            'emergency_loss_threshold': parameters.emergency_loss_threshold,
            'emergency_drawdown_threshold': parameters.emergency_drawdown_threshold,
            'decision_loop_interval': parameters.decision_loop_interval,
            'scalper_analysis_interval': parameters.scalper_analysis_interval,
            'symbol_scanner_interval': parameters.symbol_scanner_interval
        }
    
    def get_risk_level_summary(self) -> Dict[str, Any]:
        """Get summary of current risk level and parameters"""
        current_params = self.get_current_parameters()
        
        return {
            'current_risk_level': self.current_risk_level.value,
            'parameters': self._parameters_to_dict(current_params),
            'change_history_count': len(self.risk_change_history),
            'last_change': self.risk_change_history[-1] if self.risk_change_history else None
        }
    
    def get_all_risk_levels_info(self) -> Dict[str, Dict[str, Any]]:
        """Get information about all available risk levels"""
        return {
            level.value: self._parameters_to_dict(params)
            for level, params in self.risk_parameters.items()
        }
    
    def validate_risk_level_transition(self, from_level: RiskLevel, to_level: RiskLevel) -> Dict[str, Any]:
        """Validate if risk level transition is safe"""
        from_params = self.risk_parameters[from_level]
        to_params = self.risk_parameters[to_level]
        
        warnings = []
        
        # Check for significant capital increase
        capital_increase = (to_params.max_trading_capital / from_params.max_trading_capital) - 1
        if capital_increase > 2.0:  # More than 200% increase
            warnings.append(f"Large capital increase: {capital_increase:.1%}")
        
        # Check for significant risk increase
        risk_increase = (to_params.portfolio_risk_pct / from_params.portfolio_risk_pct) - 1
        if risk_increase > 1.0:  # More than 100% increase
            warnings.append(f"Large risk increase: {risk_increase:.1%}")
        
        # Check for quality threshold decrease
        quality_decrease = from_params.symbol_quality_threshold - to_params.symbol_quality_threshold
        if quality_decrease > 10.0:
            warnings.append(f"Quality threshold decrease: {quality_decrease:.1f} points")
        
        return {
            'safe': len(warnings) == 0,
            'warnings': warnings,
            'capital_change': capital_increase,
            'risk_change': risk_increase,
            'quality_change': quality_decrease
        }
    
    def get_recommended_risk_level(self, account_balance: float, recent_performance: Dict[str, float]) -> RiskLevel:
        """Recommend risk level based on account balance and performance"""
        try:
            # Base recommendation on account balance
            if account_balance < 200:
                base_recommendation = RiskLevel.ULTRA_CONSERVATIVE
            elif account_balance < 1000:
                base_recommendation = RiskLevel.CONSERVATIVE
            elif account_balance < 3000:
                base_recommendation = RiskLevel.MODERATE
            elif account_balance < 10000:
                base_recommendation = RiskLevel.AGGRESSIVE
            else:
                base_recommendation = RiskLevel.HIGH_RISK
            
            # Adjust based on recent performance
            win_rate = recent_performance.get('win_rate', 0.5)
            sharpe_ratio = recent_performance.get('sharpe_ratio', 0.0)
            max_drawdown = recent_performance.get('max_drawdown', 0.0)
            
            # Conservative adjustments for poor performance
            if win_rate < 0.4 or sharpe_ratio < 0.5 or max_drawdown > 0.15:
                # Move to more conservative level
                risk_levels = list(RiskLevel)
                current_index = risk_levels.index(base_recommendation)
                if current_index > 0:
                    base_recommendation = risk_levels[current_index - 1]
            
            # Aggressive adjustments for excellent performance
            elif win_rate > 0.7 and sharpe_ratio > 1.5 and max_drawdown < 0.05:
                # Move to more aggressive level
                risk_levels = list(RiskLevel)
                current_index = risk_levels.index(base_recommendation)
                if current_index < len(risk_levels) - 1:
                    base_recommendation = risk_levels[current_index + 1]
            
            return base_recommendation
            
        except Exception as e:
            logger.error(f"Error calculating recommended risk level: {e}")
            return RiskLevel.ULTRA_CONSERVATIVE  # Default to safest level


# Global instance
dynamic_risk_manager = DynamicRiskManager()
