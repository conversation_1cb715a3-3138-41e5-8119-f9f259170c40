2025-07-13 11:28:53,322 - __main__ - INFO - [PHASE2_TEST] Loaded configuration from config/phase2_autonomous_config.yaml
2025-07-13 11:28:53,322 - __main__ - INFO - [PHASE2_TEST] Phase 2 Integration Tester initialized
2025-07-13 11:28:53,322 - __main__ - INFO - [PHASE2_TEST] Starting comprehensive Phase 2 integration tests
2025-07-13 11:28:53,322 - __main__ - INFO - [PHASE2_TEST] Running Test 1: Unified LLM Integration...
2025-07-13 11:29:02,048 - core.unified_llm_manager - INFO - [UNIFIED_LLM] Unified LLM Manager initialized
2025-07-13 11:29:02,048 - core.unified_llm_manager - INFO - [UNIFIED_LLM] Initializing LLM providers...
2025-07-13 11:29:02,070 - config.autonomous_config - INFO - Configuration loaded from configs/autonomous_trading.yaml
2025-07-13 11:29:02,082 - llama.runner - INFO - Loaded OpenAI API key from models_config.yaml
2025-07-13 11:29:02,083 - llama.runner - INFO - Using model path: C:\Users\<USER>\.lmstudio\models\lmstudio-community\Phi-3.1-mini-128k-instruct-GGUF\Phi-3.1-mini-128k-instruct-Q4_K_M.gguf
2025-07-13 11:29:02,083 - llama.runner - INFO - Using guard path: C:\Users\<USER>\Documents\dev\Epinnox_v6\models\Llama-Guard-3-8B.gguf
2025-07-13 11:29:02,083 - llama.runner - INFO - Using binary path: C:\Users\<USER>\Documents\dev\llama.cpp\main
2025-07-13 11:29:02,096 - core.unified_llm_manager - INFO - [UNIFIED_LLM] ChatGPT provider initialized
2025-07-13 11:29:02,106 - llama.lmstudio_runner - INFO - Loaded LMStudio configuration from models_config.yaml
2025-07-13 11:29:04,238 - llama.lmstudio_runner - INFO - Discovered 8 models: ['openthinker3-7b', 'mistral-7b-claude-chat', 'fine_tuned_qwen1.7b', 'tsfc-model-0.0.1', 'codestral-22b-v0.1', 'phi-3.1-mini-128k-instruct', 'deepseek-r1-distill-qwen-7b', 'text-embedding-nomic-embed-text-v1.5']
2025-07-13 11:29:04,243 - llama.lmstudio_runner - INFO - Selected preferred model from config: phi-3.1-mini-128k-instruct
2025-07-13 11:29:04,243 - llama.lmstudio_runner - INFO - Initialized LMStudio with model: phi-3.1-mini-128k-instruct
2025-07-13 11:29:06,277 - core.unified_llm_manager - INFO - [UNIFIED_LLM] LMStudio provider initialized
2025-07-13 11:29:06,285 - core.unified_llm_manager - WARNING - [UNIFIED_LLM] Failed to initialize Transformers: __init__() missing 1 required positional argument: 'model_path'
2025-07-13 11:29:06,287 - core.unified_llm_manager - WARNING - [UNIFIED_LLM] Failed to initialize Llama: __init__() missing 1 required positional argument: 'bin_path'
2025-07-13 11:29:06,295 - llama.mock_runner - INFO - Initialized MockRunner with personality: {'caution': 0.8260453595303257, 'optimism': 0.5990279957088805, 'consistency': 0.8517716058295078, 'detail_level': 0.6586192676954399, 'confidence_bias': 2.8604734818916997}
2025-07-13 11:29:06,295 - core.unified_llm_manager - INFO - [UNIFIED_LLM] Mock provider initialized
2025-07-13 11:29:06,296 - core.unified_llm_manager - INFO - [UNIFIED_LLM] Selected provider: mock (score: 109.90)
2025-07-13 11:29:06,296 - core.unified_llm_manager - INFO - [UNIFIED_LLM] Health monitoring started
2025-07-13 11:29:06,296 - core.unified_llm_manager - INFO - [UNIFIED_LLM] Initialized with 3 providers
2025-07-13 11:29:06,296 - core.unified_llm_manager - INFO - [UNIFIED_LLM] Current provider: LLMProvider.MOCK
2025-07-13 11:29:06,296 - core.standardized_prompt_handler - INFO - [PROMPT_HANDLER] Standardized prompt handler initialized
2025-07-13 11:29:06,298 - llama.mock_runner - INFO - Running mock inference
2025-07-13 11:29:06,298 - core.unified_llm_manager - ERROR - [UNIFIED_LLM] Failed to parse response: 'LLMResponse' object has no attribute 'split'
2025-07-13 11:29:06,298 - core.unified_llm_manager - WARNING - [UNIFIED_LLM] mock response below confidence threshold
2025-07-13 11:29:06,298 - core.unified_llm_manager - WARNING - [UNIFIED_LLM] Failing over to chatgpt
2025-07-13 11:29:06,298 - llama.chatgpt_runner - ERROR - ChatGPT inference failed
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\dev\Epinnox_v6\llama\chatgpt_runner.py", line 34, in run_inference
    raise ImportError("OpenAI package not available or client not initialized")
ImportError: OpenAI package not available or client not initialized
2025-07-13 11:29:06,302 - core.unified_llm_manager - ERROR - [UNIFIED_LLM] Provider call failed: OpenAI package not available or client not initialized
2025-07-13 11:29:06,302 - core.unified_llm_manager - WARNING - [UNIFIED_LLM] Failing over to lmstudio
2025-07-13 11:29:22,008 - __main__ - INFO - [PHASE2_TEST] LLM response received from lmstudio
2025-07-13 11:29:22,008 - core.unified_llm_manager - INFO - [UNIFIED_LLM] Unified LLM Manager initialized
2025-07-13 11:29:22,008 - core.standardized_prompt_handler - INFO - [PROMPT_HANDLER] Standardized prompt handler initialized
2025-07-13 11:29:22,009 - core.autonomous_llm_integration - INFO - [AUTONOMOUS_LLM] Autonomous LLM Integration initialized
2025-07-13 11:29:22,009 - core.autonomous_llm_integration - INFO - [AUTONOMOUS_LLM] Initializing autonomous LLM integration...
2025-07-13 11:29:22,009 - core.unified_llm_manager - INFO - [UNIFIED_LLM] Initializing LLM providers...
2025-07-13 11:29:22,012 - core.unified_llm_manager - INFO - [UNIFIED_LLM] ChatGPT provider initialized
2025-07-13 11:29:22,016 - llama.lmstudio_runner - INFO - Loaded LMStudio configuration from models_config.yaml
2025-07-13 11:29:24,058 - llama.lmstudio_runner - INFO - Discovered 8 models: ['phi-3.1-mini-128k-instruct', 'openthinker3-7b', 'mistral-7b-claude-chat', 'fine_tuned_qwen1.7b', 'tsfc-model-0.0.1', 'codestral-22b-v0.1', 'deepseek-r1-distill-qwen-7b', 'text-embedding-nomic-embed-text-v1.5']
2025-07-13 11:29:24,059 - llama.lmstudio_runner - INFO - Selected preferred model from config: phi-3.1-mini-128k-instruct
2025-07-13 11:29:24,059 - llama.lmstudio_runner - INFO - Initialized LMStudio with model: phi-3.1-mini-128k-instruct
2025-07-13 11:29:26,115 - core.unified_llm_manager - INFO - [UNIFIED_LLM] LMStudio provider initialized
2025-07-13 11:29:26,115 - core.unified_llm_manager - WARNING - [UNIFIED_LLM] Failed to initialize Transformers: __init__() missing 1 required positional argument: 'model_path'
2025-07-13 11:29:26,115 - core.unified_llm_manager - WARNING - [UNIFIED_LLM] Failed to initialize Llama: __init__() missing 1 required positional argument: 'bin_path'
2025-07-13 11:29:26,116 - llama.mock_runner - INFO - Initialized MockRunner with personality: {'caution': 0.56444789834535, 'optimism': 0.5710610138694525, 'consistency': 0.7634961291362703, 'detail_level': 0.8102344216602084, 'confidence_bias': -4.598103381704165}
2025-07-13 11:29:26,116 - core.unified_llm_manager - INFO - [UNIFIED_LLM] Mock provider initialized
2025-07-13 11:29:26,116 - core.unified_llm_manager - INFO - [UNIFIED_LLM] Selected provider: mock (score: 109.90)
2025-07-13 11:29:26,117 - core.unified_llm_manager - INFO - [UNIFIED_LLM] Health monitoring started
2025-07-13 11:29:26,117 - core.unified_llm_manager - INFO - [UNIFIED_LLM] Initialized with 3 providers
2025-07-13 11:29:26,117 - core.unified_llm_manager - INFO - [UNIFIED_LLM] Current provider: LLMProvider.MOCK
2025-07-13 11:29:26,117 - core.autonomous_llm_integration - INFO - [AUTONOMOUS_LLM] Autonomous LLM integration initialized successfully
2025-07-13 11:29:26,117 - core.unified_llm_manager - INFO - [UNIFIED_LLM] Unified LLM Manager shutdown
2025-07-13 11:29:26,117 - __main__ - INFO - [PHASE2_TEST] Unified LLM integration test completed
2025-07-13 11:29:26,119 - __main__ - INFO - [PHASE2_TEST] Running Test 2: Autonomous Position Manager...
2025-07-13 11:29:26,124 - core.autonomous_position_manager - INFO - [POSITION_MANAGER] Autonomous Position Manager initialized
2025-07-13 11:29:26,124 - core.autonomous_position_manager - INFO - [POSITION_MANAGER] Position monitoring started (interval: 5s)
2025-07-13 11:29:26,124 - core.autonomous_position_manager - INFO - [POSITION_MANAGER] Autonomous Position Manager ready
2025-07-13 11:29:26,124 - core.autonomous_position_manager - INFO - [POSITION_MANAGER] Added position pos_1_1752424166: LONG 0.001 BTC/USDT:USDT @ 50000.0000
2025-07-13 11:29:26,124 - core.autonomous_position_manager - INFO - [POSITION_MANAGER] SL: 49000.0000, TP: 52000.0000
2025-07-13 11:29:26,124 - __main__ - ERROR - [PHASE2_TEST] Autonomous position manager test error: 'AutonomousPositionManager' object has no attribute 'get_position'
2025-07-13 11:29:26,127 - __main__ - INFO - [PHASE2_TEST] Running Test 3: Position Watchdog System...
2025-07-13 11:29:26,130 - core.position_watchdog - INFO - [WATCHDOG] Position Watchdog initialized
2025-07-13 11:29:26,130 - core.position_watchdog - INFO - [WATCHDOG] Monitoring started (scan: 10s, health: 30s)
2025-07-13 11:29:26,130 - core.position_watchdog - INFO - [WATCHDOG] Position Watchdog ready
2025-07-13 11:29:26,135 - core.position_watchdog - ERROR - [WATCHDOG] Error scanning positions: 'AutonomousPositionManager' object has no attribute 'get_all_positions'
2025-07-13 11:29:26,135 - core.position_watchdog - ERROR - [WATCHDOG] Health check loop error: 'PositionWatchdog' object has no attribute '_perform_health_checks'
2025-07-13 11:29:28,150 - __main__ - ERROR - [PHASE2_TEST] Position watchdog test error: 'PositionWatchdog' object has no attribute 'force_health_check'
2025-07-13 11:29:28,154 - __main__ - INFO - [PHASE2_TEST] Running Test 4: Error Recovery System...
2025-07-13 11:29:28,158 - core.error_recovery_system - INFO - [ERROR_RECOVERY] Error Recovery System initialized
2025-07-13 11:29:28,158 - core.error_recovery_system - INFO - [ERROR_RECOVERY] Health monitoring started
2025-07-13 11:29:28,158 - core.error_recovery_system - INFO - [ERROR_RECOVERY] Error Recovery System ready
2025-07-13 11:29:28,159 - core.error_recovery_system - WARNING - [ERROR_RECOVERY] Retry 1/3 for test_function_with_retries in 0.94s: Test failure for retry testing
2025-07-13 11:29:29,108 - core.error_recovery_system - INFO - [ERROR_RECOVERY] Error Recovery System shutdown complete
2025-07-13 11:29:29,109 - __main__ - INFO - [PHASE2_TEST] Error recovery system test completed
2025-07-13 11:29:29,111 - __main__ - INFO - [PHASE2_TEST] Running Test 5: Component Integration...
2025-07-13 11:29:29,117 - core.autonomous_trading_orchestrator - INFO - Autonomous Trading Orchestrator initialized in paper mode
2025-07-13 11:29:29,117 - core.autonomous_trading_orchestrator - INFO - Initializing autonomous trading system...
2025-07-13 11:29:29,117 - core.error_recovery_system - INFO - [ERROR_RECOVERY] Error Recovery System initialized
2025-07-13 11:29:29,117 - core.error_recovery_system - INFO - [ERROR_RECOVERY] Health monitoring started
2025-07-13 11:29:29,117 - core.error_recovery_system - INFO - [ERROR_RECOVERY] Error Recovery System ready
2025-07-13 11:29:29,155 - core.websocket_manager - INFO - WebSocket manager initialized for htx_market: wss://api.huobi.pro/ws
2025-07-13 11:29:29,155 - core.websocket_manager - INFO - WebSocket manager initialized for htx_futures: wss://api.hbdm.com/swap-ws
2025-07-13 11:29:29,155 - core.websocket_manager - INFO - WebSocket manager initialized for htx_backup: wss://api-aws.huobi.pro/ws
2025-07-13 11:29:29,155 - data.market_data_manager - INFO - Initialized 3 WebSocket endpoints
2025-07-13 11:29:29,155 - data.market_data_manager - INFO - Market data manager initialized for htx
2025-07-13 11:29:29,155 - data.market_data_manager - INFO - Starting market data manager...
2025-07-13 11:29:29,155 - data.market_data_manager - INFO - Started WebSocket connection: market
2025-07-13 11:29:29,156 - data.market_data_manager - INFO - Started WebSocket connection: futures
2025-07-13 11:29:29,156 - data.market_data_manager - INFO - Started WebSocket connection: backup
2025-07-13 11:29:29,156 - core.websocket_manager - INFO - htx_market: Connecting to wss://api.huobi.pro/ws (attempt 1)
2025-07-13 11:29:29,251 - core.websocket_manager - INFO - htx_futures: Connecting to wss://api.hbdm.com/swap-ws (attempt 1)
2025-07-13 11:29:29,257 - core.websocket_manager - INFO - htx_backup: Connecting to wss://api-aws.huobi.pro/ws (attempt 1)
2025-07-13 11:29:31,166 - core.websocket_manager - WARNING - htx_backup: Cannot send message - not connected
2025-07-13 11:29:31,169 - core.websocket_manager - WARNING - htx_backup: Cannot send message - not connected
2025-07-13 11:29:31,175 - core.unified_llm_manager - INFO - [UNIFIED_LLM] Unified LLM Manager initialized
2025-07-13 11:29:31,175 - core.standardized_prompt_handler - INFO - [PROMPT_HANDLER] Standardized prompt handler initialized
2025-07-13 11:29:31,175 - core.autonomous_llm_integration - INFO - [AUTONOMOUS_LLM] Autonomous LLM Integration initialized
2025-07-13 11:29:31,175 - core.autonomous_llm_integration - INFO - [AUTONOMOUS_LLM] Initializing autonomous LLM integration...
2025-07-13 11:29:31,175 - core.unified_llm_manager - INFO - [UNIFIED_LLM] Initializing LLM providers...
2025-07-13 11:29:31,179 - core.unified_llm_manager - INFO - [UNIFIED_LLM] ChatGPT provider initialized
2025-07-13 11:29:31,185 - llama.lmstudio_runner - INFO - Loaded LMStudio configuration from models_config.yaml
2025-07-13 11:29:33,246 - llama.lmstudio_runner - INFO - Discovered 8 models: ['phi-3.1-mini-128k-instruct', 'openthinker3-7b', 'mistral-7b-claude-chat', 'fine_tuned_qwen1.7b', 'tsfc-model-0.0.1', 'codestral-22b-v0.1', 'deepseek-r1-distill-qwen-7b', 'text-embedding-nomic-embed-text-v1.5']
2025-07-13 11:29:33,247 - llama.lmstudio_runner - INFO - Selected preferred model from config: phi-3.1-mini-128k-instruct
2025-07-13 11:29:33,247 - llama.lmstudio_runner - INFO - Initialized LMStudio with model: phi-3.1-mini-128k-instruct
2025-07-13 11:29:35,288 - core.unified_llm_manager - INFO - [UNIFIED_LLM] LMStudio provider initialized
2025-07-13 11:29:35,288 - core.unified_llm_manager - WARNING - [UNIFIED_LLM] Failed to initialize Transformers: __init__() missing 1 required positional argument: 'model_path'
2025-07-13 11:29:35,288 - core.unified_llm_manager - WARNING - [UNIFIED_LLM] Failed to initialize Llama: __init__() missing 1 required positional argument: 'bin_path'
2025-07-13 11:29:35,289 - llama.mock_runner - INFO - Initialized MockRunner with personality: {'caution': 0.5509825213765989, 'optimism': 0.562336072853333, 'consistency': 0.7230650352103083, 'detail_level': 0.810414174489126, 'confidence_bias': 4.132298754172201}
2025-07-13 11:29:35,289 - core.unified_llm_manager - INFO - [UNIFIED_LLM] Mock provider initialized
2025-07-13 11:29:35,289 - core.unified_llm_manager - INFO - [UNIFIED_LLM] Selected provider: mock (score: 109.90)
2025-07-13 11:29:35,289 - core.unified_llm_manager - INFO - [UNIFIED_LLM] Health monitoring started
2025-07-13 11:29:35,289 - core.unified_llm_manager - INFO - [UNIFIED_LLM] Initialized with 3 providers
2025-07-13 11:29:35,289 - core.unified_llm_manager - INFO - [UNIFIED_LLM] Current provider: LLMProvider.MOCK
2025-07-13 11:29:35,290 - core.autonomous_llm_integration - INFO - [AUTONOMOUS_LLM] Autonomous LLM integration initialized successfully
2025-07-13 11:29:35,307 - utils.cache_manager - INFO - Cache manager initialized: cache, max_age=24h, max_size=100MB
2025-07-13 11:29:35,307 - utils.cache_manager - INFO - Cache manager initialized: cache, max_age=24h, max_size=100MB
2025-07-13 11:29:35,308 - core.llm_orchestrator - INFO - LLM Prompt Orchestrator initialized
2025-07-13 11:29:41,793 - ml.models - INFO - TensorFlow available for LSTM models
2025-07-13 11:29:41,793 - ml.models - INFO - ML Model Manager initialized
2025-07-13 11:29:42,105 - absl - WARNING - Compiled the loaded model, but the compiled metrics have yet to be built. `model.compile_metrics` will be empty until you train or evaluate the model.
2025-07-13 11:29:42,165 - ml.models - ERROR - Error loading models: Could not locate function 'mse'. Make sure custom classes are decorated with `@keras.saving.register_keras_serializable()`. Full object config: {'module': 'keras.metrics', 'class_name': 'function', 'config': 'mse', 'registered_name': 'mse'}
2025-07-13 11:29:42,169 - core.autonomous_position_manager - INFO - [POSITION_MANAGER] Autonomous Position Manager initialized
2025-07-13 11:29:42,178 - core.adaptive_risk - INFO - TA-Lib not available, using pandas-ta fallback
2025-07-13 11:29:42,179 - core.adaptive_risk - INFO - Initialized adaptive risk manager
2025-07-13 11:29:42,186 - execution.unified_execution_engine - INFO - Unified execution engine initialized in paper mode
2025-07-13 11:29:42,186 - execution.execution_adapter - INFO - Execution adapter initialized in paper mode
2025-07-13 11:29:42,191 - core.autonomous_position_manager - INFO - [POSITION_MANAGER] Position monitoring started (interval: 5s)
2025-07-13 11:29:42,191 - core.autonomous_position_manager - INFO - [POSITION_MANAGER] Autonomous Position Manager ready
2025-07-13 11:29:42,194 - core.position_watchdog - INFO - [WATCHDOG] Position Watchdog initialized
2025-07-13 11:29:42,194 - core.position_watchdog - INFO - [WATCHDOG] Monitoring started (scan: 10s, health: 30s)
2025-07-13 11:29:42,194 - core.position_watchdog - INFO - [WATCHDOG] Position Watchdog ready
2025-07-13 11:29:42,199 - portfolio.portfolio_manager - INFO - Portfolio manager initialized with $10000.00 balance
2025-07-13 11:29:42,201 - core.autonomous_trading_orchestrator - ERROR - Failed to initialize autonomous trading system: System health check failed: ["Data connectivity issue: 'MarketDataManager' object has no attribute 'get_latest_data'"]
2025-07-13 11:29:42,201 - __main__ - ERROR - [PHASE2_TEST] Orchestrator initialization failed
2025-07-13 11:29:42,204 - __main__ - INFO - [PHASE2_TEST] Running Test 6: End-to-End Workflow...
2025-07-13 11:29:42,204 - __main__ - INFO - [PHASE2_TEST] Testing end-to-end workflow simulation
2025-07-13 11:29:42,204 - __main__ - INFO - [PHASE2_TEST] Simulating: Market data collection
2025-07-13 11:29:42,204 - core.position_watchdog - ERROR - [WATCHDOG] Error scanning positions: 'AutonomousPositionManager' object has no attribute 'get_all_positions'
2025-07-13 11:29:42,204 - core.position_watchdog - ERROR - [WATCHDOG] Health check loop error: 'PositionWatchdog' object has no attribute '_perform_health_checks'
2025-07-13 11:29:42,205 - core.position_watchdog - ERROR - [WATCHDOG] Error scanning positions: 'AutonomousPositionManager' object has no attribute 'get_all_positions'
2025-07-13 11:29:42,206 - core.websocket_manager - ERROR - htx_futures: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:29:42,206 - core.websocket_manager - ERROR - htx_futures: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:29:42,207 - core.websocket_manager - ERROR - htx_futures: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:29:42,207 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:29:42,207 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:29:42,207 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:29:42,207 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:29:42,207 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:29:42,207 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:29:42,207 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:29:42,207 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:29:42,208 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:29:42,208 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:29:42,208 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:29:42,208 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:29:42,208 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:29:42,208 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:29:42,208 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:29:42,208 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:29:42,208 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:29:42,208 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:29:42,209 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:29:42,209 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:29:42,209 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:29:42,209 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:29:42,209 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:29:42,209 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:29:42,209 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:29:42,209 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:29:42,209 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:29:42,210 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:29:42,210 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:29:42,210 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:29:42,210 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:29:42,210 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:29:42,210 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:29:42,210 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:29:42,211 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:29:42,211 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:29:42,211 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:29:42,211 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:29:42,211 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:29:42,211 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:29:42,211 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:29:42,211 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:29:42,211 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:29:42,211 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:29:42,211 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:29:42,211 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:29:42,211 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:29:42,212 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:29:42,212 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:29:42,212 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:29:42,212 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:29:42,212 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:29:42,212 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:29:42,212 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:29:42,212 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:29:42,213 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:29:42,213 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:29:42,213 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:29:42,213 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:29:42,285 - core.websocket_manager - INFO - htx_backup: Reconnecting in 10 seconds (attempt 2)
2025-07-13 11:29:42,308 - __main__ - INFO - [PHASE2_TEST] Simulating: LLM analysis and decision
2025-07-13 11:29:42,309 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:29:42,396 - __main__ - INFO - [PHASE2_TEST] Simulating: Position entry execution
2025-07-13 11:29:42,396 - core.websocket_manager - ERROR - htx_futures: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:29:42,397 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:29:42,397 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:29:42,497 - __main__ - INFO - [PHASE2_TEST] Simulating: Position monitoring
2025-07-13 11:29:42,497 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:29:42,497 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:29:42,601 - __main__ - INFO - [PHASE2_TEST] Simulating: Risk assessment
2025-07-13 11:29:42,607 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:29:42,707 - __main__ - INFO - [PHASE2_TEST] Simulating: Position exit execution
2025-07-13 11:29:42,707 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:29:42,708 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:29:42,803 - __main__ - INFO - [PHASE2_TEST] End-to-end workflow test completed
2025-07-13 11:29:42,806 - __main__ - INFO - [PHASE2_TEST] Running Test 7: Safety Systems Validation...
2025-07-13 11:29:42,806 - __main__ - INFO - [PHASE2_TEST] Testing safety systems
2025-07-13 11:29:42,806 - __main__ - INFO - [PHASE2_TEST] Safety systems test completed
2025-07-13 11:29:42,808 - __main__ - INFO - [PHASE2_TEST] Running Test 8: Performance Validation...
2025-07-13 11:29:42,808 - __main__ - INFO - [PHASE2_TEST] Testing performance validation
2025-07-13 11:29:42,808 - __main__ - INFO - [PHASE2_TEST] Performance validation test completed
2025-07-13 11:29:42,810 - __main__ - INFO - [PHASE2_TEST] Phase 2 integration tests completed - Overall: FAILED
