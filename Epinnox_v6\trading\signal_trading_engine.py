"""
Signal Trading Engine for Epinnox v6
Connects ML/LLM signals to automated trading decisions with risk management
"""

import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
from PyQt5.QtCore import QObject, QTimer, pyqtSignal
import logging

from core.signal_hierarchy import IntelligentSignalHierarchy, SignalInput
from core.error_handling import (
    safe_execute, retry_on_failure, ErrorContext,
    handle_trading_error, TradingError, ValidationError
)
from .real_trading_interface import RealTradingInterface

logger = logging.getLogger(__name__)


class SignalTradingEngine(QObject):
    """
    Automated trading engine that executes trades based on ML/LLM signals
    Integrates with signal hierarchy and applies sophisticated risk management
    """
    
    # Signals for UI updates
    signal_received = pyqtSignal(dict)  # signal_data
    trade_decision_made = pyqtSignal(dict)  # decision_data
    automated_trade_executed = pyqtSignal(dict)  # trade_data
    risk_limit_triggered = pyqtSignal(str, dict)  # limit_type, details
    engine_status_changed = pyqtSignal(str)  # status_message
    
    def __init__(self, trading_interface: RealTradingInterface):
        super().__init__()
        
        self.trading_interface = trading_interface
        self.signal_hierarchy = IntelligentSignalHierarchy()
        
        # Engine state
        self.is_enabled = False
        self.is_paused = False
        self.current_symbol = "DOGE/USDT:USDT"
        
        # Signal processing
        self.pending_signals = {}  # symbol -> latest signals
        self.last_trade_time = {}  # symbol -> timestamp
        self.signal_history = []   # Historical signal decisions
        
        # Trading parameters
        self.base_position_size = 50.0  # Base position size in USD
        self.max_position_size = 500.0  # Maximum position size
        self.confidence_multiplier = 2.0  # Position size multiplier for high confidence
        self.min_confidence_threshold = 0.55  # Minimum confidence to trade
        self.max_daily_trades = 20  # Maximum trades per day
        self.min_trade_interval = 300  # Minimum seconds between trades (5 minutes)
        
        # Risk management
        self.max_drawdown_percent = 5.0  # Maximum 5% drawdown
        self.max_position_correlation = 0.8  # Maximum correlation between positions
        self.daily_trade_count = 0
        self.daily_reset_time = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
        
        # Performance tracking
        self.total_signals_processed = 0
        self.total_trades_executed = 0
        self.successful_trades = 0
        self.win_rate = 0.0
        
        # Signal processing timer
        self.processing_timer = QTimer()
        self.processing_timer.timeout.connect(self.process_pending_signals)
        self.processing_timer.start(1000)  # Process every second
        
        logger.info("Signal Trading Engine initialized")
    
    def enable_automated_trading(self):
        """Enable automated trading based on signals"""
        self.is_enabled = True
        self.is_paused = False
        self.engine_status_changed.emit("Automated trading ENABLED")
        logger.info("🤖 Automated trading enabled")
    
    def disable_automated_trading(self):
        """Disable automated trading"""
        self.is_enabled = False
        self.engine_status_changed.emit("Automated trading DISABLED")
        logger.info("🛑 Automated trading disabled")
    
    def pause_automated_trading(self):
        """Pause automated trading temporarily"""
        self.is_paused = True
        self.engine_status_changed.emit("Automated trading PAUSED")
        logger.info("⏸️ Automated trading paused")
    
    def resume_automated_trading(self):
        """Resume automated trading"""
        self.is_paused = False
        self.engine_status_changed.emit("Automated trading RESUMED")
        logger.info("▶️ Automated trading resumed")
    
    def add_signal(self, signal_data: Dict):
        """Add a new signal for processing with comprehensive validation"""
        with ErrorContext("signal addition", log_level="warning"):
            # Validate input data
            if not isinstance(signal_data, dict):
                raise ValidationError("Signal data must be a dictionary")

            symbol = signal_data.get('symbol', self.current_symbol)
            source = signal_data.get('source', 'unknown')
            decision = signal_data.get('decision', 'WAIT')
            confidence = signal_data.get('confidence', 0.0)

            # Validate signal parameters
            if not symbol or not isinstance(symbol, str):
                raise ValidationError(f"Invalid symbol: {symbol}")

            if decision not in ['LONG', 'SHORT', 'WAIT']:
                raise ValidationError(f"Invalid decision: {decision}")

            if not isinstance(confidence, (int, float)) or not 0 <= confidence <= 1:
                raise ValidationError(f"Invalid confidence: {confidence}. Must be between 0 and 1")

            # Create SignalInput object
            signal = SignalInput(
                source=source,
                decision=decision,
                confidence=confidence,
                weight=signal_data.get('weight', 0.1),
                reasoning=signal_data.get('reasoning', ''),
                metadata=signal_data.get('metadata', {})
            )

            # Add timestamp if not present
            if 'timestamp' not in signal.metadata:
                signal.metadata['timestamp'] = time.time()

            # Store signal for processing
            if symbol not in self.pending_signals:
                self.pending_signals[symbol] = []

            self.pending_signals[symbol].append(signal)

            # Keep only recent signals (last 5 minutes)
            current_time = time.time()
            self.pending_signals[symbol] = [
                s for s in self.pending_signals[symbol]
                if current_time - s.metadata.get('timestamp', current_time) < 300
            ]

            self.signal_received.emit(signal_data)
            self.total_signals_processed += 1

            logger.debug(f"Signal added: {source} -> {signal.decision} ({signal.confidence:.2%})")
    
    def process_pending_signals(self):
        """Process all pending signals and make trading decisions"""
        try:
            if not self.is_enabled or self.is_paused:
                return
            
            # Reset daily counters if new day
            self._reset_daily_counters()
            
            # Process signals for each symbol
            for symbol, signals in self.pending_signals.items():
                if signals:
                    self._process_symbol_signals(symbol, signals)
                    
        except Exception as e:
            logger.error(f"Error processing signals: {e}")
    
    def _process_symbol_signals(self, symbol: str, signals: List[SignalInput]):
        """Process signals for a specific symbol"""
        try:
            # Check if we can trade this symbol
            if not self._can_trade_symbol(symbol):
                return
            
            # Resolve signals using hierarchy
            resolution = self.signal_hierarchy.resolve_signals(signals)
            
            # Emit decision for UI
            self.trade_decision_made.emit({
                'symbol': symbol,
                'decision': resolution['decision'],
                'confidence': resolution['confidence'],
                'reasoning': resolution['reasoning'],
                'signals_count': len(signals)
            })
            
            # Check if decision meets trading criteria
            if self._should_execute_trade(resolution):
                self._execute_automated_trade(symbol, resolution)
            
            # Store decision in history
            self.signal_history.append({
                'symbol': symbol,
                'timestamp': time.time(),
                'decision': resolution['decision'],
                'confidence': resolution['confidence'],
                'signals_count': len(signals),
                'executed': resolution['confidence'] >= self.min_confidence_threshold
            })
            
            # Clear processed signals
            self.pending_signals[symbol] = []
            
        except Exception as e:
            logger.error(f"Error processing signals for {symbol}: {e}")
    
    def _can_trade_symbol(self, symbol: str) -> bool:
        """Check if we can trade a symbol based on various constraints"""
        try:
            # Check daily trade limit
            if self.daily_trade_count >= self.max_daily_trades:
                logger.warning(f"Daily trade limit reached ({self.max_daily_trades})")
                return False
            
            # Check minimum time interval
            last_trade = self.last_trade_time.get(symbol, 0)
            if time.time() - last_trade < self.min_trade_interval:
                logger.debug(f"Trade interval not met for {symbol}")
                return False
            
            # Check if we have sufficient balance
            balance = self.trading_interface.get_balance_info()
            if balance:
                usdt_balance = balance.get('USDT', {}).get('free', 0)
                if usdt_balance < self.base_position_size:
                    logger.warning(f"Insufficient balance: ${usdt_balance:.2f}")
                    return False
            
            # Check existing position size
            position = self.trading_interface.get_position_info(symbol)
            if position and abs(position.get('size', 0)) * position.get('mark_price', 1) > self.max_position_size:
                logger.warning(f"Position size limit reached for {symbol}")
                return False
            
            return True
            
        except Exception as e:
            logger.error(f"Error checking trade eligibility for {symbol}: {e}")
            return False
    
    def _should_execute_trade(self, resolution: Dict) -> bool:
        """Determine if a trade should be executed based on resolution"""
        try:
            decision = resolution['decision']
            confidence = resolution['confidence']
            
            # Must have a clear direction
            if decision == 'WAIT':
                return False
            
            # Must meet minimum confidence threshold
            if confidence < self.min_confidence_threshold:
                logger.debug(f"Confidence too low: {confidence:.2%} < {self.min_confidence_threshold:.2%}")
                return False
            
            # Check for override conditions
            if resolution.get('override_type') == 'ml_high_confidence':
                logger.info(f"ML high confidence override: {confidence:.2%}")
                return True
            
            # Check risk limits
            if not self._check_risk_limits():
                return False
            
            return True
            
        except Exception as e:
            logger.error(f"Error evaluating trade execution: {e}")
            return False
    
    def _check_risk_limits(self) -> bool:
        """Check various risk limits before trading"""
        try:
            # Check drawdown limit
            pnl_summary = self.trading_interface.get_pnl_summary()
            if pnl_summary:
                total_pnl = pnl_summary.get('total_pnl', 0)
                balance = self.trading_interface.get_balance_info()
                if balance:
                    account_value = balance.get('total', {}).get('USDT', 1000)
                    drawdown_percent = abs(total_pnl) / account_value * 100
                    
                    if total_pnl < 0 and drawdown_percent > self.max_drawdown_percent:
                        self.risk_limit_triggered.emit('drawdown', {
                            'current_drawdown': drawdown_percent,
                            'limit': self.max_drawdown_percent,
                            'pnl': total_pnl
                        })
                        return False
            
            return True
            
        except Exception as e:
            logger.error(f"Error checking risk limits: {e}")
            return True  # Default to allow trading if check fails
    
    def _execute_automated_trade(self, symbol: str, resolution: Dict):
        """Execute an automated trade based on signal resolution"""
        try:
            decision = resolution['decision']
            confidence = resolution['confidence']
            
            # Calculate position size based on confidence
            position_size = self._calculate_position_size(confidence)
            
            # Determine leverage based on confidence
            leverage = self._calculate_leverage(confidence)
            
            # Execute trade
            success = False
            if decision == 'LONG':
                success = self.trading_interface.place_market_long(symbol, position_size, leverage)
            elif decision == 'SHORT':
                success = self.trading_interface.place_market_short(symbol, position_size, leverage)
            
            if success:
                # Update tracking
                self.last_trade_time[symbol] = time.time()
                self.daily_trade_count += 1
                self.total_trades_executed += 1
                
                # Emit trade execution signal
                trade_data = {
                    'symbol': symbol,
                    'decision': decision,
                    'position_size': position_size,
                    'leverage': leverage,
                    'confidence': confidence,
                    'reasoning': resolution['reasoning'],
                    'timestamp': time.time(),
                    'automated': True
                }
                
                self.automated_trade_executed.emit(trade_data)
                
                logger.info(f"🤖 Automated trade executed: {decision} {position_size} {symbol} "
                           f"@ {leverage}x leverage (confidence: {confidence:.1%})")
            else:
                logger.warning(f"Failed to execute automated trade: {decision} {symbol}")
                
        except Exception as e:
            logger.error(f"Error executing automated trade: {e}")
    
    def _calculate_position_size(self, confidence: float) -> float:
        """Calculate position size based on confidence"""
        try:
            # Base size adjusted by confidence
            size_multiplier = 1.0 + (confidence - 0.5) * self.confidence_multiplier
            position_size = self.base_position_size * size_multiplier
            
            # Ensure within limits
            position_size = max(10.0, min(position_size, self.max_position_size))
            
            return round(position_size, 2)
            
        except Exception as e:
            logger.error(f"Error calculating position size: {e}")
            return self.base_position_size
    
    def _calculate_leverage(self, confidence: float) -> int:
        """Calculate leverage based on confidence"""
        try:
            # Higher confidence = higher leverage (within limits)
            if confidence >= 0.9:
                return 20
            elif confidence >= 0.8:
                return 15
            elif confidence >= 0.7:
                return 10
            else:
                return 5
                
        except Exception as e:
            logger.error(f"Error calculating leverage: {e}")
            return 10
    
    def _reset_daily_counters(self):
        """Reset daily counters if new day"""
        try:
            now = datetime.now()
            if now.date() > self.daily_reset_time.date():
                self.daily_trade_count = 0
                self.daily_reset_time = now.replace(hour=0, minute=0, second=0, microsecond=0)
                logger.info("Daily counters reset")
        except Exception as e:
            logger.error(f"Error resetting daily counters: {e}")
    
    # Configuration methods
    def set_trading_parameters(self, params: Dict):
        """Update trading parameters"""
        try:
            if 'base_position_size' in params:
                self.base_position_size = params['base_position_size']
            if 'min_confidence_threshold' in params:
                self.min_confidence_threshold = params['min_confidence_threshold']
            if 'max_daily_trades' in params:
                self.max_daily_trades = params['max_daily_trades']
            if 'min_trade_interval' in params:
                self.min_trade_interval = params['min_trade_interval']
                
            logger.info(f"Trading parameters updated: {params}")
            
        except Exception as e:
            logger.error(f"Error updating trading parameters: {e}")
    
    def get_engine_status(self) -> Dict:
        """Get current engine status and statistics"""
        return {
            'enabled': self.is_enabled,
            'paused': self.is_paused,
            'total_signals_processed': self.total_signals_processed,
            'total_trades_executed': self.total_trades_executed,
            'daily_trade_count': self.daily_trade_count,
            'win_rate': self.win_rate,
            'pending_signals': sum(len(signals) for signals in self.pending_signals.values()),
            'last_trade_times': self.last_trade_time.copy()
        }
    
    def get_signal_history(self, limit: int = 100) -> List[Dict]:
        """Get recent signal history"""
        return self.signal_history[-limit:] if self.signal_history else []
