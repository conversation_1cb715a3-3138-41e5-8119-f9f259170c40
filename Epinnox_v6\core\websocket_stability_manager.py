"""
WebSocket Stability Manager
Robust WebSocket connection management with automatic reconnection and stability monitoring
"""

import asyncio
import time
import logging
import json
import websockets
from typing import Dict, List, Optional, Callable, Any
from dataclasses import dataclass
from enum import Enum
from datetime import datetime, timedelta
import threading
from queue import Queue

logger = logging.getLogger(__name__)

class ConnectionState(Enum):
    """WebSocket connection states"""
    DISCONNECTED = "disconnected"
    CONNECTING = "connecting"
    CONNECTED = "connected"
    RECONNECTING = "reconnecting"
    FAILED = "failed"

@dataclass
class ConnectionMetrics:
    """Connection quality metrics"""
    total_connections: int = 0
    successful_connections: int = 0
    failed_connections: int = 0
    total_reconnections: int = 0
    last_connection_time: Optional[datetime] = None
    last_disconnection_time: Optional[datetime] = None
    average_connection_duration: float = 0.0
    message_count: int = 0
    error_count: int = 0
    latency_ms: float = 0.0

class WebSocketStabilityManager:
    """
    Advanced WebSocket connection manager with stability features:
    - Automatic reconnection with exponential backoff
    - Connection health monitoring
    - Message queue for reliable delivery
    - Multiple endpoint failover
    - Connection quality metrics
    """
    
    def __init__(self, primary_url: str, backup_urls: List[str] = None):
        self.primary_url = primary_url
        self.backup_urls = backup_urls or []
        self.all_urls = [primary_url] + self.backup_urls
        self.current_url_index = 0
        
        # Connection state
        self.state = ConnectionState.DISCONNECTED
        self.websocket = None
        self.connection_task = None
        
        # Reconnection settings
        self.initial_reconnect_delay = 1.0  # seconds
        self.max_reconnect_delay = 60.0  # seconds
        self.reconnect_delay = self.initial_reconnect_delay
        self.max_reconnect_attempts = 50
        self.reconnect_attempts = 0
        
        # Message handling
        self.message_queue = Queue()
        self.message_handlers: Dict[str, Callable] = {}
        self.pending_subscriptions = set()
        
        # Health monitoring
        self.metrics = ConnectionMetrics()
        self.last_ping_time = None
        self.ping_interval = 30.0  # seconds
        self.ping_timeout = 10.0  # seconds
        self.health_check_interval = 60.0  # seconds
        
        # Control flags
        self.should_reconnect = True
        self.is_running = False
        
        logger.info(f"WebSocket Stability Manager initialized with {len(self.all_urls)} endpoints")
    
    async def connect(self) -> bool:
        """
        Connect to WebSocket with automatic failover
        
        Returns:
            bool: True if connection successful
        """
        if self.is_running:
            logger.warning("WebSocket manager already running")
            return True
        
        self.is_running = True
        self.should_reconnect = True
        
        # Start connection task
        self.connection_task = asyncio.create_task(self._connection_loop())
        
        # Wait for initial connection
        for _ in range(50):  # Wait up to 5 seconds
            if self.state == ConnectionState.CONNECTED:
                return True
            await asyncio.sleep(0.1)
        
        logger.warning("Initial connection attempt timed out")
        return False
    
    async def disconnect(self):
        """Gracefully disconnect WebSocket"""
        logger.info("Disconnecting WebSocket...")
        
        self.should_reconnect = False
        self.is_running = False
        
        if self.websocket:
            try:
                await self.websocket.close()
            except Exception as e:
                logger.error(f"Error closing WebSocket: {e}")
        
        if self.connection_task:
            self.connection_task.cancel()
            try:
                await self.connection_task
            except asyncio.CancelledError:
                pass
        
        self.state = ConnectionState.DISCONNECTED
        logger.info("WebSocket disconnected")
    
    async def _connection_loop(self):
        """Main connection loop with reconnection logic"""
        while self.is_running and self.should_reconnect:
            try:
                await self._attempt_connection()
                
                if self.state == ConnectionState.CONNECTED:
                    # Reset reconnection parameters on successful connection
                    self.reconnect_delay = self.initial_reconnect_delay
                    self.reconnect_attempts = 0
                    
                    # Start health monitoring
                    health_task = asyncio.create_task(self._health_monitor())
                    
                    # Handle messages
                    await self._message_handler()
                    
                    # Cancel health monitoring
                    health_task.cancel()
                
            except Exception as e:
                logger.error(f"Connection error: {e}")
                self.metrics.error_count += 1
            
            # Handle reconnection
            if self.should_reconnect and self.is_running:
                await self._handle_reconnection()
    
    async def _attempt_connection(self):
        """Attempt to connect to current URL"""
        current_url = self.all_urls[self.current_url_index]
        
        try:
            logger.info(f"Attempting connection to {current_url}")
            self.state = ConnectionState.CONNECTING
            self.metrics.total_connections += 1
            
            # Connect with timeout
            self.websocket = await asyncio.wait_for(
                websockets.connect(
                    current_url,
                    ping_interval=self.ping_interval,
                    ping_timeout=self.ping_timeout,
                    close_timeout=10
                ),
                timeout=30.0
            )
            
            self.state = ConnectionState.CONNECTED
            self.metrics.successful_connections += 1
            self.metrics.last_connection_time = datetime.now()
            
            logger.info(f"✅ Connected to {current_url}")
            
            # Re-subscribe to all pending subscriptions
            await self._resubscribe_all()
            
        except asyncio.TimeoutError:
            logger.error(f"Connection timeout to {current_url}")
            self.metrics.failed_connections += 1
            await self._try_next_url()
            
        except Exception as e:
            logger.error(f"Connection failed to {current_url}: {e}")
            self.metrics.failed_connections += 1
            await self._try_next_url()
    
    async def _try_next_url(self):
        """Try next URL in the list"""
        self.current_url_index = (self.current_url_index + 1) % len(self.all_urls)
        logger.info(f"Trying next URL: {self.all_urls[self.current_url_index]}")
    
    async def _handle_reconnection(self):
        """Handle reconnection with exponential backoff"""
        if self.reconnect_attempts >= self.max_reconnect_attempts:
            logger.error(f"Max reconnection attempts ({self.max_reconnect_attempts}) reached")
            self.state = ConnectionState.FAILED
            return
        
        self.state = ConnectionState.RECONNECTING
        self.reconnect_attempts += 1
        self.metrics.total_reconnections += 1
        
        logger.warning(f"Reconnecting in {self.reconnect_delay:.1f}s (attempt {self.reconnect_attempts})")
        await asyncio.sleep(self.reconnect_delay)
        
        # Exponential backoff
        self.reconnect_delay = min(self.reconnect_delay * 2, self.max_reconnect_delay)
    
    async def _message_handler(self):
        """Handle incoming WebSocket messages"""
        try:
            async for message in self.websocket:
                try:
                    # Parse message
                    if isinstance(message, str):
                        data = json.loads(message)
                    else:
                        data = message
                    
                    self.metrics.message_count += 1
                    
                    # Route message to appropriate handler
                    await self._route_message(data)
                    
                except json.JSONDecodeError as e:
                    logger.error(f"Failed to parse message: {e}")
                except Exception as e:
                    logger.error(f"Error handling message: {e}")
                    
        except websockets.exceptions.ConnectionClosed:
            logger.warning("WebSocket connection closed")
            self.state = ConnectionState.DISCONNECTED
            self.metrics.last_disconnection_time = datetime.now()
            
        except Exception as e:
            logger.error(f"Message handler error: {e}")
            self.state = ConnectionState.DISCONNECTED
    
    async def _route_message(self, data: Dict[str, Any]):
        """Route message to appropriate handler"""
        # Determine message type and route to handler
        message_type = self._determine_message_type(data)
        
        if message_type in self.message_handlers:
            try:
                await self.message_handlers[message_type](data)
            except Exception as e:
                logger.error(f"Error in message handler for {message_type}: {e}")
        else:
            # Default handler
            if 'default' in self.message_handlers:
                await self.message_handlers['default'](data)
    
    def _determine_message_type(self, data: Dict[str, Any]) -> str:
        """Determine message type from data"""
        # HTX/Huobi format
        if 'ch' in data:
            if 'ticker' in data['ch']:
                return 'ticker'
            elif 'depth' in data['ch']:
                return 'orderbook'
            elif 'trade' in data['ch']:
                return 'trade'
        
        # Binance format
        if 'e' in data:
            return data['e']  # Event type
        
        # Generic format
        return data.get('type', 'unknown')
    
    async def _health_monitor(self):
        """Monitor connection health"""
        while self.state == ConnectionState.CONNECTED:
            try:
                # Send ping and measure latency
                ping_start = time.time()
                pong_waiter = await self.websocket.ping()
                await asyncio.wait_for(pong_waiter, timeout=self.ping_timeout)
                
                latency = (time.time() - ping_start) * 1000  # ms
                self.metrics.latency_ms = latency
                
                logger.debug(f"Ping successful, latency: {latency:.1f}ms")
                
                await asyncio.sleep(self.health_check_interval)
                
            except asyncio.TimeoutError:
                logger.error("Ping timeout - connection may be unstable")
                break
            except Exception as e:
                logger.error(f"Health check error: {e}")
                break
    
    async def send_message(self, message: Dict[str, Any]) -> bool:
        """
        Send message to WebSocket
        
        Args:
            message: Message to send
            
        Returns:
            bool: True if sent successfully
        """
        if self.state != ConnectionState.CONNECTED or not self.websocket:
            logger.warning("Cannot send message - not connected")
            return False
        
        try:
            await self.websocket.send(json.dumps(message))
            return True
        except Exception as e:
            logger.error(f"Failed to send message: {e}")
            return False
    
    async def subscribe(self, subscription: Dict[str, Any]) -> bool:
        """
        Subscribe to data stream
        
        Args:
            subscription: Subscription message
            
        Returns:
            bool: True if subscription sent successfully
        """
        # Add to pending subscriptions for reconnection
        self.pending_subscriptions.add(json.dumps(subscription, sort_keys=True))
        
        return await self.send_message(subscription)
    
    async def _resubscribe_all(self):
        """Re-subscribe to all pending subscriptions after reconnection"""
        logger.info(f"Re-subscribing to {len(self.pending_subscriptions)} subscriptions")
        
        for subscription_str in self.pending_subscriptions:
            try:
                subscription = json.loads(subscription_str)
                await self.send_message(subscription)
                await asyncio.sleep(0.1)  # Small delay between subscriptions
            except Exception as e:
                logger.error(f"Failed to re-subscribe: {e}")
    
    def add_message_handler(self, message_type: str, handler: Callable):
        """Add message handler for specific message type"""
        self.message_handlers[message_type] = handler
        logger.debug(f"Added message handler for {message_type}")
    
    def get_connection_metrics(self) -> Dict[str, Any]:
        """Get connection quality metrics"""
        success_rate = 0.0
        if self.metrics.total_connections > 0:
            success_rate = self.metrics.successful_connections / self.metrics.total_connections
        
        return {
            'state': self.state.value,
            'current_url': self.all_urls[self.current_url_index],
            'success_rate': success_rate,
            'total_connections': self.metrics.total_connections,
            'reconnections': self.metrics.total_reconnections,
            'message_count': self.metrics.message_count,
            'error_count': self.metrics.error_count,
            'latency_ms': self.metrics.latency_ms,
            'last_connection': self.metrics.last_connection_time.isoformat() if self.metrics.last_connection_time else None
        }
