#!/usr/bin/env python3
"""
AUTONOMOUS TRADING EXECUTION FIX
Fix critical issues preventing autonomous trade execution

CRITICAL FIXES:
1. Fix async coroutine issue in orchestrator startup
2. Start timer coordination system
3. Initialize deployment manager
4. Verify autonomous trading loop is running
5. Ensure LLM decision loop is active
"""

import sys
import os
import asyncio
import logging
from datetime import datetime

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Configure Unicode-safe logging
try:
    from core.unicode_safe_logging import configure_unicode_safe_logging
    configure_unicode_safe_logging()
except ImportError:
    pass

logger = logging.getLogger(__name__)

async def fix_autonomous_trading_execution():
    """Fix critical issues preventing autonomous trading execution"""
    
    print("🔧 FIXING AUTONOMOUS TRADING EXECUTION")
    print("=" * 50)
    
    fixes_applied = []
    
    try:
        # 1. Fix Timer Coordination System
        print("\n1. FIXING TIMER COORDINATION SYSTEM")
        print("-" * 40)
        
        try:
            from core.timer_coordinator import timer_coordinator
            
            # Start timer coordination if not running
            if not timer_coordinator.is_running():
                print("   [TOOL] Starting timer coordinator...")
                await timer_coordinator.start()
                fixes_applied.append("Timer coordinator started")
                print("   ✅ Timer coordinator started successfully")
            else:
                print("   ✅ Timer coordinator already running")
            
            # Verify timer coordination status
            status = timer_coordinator.get_status()
            print(f"   📊 Active timers: {status.get('active_timers', 0)}")
            print(f"   📊 Total executions: {status.get('total_executions', 0)}")
            
        except Exception as e:
            print(f"   ❌ Timer coordination fix failed: {e}")
        
        # 2. Fix Autonomous Trading Orchestrator
        print("\n2. FIXING AUTONOMOUS TRADING ORCHESTRATOR")
        print("-" * 40)
        
        try:
            from core.autonomous_trading_orchestrator import AutonomousTradingOrchestrator, TradingMode
            from validation.live_trading_validator import live_trading_validator
            
            # Get the orchestrator from the validator
            orchestrator = live_trading_validator.orchestrator
            
            if orchestrator:
                print("   [LAUNCH] Found orchestrator instance")
                
                # Check if autonomous trading is actually running
                if hasattr(orchestrator, 'autonomous_trading_active'):
                    if orchestrator.autonomous_trading_active:
                        print("   ✅ Autonomous trading already active")
                    else:
                        print("   [WARNING] Autonomous trading not active - starting...")
                        
                        # Start autonomous trading properly with await
                        success = await orchestrator.start_autonomous_trading()
                        
                        if success:
                            fixes_applied.append("Autonomous trading started")
                            print("   ✅ Autonomous trading started successfully")
                        else:
                            print("   ❌ Failed to start autonomous trading")
                else:
                    print("   [WARNING] Orchestrator missing autonomous_trading_active attribute")
                    
                    # Try to start autonomous trading anyway
                    try:
                        success = await orchestrator.start_autonomous_trading()
                        if success:
                            fixes_applied.append("Autonomous trading started (fallback)")
                            print("   ✅ Autonomous trading started (fallback method)")
                    except Exception as e:
                        print(f"   ❌ Fallback start failed: {e}")
                
                # Verify LLM decision loop
                if hasattr(orchestrator, 'llm_decision_loop_active'):
                    if orchestrator.llm_decision_loop_active:
                        print("   ✅ LLM decision loop active")
                    else:
                        print("   [WARNING] LLM decision loop not active")
                        
                        # Try to start LLM decision loop
                        try:
                            if hasattr(orchestrator, 'start_llm_decision_loop'):
                                await orchestrator.start_llm_decision_loop()
                                fixes_applied.append("LLM decision loop started")
                                print("   ✅ LLM decision loop started")
                        except Exception as e:
                            print(f"   ❌ LLM decision loop start failed: {e}")
                
            else:
                print("   ❌ No orchestrator found in validator")
                
        except Exception as e:
            print(f"   ❌ Orchestrator fix failed: {e}")
        
        # 3. Fix Execution Engine Integration
        print("\n3. FIXING EXECUTION ENGINE INTEGRATION")
        print("-" * 40)
        
        try:
            from core.unified_execution_engine import unified_execution_engine
            from core.dynamic_risk_integration import dynamic_risk_integration
            
            # Register execution engine if not registered
            dynamic_risk_integration.register_execution_engine(unified_execution_engine)
            
            # Verify execution engine status
            status = unified_execution_engine.get_execution_status()
            
            if status['enforce_limit_orders_only']:
                print("   ✅ LIMIT orders enforcement: ACTIVE")
                fixes_applied.append("LIMIT orders enforcement verified")
            else:
                print("   ❌ LIMIT orders enforcement: DISABLED")
            
            print(f"   📊 Max position size: {status['max_position_size'] * 100}%")
            print(f"   📊 Active positions: {status['active_positions']}")
            
        except Exception as e:
            print(f"   ❌ Execution engine fix failed: {e}")
        
        # 4. Fix Deployment Manager
        print("\n4. FIXING DEPLOYMENT MANAGER")
        print("-" * 40)
        
        try:
            # Create deployment manager if missing
            from core.deployment_manager import DeploymentManager
            deployment_manager = DeploymentManager()
            
            # Register with integration system
            dynamic_risk_integration.register_deployment_manager(deployment_manager)
            
            fixes_applied.append("Deployment manager initialized")
            print("   ✅ Deployment manager initialized and registered")
            
        except ImportError:
            print("   [WARNING] Deployment manager module not found - creating...")
            
            # Create basic deployment manager
            deployment_manager_code = '''#!/usr/bin/env python3
"""
DEPLOYMENT MANAGER
Basic deployment management for autonomous trading
"""

import logging
from typing import Dict, Any

logger = logging.getLogger(__name__)

class DeploymentManager:
    """Basic deployment manager for autonomous trading"""
    
    def __init__(self):
        self.deployment_active = False
        self.deployment_start_time = None
        logger.info("Deployment Manager initialized")
    
    def start_deployment(self) -> bool:
        """Start deployment"""
        try:
            self.deployment_active = True
            from datetime import datetime
            self.deployment_start_time = datetime.now()
            logger.info("Deployment started")
            return True
        except Exception as e:
            logger.error(f"Deployment start failed: {e}")
            return False
    
    def get_deployment_status(self) -> Dict[str, Any]:
        """Get deployment status"""
        return {
            'active': self.deployment_active,
            'start_time': self.deployment_start_time
        }

# Global instance
deployment_manager = DeploymentManager()
'''
            
            # Write deployment manager file
            with open('core/deployment_manager.py', 'w') as f:
                f.write(deployment_manager_code)
            
            # Import and register
            from core.deployment_manager import deployment_manager
            dynamic_risk_integration.register_deployment_manager(deployment_manager)
            
            fixes_applied.append("Deployment manager created and registered")
            print("   ✅ Deployment manager created and registered")
            
        except Exception as e:
            print(f"   ❌ Deployment manager fix failed: {e}")
        
        # 5. Verify Market Data Flow
        print("\n5. VERIFYING MARKET DATA FLOW")
        print("-" * 40)
        
        try:
            from core.scalper_gpt import ScalperGPT
            
            # Test ScalperGPT analysis
            scalper_gpt = ScalperGPT()
            
            # Check if ScalperGPT is actually analyzing (not just initializing)
            if hasattr(scalper_gpt, 'last_analysis_time'):
                print(f"   📊 Last analysis: {scalper_gpt.last_analysis_time}")
            
            print("   ✅ ScalperGPT available for analysis")
            
            # Test symbol scanner
            from core.dynamic_symbol_scanner import dynamic_symbol_scanner
            
            # Get current symbols
            symbols = dynamic_symbol_scanner.get_high_quality_symbols()
            print(f"   📊 High-quality symbols found: {len(symbols)}")
            
            if len(symbols) > 0:
                print(f"   📊 Top symbol: {symbols[0] if symbols else 'None'}")
                fixes_applied.append("Market data flow verified")
            
        except Exception as e:
            print(f"   ❌ Market data verification failed: {e}")
        
        # 6. Start Autonomous Trading Loop
        print("\n6. STARTING AUTONOMOUS TRADING LOOP")
        print("-" * 40)
        
        try:
            # Get orchestrator and ensure it's running
            orchestrator = live_trading_validator.orchestrator
            
            if orchestrator:
                # Force start the main trading loop
                if hasattr(orchestrator, 'start_main_trading_loop'):
                    await orchestrator.start_main_trading_loop()
                    fixes_applied.append("Main trading loop started")
                    print("   ✅ Main trading loop started")
                
                # Start LLM decision loop if available
                if hasattr(orchestrator, 'start_llm_decision_loop'):
                    await orchestrator.start_llm_decision_loop()
                    fixes_applied.append("LLM decision loop started")
                    print("   ✅ LLM decision loop started")
                
                # Verify trading is active
                if hasattr(orchestrator, 'is_trading_active'):
                    if orchestrator.is_trading_active():
                        print("   ✅ Trading confirmed active")
                    else:
                        print("   [WARNING] Trading not confirmed active")
                
        except Exception as e:
            print(f"   ❌ Trading loop start failed: {e}")
        
        return fixes_applied
        
    except Exception as e:
        logger.error(f"Critical error in autonomous trading fix: {e}")
        print(f"❌ Critical error: {e}")
        return []

async def verify_autonomous_trading_status():
    """Verify autonomous trading is actually running"""
    
    print("\n" + "=" * 50)
    print("AUTONOMOUS TRADING STATUS VERIFICATION")
    print("=" * 50)
    
    try:
        from validation.live_trading_validator import live_trading_validator
        
        # Check validator status
        if hasattr(live_trading_validator, 'validation_active'):
            if live_trading_validator.validation_active:
                print("✅ Live validation: ACTIVE")
            else:
                print("❌ Live validation: NOT ACTIVE")
        
        # Check orchestrator status
        orchestrator = live_trading_validator.orchestrator
        if orchestrator:
            print("✅ Orchestrator: Available")
            
            # Check autonomous trading status
            if hasattr(orchestrator, 'autonomous_trading_active'):
                if orchestrator.autonomous_trading_active:
                    print("✅ Autonomous trading: ACTIVE")
                else:
                    print("❌ Autonomous trading: NOT ACTIVE")
            
            # Check LLM decision loop
            if hasattr(orchestrator, 'llm_decision_loop_active'):
                if orchestrator.llm_decision_loop_active:
                    print("✅ LLM decision loop: ACTIVE")
                else:
                    print("❌ LLM decision loop: NOT ACTIVE")
        
        # Check timer coordination
        from core.timer_coordinator import timer_coordinator
        if timer_coordinator.is_running():
            print("✅ Timer coordination: RUNNING")
        else:
            print("❌ Timer coordination: STOPPED")
        
        # Check execution engine
        from core.unified_execution_engine import unified_execution_engine
        status = unified_execution_engine.get_execution_status()
        if status['enforce_limit_orders_only']:
            print("✅ LIMIT orders enforcement: ACTIVE")
        else:
            print("❌ LIMIT orders enforcement: DISABLED")
        
        return True
        
    except Exception as e:
        print(f"❌ Status verification failed: {e}")
        return False

async def main():
    """Main fix function"""
    
    try:
        print("🚨 CRITICAL AUTONOMOUS TRADING EXECUTION FIX")
        print("⚠️ This will fix issues preventing live trading")
        print("=" * 60)
        
        # Apply fixes
        fixes_applied = await fix_autonomous_trading_execution()
        
        # Verify status
        await verify_autonomous_trading_status()
        
        # Summary
        print("\n" + "=" * 60)
        print("FIX SUMMARY")
        print("=" * 60)
        
        if fixes_applied:
            print(f"✅ FIXES APPLIED: {len(fixes_applied)}")
            for i, fix in enumerate(fixes_applied, 1):
                print(f"  {i}. {fix}")
        else:
            print("⚠️ NO FIXES APPLIED")
        
        print("\n🎯 NEXT STEPS:")
        print("1. Check GUI dashboard for updated system status")
        print("2. Monitor for actual trade execution")
        print("3. Verify LLM decision loop is making decisions")
        print("4. Watch for LIMIT order placements")
        
        if len(fixes_applied) > 0:
            print("\n🎉 AUTONOMOUS TRADING SHOULD NOW BE ACTIVE")
            print("✅ Monitor GUI for trade execution")
            return 0
        else:
            print("\n⚠️ SOME ISSUES MAY REMAIN")
            print("🔧 Check logs for additional errors")
            return 1
        
    except Exception as e:
        print(f"\n❌ FIX ERROR: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
