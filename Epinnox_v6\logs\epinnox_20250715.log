2025-07-15 06:19:05,667 - main - INFO - Epinnox v6 starting up...
2025-07-15 06:19:05,717 - core.performance_monitor - INFO - Performance monitoring started
2025-07-15 06:19:05,717 - core.performance_monitor - INFO - Performance monitor initialized with 10s interval
2025-07-15 06:19:05,717 - main - INFO - Performance monitoring initialized
2025-07-15 06:19:05,728 - utils.cache_manager - INFO - Cache manager initialized: cache, max_age=24h, max_size=100MB
2025-07-15 06:19:05,737 - utils.cache_manager - INFO - Starting automatic cache cleanup
2025-07-15 06:19:05,738 - utils.cache_manager - INFO - Auto cleanup complete: 0 files removed, 0.00 MB freed
2025-07-15 06:19:09,867 - core.timer_coordinator - INFO - [TIMER_COORDINATOR] Initialized unified timer coordinator
2025-07-15 06:19:14,809 - config.autonomous_config - INFO - Configuration loaded from configs/autonomous_trading.yaml
2025-07-15 06:19:15,724 - data.live_data_manager - INFO - Successfully subscribed to live data for BTC/USDT:USDT on timeframes: ['1m', '5m', '15m']
2025-07-15 06:19:17,031 - websocket - INFO - Websocket connected
2025-07-15 06:19:19,753 - trading.position_tracker - INFO - Position Tracker initialized
2025-07-15 06:19:20,171 - llama.runner - INFO - Loaded OpenAI API key from models_config.yaml
2025-07-15 06:19:20,171 - llama.runner - INFO - Using model path: C:\Users\<USER>\.lmstudio\models\lmstudio-community\Phi-3.1-mini-128k-instruct-GGUF\Phi-3.1-mini-128k-instruct-Q4_K_M.gguf
2025-07-15 06:19:20,172 - llama.runner - INFO - Using guard path: C:\Users\<USER>\Documents\dev\Epinnox_v6\models\Llama-Guard-3-8B.gguf
2025-07-15 06:19:20,172 - llama.runner - INFO - Using binary path: C:\Users\<USER>\Documents\dev\llama.cpp\main
2025-07-15 06:19:20,185 - llama.lmstudio_runner - INFO - Loaded LMStudio configuration from models_config.yaml
2025-07-15 06:19:22,236 - llama.lmstudio_runner - INFO - Discovered 8 models: ['phi-3.1-mini-128k-instruct', 'openthinker3-7b', 'mistral-7b-claude-chat', 'fine_tuned_qwen1.7b', 'tsfc-model-0.0.1', 'codestral-22b-v0.1', 'deepseek-r1-distill-qwen-7b', 'text-embedding-nomic-embed-text-v1.5']
2025-07-15 06:19:22,236 - llama.lmstudio_runner - INFO - Selected preferred model from config: phi-3.1-mini-128k-instruct
2025-07-15 06:19:22,236 - llama.lmstudio_runner - INFO - Initialized LMStudio with model: phi-3.1-mini-128k-instruct
2025-07-15 06:19:22,262 - trading.intelligent_limit_order_manager - INFO - Intelligent Limit Order Manager initialized for professional scalping
2025-07-15 06:19:22,263 - core.llm_action_executors - INFO - ✅ Intelligent Limit Order Manager initialized
2025-07-15 06:19:22,263 - core.llm_action_executors - INFO - 🚀 LLM Action Executors initialized with intelligent limit order system
2025-07-15 06:19:22,263 - utils.cache_manager - INFO - Cache manager initialized: cache, max_age=24h, max_size=100MB
2025-07-15 06:19:22,264 - core.llm_orchestrator - INFO - LLM Prompt Orchestrator initialized
2025-07-15 06:19:22,267 - core.signal_hierarchy - INFO - Signal Hierarchy initialized
2025-07-15 06:19:22,335 - storage.database_manager - INFO - Database tables initialized successfully with performance optimizations
2025-07-15 06:19:22,336 - storage.database_manager - INFO - Database manager initialized: data\epinnox_trading.db
2025-07-15 06:19:22,336 - storage.session_manager - INFO - Session Manager initialized
2025-07-15 06:19:22,341 - storage.database_manager - INFO - Created session: live_BTCUSDTUSDT_20250715_061922_801e4598
2025-07-15 06:19:22,341 - storage.session_manager - INFO - Started session: live_BTCUSDTUSDT_20250715_061922_801e4598
2025-07-15 06:19:22,511 - core.risk_management_system - INFO - 🛡️ Risk Management System initialized
2025-07-15 06:19:22,520 - core.error_handling_system - INFO - 🛡️ Error Handling System initialized
2025-07-15 06:19:22,521 - core.error_handling_system - INFO - 📊 Component registered: exchange_connection
2025-07-15 06:19:22,521 - core.error_handling_system - INFO - 📊 Component registered: trading_interface
2025-07-15 06:19:22,521 - core.error_handling_system - INFO - 📊 Component registered: market_data
2025-07-15 06:19:22,521 - core.error_handling_system - INFO - 📊 Component registered: llm_orchestrator
2025-07-15 06:19:22,522 - core.error_handling_system - INFO - 🔍 Health monitoring started
2025-07-15 06:19:22,533 - core.monitoring_dashboard - INFO - 📊 Monitoring Dashboard initialized
2025-07-15 06:19:22,534 - core.symbol_scanner - INFO - SymbolScanner initialized with 8 symbols
2025-07-15 06:19:22,534 - core.timer_coordinator - INFO - [TIMER_COORDINATOR] Registered timer 'symbol_scanner_update': interval=30.0s, priority=MEDIUM
2025-07-15 06:19:22,534 - core.symbol_scanner - INFO - [SYMBOL_SCANNER] Registered with timer coordinator (interval: 30.0s)
2025-07-15 06:19:22,535 - core.llm_action_executors - WARNING - ⚠️ Intelligent Limit Order Manager not available
2025-07-15 06:19:22,535 - core.llm_action_executors - INFO - 🚀 LLM Action Executors initialized with intelligent limit order system
2025-07-15 06:19:59,009 - data.live_data_manager - INFO - Successfully subscribed to live data for DOGE/USDT:USDT on timeframes: ['1m', '5m', '15m']
2025-07-15 06:20:38,947 - core.llm_orchestrator - INFO - 🔍 FULL MODE: Executing all 8 prompts in PARALLEL for comprehensive analysis
2025-07-15 06:20:38,952 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-15 06:20:38,952 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-15 06:20:38,952 - llama.lmstudio_runner - INFO - 💬 User Prompt: 📊 MARKET REGIME ANALYST - DOGE/USDT:USDT

🔍 MULTI-TIMEFRAME ANALYSIS:
1m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
5m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
15m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%

📈 VOLATILITY METRICS:
ATR: 0.001000 | Spread: 0.100%
Volume Ratio: 1.0x | Price Range: 1.00%

🎯 REGIME CLASSIFICATION:
- TRENDING_BULL: Strong upward momentum, high volume, clear direction
- TRENDING_BEAR: Strong downward momentum, high volume, clear direction
- RANGING_TIGHT: Low volatility, narrow pric...
2025-07-15 06:20:38,952 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-15 06:20:38,953 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-15 06:20:38,953 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-15 06:20:38,953 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-15 06:20:38,953 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-15 06:20:38,954 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-15 06:20:38,954 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-15 06:20:38,954 - core.llm_orchestrator - INFO - 🚀 Submitted 5 prompts for parallel execution
2025-07-15 06:20:38,954 - llama.lmstudio_runner - INFO - 💬 User Prompt: ...
2025-07-15 06:20:38,955 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🎯 ENTRY TIMING SPECIALIST - DOGE/USDT:USDT

🎯 SIGNAL ANALYSIS:
ML Ensemble: NEUTRAL (50.0%)
Technical: NEUTRAL | Momentum: 0.00
Volume: PENDING | Orderflow: NEUTRAL

📊 MARKET MICROSTRUCTURE:
Bid/Ask: $0.170000/$0.170000
Spread: 0.100% | L2 Imbalance: 0.0%
Recent Flow: BALANCED | Volume Spike: False

📈 KEY LEVELS:
Support: $0.170000
Resistance: $0.170000
Distance to Support: 0.00%

⏰ TIMING FACTORS:
- Price near key levels: NEUTRAL
- Volume confirmation: PENDING
- Spread conditions: FAVORABLE
- M...
2025-07-15 06:20:38,955 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-15 06:20:38,956 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a conservative futures trading analyst. Analyze market data carefully and provide measured recommendations. Use format: DECISION: [LONG/SHORT/WAIT], CONFIDENCE: [50-100]%, TAKE_PROFIT: [%], ST...
2025-07-15 06:20:38,956 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-15 06:20:38,956 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-15 06:20:38,956 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🔄 STRATEGY ADAPTATION SPECIALIST

📊 PERFORMANCE ANALYSIS (Last 24h):
Trades: 0 | Win Rate: 50.0%
Avg Profit: 0.80% | Avg Loss: -0.30%
Sharpe Ratio: 1.00 | Max Drawdown: 0.0%
Total PnL: $0.00 | ROI: 0.0%

🎯 CURRENT STRATEGY:
Risk per Trade: 2.0% | Avg Hold Time: 8.0min
Entry Threshold: 70% | Exit Threshold: 60%
Position Size Method: FIXED_RISK | Max Positions: 3

📈 MARKET REGIME: UNKNOWN
Regime Confidence: 50.0%
Scalp Suitability: MEDIUM

🔧 ADAPTATION FACTORS:
- Win rate trending: DOWN
- Drawdown...
2025-07-15 06:20:38,957 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🔍 INTELLIGENT OPPORTUNITY SCANNER - Account Health Aware

🚨 ACCOUNT HEALTH STATUS: HEALTHY
💰 Balance: $1000.00 | Health: Healthy - Normal Trading
📊 Current Exposure: $0.00 (0.0% of account)
🎯 Trading Allowed: YES

📊 SYMBOL ANALYSIS:
• DOGE/USDT:USDT: $0.000000 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• BTC/USDT:USDT: $95000.000000 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• ETH/USDT:USDT: $3500.000000 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• SOL/USDT:USDT: $200.000000 | Mom: +0.0% | Vol: 1.0x |...
2025-07-15 06:20:38,957 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-15 06:20:38,958 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-15 06:20:42,554 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 229 chars
2025-07-15 06:20:42,554 - llama.lmstudio_runner - INFO - 📄 Response Preview: ```json
{
  "action": "LONG",
  "confidence": 90,
  "entry_reason": "Market broke above the resistance level with a strong volume increase.",
  "take_profit": 2.5,
  "stop_loss": 1.0,
  "hold_time": "3min",
  "leverage": 40
}
```...
2025-07-15 06:20:42,554 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 174, Completion: 93, Total: 267
2025-07-15 06:20:42,555 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['action', 'confidence', 'entry_reason', 'take_profit', 'stop_loss', 'hold_time', 'leverage']
2025-07-15 06:20:42,556 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['ACTION', 'CONFIDENCE', 'ENTRY_REASON', 'TAKE_PROFIT', 'STOP_LOSS', 'HOLD_TIME', 'LEVERAGE']
2025-07-15 06:20:42,556 - core.llm_response_parsers - INFO - Risk assessment parsed: REJECTED (Risk: 50)
2025-07-15 06:20:42,556 - core.llm_orchestrator - INFO - ✅ Completed prompt: risk_assessment
2025-07-15 06:20:44,293 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 218 chars
2025-07-15 06:20:44,293 - llama.lmstudio_runner - INFO - 📄 Response Preview: ```json
{
  "REGIME": "RANGING_TIGHT",
  "CONFIDENCE": 75,
  "SCALP_SUITABILITY": "MEDIUM",
  "RECOMMENDED_TIMEFRAME": "1m",
  "RISK_LEVEL": "MEDIUM",
  "REASONING": "Consolidation phase with moderate volatility"
}
```...
2025-07-15 06:20:44,294 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 705, Completion: 108, Total: 813
2025-07-15 06:20:44,294 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['REGIME', 'CONFIDENCE', 'SCALP_SUITABILITY', 'RECOMMENDED_TIMEFRAME', 'RISK_LEVEL', 'REASONING']
2025-07-15 06:20:44,294 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['REGIME', 'CONFIDENCE', 'SCALP_SUITABILITY', 'RECOMMENDED_TIMEFRAME', 'RISK_LEVEL', 'REASONING']
2025-07-15 06:20:44,295 - core.llm_response_parsers - INFO - Market regime parsed: RANGING_TIGHT (MEDIUM scalp suitability)
2025-07-15 06:20:44,295 - core.llm_orchestrator - INFO - ✅ Completed prompt: market_regime
2025-07-15 06:20:45,853 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 229 chars
2025-07-15 06:20:45,853 - llama.lmstudio_runner - INFO - 📄 Response Preview: ```json
{
  "risk_adjustment": 1.0,
  "hold_time_target": 8,
  "entry_threshold": 70,
  "exit_threshold": 60,
  "sizing_method": "FIXED_RISK",
  "reasoning": "Maintain current strategy - performing well",
  "confidence": 85
}
```...
2025-07-15 06:20:45,854 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 619, Completion: 100, Total: 719
2025-07-15 06:20:45,854 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['risk_adjustment', 'hold_time_target', 'entry_threshold', 'exit_threshold', 'sizing_method', 'reasoning', 'confidence']
2025-07-15 06:20:45,855 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['RISK_ADJUSTMENT', 'HOLD_TIME_TARGET', 'ENTRY_THRESHOLD', 'EXIT_THRESHOLD', 'SIZING_METHOD', 'REASONING', 'CONFIDENCE']
2025-07-15 06:20:45,855 - core.llm_response_parsers - INFO - Strategy adaptation parsed: Risk adj 1.0x
2025-07-15 06:20:45,855 - core.llm_action_executors - INFO - Applying strategy adaptations: Risk 1.0x, Hold time 8min
2025-07-15 06:20:45,856 - core.llm_orchestrator - INFO - ✅ Completed prompt: strategy_adaptation
2025-07-15 06:20:47,369 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 311 chars
2025-07-15 06:20:47,370 - llama.lmstudio_runner - INFO - 📄 Response Preview: {
  "action": "ENTER_NOW",
  "entry_type": "LIMIT",
  "confidence": 90,
  "wait_for": null,
  "max_wait_seconds": null,
  "reasoning": "Market conditions are favorable with a narrow spread and no significant imbalance. The price is near key levels indicating potential for movement within the next ti...
2025-07-15 06:20:47,370 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 610, Completion: 96, Total: 706
2025-07-15 06:20:47,371 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['action', 'entry_type', 'confidence', 'wait_for', 'max_wait_seconds', 'reasoning']
2025-07-15 06:20:47,371 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['ACTION', 'ENTRY_TYPE', 'CONFIDENCE', 'WAIT_FOR', 'MAX_WAIT_SECONDS', 'REASONING']
2025-07-15 06:20:47,372 - core.llm_response_parsers - INFO - Entry timing parsed: ENTER_NOW (LIMIT order)
2025-07-15 06:20:47,372 - core.llm_action_executors - INFO - Executing entry action: ENTER_NOW (LIMIT order)
2025-07-15 06:20:47,372 - core.llm_action_executors - WARNING - No position capacity available - skipping entry
2025-07-15 06:20:47,373 - core.llm_orchestrator - INFO - ✅ Completed prompt: entry_timing
2025-07-15 06:20:51,679 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 1147 chars
2025-07-15 06:20:51,679 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: SHORT, CONFIDENCE: 85%, TAKE_PROFIT: 2%, STOP_LOSS: -3%, EXPLANATION: The current market data shows a neutral sector momentum and normal volatility with an average volume profile. Given the account's healthy status and risk-adjusted capacity, there is room for conservative trading strategi...
2025-07-15 06:20:51,680 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 933, Completion: 271, Total: 1204
2025-07-15 06:20:51,680 - core.llm_response_parsers - INFO - 🔄 No JSON found, trying structured text parsing
2025-07-15 06:20:51,682 - core.llm_response_parsers - INFO - Parsed structured text: {'DECISION': 'SHORT', 'CONFIDENCE': 85.0, 'TAKE_PROFIT': 2.0, 'STOP_LOSS': -3.0, 'EXPLANATION': "THE CURRENT MARKET DATA SHOWS A NEUTRAL SECTOR MOMENTUM AND NORMAL VOLATILITY WITH AN AVERAGE VOLUME PROFILE. GIVEN THE ACCOUNT'S HEALTHY STATUS AND RISK-ADJUSTED CAPACITY, THERE IS ROOM FOR CONSERVATIVE TRADING STRATEGIES THAT AIM TO CAPITALIZE ON POTENTIAL SHORT POSITIONS IN CRYPTOCURRENCIES LIKE DOGE/USDT WHICH CURRENTLY HAVE NO SIGNIFICANT PRICE MOVEMENT (MOM: +0.0%). THE HISTORICAL CONTEXT OF LAST 5 PRICES BEING STATIC AT $0.0 AND SIGNALS ALL INDICATING 'WAIT' SUGGESTS A LACK OF IMMEDIATE TRADING OPPORTUNITIES, BUT ALSO THE POTENTIAL FOR FUTURE DOWNWARD MOVEMENTS DUE TO MARKET INERTIA OR NEGATIVE NEWS AFFECTING SENTIMENT TOWARDS ALTCOINS LIKE DOGE/USDT. A CONSERVATIVE SHORT POSITION WITH HIGH CONFIDENCE IS RECOMMENDED AS IT ALIGNS WELL WITH BOTH OPPORTUNITY CRITERIA AND ACCOUNT PRESERVATION PARAMETERS WHILE MAINTAINING A HEALTHY RISK BUDGET PER TRADE AT 2%. THE TAKE-PROFIT LEVEL OF 2% ENSURES PROFITABILITY WITHIN THE EXPECTED VOLATILITY RANGE, WHEREAS THE STOP LOSS SET TO -3% PROVIDES ADEQUATE PROTECTION AGAINST POTENTIAL ADVERSE PRICE MOVEMENTS.", 'ACTION': 'ENTER_NOW'}
2025-07-15 06:20:51,682 - core.llm_response_parsers - INFO - ✅ Structured text parsing successful: ['DECISION', 'CONFIDENCE', 'TAKE_PROFIT', 'STOP_LOSS', 'EXPLANATION', 'ACTION']
2025-07-15 06:20:51,682 - core.llm_response_parsers - INFO - Opportunity scanner parsed: BREAKOUT (REVERSAL)
2025-07-15 06:20:51,682 - core.llm_orchestrator - INFO - ✅ Completed prompt: opportunity_scanner
2025-07-15 06:20:51,683 - core.llm_orchestrator - INFO - 🚀 PARALLEL LLM cycle completed in 12.74s - 5 prompts executed concurrently
2025-07-15 06:21:08,555 - core.llm_orchestrator - INFO - 🔍 FULL MODE: Executing all 8 prompts in PARALLEL for comprehensive analysis
2025-07-15 06:21:08,556 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-15 06:21:08,556 - core.llm_orchestrator - INFO - 🚀 Submitted 1 prompts for parallel execution
2025-07-15 06:21:08,556 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-15 06:21:08,557 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🎯 ENTRY TIMING SPECIALIST - DOGE/USDT:USDT

🎯 SIGNAL ANALYSIS:
ML Ensemble: NEUTRAL (50.0%)
Technical: NEUTRAL | Momentum: 0.00
Volume: PENDING | Orderflow: NEUTRAL

📊 MARKET MICROSTRUCTURE:
Bid/Ask: $0.170000/$0.170000
Spread: 0.100% | L2 Imbalance: 0.0%
Recent Flow: BALANCED | Volume Spike: False

📈 KEY LEVELS:
Support: $0.170000
Resistance: $0.170000
Distance to Support: 0.00%

⏰ TIMING FACTORS:
- Price near key levels: NEUTRAL
- Volume confirmation: PENDING
- Spread conditions: FAVORABLE
- M...
2025-07-15 06:21:08,557 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-15 06:21:12,529 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 354 chars
2025-07-15 06:21:12,529 - llama.lmstudio_runner - INFO - 📄 Response Preview: {
  "action": "ENTER",
  "entry_type": "LIMIT",
  "confidence": 85,
  "wait_for": null,
  "max_wait_seconds": 0,
  "reasoning": "Market conditions are favorable with a NEUTRAL signal analysis and the spread is FAVORABLE. Although volume confirmation is pending, historical context shows no recent pri...
2025-07-15 06:21:12,530 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 610, Completion: 104, Total: 714
2025-07-15 06:21:12,530 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['action', 'entry_type', 'confidence', 'wait_for', 'max_wait_seconds', 'reasoning']
2025-07-15 06:21:12,530 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['ACTION', 'ENTRY_TYPE', 'CONFIDENCE', 'WAIT_FOR', 'MAX_WAIT_SECONDS', 'REASONING']
2025-07-15 06:21:12,531 - core.llm_response_parsers - INFO - Entry timing parsed: WAIT (LIMIT order)
2025-07-15 06:21:12,531 - core.llm_orchestrator - INFO - ✅ Completed prompt: entry_timing
2025-07-15 06:21:12,531 - core.llm_orchestrator - INFO - 🚀 PARALLEL LLM cycle completed in 3.98s - 1 prompts executed concurrently
2025-07-15 06:21:38,948 - core.llm_orchestrator - INFO - 🔍 FULL MODE: Executing all 8 prompts in PARALLEL for comprehensive analysis
2025-07-15 06:21:38,950 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-15 06:21:38,950 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-15 06:21:38,951 - llama.lmstudio_runner - INFO - 💬 User Prompt: 📊 MARKET REGIME ANALYST - DOGE/USDT:USDT

🔍 MULTI-TIMEFRAME ANALYSIS:
1m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
5m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
15m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%

📈 VOLATILITY METRICS:
ATR: 0.001000 | Spread: 0.100%
Volume Ratio: 1.0x | Price Range: 1.00%

🎯 REGIME CLASSIFICATION:
- TRENDING_BULL: Strong upward momentum, high volume, clear direction
- TRENDING_BEAR: Strong downward momentum, high volume, clear direction
- RANGING_TIGHT: Low volatility, narrow pric...
2025-07-15 06:21:38,951 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-15 06:21:38,951 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-15 06:21:38,952 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-15 06:21:38,952 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-15 06:21:38,952 - core.llm_orchestrator - INFO - 🚀 Submitted 4 prompts for parallel execution
2025-07-15 06:21:38,953 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🎯 ENTRY TIMING SPECIALIST - DOGE/USDT:USDT

🎯 SIGNAL ANALYSIS:
ML Ensemble: NEUTRAL (50.0%)
Technical: NEUTRAL | Momentum: 0.00
Volume: PENDING | Orderflow: NEUTRAL

📊 MARKET MICROSTRUCTURE:
Bid/Ask: $0.170000/$0.170000
Spread: 0.100% | L2 Imbalance: 0.0%
Recent Flow: BALANCED | Volume Spike: False

📈 KEY LEVELS:
Support: $0.170000
Resistance: $0.170000
Distance to Support: 0.00%

⏰ TIMING FACTORS:
- Price near key levels: NEUTRAL
- Volume confirmation: PENDING
- Spread conditions: FAVORABLE
- M...
2025-07-15 06:21:38,952 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-15 06:21:38,952 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-15 06:21:38,953 - llama.lmstudio_runner - INFO - 💬 User Prompt: ...
2025-07-15 06:21:38,953 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-15 06:21:38,953 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a conservative futures trading analyst. Analyze market data carefully and provide measured recommendations. Use format: DECISION: [LONG/SHORT/WAIT], CONFIDENCE: [50-100]%, TAKE_PROFIT: [%], ST...
2025-07-15 06:21:38,954 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-15 06:21:38,954 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🔍 INTELLIGENT OPPORTUNITY SCANNER - Account Health Aware

🚨 ACCOUNT HEALTH STATUS: HEALTHY
💰 Balance: $1000.00 | Health: Healthy - Normal Trading
📊 Current Exposure: $0.00 (0.0% of account)
🎯 Trading Allowed: YES

📊 SYMBOL ANALYSIS:
• DOGE/USDT:USDT: $0.000000 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• BTC/USDT:USDT: $95000.000000 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• ETH/USDT:USDT: $3500.000000 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• SOL/USDT:USDT: $200.000000 | Mom: +0.0% | Vol: 1.0x |...
2025-07-15 06:21:38,955 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-15 06:21:42,680 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 306 chars
2025-07-15 06:21:42,681 - llama.lmstudio_runner - INFO - 📄 Response Preview: {
  "action": "ENTER",
  "entry_type": "LIMIT",
  "confidence": 90,
  "wait_for": null,
  "max_wait_seconds": 30,
  "reasoning": "Market conditions are favorable with a narrow spread and no significant imbalance. The price is near key support levels which historically have been strong points for ent...
2025-07-15 06:21:42,681 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 610, Completion: 96, Total: 706
2025-07-15 06:21:42,681 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['action', 'entry_type', 'confidence', 'wait_for', 'max_wait_seconds', 'reasoning']
2025-07-15 06:21:42,682 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['ACTION', 'ENTRY_TYPE', 'CONFIDENCE', 'WAIT_FOR', 'MAX_WAIT_SECONDS', 'REASONING']
2025-07-15 06:21:42,682 - core.llm_response_parsers - INFO - Entry timing parsed: WAIT (LIMIT order)
2025-07-15 06:21:42,682 - core.llm_orchestrator - INFO - ✅ Completed prompt: entry_timing
2025-07-15 06:21:44,402 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 218 chars
2025-07-15 06:21:44,402 - llama.lmstudio_runner - INFO - 📄 Response Preview: ```json
{
  "REGIME": "RANGING_TIGHT",
  "CONFIDENCE": 75,
  "SCALP_SUITABILITY": "MEDIUM",
  "RECOMMENDED_TIMEFRAME": "1m",
  "RISK_LEVEL": "MEDIUM",
  "REASONING": "Consolidation phase with moderate volatility"
}
```...
2025-07-15 06:21:44,402 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 705, Completion: 108, Total: 813
2025-07-15 06:21:44,402 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['REGIME', 'CONFIDENCE', 'SCALP_SUITABILITY', 'RECOMMENDED_TIMEFRAME', 'RISK_LEVEL', 'REASONING']
2025-07-15 06:21:44,403 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['REGIME', 'CONFIDENCE', 'SCALP_SUITABILITY', 'RECOMMENDED_TIMEFRAME', 'RISK_LEVEL', 'REASONING']
2025-07-15 06:21:44,403 - core.llm_response_parsers - INFO - Market regime parsed: RANGING_TIGHT (MEDIUM scalp suitability)
2025-07-15 06:21:44,403 - core.llm_orchestrator - INFO - ✅ Completed prompt: market_regime
2025-07-15 06:21:48,312 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 967 chars
2025-07-15 06:21:48,312 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: LONG, CONFIDENCE: 85%, TAKE_PROFIT: 2%, STOP_LOSS: -3%, EXPLANATION: The account is in a healthy state with normal trading parameters and the current setup for DOGE/USDT shows high quality signals. With an acceptable risk-reward ratio of >3:1, excellent liquidity indicated by low spreads (...
2025-07-15 06:21:48,313 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 933, Completion: 242, Total: 1175
2025-07-15 06:21:48,313 - core.llm_response_parsers - INFO - 🔄 No JSON found, trying structured text parsing
2025-07-15 06:21:48,313 - core.llm_response_parsers - INFO - Parsed structured text: {'DECISION': 'LONG', 'CONFIDENCE': 85.0, 'TAKE_PROFIT': 2.0, 'STOP_LOSS': -3.0, 'EXPLANATION': "THE ACCOUNT IS IN A HEALTHY STATE WITH NORMAL TRADING PARAMETERS AND THE CURRENT SETUP FOR DOGE/USDT SHOWS HIGH QUALITY SIGNALS. WITH AN ACCEPTABLE RISK-REWARD RATIO OF >3:1, EXCELLENT LIQUIDITY INDICATED BY LOW SPREADS (<0.2%), STRONG MOMENTUM ALIGNMENT, AND CONSERVATIVE VOLATILITY RANGE PREFERRED AT 1-3%, IT IS A STRATEGIC MOVE TO ENTER THE MARKET LONG ON DOGE/USDT WITH MODERATE CONFIDENCE LEVELS BUT CAUTIOUS PROFIT TARGETS SET FOR TAKE PROFITS AT +2%. THE STOP LOSS WILL BE -3% OF ENTRY PRICE AS PER ACCOUNT PRESERVATION CRITERIA AND OPPORTUNITY RANKING. THIS APPROACH BALANCES POTENTIAL GAINS WHILE MANAGING RISK EFFECTIVELY IN LINE WITH THE HEALTH-ADJUSTED TRADING STRATEGY, ACKNOWLEDGING THAT HISTORICAL DATA SHOWS A 'WAIT' SIGNAL WHICH SUGGESTS CAUTION BUT DOES NOT RULE OUT FUTURE POSITIVE MOVEMENTS GIVEN CURRENT MARKET CONDITIONS ARE FAVORABLE FOR DOGE/USDT AS PER ANALYSIS.", 'ACTION': 'ENTER_NOW'}
2025-07-15 06:21:48,313 - core.llm_response_parsers - INFO - ✅ Structured text parsing successful: ['DECISION', 'CONFIDENCE', 'TAKE_PROFIT', 'STOP_LOSS', 'EXPLANATION', 'ACTION']
2025-07-15 06:21:48,314 - core.llm_response_parsers - INFO - Opportunity scanner parsed: BREAKOUT (MOMENTUM)
2025-07-15 06:21:48,314 - core.llm_orchestrator - INFO - ✅ Completed prompt: opportunity_scanner
2025-07-15 06:21:49,695 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 223 chars
2025-07-15 06:21:49,695 - llama.lmstudio_runner - INFO - 📄 Response Preview: ```json
{
  "action": "LONG",
  "confidence": 90,
  "entry_reason": "Breakout above the resistance level at $125 with a volume spike.",
  "take_profit": 3.0,
  "stop_loss": 1.5,
  "hold_time": "3min",
  "leverage": 40
}
```...
2025-07-15 06:21:49,695 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 174, Completion: 98, Total: 272
2025-07-15 06:21:49,696 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['action', 'confidence', 'entry_reason', 'take_profit', 'stop_loss', 'hold_time', 'leverage']
2025-07-15 06:21:49,696 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['ACTION', 'CONFIDENCE', 'ENTRY_REASON', 'TAKE_PROFIT', 'STOP_LOSS', 'HOLD_TIME', 'LEVERAGE']
2025-07-15 06:21:49,696 - core.llm_response_parsers - INFO - Risk assessment parsed: REJECTED (Risk: 50)
2025-07-15 06:21:49,697 - core.llm_orchestrator - INFO - ✅ Completed prompt: risk_assessment
2025-07-15 06:21:49,698 - core.llm_orchestrator - INFO - 🚀 PARALLEL LLM cycle completed in 10.75s - 4 prompts executed concurrently
2025-07-15 06:22:09,467 - core.llm_orchestrator - INFO - 🔍 FULL MODE: Executing all 8 prompts in PARALLEL for comprehensive analysis
2025-07-15 06:22:09,468 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-15 06:22:09,468 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-15 06:22:09,468 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-15 06:22:09,469 - llama.lmstudio_runner - INFO - 💬 User Prompt: 📊 MARKET REGIME ANALYST - DOGE/USDT:USDT

🔍 MULTI-TIMEFRAME ANALYSIS:
1m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
5m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
15m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%

📈 VOLATILITY METRICS:
ATR: 0.001000 | Spread: 0.100%
Volume Ratio: 1.0x | Price Range: 1.00%

🎯 REGIME CLASSIFICATION:
- TRENDING_BULL: Strong upward momentum, high volume, clear direction
- TRENDING_BEAR: Strong downward momentum, high volume, clear direction
- RANGING_TIGHT: Low volatility, narrow pric...
2025-07-15 06:22:09,468 - core.llm_orchestrator - INFO - 🚀 Submitted 2 prompts for parallel execution
2025-07-15 06:22:09,469 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-15 06:22:09,469 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-15 06:22:09,469 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🎯 ENTRY TIMING SPECIALIST - DOGE/USDT:USDT

🎯 SIGNAL ANALYSIS:
ML Ensemble: NEUTRAL (50.0%)
Technical: NEUTRAL | Momentum: 0.00
Volume: PENDING | Orderflow: NEUTRAL

📊 MARKET MICROSTRUCTURE:
Bid/Ask: $0.170000/$0.170000
Spread: 0.100% | L2 Imbalance: 0.0%
Recent Flow: BALANCED | Volume Spike: False

📈 KEY LEVELS:
Support: $0.170000
Resistance: $0.170000
Distance to Support: 0.00%

⏰ TIMING FACTORS:
- Price near key levels: NEUTRAL
- Volume confirmation: PENDING
- Spread conditions: FAVORABLE
- M...
2025-07-15 06:22:09,470 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-15 06:22:13,351 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 262 chars
2025-07-15 06:22:13,352 - llama.lmstudio_runner - INFO - 📄 Response Preview: {
  "action": "ENTER",
  "entry_type": "LIMIT",
  "confidence": 90,
  "wait_for": null,
  "max_wait_seconds": 30,
  "reasoning": "Market conditions are favorable with a narrow spread and no significant volume spike. The risk/reward ratio is optimal for entry."
}...
2025-07-15 06:22:13,352 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 610, Completion: 90, Total: 700
2025-07-15 06:22:13,353 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['action', 'entry_type', 'confidence', 'wait_for', 'max_wait_seconds', 'reasoning']
2025-07-15 06:22:13,353 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['ACTION', 'ENTRY_TYPE', 'CONFIDENCE', 'WAIT_FOR', 'MAX_WAIT_SECONDS', 'REASONING']
2025-07-15 06:22:13,353 - core.llm_response_parsers - INFO - Entry timing parsed: WAIT (LIMIT order)
2025-07-15 06:22:13,354 - core.llm_orchestrator - INFO - ✅ Completed prompt: entry_timing
2025-07-15 06:22:15,800 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 415 chars
2025-07-15 06:22:15,801 - llama.lmstudio_runner - INFO - 📄 Response Preview: ```json
{
  "REGIME": "RANGING_TIGHT",
  "CONFIDENCE": 75,
  "SCALP_SUITABILITY": "MEDIUM",
  "RECOMMENDED_TIMEFRAME": "1m",
  "RISK_LEVEL": "MEDIUM",
  "REASONING": "Consolidation phase with moderate volatility, suitable for scalping during clear trends and good volume. However, due to the tight ra...
2025-07-15 06:22:15,801 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 705, Completion: 152, Total: 857
2025-07-15 06:22:15,801 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['REGIME', 'CONFIDENCE', 'SCALP_SUITABILITY', 'RECOMMENDED_TIMEFRAME', 'RISK_LEVEL', 'REASONING']
2025-07-15 06:22:15,801 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['REGIME', 'CONFIDENCE', 'SCALP_SUITABILITY', 'RECOMMENDED_TIMEFRAME', 'RISK_LEVEL', 'REASONING']
2025-07-15 06:22:15,802 - core.llm_response_parsers - INFO - Market regime parsed: RANGING_TIGHT (MEDIUM scalp suitability)
2025-07-15 06:22:15,802 - core.llm_orchestrator - INFO - ✅ Completed prompt: market_regime
2025-07-15 06:22:15,803 - core.llm_orchestrator - INFO - 🚀 PARALLEL LLM cycle completed in 6.34s - 2 prompts executed concurrently
2025-07-15 06:22:38,494 - core.llm_orchestrator - INFO - 🔍 FULL MODE: Executing all 8 prompts in PARALLEL for comprehensive analysis
2025-07-15 06:22:38,495 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-15 06:22:38,495 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-15 06:22:38,495 - llama.lmstudio_runner - INFO - 💬 User Prompt: ...
2025-07-15 06:22:38,495 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-15 06:22:38,495 - core.llm_orchestrator - INFO - 🚀 Submitted 2 prompts for parallel execution
2025-07-15 06:22:38,495 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-15 06:22:38,495 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-15 06:22:38,496 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🎯 ENTRY TIMING SPECIALIST - DOGE/USDT:USDT

🎯 SIGNAL ANALYSIS:
ML Ensemble: NEUTRAL (50.0%)
Technical: NEUTRAL | Momentum: 0.00
Volume: PENDING | Orderflow: NEUTRAL

📊 MARKET MICROSTRUCTURE:
Bid/Ask: $0.170000/$0.170000
Spread: 0.100% | L2 Imbalance: 0.0%
Recent Flow: BALANCED | Volume Spike: False

📈 KEY LEVELS:
Support: $0.170000
Resistance: $0.170000
Distance to Support: 0.00%

⏰ TIMING FACTORS:
- Price near key levels: NEUTRAL
- Volume confirmation: PENDING
- Spread conditions: FAVORABLE
- M...
2025-07-15 06:22:38,496 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-15 06:22:42,188 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 224 chars
2025-07-15 06:22:42,188 - llama.lmstudio_runner - INFO - 📄 Response Preview: ```json
{
  "action": "LONG",
  "confidence": 90,
  "entry_reason": "Market broke through resistance level with a strong volume surge.",
  "take_profit": 2.5,
  "stop_loss": 1.7,
  "hold_time": "3min",
  "leverage": 40
}
```...
2025-07-15 06:22:42,188 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 174, Completion: 93, Total: 267
2025-07-15 06:22:42,189 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['action', 'confidence', 'entry_reason', 'take_profit', 'stop_loss', 'hold_time', 'leverage']
2025-07-15 06:22:42,189 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['ACTION', 'CONFIDENCE', 'ENTRY_REASON', 'TAKE_PROFIT', 'STOP_LOSS', 'HOLD_TIME', 'LEVERAGE']
2025-07-15 06:22:42,189 - core.llm_response_parsers - INFO - Risk assessment parsed: REJECTED (Risk: 50)
2025-07-15 06:22:42,189 - core.llm_orchestrator - INFO - ✅ Completed prompt: risk_assessment
2025-07-15 06:22:47,662 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 1830 chars
2025-07-15 06:22:47,663 - llama.lmstudio_runner - INFO - 📄 Response Preview: {
  "action": "ENTER", // Decision to enter the market based on favorable spread conditions, despite other factors being neutral or pending confirmation.
  "entry_type": "LIMIT", // Preference for a limit order over an open outcry system in high-frequency trading environments where precision is key ...
2025-07-15 06:22:47,663 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 610, Completion: 374, Total: 984
2025-07-15 06:22:47,664 - core.llm_response_parsers - WARNING - 🚨 JSON decode error: Expecting ',' delimiter: line 1 column 180 (char 179)
2025-07-15 06:22:47,664 - core.llm_response_parsers - WARNING - 🔍 Problematic JSON: { "action": "ENTER", "entry_type": "LIMIT", "confidence": 85, "wait_for": null, "reasoning": "The decision is driven by a combination of factors that align well for an immediate "entry": favorable spr...
2025-07-15 06:22:47,665 - core.llm_response_parsers - INFO - ✅ Aggressive cleanup extracted: ['action', 'confidence', 'reasoning']
2025-07-15 06:22:47,666 - core.llm_response_parsers - INFO - ✅ JSON fixed with aggressive cleanup
2025-07-15 06:22:47,666 - core.llm_response_parsers - INFO - Entry timing parsed: WAIT (LIMIT order)
2025-07-15 06:22:47,666 - core.llm_orchestrator - INFO - ✅ Completed prompt: entry_timing
2025-07-15 06:22:47,667 - core.llm_orchestrator - INFO - 🚀 PARALLEL LLM cycle completed in 9.17s - 2 prompts executed concurrently
2025-07-15 06:23:08,499 - core.llm_orchestrator - INFO - 🔍 FULL MODE: Executing all 8 prompts in PARALLEL for comprehensive analysis
2025-07-15 06:23:08,501 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-15 06:23:08,502 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-15 06:23:08,503 - llama.lmstudio_runner - INFO - 💬 User Prompt: 📊 MARKET REGIME ANALYST - DOGE/USDT:USDT

🔍 MULTI-TIMEFRAME ANALYSIS:
1m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
5m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
15m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%

📈 VOLATILITY METRICS:
ATR: 0.001000 | Spread: 0.100%
Volume Ratio: 1.0x | Price Range: 1.00%

🎯 REGIME CLASSIFICATION:
- TRENDING_BULL: Strong upward momentum, high volume, clear direction
- TRENDING_BEAR: Strong downward momentum, high volume, clear direction
- RANGING_TIGHT: Low volatility, narrow pric...
2025-07-15 06:23:08,503 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-15 06:23:08,504 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-15 06:23:08,504 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-15 06:23:08,504 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-15 06:23:08,505 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-15 06:23:08,505 - core.llm_orchestrator - INFO - 🚀 Submitted 4 prompts for parallel execution
2025-07-15 06:23:08,505 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🎯 ENTRY TIMING SPECIALIST - DOGE/USDT:USDT

🎯 SIGNAL ANALYSIS:
ML Ensemble: NEUTRAL (50.0%)
Technical: NEUTRAL | Momentum: 0.00
Volume: PENDING | Orderflow: NEUTRAL

📊 MARKET MICROSTRUCTURE:
Bid/Ask: $0.170000/$0.170000
Spread: 0.100% | L2 Imbalance: 0.0%
Recent Flow: BALANCED | Volume Spike: False

📈 KEY LEVELS:
Support: $0.170000
Resistance: $0.170000
Distance to Support: 0.00%

⏰ TIMING FACTORS:
- Price near key levels: NEUTRAL
- Volume confirmation: PENDING
- Spread conditions: FAVORABLE
- M...
2025-07-15 06:23:08,507 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a conservative futures trading analyst. Analyze market data carefully and provide measured recommendations. Use format: DECISION: [LONG/SHORT/WAIT], CONFIDENCE: [50-100]%, TAKE_PROFIT: [%], ST...
2025-07-15 06:23:08,507 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-15 06:23:08,508 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-15 06:23:08,508 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🔍 INTELLIGENT OPPORTUNITY SCANNER - Account Health Aware

🚨 ACCOUNT HEALTH STATUS: HEALTHY
💰 Balance: $1000.00 | Health: Healthy - Normal Trading
📊 Current Exposure: $0.00 (0.0% of account)
🎯 Trading Allowed: YES

📊 SYMBOL ANALYSIS:
• DOGE/USDT:USDT: $0.000000 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• BTC/USDT:USDT: $95000.000000 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• ETH/USDT:USDT: $3500.000000 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• SOL/USDT:USDT: $200.000000 | Mom: +0.0% | Vol: 1.0x |...
2025-07-15 06:23:08,509 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🔄 STRATEGY ADAPTATION SPECIALIST

📊 PERFORMANCE ANALYSIS (Last 24h):
Trades: 0 | Win Rate: 50.0%
Avg Profit: 0.80% | Avg Loss: -0.30%
Sharpe Ratio: 1.00 | Max Drawdown: 0.0%
Total PnL: $0.00 | ROI: 0.0%

🎯 CURRENT STRATEGY:
Risk per Trade: 2.0% | Avg Hold Time: 8.0min
Entry Threshold: 70% | Exit Threshold: 60%
Position Size Method: FIXED_RISK | Max Positions: 3

📈 MARKET REGIME: UNKNOWN
Regime Confidence: 50.0%
Scalp Suitability: MEDIUM

🔧 ADAPTATION FACTORS:
- Win rate trending: DOWN
- Drawdown...
2025-07-15 06:23:08,510 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-15 06:23:08,510 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-15 06:23:13,340 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 497 chars
2025-07-15 06:23:13,341 - llama.lmstudio_runner - INFO - 📄 Response Preview: ```json
{
  "REGIME": "RANGING_TIGHT",
  "CONFIDENCE": 75,
  "SCALP_SUITABILITY": "MEDIUM",
  "RECOMMENDED_TIMEFRAME": "1m",
  "RISK_LEVEL": "MEDIUM",
  "REASONING": "Consolidation phase with moderate volatility indicates a period where price action is tight and ranges are narrow. This environment p...
2025-07-15 06:23:13,341 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 705, Completion: 162, Total: 867
2025-07-15 06:23:13,341 - core.llm_response_parsers - WARNING - 🚨 JSON decode error: Expecting ',' delimiter: line 1 column 434 (char 433)
2025-07-15 06:23:13,341 - core.llm_response_parsers - WARNING - 🔍 Problematic JSON: { "REGIME": "RANGING_TIGHT", "CONFIDENCE": 75, "SCALP_SUITABILITY": "MEDIUM", "RECOMMENDED_TIMEFRAME": "1m", "RISK_LEVEL": "MEDIUM", "REASONING": "Consolidation phase with moderate volatility indicate...
2025-07-15 06:23:13,342 - core.llm_response_parsers - INFO - ✅ Aggressive cleanup extracted: ['confidence', 'reasoning']
2025-07-15 06:23:13,342 - core.llm_response_parsers - INFO - ✅ JSON fixed with aggressive cleanup
2025-07-15 06:23:13,342 - core.llm_response_parsers - INFO - Market regime parsed: RANGING_TIGHT (MEDIUM scalp suitability)
2025-07-15 06:23:13,342 - core.llm_orchestrator - INFO - ✅ Completed prompt: market_regime
2025-07-15 06:23:14,890 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 322 chars
2025-07-15 06:23:14,890 - llama.lmstudio_runner - INFO - 📄 Response Preview: {"risk_adjustment":1.5, "hold_time_target":30, "entry_threshold":75, "exit_threshold":65, "sizing_method":"VARIABLE", "reasoning":"Increase risk to capitalize on current downtrend and improve win rate while maintaining a manageable drawdown level. Adjust entry threshold for higher confidence in tren...
2025-07-15 06:23:14,891 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 619, Completion: 98, Total: 717
2025-07-15 06:23:14,891 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['risk_adjustment', 'hold_time_target', 'entry_threshold', 'exit_threshold', 'sizing_method', 'reasoning', 'confidence']
2025-07-15 06:23:14,891 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['RISK_ADJUSTMENT', 'HOLD_TIME_TARGET', 'ENTRY_THRESHOLD', 'EXIT_THRESHOLD', 'SIZING_METHOD', 'REASONING', 'CONFIDENCE']
2025-07-15 06:23:14,892 - core.llm_response_parsers - INFO - Strategy adaptation parsed: Risk adj 1.5x
2025-07-15 06:23:14,892 - core.llm_action_executors - INFO - Applying strategy adaptations: Risk 1.5x, Hold time 30min
2025-07-15 06:23:14,892 - core.llm_orchestrator - INFO - ✅ Completed prompt: strategy_adaptation
2025-07-15 06:23:16,580 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 318 chars
2025-07-15 06:23:16,581 - llama.lmstudio_runner - INFO - 📄 Response Preview: ```json
{
  "action": "ENTER_NOW",
  "entry_type": "LIMIT",
  "confidence": 90,
  "wait_for": null,
  "max_wait_seconds": 30,
  "reasoning": "Market conditions are favorable with a low spread and balanced recent flow. The risk/reward ratio is optimal at 2:1 considering the current bid price near sup...
2025-07-15 06:23:16,581 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 610, Completion: 108, Total: 718
2025-07-15 06:23:16,582 - core.llm_response_parsers - WARNING - 🚨 JSON decode error: Expecting ',' delimiter: line 1 column 236 (char 235)
2025-07-15 06:23:16,582 - core.llm_response_parsers - WARNING - 🔍 Problematic JSON: { "action": "ENTER_NOW", "entry_type": "LIMIT", "confidence": 90, "wait_for": null, "max_wait_seconds": 30, "reasoning": "Market conditions are favorable with a low spread and balanced recent flow. Th...
2025-07-15 06:23:16,582 - core.llm_response_parsers - INFO - ✅ Aggressive cleanup extracted: ['action', 'confidence', 'reasoning']
2025-07-15 06:23:16,582 - core.llm_response_parsers - INFO - ✅ JSON fixed with aggressive cleanup
2025-07-15 06:23:16,583 - core.llm_response_parsers - INFO - Entry timing parsed: WAIT (LIMIT order)
2025-07-15 06:23:16,583 - core.llm_orchestrator - INFO - ✅ Completed prompt: entry_timing
2025-07-15 06:23:21,454 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 1250 chars
2025-07-15 06:23:21,454 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: SHORT, CONFIDENCE: 70%, TAKE_PROFIT: 2%, STOP_LOSS: -3%, EXPLANATION: Given the current market data and account health status which is marked as 'HEALTHY', there are no significant price movements or strong signals to suggest an immediate long position. The historical context shows a lack ...
2025-07-15 06:23:21,455 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 933, Completion: 290, Total: 1223
2025-07-15 06:23:21,455 - core.llm_response_parsers - INFO - 🔄 No JSON found, trying structured text parsing
2025-07-15 06:23:21,455 - core.llm_response_parsers - INFO - Parsed structured text: {'DECISION': 'SHORT', 'CONFIDENCE': 70.0, 'TAKE_PROFIT': 2.0, 'STOP_LOSS': -3.0, 'EXPLANATION': "GIVEN THE CURRENT MARKET DATA AND ACCOUNT HEALTH STATUS WHICH IS MARKED AS 'HEALTHY', THERE ARE NO SIGNIFICANT PRICE MOVEMENTS OR STRONG SIGNALS TO SUGGEST AN IMMEDIATE LONG POSITION. THE HISTORICAL CONTEXT SHOWS A LACK OF RECENT VOLATILITY, WITH LAST 5 PRICES REMAINING AT $0.00 FOR DOGE/USDT, INDICATING STABILITY IN THE CURRENT MARKET CONDITIONS. HOWEVER, CONSIDERING THAT WE HAVE ROOM WITHIN OUR RISK BUDGET AND TOTAL EXPOSURE LIMITS DUE TO ACCOUNT HEALTH ADJUSTMENTS, IT IS PRUDENT TO INITIATE A SHORT POSITION ON BTC/USDT WITH HIGH SETUP QUALITY AS INDICATED BY ITS MEDIUM-LEVEL MOTHER (MOM) CHANGE OF +0.0% WHICH SUGGESTS POTENTIAL FOR PRICE DECLINE WITHOUT SIGNIFICANT IMMEDIATE MOVEMENT BUT STILL WITHIN THE CONSERVATIVE RANGE PREFERRED DUE TO ACCOUNT HEALTH CONSIDERATIONS AND CURRENT MARKET VOLATILITY BEING NORMAL, NOT EXCESSIVE OR LOW. THE TAKE PROFIT AT 2% ENSURES WE CAPITALIZE ON ANY MINOR DOWNWARD TREND WHILE A STOP LOSS OF -3% PROVIDES ADEQUATE PROTECTION AGAINST SUDDEN ADVERSE MOVEMENTS IN LINE WITH THE CONSERVATIVE RISK/REWARD RATIO REQUIRED FOR OUR ACCOUNT'S HEALTH-ADJUSTED CAPACITY, THUS MAINTAINING BALANCE BETWEEN POTENTIAL PROFIT AND SAFETY.", 'ACTION': 'ENTER_NOW'}
2025-07-15 06:23:21,456 - core.llm_response_parsers - INFO - ✅ Structured text parsing successful: ['DECISION', 'CONFIDENCE', 'TAKE_PROFIT', 'STOP_LOSS', 'EXPLANATION', 'ACTION']
2025-07-15 06:23:21,456 - core.llm_response_parsers - INFO - Opportunity scanner parsed: MOMENTUM (REVERSAL)
2025-07-15 06:23:21,457 - core.llm_orchestrator - INFO - ✅ Completed prompt: opportunity_scanner
2025-07-15 06:23:21,458 - core.llm_orchestrator - INFO - 🚀 PARALLEL LLM cycle completed in 12.96s - 4 prompts executed concurrently
2025-07-15 06:23:38,466 - core.llm_orchestrator - INFO - 🔍 FULL MODE: Executing all 8 prompts in PARALLEL for comprehensive analysis
2025-07-15 06:23:38,468 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-15 06:23:38,469 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-15 06:23:38,469 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-15 06:23:38,469 - core.llm_orchestrator - INFO - 🚀 Submitted 2 prompts for parallel execution
2025-07-15 06:23:38,470 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-15 06:23:38,470 - llama.lmstudio_runner - INFO - 💬 User Prompt: ...
2025-07-15 06:23:38,470 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🎯 ENTRY TIMING SPECIALIST - DOGE/USDT:USDT

🎯 SIGNAL ANALYSIS:
ML Ensemble: NEUTRAL (50.0%)
Technical: NEUTRAL | Momentum: 0.00
Volume: PENDING | Orderflow: NEUTRAL

📊 MARKET MICROSTRUCTURE:
Bid/Ask: $0.170000/$0.170000
Spread: 0.100% | L2 Imbalance: 0.0%
Recent Flow: BALANCED | Volume Spike: False

📈 KEY LEVELS:
Support: $0.170000
Resistance: $0.170000
Distance to Support: 0.00%

⏰ TIMING FACTORS:
- Price near key levels: NEUTRAL
- Volume confirmation: PENDING
- Spread conditions: FAVORABLE
- M...
2025-07-15 06:23:38,471 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-15 06:23:38,471 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-15 06:23:42,485 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 321 chars
2025-07-15 06:23:42,485 - llama.lmstudio_runner - INFO - 📄 Response Preview: {
  "action": "ENTER_NOW",
  "entry_type": "LIMIT",
  "confidence": 85,
  "wait_for": null,
  "max_wait_seconds": -1,
  "reasoning": "Market conditions are favorable with a narrow spread and no significant imbalance. The price is near key support levels which historically have been reliable entry po...
2025-07-15 06:23:42,486 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 610, Completion: 100, Total: 710
2025-07-15 06:23:42,486 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['action', 'entry_type', 'confidence', 'wait_for', 'max_wait_seconds', 'reasoning']
2025-07-15 06:23:42,486 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['ACTION', 'ENTRY_TYPE', 'CONFIDENCE', 'WAIT_FOR', 'MAX_WAIT_SECONDS', 'REASONING']
2025-07-15 06:23:42,487 - core.llm_response_parsers - INFO - Entry timing parsed: ENTER_NOW (LIMIT order)
2025-07-15 06:23:42,487 - core.llm_action_executors - INFO - Executing entry action: ENTER_NOW (LIMIT order)
2025-07-15 06:23:42,487 - core.llm_action_executors - WARNING - No position capacity available - skipping entry
2025-07-15 06:23:42,487 - core.llm_orchestrator - INFO - ✅ Completed prompt: entry_timing
2025-07-15 06:23:43,770 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 209 chars
2025-07-15 06:23:43,770 - llama.lmstudio_runner - INFO - 📄 Response Preview: ```json
{
  "action": "LONG",
  "confidence": 90,
  "entry_reason": "Breakout above resistance level with volume spike.",
  "take_profit": 2.5,
  "stop_loss": 1.0,
  "hold_time": "3min",
  "leverage": 40
}
```...
2025-07-15 06:23:43,770 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 174, Completion: 91, Total: 265
2025-07-15 06:23:43,771 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['action', 'confidence', 'entry_reason', 'take_profit', 'stop_loss', 'hold_time', 'leverage']
2025-07-15 06:23:43,771 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['ACTION', 'CONFIDENCE', 'ENTRY_REASON', 'TAKE_PROFIT', 'STOP_LOSS', 'HOLD_TIME', 'LEVERAGE']
2025-07-15 06:23:43,771 - core.llm_response_parsers - INFO - Risk assessment parsed: REJECTED (Risk: 50)
2025-07-15 06:23:43,771 - core.llm_orchestrator - INFO - ✅ Completed prompt: risk_assessment
2025-07-15 06:23:43,772 - core.llm_orchestrator - INFO - 🚀 PARALLEL LLM cycle completed in 5.31s - 2 prompts executed concurrently
2025-07-15 06:24:08,866 - core.llm_orchestrator - INFO - 🔍 FULL MODE: Executing all 8 prompts in PARALLEL for comprehensive analysis
2025-07-15 06:24:08,866 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-15 06:24:08,867 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-15 06:24:08,867 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-15 06:24:08,867 - llama.lmstudio_runner - INFO - 💬 User Prompt: 📊 MARKET REGIME ANALYST - DOGE/USDT:USDT

🔍 MULTI-TIMEFRAME ANALYSIS:
1m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
5m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
15m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%

📈 VOLATILITY METRICS:
ATR: 0.001000 | Spread: 0.100%
Volume Ratio: 1.0x | Price Range: 1.00%

🎯 REGIME CLASSIFICATION:
- TRENDING_BULL: Strong upward momentum, high volume, clear direction
- TRENDING_BEAR: Strong downward momentum, high volume, clear direction
- RANGING_TIGHT: Low volatility, narrow pric...
2025-07-15 06:24:08,867 - core.llm_orchestrator - INFO - 🚀 Submitted 3 prompts for parallel execution
2025-07-15 06:24:08,867 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-15 06:24:08,868 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-15 06:24:08,868 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-15 06:24:08,869 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🎯 ENTRY TIMING SPECIALIST - DOGE/USDT:USDT

🎯 SIGNAL ANALYSIS:
ML Ensemble: NEUTRAL (50.0%)
Technical: NEUTRAL | Momentum: 0.00
Volume: PENDING | Orderflow: NEUTRAL

📊 MARKET MICROSTRUCTURE:
Bid/Ask: $0.170000/$0.170000
Spread: 0.100% | L2 Imbalance: 0.0%
Recent Flow: BALANCED | Volume Spike: False

📈 KEY LEVELS:
Support: $0.170000
Resistance: $0.170000
Distance to Support: 0.00%

⏰ TIMING FACTORS:
- Price near key levels: NEUTRAL
- Volume confirmation: PENDING
- Spread conditions: FAVORABLE
- M...
2025-07-15 06:24:08,869 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a conservative futures trading analyst. Analyze market data carefully and provide measured recommendations. Use format: DECISION: [LONG/SHORT/WAIT], CONFIDENCE: [50-100]%, TAKE_PROFIT: [%], ST...
2025-07-15 06:24:08,870 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-15 06:24:08,870 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🔍 INTELLIGENT OPPORTUNITY SCANNER - Account Health Aware

🚨 ACCOUNT HEALTH STATUS: HEALTHY
💰 Balance: $1000.00 | Health: Healthy - Normal Trading
📊 Current Exposure: $0.00 (0.0% of account)
🎯 Trading Allowed: YES

📊 SYMBOL ANALYSIS:
• DOGE/USDT:USDT: $0.000000 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• BTC/USDT:USDT: $95000.000000 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• ETH/USDT:USDT: $3500.000000 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• SOL/USDT:USDT: $200.000000 | Mom: +0.0% | Vol: 1.0x |...
2025-07-15 06:24:08,871 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-15 06:24:12,923 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 311 chars
2025-07-15 06:24:12,924 - llama.lmstudio_runner - INFO - 📄 Response Preview: ```json
{
  "action": "ENTER_NOW",
  "entry_type": "LIMIT",
  "confidence": 85,
  "wait_for": null,
  "max_wait_seconds": 0,
  "reasoning": "Market conditions are favorable with a narrow spread and no significant volume spike. The price is near key support levels indicating potential for an upward m...
2025-07-15 06:24:12,924 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 610, Completion: 101, Total: 711
2025-07-15 06:24:12,924 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['action', 'entry_type', 'confidence', 'wait_for', 'max_wait_seconds', 'reasoning']
2025-07-15 06:24:12,925 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['ACTION', 'ENTRY_TYPE', 'CONFIDENCE', 'WAIT_FOR', 'MAX_WAIT_SECONDS', 'REASONING']
2025-07-15 06:24:12,925 - core.llm_response_parsers - INFO - Entry timing parsed: ENTER_NOW (LIMIT order)
2025-07-15 06:24:12,925 - core.llm_action_executors - INFO - Executing entry action: ENTER_NOW (LIMIT order)
2025-07-15 06:24:12,925 - core.llm_action_executors - WARNING - No position capacity available - skipping entry
2025-07-15 06:24:12,926 - core.llm_orchestrator - INFO - ✅ Completed prompt: entry_timing
2025-07-15 06:24:14,440 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 155 chars
2025-07-15 06:24:14,441 - llama.lmstudio_runner - INFO - 📄 Response Preview: ```json
{
  "REGIME": "RANGING_TIGHT",
  "CONFIDENCE": 75,
  "SCALP_SUITABILITY": "MEDIUM",
  "RECOMMENDED_TIMEFRAME": "1m",
  "RISK_LEVEL": "MEDIUM"
}
```...
2025-07-15 06:24:14,441 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 705, Completion: 88, Total: 793
2025-07-15 06:24:14,442 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['REGIME', 'CONFIDENCE', 'SCALP_SUITABILITY', 'RECOMMENDED_TIMEFRAME', 'RISK_LEVEL']
2025-07-15 06:24:14,442 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['REGIME', 'CONFIDENCE', 'SCALP_SUITABILITY', 'RECOMMENDED_TIMEFRAME', 'RISK_LEVEL']
2025-07-15 06:24:14,442 - core.llm_response_parsers - INFO - Market regime parsed: RANGING_TIGHT (MEDIUM scalp suitability)
2025-07-15 06:24:14,443 - core.llm_orchestrator - INFO - ✅ Completed prompt: market_regime
2025-07-15 06:24:17,328 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 678 chars
2025-07-15 06:24:17,328 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: SHORT, CONFIDENCE: 75%, TAKE_PROFIT: 2%, STOP_LOSS: 4%, EXPLANATION: Given the neutral sector momentum and normal overall volatility with a healthy account status, there's no immediate indication of an uptrend in DOGE/USDT. The historical context shows stagnant prices without clear signals...
2025-07-15 06:24:17,329 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 933, Completion: 167, Total: 1100
2025-07-15 06:24:17,329 - core.llm_response_parsers - INFO - 🔄 No JSON found, trying structured text parsing
2025-07-15 06:24:17,329 - core.llm_response_parsers - INFO - Parsed structured text: {'DECISION': 'SHORT', 'CONFIDENCE': 75.0, 'TAKE_PROFIT': 2.0, 'STOP_LOSS': 4.0, 'EXPLANATION': "GIVEN THE NEUTRAL SECTOR MOMENTUM AND NORMAL OVERALL VOLATILITY WITH A HEALTHY ACCOUNT STATUS, THERE'S NO IMMEDIATE INDICATION OF AN UPTREND IN DOGE/USDT. THE HISTORICAL CONTEXT SHOWS STAGNANT PRICES WITHOUT CLEAR SIGNALS FOR ENTRY OR EXIT POINTS OVER THE PAST FIVE TRADES. A CONSERVATIVE SHORT POSITION IS RECOMMENDED TO CAPITALIZE ON POTENTIAL DOWNWARD PRICE MOVEMENTS WHILE ADHERING TO ACCOUNT PRESERVATION CRITERIA AND MAINTAINING A RISK-REWARD RATIO THAT ALIGNS WITH OUR HEALTHY TRADING ENVIRONMENT, WHICH ALLOWS MORE AGGRESSIVE POSITIONS THAN TYPICALLY REQUIRED FOR AN UNHEALTHY BALANCE SHEET.", 'ACTION': 'ENTER_NOW'}
2025-07-15 06:24:17,330 - core.llm_response_parsers - INFO - ✅ Structured text parsing successful: ['DECISION', 'CONFIDENCE', 'TAKE_PROFIT', 'STOP_LOSS', 'EXPLANATION', 'ACTION']
2025-07-15 06:24:17,330 - core.llm_response_parsers - INFO - Opportunity scanner parsed: MOMENTUM (REVERSAL)
2025-07-15 06:24:17,330 - core.llm_orchestrator - INFO - ✅ Completed prompt: opportunity_scanner
2025-07-15 06:24:17,331 - core.llm_orchestrator - INFO - 🚀 PARALLEL LLM cycle completed in 8.47s - 3 prompts executed concurrently
2025-07-15 06:24:38,489 - core.llm_orchestrator - INFO - 🔍 FULL MODE: Executing all 8 prompts in PARALLEL for comprehensive analysis
2025-07-15 06:24:38,490 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-15 06:24:38,490 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-15 06:24:38,490 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-15 06:24:38,490 - core.llm_orchestrator - INFO - 🚀 Submitted 2 prompts for parallel execution
2025-07-15 06:24:38,490 - llama.lmstudio_runner - INFO - 💬 User Prompt: ...
2025-07-15 06:24:38,491 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-15 06:24:38,491 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-15 06:24:38,491 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🎯 ENTRY TIMING SPECIALIST - DOGE/USDT:USDT

🎯 SIGNAL ANALYSIS:
ML Ensemble: NEUTRAL (50.0%)
Technical: NEUTRAL | Momentum: 0.00
Volume: PENDING | Orderflow: NEUTRAL

📊 MARKET MICROSTRUCTURE:
Bid/Ask: $0.170000/$0.170000
Spread: 0.100% | L2 Imbalance: 0.0%
Recent Flow: BALANCED | Volume Spike: False

📈 KEY LEVELS:
Support: $0.170000
Resistance: $0.170000
Distance to Support: 0.00%

⏰ TIMING FACTORS:
- Price near key levels: NEUTRAL
- Volume confirmation: PENDING
- Spread conditions: FAVORABLE
- M...
2025-07-15 06:24:38,492 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-15 06:24:42,283 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 234 chars
2025-07-15 06:24:42,283 - llama.lmstudio_runner - INFO - 📄 Response Preview: ```json
{
  "action": "LONG",
  "confidence": 90,
  "entry_reason": "Market broke through resistance level with a rapid move of more than 2 pip.",
  "take_profit": 1.5,
  "stop_loss": 0.3,
  "hold_time": "3min",
  "leverage": 40
}
```...
2025-07-15 06:24:42,283 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 174, Completion: 97, Total: 271
2025-07-15 06:24:42,284 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['action', 'confidence', 'entry_reason', 'take_profit', 'stop_loss', 'hold_time', 'leverage']
2025-07-15 06:24:42,284 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['ACTION', 'CONFIDENCE', 'ENTRY_REASON', 'TAKE_PROFIT', 'STOP_LOSS', 'HOLD_TIME', 'LEVERAGE']
2025-07-15 06:24:42,284 - core.llm_response_parsers - INFO - Risk assessment parsed: REJECTED (Risk: 50)
2025-07-15 06:24:42,284 - core.llm_orchestrator - INFO - ✅ Completed prompt: risk_assessment
2025-07-15 06:24:44,136 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 358 chars
2025-07-15 06:24:44,136 - llama.lmstudio_runner - INFO - 📄 Response Preview: {
  "action": "ENTER_NOW",
  "entry_type": "LIMIT",
  "confidence": 90,
  "wait_for": null,
  "max_wait_seconds": 30,
  "reasoning": "Market conditions are favorable with a narrow bid/ask spread and no significant volume spikes. Momentum is neutral but the risk to reward ratio of 2:1 justifies an im...
2025-07-15 06:24:44,136 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 610, Completion: 114, Total: 724
2025-07-15 06:24:44,137 - core.llm_response_parsers - WARNING - 🚨 JSON decode error: Expecting ',' delimiter: line 1 column 271 (char 270)
2025-07-15 06:24:44,137 - core.llm_response_parsers - WARNING - 🔍 Problematic JSON: { "action": "ENTER_NOW", "entry_type": "LIMIT", "confidence": 90, "wait_for": null, "max_wait_seconds": 30, "reasoning": "Market conditions are favorable with a narrow bid/ask spread and no significan...
2025-07-15 06:24:44,137 - core.llm_response_parsers - INFO - ✅ Aggressive cleanup extracted: ['action', 'confidence', 'reasoning']
2025-07-15 06:24:44,137 - core.llm_response_parsers - INFO - ✅ JSON fixed with aggressive cleanup
2025-07-15 06:24:44,137 - core.llm_response_parsers - INFO - Entry timing parsed: WAIT (LIMIT order)
2025-07-15 06:24:44,137 - core.llm_orchestrator - INFO - ✅ Completed prompt: entry_timing
2025-07-15 06:24:44,138 - core.llm_orchestrator - INFO - 🚀 PARALLEL LLM cycle completed in 5.65s - 2 prompts executed concurrently
2025-07-15 06:25:09,685 - core.llm_orchestrator - INFO - 🔍 FULL MODE: Executing all 8 prompts in PARALLEL for comprehensive analysis
2025-07-15 06:25:09,686 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-15 06:25:09,686 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-15 06:25:09,686 - llama.lmstudio_runner - INFO - 💬 User Prompt: 📊 MARKET REGIME ANALYST - DOGE/USDT:USDT

🔍 MULTI-TIMEFRAME ANALYSIS:
1m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
5m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
15m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%

📈 VOLATILITY METRICS:
ATR: 0.001000 | Spread: 0.100%
Volume Ratio: 1.0x | Price Range: 1.00%

🎯 REGIME CLASSIFICATION:
- TRENDING_BULL: Strong upward momentum, high volume, clear direction
- TRENDING_BEAR: Strong downward momentum, high volume, clear direction
- RANGING_TIGHT: Low volatility, narrow pric...
2025-07-15 06:25:09,686 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-15 06:25:09,687 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-15 06:25:09,687 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-15 06:25:09,688 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🎯 ENTRY TIMING SPECIALIST - DOGE/USDT:USDT

🎯 SIGNAL ANALYSIS:
ML Ensemble: NEUTRAL (50.0%)
Technical: NEUTRAL | Momentum: 0.00
Volume: PENDING | Orderflow: NEUTRAL

📊 MARKET MICROSTRUCTURE:
Bid/Ask: $0.170000/$0.170000
Spread: 0.100% | L2 Imbalance: 0.0%
Recent Flow: BALANCED | Volume Spike: False

📈 KEY LEVELS:
Support: $0.170000
Resistance: $0.170000
Distance to Support: 0.00%

⏰ TIMING FACTORS:
- Price near key levels: NEUTRAL
- Volume confirmation: PENDING
- Spread conditions: FAVORABLE
- M...
2025-07-15 06:25:09,687 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-15 06:25:09,689 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-15 06:25:09,689 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a conservative futures trading analyst. Analyze market data carefully and provide measured recommendations. Use format: DECISION: [LONG/SHORT/WAIT], CONFIDENCE: [50-100]%, TAKE_PROFIT: [%], ST...
2025-07-15 06:25:09,689 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🔍 INTELLIGENT OPPORTUNITY SCANNER - Account Health Aware

🚨 ACCOUNT HEALTH STATUS: HEALTHY
💰 Balance: $1000.00 | Health: Healthy - Normal Trading
📊 Current Exposure: $0.00 (0.0% of account)
🎯 Trading Allowed: YES

📊 SYMBOL ANALYSIS:
• DOGE/USDT:USDT: $0.000000 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• BTC/USDT:USDT: $95000.000000 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• ETH/USDT:USDT: $3500.000000 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• SOL/USDT:USDT: $200.000000 | Mom: +0.0% | Vol: 1.0x |...
2025-07-15 06:25:09,690 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-15 06:25:09,694 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-15 06:25:09,694 - core.llm_orchestrator - INFO - 🚀 Submitted 4 prompts for parallel execution
2025-07-15 06:25:09,695 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-15 06:25:09,695 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🔄 STRATEGY ADAPTATION SPECIALIST

📊 PERFORMANCE ANALYSIS (Last 24h):
Trades: 0 | Win Rate: 50.0%
Avg Profit: 0.80% | Avg Loss: -0.30%
Sharpe Ratio: 1.00 | Max Drawdown: 0.0%
Total PnL: $0.00 | ROI: 0.0%

🎯 CURRENT STRATEGY:
Risk per Trade: 2.0% | Avg Hold Time: 8.0min
Entry Threshold: 70% | Exit Threshold: 60%
Position Size Method: FIXED_RISK | Max Positions: 3

📈 MARKET REGIME: UNKNOWN
Regime Confidence: 50.0%
Scalp Suitability: MEDIUM

🔧 ADAPTATION FACTORS:
- Win rate trending: DOWN
- Drawdown...
2025-07-15 06:25:09,695 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-15 06:25:15,150 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 717 chars
2025-07-15 06:25:15,150 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: LONG, CONFIDENCE: 85%, TAKE_PROFIT: 12%, STOP_LOSS: -7%, EXPLANATION: The account is in a healthy state with normal trading parameters and the historical context shows no recent price movement or signals. Given that all four symbols have similar setup quality, I recommend going long on DOG...
2025-07-15 06:25:15,151 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 933, Completion: 181, Total: 1114
2025-07-15 06:25:15,151 - core.llm_response_parsers - INFO - 🔄 No JSON found, trying structured text parsing
2025-07-15 06:25:15,152 - core.llm_response_parsers - INFO - Parsed structured text: {'DECISION': 'LONG', 'CONFIDENCE': 85.0, 'TAKE_PROFIT': 12.0, 'STOP_LOSS': -7.0, 'EXPLANATION': "THE ACCOUNT IS IN A HEALTHY STATE WITH NORMAL TRADING PARAMETERS AND THE HISTORICAL CONTEXT SHOWS NO RECENT PRICE MOVEMENT OR SIGNALS. GIVEN THAT ALL FOUR SYMBOLS HAVE SIMILAR SETUP QUALITY, I RECOMMEND GOING LONG ON DOGE/USDT DUE TO ITS LOWER CURRENT EXPOSURE COMPARED TO BTC WHICH HAS HIGHER VOLATILITY BUT ALSO OFFERS POTENTIALLY GREATER REWARDS IN A CONSERVATIVE STRATEGY ALIGNED WITH ACCOUNT HEALTH AND RISK CRITERIA. THE TAKE-PROFIT IS SET AT 12% ABOVE THE ENTRY PRICE CONSIDERING NORMAL MARKET FLUCTUATIONS, WHILE STOP LOSS WILL BE TRIGGERED IF LOSSES EXCEED -7%, ENSURING WE DON'T BREACH OUR MAXIMUM TOTAL EXPOSURE LIMIT OF $70%.", 'ACTION': 'ENTER_NOW'}
2025-07-15 06:25:15,153 - core.llm_response_parsers - INFO - ✅ Structured text parsing successful: ['DECISION', 'CONFIDENCE', 'TAKE_PROFIT', 'STOP_LOSS', 'EXPLANATION', 'ACTION']
2025-07-15 06:25:15,153 - core.llm_response_parsers - INFO - Opportunity scanner parsed: BREAKOUT (MOMENTUM)
2025-07-15 06:25:15,154 - core.llm_orchestrator - INFO - ✅ Completed prompt: opportunity_scanner
2025-07-15 06:25:17,267 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 425 chars
2025-07-15 06:25:17,268 - llama.lmstudio_runner - INFO - 📄 Response Preview: {
  "action": "ENTER",
  "entry_type": "LIMIT",
  "confidence": 85,
  "wait_for": null,
  "max_wait_seconds": 0,
  "reasoning": "Market conditions are favorable with a tight spread and balanced recent flow. Despite the neutral signals from ML Ensemble and technical indicators, historical context sho...
2025-07-15 06:25:17,268 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 610, Completion: 117, Total: 727
2025-07-15 06:25:17,269 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['action', 'entry_type', 'confidence', 'wait_for', 'max_wait_seconds', 'reasoning']
2025-07-15 06:25:17,269 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['ACTION', 'ENTRY_TYPE', 'CONFIDENCE', 'WAIT_FOR', 'MAX_WAIT_SECONDS', 'REASONING']
2025-07-15 06:25:17,270 - core.llm_response_parsers - INFO - Entry timing parsed: WAIT (LIMIT order)
2025-07-15 06:25:17,270 - core.llm_orchestrator - INFO - ✅ Completed prompt: entry_timing
2025-07-15 06:25:22,579 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 1437 chars
2025-07-15 06:25:22,580 - llama.lmstudio_runner - INFO - 📄 Response Preview: {
  "risk_adjustment": [0.5, 2.0],
  "hold_time_target": "8", // in minutes as specified by the original instruction to maintain current hold time target of 8min
  "entry_threshold": 70, // remains at a high confidence level for entry threshold
  "exit_threshold": 60, // slightly lower exit threshol...
2025-07-15 06:25:22,580 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 619, Completion: 336, Total: 955
2025-07-15 06:25:22,580 - core.llm_response_parsers - WARNING - 🚨 JSON decode error: Expecting ',' delimiter: line 1 column 287 (char 286)
2025-07-15 06:25:22,581 - core.llm_response_parsers - WARNING - 🔍 Problematic JSON: { "risk_adjustment": [0.5, 2.0], "hold_time_target": "8", "entry_threshold": 70, "exit_threshold": 60, "sizing_method": "FIXED_RISK", "reasoning": "Maintain current strategy - although there has been ...
2025-07-15 06:25:22,581 - core.llm_response_parsers - INFO - ✅ Aggressive cleanup extracted: ['reasoning']
2025-07-15 06:25:22,581 - core.llm_response_parsers - INFO - ✅ JSON fixed with aggressive cleanup
2025-07-15 06:25:22,581 - core.llm_response_parsers - INFO - Strategy adaptation parsed: Risk adj 1.0x
2025-07-15 06:25:22,581 - core.llm_action_executors - INFO - Applying strategy adaptations: Risk 1.0x, Hold time 8min
2025-07-15 06:25:22,582 - core.llm_orchestrator - INFO - ✅ Completed prompt: strategy_adaptation
2025-07-15 06:25:25,073 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 427 chars
2025-07-15 06:25:25,074 - llama.lmstudio_runner - INFO - 📄 Response Preview: ```json
{
  "REGIME": "RANGING_TIGHT",
  "CONFIDENCE": 75,
  "SCALP_SUITABILITY": "MEDIUM",
  "RECOMMENDED_TIMEFRAME": "1m",
  "RISK_LEVEL": "MEDIUM",
  "REASONING": "The market is currently in a consolidation phase with moderate volatility, which presents some challenges for scalping but still offe...
2025-07-15 06:25:25,074 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 705, Completion: 149, Total: 854
2025-07-15 06:25:25,074 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['REGIME', 'CONFIDENCE', 'SCALP_SUITABILITY', 'RECOMMENDED_TIMEFRAME', 'RISK_LEVEL', 'REASONING']
2025-07-15 06:25:25,075 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['REGIME', 'CONFIDENCE', 'SCALP_SUITABILITY', 'RECOMMENDED_TIMEFRAME', 'RISK_LEVEL', 'REASONING']
2025-07-15 06:25:25,075 - core.llm_response_parsers - INFO - Market regime parsed: RANGING_TIGHT (MEDIUM scalp suitability)
2025-07-15 06:25:25,075 - core.llm_orchestrator - INFO - ✅ Completed prompt: market_regime
2025-07-15 06:25:25,076 - core.llm_orchestrator - INFO - 🚀 PARALLEL LLM cycle completed in 15.39s - 4 prompts executed concurrently
2025-07-15 06:25:38,475 - core.llm_orchestrator - INFO - 🔍 FULL MODE: Executing all 8 prompts in PARALLEL for comprehensive analysis
2025-07-15 06:25:38,476 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-15 06:25:38,477 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-15 06:25:38,477 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-15 06:25:38,477 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-15 06:25:38,477 - llama.lmstudio_runner - INFO - 💬 User Prompt: ...
2025-07-15 06:25:38,477 - core.llm_orchestrator - INFO - 🚀 Submitted 2 prompts for parallel execution
2025-07-15 06:25:38,477 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🎯 ENTRY TIMING SPECIALIST - DOGE/USDT:USDT

🎯 SIGNAL ANALYSIS:
ML Ensemble: NEUTRAL (50.0%)
Technical: NEUTRAL | Momentum: 0.00
Volume: PENDING | Orderflow: NEUTRAL

📊 MARKET MICROSTRUCTURE:
Bid/Ask: $0.170000/$0.170000
Spread: 0.100% | L2 Imbalance: 0.0%
Recent Flow: BALANCED | Volume Spike: False

📈 KEY LEVELS:
Support: $0.170000
Resistance: $0.170000
Distance to Support: 0.00%

⏰ TIMING FACTORS:
- Price near key levels: NEUTRAL
- Volume confirmation: PENDING
- Spread conditions: FAVORABLE
- M...
2025-07-15 06:25:38,478 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-15 06:25:38,478 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-15 06:25:42,449 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 341 chars
2025-07-15 06:25:42,450 - llama.lmstudio_runner - INFO - 📄 Response Preview: {
  "action": "ENTER_NOW",
  "entry_type": "LIMIT",
  "confidence": 92,
  "wait_for": null,
  "max_wait_seconds": -1,
  "reasoning": "Market conditions are favorable with a low spread and balanced recent flow. The price is near key levels indicating potential for movement without immediate risk of b...
2025-07-15 06:25:42,450 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 610, Completion: 100, Total: 710
2025-07-15 06:25:42,450 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['action', 'entry_type', 'confidence', 'wait_for', 'max_wait_seconds', 'reasoning']
2025-07-15 06:25:42,451 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['ACTION', 'ENTRY_TYPE', 'CONFIDENCE', 'WAIT_FOR', 'MAX_WAIT_SECONDS', 'REASONING']
2025-07-15 06:25:42,451 - core.llm_response_parsers - INFO - Entry timing parsed: ENTER_NOW (LIMIT order)
2025-07-15 06:25:42,451 - core.llm_action_executors - INFO - Executing entry action: ENTER_NOW (LIMIT order)
2025-07-15 06:25:42,451 - core.llm_action_executors - WARNING - No position capacity available - skipping entry
2025-07-15 06:25:42,451 - core.llm_orchestrator - INFO - ✅ Completed prompt: entry_timing
2025-07-15 06:25:43,790 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 209 chars
2025-07-15 06:25:43,790 - llama.lmstudio_runner - INFO - 📄 Response Preview: ```json
{
  "action": "LONG",
  "confidence": 90,
  "entry_reason": "Breakout above resistance level with volume spike.",
  "take_profit": 2.5,
  "stop_loss": 1.0,
  "hold_time": "3min",
  "leverage": 40
}
```...
2025-07-15 06:25:43,791 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 174, Completion: 91, Total: 265
2025-07-15 06:25:43,791 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['action', 'confidence', 'entry_reason', 'take_profit', 'stop_loss', 'hold_time', 'leverage']
2025-07-15 06:25:43,791 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['ACTION', 'CONFIDENCE', 'ENTRY_REASON', 'TAKE_PROFIT', 'STOP_LOSS', 'HOLD_TIME', 'LEVERAGE']
2025-07-15 06:25:43,791 - core.llm_response_parsers - INFO - Risk assessment parsed: REJECTED (Risk: 50)
2025-07-15 06:25:43,792 - core.llm_orchestrator - INFO - ✅ Completed prompt: risk_assessment
2025-07-15 06:25:43,792 - core.llm_orchestrator - INFO - 🚀 PARALLEL LLM cycle completed in 5.32s - 2 prompts executed concurrently
2025-07-15 06:26:08,450 - core.llm_orchestrator - INFO - 🔍 FULL MODE: Executing all 8 prompts in PARALLEL for comprehensive analysis
2025-07-15 06:26:08,451 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-15 06:26:08,452 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-15 06:26:08,452 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-15 06:26:08,452 - core.llm_orchestrator - INFO - 🚀 Submitted 2 prompts for parallel execution
2025-07-15 06:26:08,452 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-15 06:26:08,452 - llama.lmstudio_runner - INFO - 💬 User Prompt: 📊 MARKET REGIME ANALYST - DOGE/USDT:USDT

🔍 MULTI-TIMEFRAME ANALYSIS:
1m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
5m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
15m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%

📈 VOLATILITY METRICS:
ATR: 0.001000 | Spread: 0.100%
Volume Ratio: 1.0x | Price Range: 1.00%

🎯 REGIME CLASSIFICATION:
- TRENDING_BULL: Strong upward momentum, high volume, clear direction
- TRENDING_BEAR: Strong downward momentum, high volume, clear direction
- RANGING_TIGHT: Low volatility, narrow pric...
2025-07-15 06:26:08,453 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🎯 ENTRY TIMING SPECIALIST - DOGE/USDT:USDT

🎯 SIGNAL ANALYSIS:
ML Ensemble: NEUTRAL (50.0%)
Technical: NEUTRAL | Momentum: 0.00
Volume: PENDING | Orderflow: NEUTRAL

📊 MARKET MICROSTRUCTURE:
Bid/Ask: $0.170000/$0.170000
Spread: 0.100% | L2 Imbalance: 0.0%
Recent Flow: BALANCED | Volume Spike: False

📈 KEY LEVELS:
Support: $0.170000
Resistance: $0.170000
Distance to Support: 0.00%

⏰ TIMING FACTORS:
- Price near key levels: NEUTRAL
- Volume confirmation: PENDING
- Spread conditions: FAVORABLE
- M...
2025-07-15 06:26:08,453 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-15 06:26:08,454 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-15 06:26:12,454 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 295 chars
2025-07-15 06:26:12,454 - llama.lmstudio_runner - INFO - 📄 Response Preview: {
  "action": "ENTER",
  "entry_type": "LIMIT",
  "confidence": 90,
  "wait_for": null,
  "max_wait_seconds": 30,
  "reasoning": "Market conditions are favorable with a narrow spread and no significant volume spike. Momentum is neutral but the risk/reward ratio of 2:1 justifies entering now."
}...
2025-07-15 06:26:12,455 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 610, Completion: 101, Total: 711
2025-07-15 06:26:12,455 - core.llm_response_parsers - WARNING - 🚨 JSON decode error: Expecting ',' delimiter: line 1 column 255 (char 254)
2025-07-15 06:26:12,456 - core.llm_response_parsers - WARNING - 🔍 Problematic JSON: { "action": "ENTER", "entry_type": "LIMIT", "confidence": 90, "wait_for": null, "max_wait_seconds": 30, "reasoning": "Market conditions are favorable with a narrow spread and no significant volume spi...
2025-07-15 06:26:12,456 - core.llm_response_parsers - INFO - ✅ Aggressive cleanup extracted: ['action', 'confidence', 'reasoning']
2025-07-15 06:26:12,456 - core.llm_response_parsers - INFO - ✅ JSON fixed with aggressive cleanup
2025-07-15 06:26:12,456 - core.llm_response_parsers - INFO - Entry timing parsed: WAIT (LIMIT order)
2025-07-15 06:26:12,457 - core.llm_orchestrator - INFO - ✅ Completed prompt: entry_timing
2025-07-15 06:26:14,235 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 218 chars
2025-07-15 06:26:14,235 - llama.lmstudio_runner - INFO - 📄 Response Preview: ```json
{
  "REGIME": "RANGING_TIGHT",
  "CONFIDENCE": 75,
  "SCALP_SUITABILITY": "MEDIUM",
  "RECOMMENDED_TIMEFRAME": "1m",
  "RISK_LEVEL": "MEDIUM",
  "REASONING": "Consolidation phase with moderate volatility"
}
```...
2025-07-15 06:26:14,235 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 705, Completion: 108, Total: 813
2025-07-15 06:26:14,236 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['REGIME', 'CONFIDENCE', 'SCALP_SUITABILITY', 'RECOMMENDED_TIMEFRAME', 'RISK_LEVEL', 'REASONING']
2025-07-15 06:26:14,236 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['REGIME', 'CONFIDENCE', 'SCALP_SUITABILITY', 'RECOMMENDED_TIMEFRAME', 'RISK_LEVEL', 'REASONING']
2025-07-15 06:26:14,236 - core.llm_response_parsers - INFO - Market regime parsed: RANGING_TIGHT (MEDIUM scalp suitability)
2025-07-15 06:26:14,236 - core.llm_orchestrator - INFO - ✅ Completed prompt: market_regime
2025-07-15 06:26:14,237 - core.llm_orchestrator - INFO - 🚀 PARALLEL LLM cycle completed in 5.79s - 2 prompts executed concurrently
2025-07-15 06:26:38,736 - core.llm_orchestrator - INFO - 🔍 FULL MODE: Executing all 8 prompts in PARALLEL for comprehensive analysis
2025-07-15 06:26:38,737 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-15 06:26:38,737 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-15 06:26:38,738 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-15 06:26:38,738 - llama.lmstudio_runner - INFO - 💬 User Prompt: 📊 MARKET REGIME ANALYST - DOGE/USDT:USDT

🔍 MULTI-TIMEFRAME ANALYSIS:
1m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
5m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
15m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%

📈 VOLATILITY METRICS:
ATR: 0.001000 | Spread: 0.100%
Volume Ratio: 1.0x | Price Range: 1.00%

🎯 REGIME CLASSIFICATION:
- TRENDING_BULL: Strong upward momentum, high volume, clear direction
- TRENDING_BEAR: Strong downward momentum, high volume, clear direction
- RANGING_TIGHT: Low volatility, narrow pric...
2025-07-15 06:26:38,738 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-15 06:26:38,738 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-15 06:26:38,739 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-15 06:26:38,739 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-15 06:26:38,739 - core.llm_orchestrator - INFO - 🚀 Submitted 4 prompts for parallel execution
2025-07-15 06:26:38,739 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🎯 ENTRY TIMING SPECIALIST - DOGE/USDT:USDT

🎯 SIGNAL ANALYSIS:
ML Ensemble: NEUTRAL (50.0%)
Technical: NEUTRAL | Momentum: 0.00
Volume: PENDING | Orderflow: NEUTRAL

📊 MARKET MICROSTRUCTURE:
Bid/Ask: $0.170000/$0.170000
Spread: 0.100% | L2 Imbalance: 0.0%
Recent Flow: BALANCED | Volume Spike: False

📈 KEY LEVELS:
Support: $0.170000
Resistance: $0.170000
Distance to Support: 0.00%

⏰ TIMING FACTORS:
- Price near key levels: NEUTRAL
- Volume confirmation: PENDING
- Spread conditions: FAVORABLE
- M...
2025-07-15 06:26:38,739 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-15 06:26:38,739 - llama.lmstudio_runner - INFO - 💬 User Prompt: ...
2025-07-15 06:26:38,741 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a conservative futures trading analyst. Analyze market data carefully and provide measured recommendations. Use format: DECISION: [LONG/SHORT/WAIT], CONFIDENCE: [50-100]%, TAKE_PROFIT: [%], ST...
2025-07-15 06:26:38,740 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-15 06:26:38,741 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-15 06:26:38,741 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🔍 INTELLIGENT OPPORTUNITY SCANNER - Account Health Aware

🚨 ACCOUNT HEALTH STATUS: HEALTHY
💰 Balance: $1000.00 | Health: Healthy - Normal Trading
📊 Current Exposure: $0.00 (0.0% of account)
🎯 Trading Allowed: YES

📊 SYMBOL ANALYSIS:
• DOGE/USDT:USDT: $0.000000 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• BTC/USDT:USDT: $95000.000000 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• ETH/USDT:USDT: $3500.000000 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• SOL/USDT:USDT: $200.000000 | Mom: +0.0% | Vol: 1.0x |...
2025-07-15 06:26:38,742 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-15 06:26:42,854 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 282 chars
2025-07-15 06:26:42,855 - llama.lmstudio_runner - INFO - 📄 Response Preview: ```json
{
  "REGIME": "RANGING_TIGHT",
  "CONFIDENCE": 75,
  "SCALP_SUITABILITY": "MEDIUM",
  "RECOMMENDED_TIMEFRAME": "1m",
  "RISK_LEVEL": "MEDIUM",
  "REASONING": "Consolidation phase with moderate volatility, suitable for scalping due to potential breakout opportunities."
}
```...
2025-07-15 06:26:42,855 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 705, Completion: 120, Total: 825
2025-07-15 06:26:42,856 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['REGIME', 'CONFIDENCE', 'SCALP_SUITABILITY', 'RECOMMENDED_TIMEFRAME', 'RISK_LEVEL', 'REASONING']
2025-07-15 06:26:42,857 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['REGIME', 'CONFIDENCE', 'SCALP_SUITABILITY', 'RECOMMENDED_TIMEFRAME', 'RISK_LEVEL', 'REASONING']
2025-07-15 06:26:42,857 - core.llm_response_parsers - INFO - Market regime parsed: RANGING_TIGHT (MEDIUM scalp suitability)
2025-07-15 06:26:42,858 - core.llm_orchestrator - INFO - ✅ Completed prompt: market_regime
2025-07-15 06:26:44,781 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 373 chars
2025-07-15 06:26:44,781 - llama.lmstudio_runner - INFO - 📄 Response Preview: {
  "action": "ENTER_NOW",
  "entry_type": "LIMIT",
  "confidence": 85,
  "wait_for": null,
  "max_wait_seconds": -1,
  "reasoning": "Market conditions are currently favorable with a narrow spread and balanced recent flow. Although volume confirmation is pending, the risk/reward ratio of 2:1 justifi...
2025-07-15 06:26:44,781 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 610, Completion: 115, Total: 725
2025-07-15 06:26:44,782 - core.llm_response_parsers - WARNING - 🚨 JSON decode error: Expecting ',' delimiter: line 1 column 279 (char 278)
2025-07-15 06:26:44,783 - core.llm_response_parsers - WARNING - 🔍 Problematic JSON: { "action": "ENTER_NOW", "entry_type": "LIMIT", "confidence": 85, "wait_for": null, "max_wait_seconds": -1, "reasoning": "Market conditions are currently favorable with a narrow spread and balanced re...
2025-07-15 06:26:44,783 - core.llm_response_parsers - INFO - ✅ Aggressive cleanup extracted: ['action', 'confidence', 'reasoning']
2025-07-15 06:26:44,784 - core.llm_response_parsers - INFO - ✅ JSON fixed with aggressive cleanup
2025-07-15 06:26:44,784 - core.llm_response_parsers - INFO - Entry timing parsed: WAIT (LIMIT order)
2025-07-15 06:26:44,784 - core.llm_orchestrator - INFO - ✅ Completed prompt: entry_timing
2025-07-15 06:26:46,147 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 209 chars
2025-07-15 06:26:46,147 - llama.lmstudio_runner - INFO - 📄 Response Preview: ```json
{
  "action": "LONG",
  "confidence": 90,
  "entry_reason": "Breakout above resistance level with volume surge.",
  "take_profit": 2.5,
  "stop_loss": 1.0,
  "hold_time": "3min",
  "leverage": 40
}
```...
2025-07-15 06:26:46,147 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 174, Completion: 91, Total: 265
2025-07-15 06:26:46,148 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['action', 'confidence', 'entry_reason', 'take_profit', 'stop_loss', 'hold_time', 'leverage']
2025-07-15 06:26:46,148 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['ACTION', 'CONFIDENCE', 'ENTRY_REASON', 'TAKE_PROFIT', 'STOP_LOSS', 'HOLD_TIME', 'LEVERAGE']
2025-07-15 06:26:46,148 - core.llm_response_parsers - INFO - Risk assessment parsed: REJECTED (Risk: 50)
2025-07-15 06:26:46,148 - core.llm_orchestrator - INFO - ✅ Completed prompt: risk_assessment
2025-07-15 06:26:49,283 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 771 chars
2025-07-15 06:26:49,284 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: LONG, CONFIDENCE: 85%, TAKE_PROFIT: 2%, STOP_LOSS: -3%, EXPLANATION: The market data indicates a neutral sector momentum with normal volatility and average volume profile. Given the account's healthy status and adherence to preservation criteria, there is an opportunity for conservative en...
2025-07-15 06:26:49,284 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 933, Completion: 176, Total: 1109
2025-07-15 06:26:49,284 - core.llm_response_parsers - INFO - 🔄 No JSON found, trying structured text parsing
2025-07-15 06:26:49,285 - core.llm_response_parsers - INFO - Parsed structured text: {'DECISION': 'LONG', 'CONFIDENCE': 85.0, 'TAKE_PROFIT': 2.0, 'STOP_LOSS': -3.0, 'EXPLANATION': "THE MARKET DATA INDICATES A NEUTRAL SECTOR MOMENTUM WITH NORMAL VOLATILITY AND AVERAGE VOLUME PROFILE. GIVEN THE ACCOUNT'S HEALTHY STATUS AND ADHERENCE TO PRESERVATION CRITERIA, THERE IS AN OPPORTUNITY FOR CONSERVATIVE ENTRY INTO DOGE/USDT DUE TO ITS MEDIUM SETUP QUALITY WITHOUT COMPROMISING RISK MANAGEMENT PRINCIPLES OR EXCEEDING LIQUIDITY REQUIREMENTS. THE HISTORICAL CONTEXT SHOWS NO RECENT PRICE MOVEMENT IN THIS CRYPTOCURRENCY PAIR; HOWEVER, THE LACK OF VOLATILITY SUGGESTS A STABLE ENVIRONMENT SUITABLE FOR LONG POSITIONING WITH MODERATE STOP LOSS AND TAKE PROFIT LEVELS SET BASED ON ACCOUNT'S HEALTH-ADJUSTED PARAMETERS TO ENSURE CAPITAL PRESERVATION WHILE CAPTURING POTENTIAL GAINS.", 'ACTION': 'ENTER_NOW'}
2025-07-15 06:26:49,285 - core.llm_response_parsers - INFO - ✅ Structured text parsing successful: ['DECISION', 'CONFIDENCE', 'TAKE_PROFIT', 'STOP_LOSS', 'EXPLANATION', 'ACTION']
2025-07-15 06:26:49,285 - core.llm_response_parsers - INFO - Opportunity scanner parsed: BREAKOUT (MOMENTUM)
2025-07-15 06:26:49,285 - core.llm_orchestrator - INFO - ✅ Completed prompt: opportunity_scanner
2025-07-15 06:26:49,287 - core.llm_orchestrator - INFO - 🚀 PARALLEL LLM cycle completed in 10.55s - 4 prompts executed concurrently
2025-07-15 06:27:08,504 - core.llm_orchestrator - INFO - 🔍 FULL MODE: Executing all 8 prompts in PARALLEL for comprehensive analysis
2025-07-15 06:27:08,506 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-15 06:27:08,506 - core.llm_orchestrator - INFO - 🚀 Submitted 1 prompts for parallel execution
2025-07-15 06:27:08,506 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-15 06:27:08,507 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🎯 ENTRY TIMING SPECIALIST - DOGE/USDT:USDT

🎯 SIGNAL ANALYSIS:
ML Ensemble: NEUTRAL (50.0%)
Technical: NEUTRAL | Momentum: 0.00
Volume: PENDING | Orderflow: NEUTRAL

📊 MARKET MICROSTRUCTURE:
Bid/Ask: $0.170000/$0.170000
Spread: 0.100% | L2 Imbalance: 0.0%
Recent Flow: BALANCED | Volume Spike: False

📈 KEY LEVELS:
Support: $0.170000
Resistance: $0.170000
Distance to Support: 0.00%

⏰ TIMING FACTORS:
- Price near key levels: NEUTRAL
- Volume confirmation: PENDING
- Spread conditions: FAVORABLE
- M...
2025-07-15 06:27:08,508 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-15 06:27:12,356 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 277 chars
2025-07-15 06:27:12,357 - llama.lmstudio_runner - INFO - 📄 Response Preview: {
  "action": "ENTER",
  "entry_type": "LIMIT",
  "confidence": 80,
  "wait_for": null,
  "max_wait_seconds": 30,
  "reasoning": "Market conditions are favorable with a narrow spread and volume spike not yet confirmed. Entry now would capitalize on potential momentum shift."
}...
2025-07-15 06:27:12,357 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 610, Completion: 90, Total: 700
2025-07-15 06:27:12,358 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['action', 'entry_type', 'confidence', 'wait_for', 'max_wait_seconds', 'reasoning']
2025-07-15 06:27:12,358 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['ACTION', 'ENTRY_TYPE', 'CONFIDENCE', 'WAIT_FOR', 'MAX_WAIT_SECONDS', 'REASONING']
2025-07-15 06:27:12,358 - core.llm_response_parsers - INFO - Entry timing parsed: WAIT (LIMIT order)
2025-07-15 06:27:12,358 - core.llm_orchestrator - INFO - ✅ Completed prompt: entry_timing
2025-07-15 06:27:12,359 - core.llm_orchestrator - INFO - 🚀 PARALLEL LLM cycle completed in 3.85s - 1 prompts executed concurrently
2025-07-15 06:27:38,499 - core.llm_orchestrator - INFO - 🔍 FULL MODE: Executing all 8 prompts in PARALLEL for comprehensive analysis
2025-07-15 06:27:38,500 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-15 06:27:38,500 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-15 06:27:38,500 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-15 06:27:38,501 - llama.lmstudio_runner - INFO - 💬 User Prompt: 📊 MARKET REGIME ANALYST - DOGE/USDT:USDT

🔍 MULTI-TIMEFRAME ANALYSIS:
1m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
5m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
15m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%

📈 VOLATILITY METRICS:
ATR: 0.001000 | Spread: 0.100%
Volume Ratio: 1.0x | Price Range: 1.00%

🎯 REGIME CLASSIFICATION:
- TRENDING_BULL: Strong upward momentum, high volume, clear direction
- TRENDING_BEAR: Strong downward momentum, high volume, clear direction
- RANGING_TIGHT: Low volatility, narrow pric...
2025-07-15 06:27:38,501 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-15 06:27:38,501 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-15 06:27:38,501 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-15 06:27:38,501 - core.llm_orchestrator - INFO - 🚀 Submitted 4 prompts for parallel execution
2025-07-15 06:27:38,501 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-15 06:27:38,501 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-15 06:27:38,502 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🎯 ENTRY TIMING SPECIALIST - DOGE/USDT:USDT

🎯 SIGNAL ANALYSIS:
ML Ensemble: NEUTRAL (50.0%)
Technical: NEUTRAL | Momentum: 0.00
Volume: PENDING | Orderflow: NEUTRAL

📊 MARKET MICROSTRUCTURE:
Bid/Ask: $0.170000/$0.170000
Spread: 0.100% | L2 Imbalance: 0.0%
Recent Flow: BALANCED | Volume Spike: False

📈 KEY LEVELS:
Support: $0.170000
Resistance: $0.170000
Distance to Support: 0.00%

⏰ TIMING FACTORS:
- Price near key levels: NEUTRAL
- Volume confirmation: PENDING
- Spread conditions: FAVORABLE
- M...
2025-07-15 06:27:38,502 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-15 06:27:38,502 - llama.lmstudio_runner - INFO - 💬 User Prompt: ...
2025-07-15 06:27:38,503 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-15 06:27:38,504 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🔄 STRATEGY ADAPTATION SPECIALIST

📊 PERFORMANCE ANALYSIS (Last 24h):
Trades: 0 | Win Rate: 50.0%
Avg Profit: 0.80% | Avg Loss: -0.30%
Sharpe Ratio: 1.00 | Max Drawdown: 0.0%
Total PnL: $0.00 | ROI: 0.0%

🎯 CURRENT STRATEGY:
Risk per Trade: 2.0% | Avg Hold Time: 8.0min
Entry Threshold: 70% | Exit Threshold: 60%
Position Size Method: FIXED_RISK | Max Positions: 3

📈 MARKET REGIME: UNKNOWN
Regime Confidence: 50.0%
Scalp Suitability: MEDIUM

🔧 ADAPTATION FACTORS:
- Win rate trending: DOWN
- Drawdown...
2025-07-15 06:27:38,504 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-15 06:27:38,504 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-15 06:27:42,834 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 311 chars
2025-07-15 06:27:42,835 - llama.lmstudio_runner - INFO - 📄 Response Preview: ```json
{
  "REGIME": "RANGING_TIGHT",
  "CONFIDENCE": 75,
  "SCALP_SUITABILITY": "MEDIUM",
  "RECOMMENDED_TIMEFRAME": "1m",
  "RISK_LEVEL": "MEDIUM",
  "REASONING": "Consolidation phase with moderate volatility, suitable for scalping but requires careful monitoring of the market to avoid sudden bre...
2025-07-15 06:27:42,835 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 705, Completion: 124, Total: 829
2025-07-15 06:27:42,835 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['REGIME', 'CONFIDENCE', 'SCALP_SUITABILITY', 'RECOMMENDED_TIMEFRAME', 'RISK_LEVEL', 'REASONING']
2025-07-15 06:27:42,836 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['REGIME', 'CONFIDENCE', 'SCALP_SUITABILITY', 'RECOMMENDED_TIMEFRAME', 'RISK_LEVEL', 'REASONING']
2025-07-15 06:27:42,837 - core.llm_response_parsers - INFO - Market regime parsed: RANGING_TIGHT (MEDIUM scalp suitability)
2025-07-15 06:27:42,837 - core.llm_orchestrator - INFO - ✅ Completed prompt: market_regime
2025-07-15 06:27:49,199 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 1566 chars
2025-07-15 06:27:49,200 - llama.lmstudio_runner - INFO - 📄 Response Preview: {
  "risk_adjustment": {"min": 0.5, "max": 2.0},
  "hold_time_target": "8", // in minutes
  "entry_threshold": 70, // as a percentage confidence level for market movement confirmation
  "exit_threshold": 60, // same format as entry threshold; used to determine when to exit based on the strength of t...
2025-07-15 06:27:49,200 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 619, Completion: 402, Total: 1021
2025-07-15 06:27:49,200 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['min', 'max']
2025-07-15 06:27:49,201 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['MIN', 'MAX', 'CONFIDENCE']
2025-07-15 06:27:49,201 - core.llm_response_parsers - INFO - Strategy adaptation parsed: Risk adj 1.0x
2025-07-15 06:27:49,201 - core.llm_action_executors - INFO - Applying strategy adaptations: Risk 1.0x, Hold time 8min
2025-07-15 06:27:49,201 - core.llm_orchestrator - INFO - ✅ Completed prompt: strategy_adaptation
2025-07-15 06:27:51,110 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 293 chars
2025-07-15 06:27:51,111 - llama.lmstudio_runner - INFO - 📄 Response Preview: {
  "action": "ENTER",
  "entry_type": "LIMIT",
  "confidence": 85,
  "wait_for": null,
  "max_wait_seconds": 0,
  "reasoning": "Market conditions are favorable with a narrow spread and balanced recent flow. Momentum is neutral but the risk/reward ratio of 2:1 justifies an immediate entry."
}...
2025-07-15 06:27:51,111 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 610, Completion: 100, Total: 710
2025-07-15 06:27:51,112 - core.llm_response_parsers - WARNING - 🚨 JSON decode error: Expecting ',' delimiter: line 1 column 247 (char 246)
2025-07-15 06:27:51,113 - core.llm_response_parsers - WARNING - 🔍 Problematic JSON: { "action": "ENTER", "entry_type": "LIMIT", "confidence": 85, "wait_for": null, "max_wait_seconds": 0, "reasoning": "Market conditions are favorable with a narrow spread and balanced recent flow. Mome...
2025-07-15 06:27:51,114 - core.llm_response_parsers - INFO - ✅ Aggressive cleanup extracted: ['action', 'confidence', 'reasoning']
2025-07-15 06:27:51,117 - core.llm_response_parsers - INFO - ✅ JSON fixed with aggressive cleanup
2025-07-15 06:27:51,117 - core.llm_response_parsers - INFO - Entry timing parsed: WAIT (LIMIT order)
2025-07-15 06:27:51,119 - core.llm_orchestrator - INFO - ✅ Completed prompt: entry_timing
2025-07-15 06:27:52,638 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 221 chars
2025-07-15 06:27:52,647 - llama.lmstudio_runner - INFO - 📄 Response Preview: ```json
{
  "action": "LONG",
  "confidence": 90,
  "entry_reason": "Breakout above resistance level with strong volume indicators.",
  "take_profit": 2.5,
  "stop_loss": 1.0,
  "hold_time": "3min",
  "leverage": 40
}
```...
2025-07-15 06:27:52,651 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 174, Completion: 92, Total: 266
2025-07-15 06:27:52,654 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['action', 'confidence', 'entry_reason', 'take_profit', 'stop_loss', 'hold_time', 'leverage']
2025-07-15 06:27:52,658 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['ACTION', 'CONFIDENCE', 'ENTRY_REASON', 'TAKE_PROFIT', 'STOP_LOSS', 'HOLD_TIME', 'LEVERAGE']
2025-07-15 06:27:52,659 - core.llm_response_parsers - INFO - Risk assessment parsed: REJECTED (Risk: 50)
2025-07-15 06:27:52,661 - core.llm_orchestrator - INFO - ✅ Completed prompt: risk_assessment
2025-07-15 06:27:52,671 - core.llm_orchestrator - INFO - 🚀 PARALLEL LLM cycle completed in 14.17s - 4 prompts executed concurrently
2025-07-15 06:28:06,798 - main - INFO - Epinnox v6 starting up...
2025-07-15 06:28:06,815 - core.performance_monitor - INFO - Performance monitoring started
2025-07-15 06:28:06,815 - core.performance_monitor - INFO - Performance monitor initialized with 10s interval
2025-07-15 06:28:06,815 - main - INFO - Performance monitoring initialized
2025-07-15 06:28:06,826 - utils.cache_manager - INFO - Cache manager initialized: cache, max_age=24h, max_size=100MB
2025-07-15 06:28:06,827 - utils.cache_manager - INFO - Starting automatic cache cleanup
2025-07-15 06:28:06,828 - utils.cache_manager - INFO - Auto cleanup complete: 0 files removed, 0.00 MB freed
2025-07-15 06:28:10,430 - core.timer_coordinator - INFO - [TIMER_COORDINATOR] Initialized unified timer coordinator
2025-07-15 06:28:14,643 - config.autonomous_config - INFO - Configuration loaded from configs/autonomous_trading.yaml
2025-07-15 06:28:15,507 - data.live_data_manager - INFO - Successfully subscribed to live data for BTC/USDT:USDT on timeframes: ['1m', '5m', '15m']
2025-07-15 06:28:16,801 - websocket - INFO - Websocket connected
2025-07-15 06:28:18,827 - trading.position_tracker - INFO - Position Tracker initialized
2025-07-15 06:28:19,221 - llama.runner - INFO - Loaded OpenAI API key from models_config.yaml
2025-07-15 06:28:19,221 - llama.runner - INFO - Using model path: C:\Users\<USER>\.lmstudio\models\lmstudio-community\Phi-3.1-mini-128k-instruct-GGUF\Phi-3.1-mini-128k-instruct-Q4_K_M.gguf
2025-07-15 06:28:19,222 - llama.runner - INFO - Using guard path: C:\Users\<USER>\Documents\dev\Epinnox_v6\models\Llama-Guard-3-8B.gguf
2025-07-15 06:28:19,222 - llama.runner - INFO - Using binary path: C:\Users\<USER>\Documents\dev\llama.cpp\main
2025-07-15 06:28:19,227 - llama.lmstudio_runner - INFO - Loaded LMStudio configuration from models_config.yaml
2025-07-15 06:28:21,254 - llama.lmstudio_runner - INFO - Discovered 8 models: ['phi-3.1-mini-128k-instruct', 'openthinker3-7b', 'mistral-7b-claude-chat', 'fine_tuned_qwen1.7b', 'tsfc-model-0.0.1', 'codestral-22b-v0.1', 'deepseek-r1-distill-qwen-7b', 'text-embedding-nomic-embed-text-v1.5']
2025-07-15 06:28:21,255 - llama.lmstudio_runner - INFO - Selected preferred model from config: phi-3.1-mini-128k-instruct
2025-07-15 06:28:21,255 - llama.lmstudio_runner - INFO - Initialized LMStudio with model: phi-3.1-mini-128k-instruct
2025-07-15 06:28:21,257 - trading.intelligent_limit_order_manager - INFO - Intelligent Limit Order Manager initialized for professional scalping
2025-07-15 06:28:21,258 - core.llm_action_executors - INFO - ✅ Intelligent Limit Order Manager initialized
2025-07-15 06:28:21,258 - core.llm_action_executors - INFO - 🚀 LLM Action Executors initialized with intelligent limit order system
2025-07-15 06:28:21,258 - utils.cache_manager - INFO - Cache manager initialized: cache, max_age=24h, max_size=100MB
2025-07-15 06:28:21,258 - core.llm_orchestrator - INFO - LLM Prompt Orchestrator initialized
2025-07-15 06:28:21,261 - core.signal_hierarchy - INFO - Signal Hierarchy initialized
2025-07-15 06:28:21,280 - storage.database_manager - INFO - Database tables initialized successfully with performance optimizations
2025-07-15 06:28:21,280 - storage.database_manager - INFO - Database manager initialized: data\epinnox_trading.db
2025-07-15 06:28:21,280 - storage.session_manager - INFO - Session Manager initialized
2025-07-15 06:28:21,285 - storage.database_manager - INFO - Created session: live_BTCUSDTUSDT_20250715_062821_7f21e7a9
2025-07-15 06:28:21,285 - storage.session_manager - INFO - Started session: live_BTCUSDTUSDT_20250715_062821_7f21e7a9
2025-07-15 06:28:21,472 - core.risk_management_system - INFO - 🛡️ Risk Management System initialized
2025-07-15 06:28:21,475 - core.error_handling_system - INFO - 🛡️ Error Handling System initialized
2025-07-15 06:28:21,475 - core.error_handling_system - INFO - 📊 Component registered: exchange_connection
2025-07-15 06:28:21,475 - core.error_handling_system - INFO - 📊 Component registered: trading_interface
2025-07-15 06:28:21,475 - core.error_handling_system - INFO - 📊 Component registered: market_data
2025-07-15 06:28:21,475 - core.error_handling_system - INFO - 📊 Component registered: llm_orchestrator
2025-07-15 06:28:21,477 - core.error_handling_system - INFO - 🔍 Health monitoring started
2025-07-15 06:28:21,480 - core.monitoring_dashboard - INFO - 📊 Monitoring Dashboard initialized
2025-07-15 06:28:21,482 - core.symbol_scanner - INFO - SymbolScanner initialized with 8 symbols
2025-07-15 06:28:21,482 - core.timer_coordinator - INFO - [TIMER_COORDINATOR] Registered timer 'symbol_scanner_update': interval=30.0s, priority=MEDIUM
2025-07-15 06:28:21,482 - core.symbol_scanner - INFO - [SYMBOL_SCANNER] Registered with timer coordinator (interval: 30.0s)
2025-07-15 06:28:21,483 - core.llm_action_executors - WARNING - ⚠️ Intelligent Limit Order Manager not available
2025-07-15 06:28:21,483 - core.llm_action_executors - INFO - 🚀 LLM Action Executors initialized with intelligent limit order system
2025-07-15 06:29:44,112 - data.live_data_manager - INFO - Successfully subscribed to live data for DOGE/USDT:USDT on timeframes: ['1m', '5m', '15m']
2025-07-15 06:30:03,458 - core.llm_orchestrator - INFO - 🏃 SCALPING MODE: Executing 3 essential prompts in PARALLEL for maximum speed
2025-07-15 06:30:03,461 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-15 06:30:03,461 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-15 06:30:03,461 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-15 06:30:03,462 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-15 06:30:03,462 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-15 06:30:03,462 - core.llm_orchestrator - INFO - 🚀 Submitted 3 prompts for parallel execution
2025-07-15 06:30:03,462 - llama.lmstudio_runner - INFO - 💬 User Prompt: ...
2025-07-15 06:30:03,462 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🎯 ENTRY TIMING SPECIALIST - DOGE/USDT:USDT

🎯 SIGNAL ANALYSIS:
ML Ensemble: NEUTRAL (60.0%)
Technical: NEUTRAL | Momentum: 0.00
Volume: PENDING | Orderflow: NEUTRAL

📊 MARKET MICROSTRUCTURE:
Bid/Ask: $0.191058/$0.191067
Spread: 0.005% | L2 Imbalance: 0.0%
Recent Flow: BALANCED | Volume Spike: False

📈 KEY LEVELS:
Support: $0.190776
Resistance: $0.192766
Distance to Support: 0.15%

⏰ TIMING FACTORS:
- Price near key levels: CLOSE
- Volume confirmation: PENDING
- Spread conditions: FAVORABLE
- Mom...
2025-07-15 06:30:03,462 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a conservative futures trading analyst. Analyze market data carefully and provide measured recommendations. Use format: DECISION: [LONG/SHORT/WAIT], CONFIDENCE: [50-100]%, TAKE_PROFIT: [%], ST...
2025-07-15 06:30:03,463 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-15 06:30:03,463 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-15 06:30:03,464 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🔍 INTELLIGENT OPPORTUNITY SCANNER - Account Health Aware

🚨 ACCOUNT HEALTH STATUS: HEALTHY
💰 Balance: $99.54 | Health: Healthy - Normal Trading
📊 Current Exposure: $0.00 (0.0% of account)
🎯 Trading Allowed: YES

📊 SYMBOL ANALYSIS:
• DOGE/USDT:USDT: $0.191062 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• BTC/USDT:USDT: $95000.000000 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• ETH/USDT:USDT: $3500.000000 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• SOL/USDT:USDT: $200.000000 | Mom: +0.0% | Vol: 1.0x | S...
2025-07-15 06:30:03,465 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-15 06:30:07,010 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 221 chars
2025-07-15 06:30:07,011 - llama.lmstudio_runner - INFO - 📄 Response Preview: ```json
{
  "action": "LONG",
  "confidence": 90,
  "entry_reason": "Breakout above resistance level with strong volume indication.",
  "take_profit": 2.5,
  "stop_loss": 1.0,
  "hold_time": "3min",
  "leverage": 40
}
```...
2025-07-15 06:30:07,011 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 174, Completion: 92, Total: 266
2025-07-15 06:30:07,011 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['action', 'confidence', 'entry_reason', 'take_profit', 'stop_loss', 'hold_time', 'leverage']
2025-07-15 06:30:07,012 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['ACTION', 'CONFIDENCE', 'ENTRY_REASON', 'TAKE_PROFIT', 'STOP_LOSS', 'HOLD_TIME', 'LEVERAGE']
2025-07-15 06:30:07,012 - core.llm_response_parsers - INFO - Risk assessment parsed: REJECTED (Risk: 50)
2025-07-15 06:30:07,012 - core.llm_orchestrator - INFO - ✅ Completed prompt: risk_assessment
2025-07-15 06:30:08,567 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 331 chars
2025-07-15 06:30:08,568 - llama.lmstudio_runner - INFO - 📄 Response Preview: {
  "action": "ENTER",
  "entry_type": "LIMIT",
  "confidence": 78,
  "wait_for": null,
  "max_wait_seconds": 0,
  "reasoning": "Market conditions are favorable with a narrow spread and price near support level. Momentum is neutral but recent flow has been balanced without volume spikes indicating p...
2025-07-15 06:30:08,568 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 615, Completion: 101, Total: 716
2025-07-15 06:30:08,568 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['action', 'entry_type', 'confidence', 'wait_for', 'max_wait_seconds', 'reasoning']
2025-07-15 06:30:08,568 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['ACTION', 'ENTRY_TYPE', 'CONFIDENCE', 'WAIT_FOR', 'MAX_WAIT_SECONDS', 'REASONING']
2025-07-15 06:30:08,569 - core.llm_response_parsers - INFO - Entry timing parsed: WAIT (LIMIT order)
2025-07-15 06:30:08,569 - core.llm_orchestrator - INFO - ✅ Completed prompt: entry_timing
2025-07-15 06:30:12,146 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 960 chars
2025-07-15 06:30:12,147 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: LONG, CONFIDENCE: 80%, TAKE_PROFIT: 30%, STOP_LOSS: -40%, EXPLANATION: The DOGE/USDT pair shows a neutral sector momentum with an average volume profile and normal overall volatility. Given the account's healthy status, it is appropriate to consider this trade as there are no immediate red...
2025-07-15 06:30:12,147 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 934, Completion: 220, Total: 1154
2025-07-15 06:30:12,148 - core.llm_response_parsers - INFO - 🔄 No JSON found, trying structured text parsing
2025-07-15 06:30:12,150 - core.llm_response_parsers - INFO - Parsed structured text: {'DECISION': 'LONG', 'CONFIDENCE': 80.0, 'TAKE_PROFIT': 30.0, 'STOP_LOSS': -40.0, 'EXPLANATION': "THE DOGE/USDT PAIR SHOWS A NEUTRAL SECTOR MOMENTUM WITH AN AVERAGE VOLUME PROFILE AND NORMAL OVERALL VOLATILITY. GIVEN THE ACCOUNT'S HEALTHY STATUS, IT IS APPROPRIATE TO CONSIDER THIS TRADE AS THERE ARE NO IMMEDIATE RED FLAGS SUCH AS EXTREME PRICE MOVEMENTS OR HIGH-RISK INDICATORS PRESENT IN HISTORICAL DATA. A CONSERVATIVE ENTRY STRATEGY ALIGNS WELL DUE TO MARKET CONDITIONS BEING WITHIN A MODERATE RISK ENVIRONMENT AND LIQUIDITY SPREAD REQUIREMENTS MET AT LESS THAN 0.2%. THE CONFIDENCE LEVEL IS SET BASED ON THE ABSENCE OF RECENT SIGNIFICANT POSITIVE SIGNALS, YET THERE'S POTENTIAL FOR PROFITABILITY WITH AN ACCEPTABLE REWARD-RISK RATIO THAT RESPECTS ACCOUNT HEALTH CONSTRAINTS. A TAKE_PROFIT PERCENTAGE HIGHER THAN STOP LOSS INDICATES A STRONG BELIEF IN SHORT TO MEDIUM TERM UPSIDE WHILE MAINTAINING RISK WITHIN 2% PER TRADE AS PRESCRIBED BY THE ACCOUNT PROTECTION CRITERIA.", 'ACTION': 'ENTER_NOW'}
2025-07-15 06:30:12,150 - core.llm_response_parsers - INFO - ✅ Structured text parsing successful: ['DECISION', 'CONFIDENCE', 'TAKE_PROFIT', 'STOP_LOSS', 'EXPLANATION', 'ACTION']
2025-07-15 06:30:12,150 - core.llm_response_parsers - INFO - Opportunity scanner parsed: BREAKOUT (MOMENTUM)
2025-07-15 06:30:12,150 - core.llm_orchestrator - INFO - ✅ Completed prompt: opportunity_scanner
2025-07-15 06:30:12,151 - core.llm_orchestrator - INFO - 🚀 PARALLEL LLM cycle completed in 8.69s - 3 prompts executed concurrently
2025-07-15 06:30:12,152 - core.vote_aggregator - INFO - 🗳️ Vote aggregation: WAIT (51.6%) - Strong WAIT signal (2.6) → WAIT
2025-07-15 06:30:12,152 - core.llm_orchestrator - INFO - 🗳️ Aggregated Decision: WAIT (51.6%) - Strong WAIT signal (2.6) → WAIT
2025-07-15 06:30:12,152 - core.llm_orchestrator - INFO - 🗳️ Vote Breakdown: LONG: 1.6, WAIT: 2.6
2025-07-15 06:30:12,153 - core.vote_aggregator - INFO - 🗳️ Vote aggregation: WAIT (51.6%) - Strong WAIT signal (2.6) → WAIT
2025-07-15 06:30:12,153 - core.llm_orchestrator - INFO - 🗳️ Aggregated Decision: WAIT (51.6%) - Strong WAIT signal (2.6) → WAIT
2025-07-15 06:30:12,153 - core.llm_orchestrator - INFO - 🗳️ Vote Breakdown: LONG: 1.6, WAIT: 2.6
2025-07-15 06:30:31,083 - core.llm_orchestrator - INFO - 🔍 FULL MODE: Executing all 8 prompts in PARALLEL for comprehensive analysis
2025-07-15 06:30:31,084 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-15 06:30:31,085 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-15 06:30:31,085 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-15 06:30:31,085 - llama.lmstudio_runner - INFO - 💬 User Prompt: 📊 MARKET REGIME ANALYST - DOGE/USDT:USDT

🔍 MULTI-TIMEFRAME ANALYSIS:
1m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
5m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
15m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%

📈 VOLATILITY METRICS:
ATR: 0.001000 | Spread: 0.100%
Volume Ratio: 1.0x | Price Range: 1.00%

🎯 REGIME CLASSIFICATION:
- TRENDING_BULL: Strong upward momentum, high volume, clear direction
- TRENDING_BEAR: Strong downward momentum, high volume, clear direction
- RANGING_TIGHT: Low volatility, narrow pric...
2025-07-15 06:30:31,085 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-15 06:30:31,086 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-15 06:30:31,086 - core.llm_orchestrator - INFO - 🚀 Submitted 3 prompts for parallel execution
2025-07-15 06:30:31,086 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-15 06:30:31,086 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-15 06:30:31,087 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🔄 STRATEGY ADAPTATION SPECIALIST

📊 PERFORMANCE ANALYSIS (Last 24h):
Trades: 0 | Win Rate: 50.0%
Avg Profit: 0.80% | Avg Loss: -0.30%
Sharpe Ratio: 1.00 | Max Drawdown: 0.0%
Total PnL: $0.00 | ROI: 0.0%

🎯 CURRENT STRATEGY:
Risk per Trade: 2.0% | Avg Hold Time: 8.0min
Entry Threshold: 70% | Exit Threshold: 60%
Position Size Method: FIXED_RISK | Max Positions: 3

📈 MARKET REGIME: UNKNOWN
Regime Confidence: 50.0%
Scalp Suitability: MEDIUM

🔧 ADAPTATION FACTORS:
- Win rate trending: DOWN
- Drawdown...
2025-07-15 06:30:31,087 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🎯 ENTRY TIMING SPECIALIST - DOGE/USDT:USDT

🎯 SIGNAL ANALYSIS:
ML Ensemble: NEUTRAL (50.0%)
Technical: NEUTRAL | Momentum: 0.00
Volume: PENDING | Orderflow: NEUTRAL

📊 MARKET MICROSTRUCTURE:
Bid/Ask: $0.170000/$0.170000
Spread: 0.100% | L2 Imbalance: 0.0%
Recent Flow: BALANCED | Volume Spike: False

📈 KEY LEVELS:
Support: $0.170000
Resistance: $0.170000
Distance to Support: 0.00%

⏰ TIMING FACTORS:
- Price near key levels: NEUTRAL
- Volume confirmation: PENDING
- Spread conditions: FAVORABLE
- M...
2025-07-15 06:30:31,088 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-15 06:30:31,088 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-15 06:30:34,779 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 218 chars
2025-07-15 06:30:34,779 - llama.lmstudio_runner - INFO - 📄 Response Preview: {
    "regime": "RANGING_TIGHT",
    "confidence": 75,
    "scalp_suitability": "MEDIUM",
    "recommended_timeframe": "1m",
    "risk_level": "MEDIUM",
    "reasoning": "Consolidation phase with moderate volatility"
}...
2025-07-15 06:30:34,779 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 705, Completion: 86, Total: 791
2025-07-15 06:30:34,780 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['regime', 'confidence', 'scalp_suitability', 'recommended_timeframe', 'risk_level', 'reasoning']
2025-07-15 06:30:34,780 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['REGIME', 'CONFIDENCE', 'SCALP_SUITABILITY', 'RECOMMENDED_TIMEFRAME', 'RISK_LEVEL', 'REASONING']
2025-07-15 06:30:34,780 - core.llm_response_parsers - INFO - Market regime parsed: RANGING_TIGHT (MEDIUM scalp suitability)
2025-07-15 06:30:34,780 - core.llm_orchestrator - INFO - ✅ Completed prompt: market_regime
2025-07-15 06:30:36,279 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 311 chars
2025-07-15 06:30:36,279 - llama.lmstudio_runner - INFO - 📄 Response Preview: {
  "action": "ENTER",
  "entry_type": "MARKET",
  "confidence": 85,
  "wait_for": null,
  "max_wait_seconds": 0,
  "reasoning": "Market conditions are favorable with a narrow spread and no significant volume spikes. The price is near key levels indicating potential for quick entry without the need ...
2025-07-15 06:30:36,280 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 610, Completion: 97, Total: 707
2025-07-15 06:30:36,280 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['action', 'entry_type', 'confidence', 'wait_for', 'max_wait_seconds', 'reasoning']
2025-07-15 06:30:36,280 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['ACTION', 'ENTRY_TYPE', 'CONFIDENCE', 'WAIT_FOR', 'MAX_WAIT_SECONDS', 'REASONING']
2025-07-15 06:30:36,280 - core.llm_response_parsers - INFO - Entry timing parsed: WAIT (MARKET order)
2025-07-15 06:30:36,280 - core.llm_orchestrator - INFO - ✅ Completed prompt: entry_timing
2025-07-15 06:30:37,579 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 257 chars
2025-07-15 06:30:37,579 - llama.lmstudio_runner - INFO - 📄 Response Preview: {"risk_adjustment":1.5, "hold_time_target":30, "entry_threshold":65, "exit_threshold":55, "sizing_method":"VARIABLE", "reasoning":"Increase risk to capitalize on high confidence regime and adjust hold time for better scalping opportunities","confidence":85}...
2025-07-15 06:30:37,579 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 619, Completion: 82, Total: 701
2025-07-15 06:30:37,579 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['risk_adjustment', 'hold_time_target', 'entry_threshold', 'exit_threshold', 'sizing_method', 'reasoning', 'confidence']
2025-07-15 06:30:37,580 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['RISK_ADJUSTMENT', 'HOLD_TIME_TARGET', 'ENTRY_THRESHOLD', 'EXIT_THRESHOLD', 'SIZING_METHOD', 'REASONING', 'CONFIDENCE']
2025-07-15 06:30:37,580 - core.llm_response_parsers - INFO - Strategy adaptation parsed: Risk adj 1.5x
2025-07-15 06:30:37,580 - core.llm_action_executors - INFO - Applying strategy adaptations: Risk 1.5x, Hold time 30min
2025-07-15 06:30:37,580 - core.llm_orchestrator - INFO - ✅ Completed prompt: strategy_adaptation
2025-07-15 06:30:37,581 - core.llm_orchestrator - INFO - 🚀 PARALLEL LLM cycle completed in 6.50s - 3 prompts executed concurrently
2025-07-15 06:30:37,582 - core.vote_aggregator - INFO - 🗳️ Vote aggregation: WAIT (32.5%) - Strong WAIT signal (1.6) → WAIT
2025-07-15 06:30:37,582 - core.llm_orchestrator - INFO - 🗳️ Aggregated Decision: WAIT (32.5%) - Strong WAIT signal (1.6) → WAIT
2025-07-15 06:30:37,582 - core.llm_orchestrator - INFO - 🗳️ Vote Breakdown: WAIT: 1.6
2025-07-15 06:31:01,115 - core.llm_orchestrator - INFO - 🔍 FULL MODE: Executing all 8 prompts in PARALLEL for comprehensive analysis
2025-07-15 06:31:01,117 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-15 06:31:01,117 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-15 06:31:01,117 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-15 06:31:01,118 - llama.lmstudio_runner - INFO - 💬 User Prompt: 📊 MARKET REGIME ANALYST - DOGE/USDT:USDT

🔍 MULTI-TIMEFRAME ANALYSIS:
1m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
5m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
15m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%

📈 VOLATILITY METRICS:
ATR: 0.001000 | Spread: 0.100%
Volume Ratio: 1.0x | Price Range: 1.00%

🎯 REGIME CLASSIFICATION:
- TRENDING_BULL: Strong upward momentum, high volume, clear direction
- TRENDING_BEAR: Strong downward momentum, high volume, clear direction
- RANGING_TIGHT: Low volatility, narrow pric...
2025-07-15 06:31:01,118 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-15 06:31:01,119 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-15 06:31:01,119 - core.llm_orchestrator - INFO - 🚀 Submitted 3 prompts for parallel execution
2025-07-15 06:31:01,119 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-15 06:31:01,120 - llama.lmstudio_runner - INFO - 💬 User Prompt: ...
2025-07-15 06:31:01,120 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-15 06:31:01,122 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🎯 ENTRY TIMING SPECIALIST - DOGE/USDT:USDT

🎯 SIGNAL ANALYSIS:
ML Ensemble: NEUTRAL (50.0%)
Technical: NEUTRAL | Momentum: 0.00
Volume: PENDING | Orderflow: NEUTRAL

📊 MARKET MICROSTRUCTURE:
Bid/Ask: $0.170000/$0.170000
Spread: 0.100% | L2 Imbalance: 0.0%
Recent Flow: BALANCED | Volume Spike: False

📈 KEY LEVELS:
Support: $0.170000
Resistance: $0.170000
Distance to Support: 0.00%

⏰ TIMING FACTORS:
- Price near key levels: NEUTRAL
- Volume confirmation: PENDING
- Spread conditions: FAVORABLE
- M...
2025-07-15 06:31:01,121 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-15 06:31:01,122 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-15 06:31:04,809 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 227 chars
2025-07-15 06:31:04,809 - llama.lmstudio_runner - INFO - 📄 Response Preview: ```json
{
  "action": "LONG",
  "confidence": 90,
  "entry_reason": "Market broke through resistance level with a sudden spike in volume.",
  "take_profit": 1.5,
  "stop_loss": 0.7,
  "hold_time": "3min",
  "leverage": 40
}
```...
2025-07-15 06:31:04,809 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 174, Completion: 94, Total: 268
2025-07-15 06:31:04,810 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['action', 'confidence', 'entry_reason', 'take_profit', 'stop_loss', 'hold_time', 'leverage']
2025-07-15 06:31:04,810 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['ACTION', 'CONFIDENCE', 'ENTRY_REASON', 'TAKE_PROFIT', 'STOP_LOSS', 'HOLD_TIME', 'LEVERAGE']
2025-07-15 06:31:04,810 - core.llm_response_parsers - INFO - Risk assessment parsed: REJECTED (Risk: 50)
2025-07-15 06:31:04,810 - core.llm_orchestrator - INFO - ✅ Completed prompt: risk_assessment
2025-07-15 06:31:06,537 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 218 chars
2025-07-15 06:31:06,537 - llama.lmstudio_runner - INFO - 📄 Response Preview: ```json
{
  "REGIME": "RANGING_TIGHT",
  "CONFIDENCE": 75,
  "SCALP_SUITABILITY": "MEDIUM",
  "RECOMMENDED_TIMEFRAME": "1m",
  "RISK_LEVEL": "MEDIUM",
  "REASONING": "Consolidation phase with moderate volatility"
}
```...
2025-07-15 06:31:06,537 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 705, Completion: 108, Total: 813
2025-07-15 06:31:06,538 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['REGIME', 'CONFIDENCE', 'SCALP_SUITABILITY', 'RECOMMENDED_TIMEFRAME', 'RISK_LEVEL', 'REASONING']
2025-07-15 06:31:06,538 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['REGIME', 'CONFIDENCE', 'SCALP_SUITABILITY', 'RECOMMENDED_TIMEFRAME', 'RISK_LEVEL', 'REASONING']
2025-07-15 06:31:06,538 - core.llm_response_parsers - INFO - Market regime parsed: RANGING_TIGHT (MEDIUM scalp suitability)
2025-07-15 06:31:06,538 - core.llm_orchestrator - INFO - ✅ Completed prompt: market_regime
2025-07-15 06:31:08,085 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 315 chars
2025-07-15 06:31:08,085 - llama.lmstudio_runner - INFO - 📄 Response Preview: {
  "action": "ENTER",
  "entry_type": "LIMIT",
  "confidence": 80,
  "wait_for": null,
  "max_wait_seconds": 30,
  "reasoning": "Market conditions are favorable with a narrow spread and no significant imbalance. The price is near key levels indicating potential for movement without immediate risk o...
2025-07-15 06:31:08,085 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 610, Completion: 96, Total: 706
2025-07-15 06:31:08,086 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['action', 'entry_type', 'confidence', 'wait_for', 'max_wait_seconds', 'reasoning']
2025-07-15 06:31:08,086 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['ACTION', 'ENTRY_TYPE', 'CONFIDENCE', 'WAIT_FOR', 'MAX_WAIT_SECONDS', 'REASONING']
2025-07-15 06:31:08,086 - core.llm_response_parsers - INFO - Entry timing parsed: WAIT (LIMIT order)
2025-07-15 06:31:08,086 - core.llm_orchestrator - INFO - ✅ Completed prompt: entry_timing
2025-07-15 06:31:08,087 - core.llm_orchestrator - INFO - 🚀 PARALLEL LLM cycle completed in 6.97s - 3 prompts executed concurrently
2025-07-15 06:31:08,088 - core.vote_aggregator - INFO - 🗳️ Vote aggregation: WAIT (63.2%) - Strong WAIT signal (3.2) → WAIT
2025-07-15 06:31:08,088 - core.llm_orchestrator - INFO - 🗳️ Aggregated Decision: WAIT (63.2%) - Strong WAIT signal (3.2) → WAIT
2025-07-15 06:31:08,088 - core.llm_orchestrator - INFO - 🗳️ Vote Breakdown: WAIT: 3.2
2025-07-15 06:31:31,239 - core.llm_orchestrator - INFO - 🔍 FULL MODE: Executing all 8 prompts in PARALLEL for comprehensive analysis
2025-07-15 06:31:31,241 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-15 06:31:31,241 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-15 06:31:31,241 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-15 06:31:31,242 - llama.lmstudio_runner - INFO - 💬 User Prompt: 📊 MARKET REGIME ANALYST - DOGE/USDT:USDT

🔍 MULTI-TIMEFRAME ANALYSIS:
1m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
5m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
15m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%

📈 VOLATILITY METRICS:
ATR: 0.001000 | Spread: 0.100%
Volume Ratio: 1.0x | Price Range: 1.00%

🎯 REGIME CLASSIFICATION:
- TRENDING_BULL: Strong upward momentum, high volume, clear direction
- TRENDING_BEAR: Strong downward momentum, high volume, clear direction
- RANGING_TIGHT: Low volatility, narrow pric...
2025-07-15 06:31:31,242 - core.llm_orchestrator - INFO - 🚀 Submitted 3 prompts for parallel execution
2025-07-15 06:31:31,242 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-15 06:31:31,242 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-15 06:31:31,242 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-15 06:31:31,243 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a conservative futures trading analyst. Analyze market data carefully and provide measured recommendations. Use format: DECISION: [LONG/SHORT/WAIT], CONFIDENCE: [50-100]%, TAKE_PROFIT: [%], ST...
2025-07-15 06:31:31,243 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🎯 ENTRY TIMING SPECIALIST - DOGE/USDT:USDT

🎯 SIGNAL ANALYSIS:
ML Ensemble: NEUTRAL (50.0%)
Technical: NEUTRAL | Momentum: 0.00
Volume: PENDING | Orderflow: NEUTRAL

📊 MARKET MICROSTRUCTURE:
Bid/Ask: $0.170000/$0.170000
Spread: 0.100% | L2 Imbalance: 0.0%
Recent Flow: BALANCED | Volume Spike: False

📈 KEY LEVELS:
Support: $0.170000
Resistance: $0.170000
Distance to Support: 0.00%

⏰ TIMING FACTORS:
- Price near key levels: NEUTRAL
- Volume confirmation: PENDING
- Spread conditions: FAVORABLE
- M...
2025-07-15 06:31:31,243 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🔍 INTELLIGENT OPPORTUNITY SCANNER - Account Health Aware

🚨 ACCOUNT HEALTH STATUS: HEALTHY
💰 Balance: $1000.00 | Health: Healthy - Normal Trading
📊 Current Exposure: $0.00 (0.0% of account)
🎯 Trading Allowed: YES

📊 SYMBOL ANALYSIS:
• DOGE/USDT:USDT: $0.000000 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• BTC/USDT:USDT: $95000.000000 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• ETH/USDT:USDT: $3500.000000 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• SOL/USDT:USDT: $200.000000 | Mom: +0.0% | Vol: 1.0x |...
2025-07-15 06:31:31,244 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-15 06:31:31,244 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-15 06:31:35,423 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 218 chars
2025-07-15 06:31:35,423 - llama.lmstudio_runner - INFO - 📄 Response Preview: ```json
{
  "REGIME": "RANGING_TIGHT",
  "CONFIDENCE": 75,
  "SCALP_SUITABILITY": "MEDIUM",
  "RECOMMENDED_TIMEFRAME": "1m",
  "RISK_LEVEL": "MEDIUM",
  "REASONING": "Consolidation phase with moderate volatility"
}
```...
2025-07-15 06:31:35,424 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 705, Completion: 108, Total: 813
2025-07-15 06:31:35,424 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['REGIME', 'CONFIDENCE', 'SCALP_SUITABILITY', 'RECOMMENDED_TIMEFRAME', 'RISK_LEVEL', 'REASONING']
2025-07-15 06:31:35,424 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['REGIME', 'CONFIDENCE', 'SCALP_SUITABILITY', 'RECOMMENDED_TIMEFRAME', 'RISK_LEVEL', 'REASONING']
2025-07-15 06:31:35,424 - core.llm_response_parsers - INFO - Market regime parsed: RANGING_TIGHT (MEDIUM scalp suitability)
2025-07-15 06:31:35,425 - core.llm_orchestrator - INFO - ✅ Completed prompt: market_regime
2025-07-15 06:31:39,264 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 1027 chars
2025-07-15 06:31:39,264 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: LONG, CONFIDENCE: 85%, TAKE_PROFIT: 12%, STOP_LOSS: -4%, EXPLANATION: The market data indicates a neutral sector momentum with normal volatility and average volume profile. Given the account's healthy status and adherence to preservation criteria, there is an opportunity for conservative e...
2025-07-15 06:31:39,265 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 933, Completion: 238, Total: 1171
2025-07-15 06:31:39,265 - core.llm_response_parsers - INFO - 🔄 No JSON found, trying structured text parsing
2025-07-15 06:31:39,265 - core.llm_response_parsers - INFO - Parsed structured text: {'DECISION': 'LONG', 'CONFIDENCE': 85.0, 'TAKE_PROFIT': 12.0, 'STOP_LOSS': -4.0, 'EXPLANATION': "THE MARKET DATA INDICATES A NEUTRAL SECTOR MOMENTUM WITH NORMAL VOLATILITY AND AVERAGE VOLUME PROFILE. GIVEN THE ACCOUNT'S HEALTHY STATUS AND ADHERENCE TO PRESERVATION CRITERIA, THERE IS AN OPPORTUNITY FOR CONSERVATIVE ENTRY INTO DOGE/USDT DUE TO ITS MEDIUM SETUP QUALITY WHICH ALIGNS WELL WITHIN OUR RISK-REWARD REQUIREMENTS OF >3:1 RATIO WHILE MAINTAINING A LIQUIDITY PREFERENCE WITH LESS THAN 0.2% SPREAD REQUIRED. THE HISTORICAL CONTEXT SHOWS NO RECENT PRICE MOVEMENT AND SIGNALS HAVE BEEN 'WAIT', SUGGESTING THAT THE MARKET IS CURRENTLY STABLE, PROVIDING AN OPPORTUNE MOMENT FOR ENTRY WITHOUT IMMEDIATE PRESSURE FROM SHORT-TERM FLUCTUATIONS. A TAKE PROFIT OF 12% ENSURES A REASONABLE RETURN WHILE MAINTAINING RISK WITHIN OUR HEALTH-ADJUSTED BUDGET WITH STOP LOSS SET AT -4%, WHICH PROVIDES ADEQUATE PROTECTION AGAINST SUDDEN ADVERSE PRICE MOVEMENTS, KEEPING THE OVERALL ACCOUNT IMPACT MINIMAL AND ALIGNED WITH CONSERVATIVE TRADING PRINCIPLES.", 'ACTION': 'ENTER_NOW'}
2025-07-15 06:31:39,266 - core.llm_response_parsers - INFO - ✅ Structured text parsing successful: ['DECISION', 'CONFIDENCE', 'TAKE_PROFIT', 'STOP_LOSS', 'EXPLANATION', 'ACTION']
2025-07-15 06:31:39,266 - core.llm_response_parsers - INFO - Opportunity scanner parsed: BREAKOUT (MOMENTUM)
2025-07-15 06:31:39,266 - core.llm_orchestrator - INFO - ✅ Completed prompt: opportunity_scanner
2025-07-15 06:31:41,041 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 345 chars
2025-07-15 06:31:41,042 - llama.lmstudio_runner - INFO - 📄 Response Preview: {
  "action": "ENTER",
  "entry_type": "LIMIT",
  "confidence": 90,
  "wait_for": null,
  "max_wait_seconds": 30,
  "reasoning": "Market conditions are favorable with a low spread and stable price near key support levels. Momentum is neutral but volume confirmation pending; however, the risk/reward ...
2025-07-15 06:31:41,042 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 610, Completion: 110, Total: 720
2025-07-15 06:31:41,042 - core.llm_response_parsers - WARNING - 🚨 JSON decode error: Expecting ',' delimiter: line 1 column 299 (char 298)
2025-07-15 06:31:41,043 - core.llm_response_parsers - WARNING - 🔍 Problematic JSON: { "action": "ENTER", "entry_type": "LIMIT", "confidence": 90, "wait_for": null, "max_wait_seconds": 30, "reasoning": "Market conditions are favorable with a low spread and stable price near key suppor...
2025-07-15 06:31:41,044 - core.llm_response_parsers - INFO - ✅ Aggressive cleanup extracted: ['action', 'confidence', 'reasoning']
2025-07-15 06:31:41,044 - core.llm_response_parsers - INFO - ✅ JSON fixed with aggressive cleanup
2025-07-15 06:31:41,044 - core.llm_response_parsers - INFO - Entry timing parsed: WAIT (LIMIT order)
2025-07-15 06:31:41,044 - core.llm_orchestrator - INFO - ✅ Completed prompt: entry_timing
2025-07-15 06:31:41,045 - core.llm_orchestrator - INFO - 🚀 PARALLEL LLM cycle completed in 9.81s - 3 prompts executed concurrently
2025-07-15 06:31:41,046 - core.vote_aggregator - INFO - 🗳️ Vote aggregation: LONG (61.5%) - Clear LONG signal (weight: 1.7, consensus: 61.5%)
2025-07-15 06:31:41,046 - core.llm_orchestrator - INFO - 🗳️ Aggregated Decision: LONG (61.5%) - Clear LONG signal (weight: 1.7, consensus: 61.5%)
2025-07-15 06:31:41,046 - core.llm_orchestrator - INFO - 🗳️ Vote Breakdown: LONG: 1.7, WAIT: 1.1
2025-07-15 06:32:01,116 - core.llm_orchestrator - INFO - 🔍 FULL MODE: Executing all 8 prompts in PARALLEL for comprehensive analysis
2025-07-15 06:32:01,117 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-15 06:32:01,118 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-15 06:32:01,118 - llama.lmstudio_runner - INFO - 💬 User Prompt: ...
2025-07-15 06:32:01,119 - core.llm_orchestrator - INFO - 🚀 Submitted 2 prompts for parallel execution
2025-07-15 06:32:01,119 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-15 06:32:01,119 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-15 06:32:01,120 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-15 06:32:01,121 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🎯 ENTRY TIMING SPECIALIST - DOGE/USDT:USDT

🎯 SIGNAL ANALYSIS:
ML Ensemble: NEUTRAL (50.0%)
Technical: NEUTRAL | Momentum: 0.00
Volume: PENDING | Orderflow: NEUTRAL

📊 MARKET MICROSTRUCTURE:
Bid/Ask: $0.170000/$0.170000
Spread: 0.100% | L2 Imbalance: 0.0%
Recent Flow: BALANCED | Volume Spike: False

📈 KEY LEVELS:
Support: $0.170000
Resistance: $0.170000
Distance to Support: 0.00%

⏰ TIMING FACTORS:
- Price near key levels: NEUTRAL
- Volume confirmation: PENDING
- Spread conditions: FAVORABLE
- M...
2025-07-15 06:32:01,122 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-15 06:32:04,769 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 262 chars
2025-07-15 06:32:04,770 - llama.lmstudio_runner - INFO - 📄 Response Preview: {
  "action": "ENTER",
  "entry_type": "LIMIT",
  "confidence": 82,
  "wait_for": null,
  "max_wait_seconds": 0,
  "reasoning": "Market conditions are favorable with a narrow spread and no significant volume spikes. The risk/reward ratio is optimal for entry."
}...
2025-07-15 06:32:04,770 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 610, Completion: 89, Total: 699
2025-07-15 06:32:04,770 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['action', 'entry_type', 'confidence', 'wait_for', 'max_wait_seconds', 'reasoning']
2025-07-15 06:32:04,770 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['ACTION', 'ENTRY_TYPE', 'CONFIDENCE', 'WAIT_FOR', 'MAX_WAIT_SECONDS', 'REASONING']
2025-07-15 06:32:04,770 - core.llm_response_parsers - INFO - Entry timing parsed: WAIT (LIMIT order)
2025-07-15 06:32:04,770 - core.llm_orchestrator - INFO - ✅ Completed prompt: entry_timing
2025-07-15 06:32:06,072 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 229 chars
2025-07-15 06:32:06,072 - llama.lmstudio_runner - INFO - 📄 Response Preview: ```json
{
  "action": "LONG",
  "confidence": 90,
  "entry_reason": "Market broke above the resistance level with a sudden surge of volume.",
  "take_profit": 2.5,
  "stop_loss": 1.0,
  "hold_time": "3min",
  "leverage": 40
}
```...
2025-07-15 06:32:06,073 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 174, Completion: 95, Total: 269
2025-07-15 06:32:06,073 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['action', 'confidence', 'entry_reason', 'take_profit', 'stop_loss', 'hold_time', 'leverage']
2025-07-15 06:32:06,073 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['ACTION', 'CONFIDENCE', 'ENTRY_REASON', 'TAKE_PROFIT', 'STOP_LOSS', 'HOLD_TIME', 'LEVERAGE']
2025-07-15 06:32:06,073 - core.llm_response_parsers - INFO - Risk assessment parsed: REJECTED (Risk: 50)
2025-07-15 06:32:06,074 - core.llm_orchestrator - INFO - ✅ Completed prompt: risk_assessment
2025-07-15 06:32:06,074 - core.llm_orchestrator - INFO - 🚀 PARALLEL LLM cycle completed in 4.96s - 2 prompts executed concurrently
2025-07-15 06:32:06,075 - core.vote_aggregator - INFO - 🗳️ Vote aggregation: WAIT (52.4%) - Strong WAIT signal (2.6) → WAIT
2025-07-15 06:32:06,075 - core.llm_orchestrator - INFO - 🗳️ Aggregated Decision: WAIT (52.4%) - Strong WAIT signal (2.6) → WAIT
2025-07-15 06:32:06,075 - core.llm_orchestrator - INFO - 🗳️ Vote Breakdown: WAIT: 2.6
2025-07-15 06:32:31,117 - core.llm_orchestrator - INFO - 🔍 FULL MODE: Executing all 8 prompts in PARALLEL for comprehensive analysis
2025-07-15 06:32:31,118 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-15 06:32:31,119 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-15 06:32:31,119 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-15 06:32:31,119 - llama.lmstudio_runner - INFO - 💬 User Prompt: 📊 MARKET REGIME ANALYST - DOGE/USDT:USDT

🔍 MULTI-TIMEFRAME ANALYSIS:
1m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
5m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
15m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%

📈 VOLATILITY METRICS:
ATR: 0.001000 | Spread: 0.100%
Volume Ratio: 1.0x | Price Range: 1.00%

🎯 REGIME CLASSIFICATION:
- TRENDING_BULL: Strong upward momentum, high volume, clear direction
- TRENDING_BEAR: Strong downward momentum, high volume, clear direction
- RANGING_TIGHT: Low volatility, narrow pric...
2025-07-15 06:32:31,120 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-15 06:32:31,120 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-15 06:32:31,121 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-15 06:32:31,120 - core.llm_orchestrator - INFO - 🚀 Submitted 3 prompts for parallel execution
2025-07-15 06:32:31,121 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🔄 STRATEGY ADAPTATION SPECIALIST

📊 PERFORMANCE ANALYSIS (Last 24h):
Trades: 0 | Win Rate: 50.0%
Avg Profit: 0.80% | Avg Loss: -0.30%
Sharpe Ratio: 1.00 | Max Drawdown: 0.0%
Total PnL: $0.00 | ROI: 0.0%

🎯 CURRENT STRATEGY:
Risk per Trade: 2.0% | Avg Hold Time: 8.0min
Entry Threshold: 70% | Exit Threshold: 60%
Position Size Method: FIXED_RISK | Max Positions: 3

📈 MARKET REGIME: UNKNOWN
Regime Confidence: 50.0%
Scalp Suitability: MEDIUM

🔧 ADAPTATION FACTORS:
- Win rate trending: DOWN
- Drawdown...
2025-07-15 06:32:31,120 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-15 06:32:31,121 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🎯 ENTRY TIMING SPECIALIST - DOGE/USDT:USDT

🎯 SIGNAL ANALYSIS:
ML Ensemble: NEUTRAL (50.0%)
Technical: NEUTRAL | Momentum: 0.00
Volume: PENDING | Orderflow: NEUTRAL

📊 MARKET MICROSTRUCTURE:
Bid/Ask: $0.170000/$0.170000
Spread: 0.100% | L2 Imbalance: 0.0%
Recent Flow: BALANCED | Volume Spike: False

📈 KEY LEVELS:
Support: $0.170000
Resistance: $0.170000
Distance to Support: 0.00%

⏰ TIMING FACTORS:
- Price near key levels: NEUTRAL
- Volume confirmation: PENDING
- Spread conditions: FAVORABLE
- M...
2025-07-15 06:32:31,122 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-15 06:32:31,123 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-15 06:32:35,042 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 319 chars
2025-07-15 06:32:35,042 - llama.lmstudio_runner - INFO - 📄 Response Preview: {
  "action": "ENTER",
  "entry_type": "LIMIT",
  "confidence": 90,
  "wait_for": "",
  "max_wait_seconds": 30,
  "reasoning": "Market conditions are favorable with a narrow spread and volume spike indication. Momentum is neutral but the recent flow has been balanced which suggests potential for an ...
2025-07-15 06:32:35,042 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 610, Completion: 100, Total: 710
2025-07-15 06:32:35,043 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['action', 'entry_type', 'confidence', 'wait_for', 'max_wait_seconds', 'reasoning']
2025-07-15 06:32:35,043 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['ACTION', 'ENTRY_TYPE', 'CONFIDENCE', 'WAIT_FOR', 'MAX_WAIT_SECONDS', 'REASONING']
2025-07-15 06:32:35,043 - core.llm_response_parsers - INFO - Entry timing parsed: WAIT (LIMIT order)
2025-07-15 06:32:35,043 - core.llm_orchestrator - INFO - ✅ Completed prompt: entry_timing
2025-07-15 06:32:36,228 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 195 chars
2025-07-15 06:32:36,228 - llama.lmstudio_runner - INFO - 📄 Response Preview: {"risk_adjustment":1.0, "hold_time_target":8, "entry_threshold":70, "exit_threshold":60, "sizing_method":"FIXED_RISK", "reasoning":"Maintain current strategy - performing well", "confidence":95}
...
2025-07-15 06:32:36,228 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 619, Completion: 73, Total: 692
2025-07-15 06:32:36,228 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['risk_adjustment', 'hold_time_target', 'entry_threshold', 'exit_threshold', 'sizing_method', 'reasoning', 'confidence']
2025-07-15 06:32:36,229 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['RISK_ADJUSTMENT', 'HOLD_TIME_TARGET', 'ENTRY_THRESHOLD', 'EXIT_THRESHOLD', 'SIZING_METHOD', 'REASONING', 'CONFIDENCE']
2025-07-15 06:32:36,229 - core.llm_response_parsers - INFO - Strategy adaptation parsed: Risk adj 1.0x
2025-07-15 06:32:36,229 - core.llm_action_executors - INFO - Applying strategy adaptations: Risk 1.0x, Hold time 8min
2025-07-15 06:32:36,229 - core.llm_orchestrator - INFO - ✅ Completed prompt: strategy_adaptation
2025-07-15 06:32:38,097 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 265 chars
2025-07-15 06:32:38,097 - llama.lmstudio_runner - INFO - 📄 Response Preview: ```json
{
  "REGIME": "RANGING_TIGHT",
  "CONFIDENCE": 75,
  "SCALP_SUITABILITY": "MEDIUM",
  "RECOMMENDED_TIMEFRAME": "1m",
  "RISK_LEVEL": "MEDIUM",
  "REASONING": "Consolidation phase with moderate volatility, suitable for medium risk scalping strategies."
}
```...
2025-07-15 06:32:38,098 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 705, Completion: 117, Total: 822
2025-07-15 06:32:38,098 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['REGIME', 'CONFIDENCE', 'SCALP_SUITABILITY', 'RECOMMENDED_TIMEFRAME', 'RISK_LEVEL', 'REASONING']
2025-07-15 06:32:38,098 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['REGIME', 'CONFIDENCE', 'SCALP_SUITABILITY', 'RECOMMENDED_TIMEFRAME', 'RISK_LEVEL', 'REASONING']
2025-07-15 06:32:38,098 - core.llm_response_parsers - INFO - Market regime parsed: RANGING_TIGHT (MEDIUM scalp suitability)
2025-07-15 06:32:38,098 - core.llm_orchestrator - INFO - ✅ Completed prompt: market_regime
2025-07-15 06:32:38,099 - core.llm_orchestrator - INFO - 🚀 PARALLEL LLM cycle completed in 6.98s - 3 prompts executed concurrently
2025-07-15 06:32:38,100 - core.vote_aggregator - INFO - 🗳️ Vote aggregation: WAIT (34.0%) - Strong WAIT signal (1.7) → WAIT
2025-07-15 06:32:38,100 - core.llm_orchestrator - INFO - 🗳️ Aggregated Decision: WAIT (34.0%) - Strong WAIT signal (1.7) → WAIT
2025-07-15 06:32:38,100 - core.llm_orchestrator - INFO - 🗳️ Vote Breakdown: WAIT: 1.7
2025-07-15 06:33:01,162 - core.llm_orchestrator - INFO - 🔍 FULL MODE: Executing all 8 prompts in PARALLEL for comprehensive analysis
2025-07-15 06:33:01,163 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-15 06:33:01,163 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-15 06:33:01,164 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-15 06:33:01,164 - llama.lmstudio_runner - INFO - 💬 User Prompt: 📊 MARKET REGIME ANALYST - DOGE/USDT:USDT

🔍 MULTI-TIMEFRAME ANALYSIS:
1m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
5m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
15m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%

📈 VOLATILITY METRICS:
ATR: 0.001000 | Spread: 0.100%
Volume Ratio: 1.0x | Price Range: 1.00%

🎯 REGIME CLASSIFICATION:
- TRENDING_BULL: Strong upward momentum, high volume, clear direction
- TRENDING_BEAR: Strong downward momentum, high volume, clear direction
- RANGING_TIGHT: Low volatility, narrow pric...
2025-07-15 06:33:01,164 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-15 06:33:01,164 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-15 06:33:01,165 - core.llm_orchestrator - INFO - 🚀 Submitted 4 prompts for parallel execution
2025-07-15 06:33:01,165 - llama.lmstudio_runner - INFO - 💬 User Prompt: ...
2025-07-15 06:33:01,164 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-15 06:33:01,165 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-15 06:33:01,165 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-15 06:33:01,165 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-15 06:33:01,166 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a conservative futures trading analyst. Analyze market data carefully and provide measured recommendations. Use format: DECISION: [LONG/SHORT/WAIT], CONFIDENCE: [50-100]%, TAKE_PROFIT: [%], ST...
2025-07-15 06:33:01,166 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🎯 ENTRY TIMING SPECIALIST - DOGE/USDT:USDT

🎯 SIGNAL ANALYSIS:
ML Ensemble: NEUTRAL (50.0%)
Technical: NEUTRAL | Momentum: 0.00
Volume: PENDING | Orderflow: NEUTRAL

📊 MARKET MICROSTRUCTURE:
Bid/Ask: $0.170000/$0.170000
Spread: 0.100% | L2 Imbalance: 0.0%
Recent Flow: BALANCED | Volume Spike: False

📈 KEY LEVELS:
Support: $0.170000
Resistance: $0.170000
Distance to Support: 0.00%

⏰ TIMING FACTORS:
- Price near key levels: NEUTRAL
- Volume confirmation: PENDING
- Spread conditions: FAVORABLE
- M...
2025-07-15 06:33:01,167 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🔍 INTELLIGENT OPPORTUNITY SCANNER - Account Health Aware

🚨 ACCOUNT HEALTH STATUS: HEALTHY
💰 Balance: $1000.00 | Health: Healthy - Normal Trading
📊 Current Exposure: $0.00 (0.0% of account)
🎯 Trading Allowed: YES

📊 SYMBOL ANALYSIS:
• DOGE/USDT:USDT: $0.000000 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• BTC/USDT:USDT: $95000.000000 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• ETH/USDT:USDT: $3500.000000 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• SOL/USDT:USDT: $200.000000 | Mom: +0.0% | Vol: 1.0x |...
2025-07-15 06:33:01,167 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-15 06:33:01,167 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-15 06:33:05,007 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 312 chars
2025-07-15 06:33:05,007 - llama.lmstudio_runner - INFO - 📄 Response Preview: {
  "action": "ENTER",
  "entry_type": "LIMIT",
  "confidence": 90,
  "wait_for": null,
  "max_wait_seconds": 30,
  "reasoning": "Market conditions are favorable with a narrow spread and no significant imbalance. The recent flow is balanced without volume spikes which indicates stability in the mark...
2025-07-15 06:33:05,008 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 610, Completion: 98, Total: 708
2025-07-15 06:33:05,008 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['action', 'entry_type', 'confidence', 'wait_for', 'max_wait_seconds', 'reasoning']
2025-07-15 06:33:05,008 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['ACTION', 'ENTRY_TYPE', 'CONFIDENCE', 'WAIT_FOR', 'MAX_WAIT_SECONDS', 'REASONING']
2025-07-15 06:33:05,008 - core.llm_response_parsers - INFO - Entry timing parsed: WAIT (LIMIT order)
2025-07-15 06:33:05,009 - core.llm_orchestrator - INFO - ✅ Completed prompt: entry_timing
2025-07-15 06:33:06,752 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 218 chars
2025-07-15 06:33:06,752 - llama.lmstudio_runner - INFO - 📄 Response Preview: ```json
{
  "REGIME": "RANGING_TIGHT",
  "CONFIDENCE": 75,
  "SCALP_SUITABILITY": "MEDIUM",
  "RECOMMENDED_TIMEFRAME": "1m",
  "RISK_LEVEL": "MEDIUM",
  "REASONING": "Consolidation phase with moderate volatility"
}
```...
2025-07-15 06:33:06,752 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 705, Completion: 108, Total: 813
2025-07-15 06:33:06,753 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['REGIME', 'CONFIDENCE', 'SCALP_SUITABILITY', 'RECOMMENDED_TIMEFRAME', 'RISK_LEVEL', 'REASONING']
2025-07-15 06:33:06,753 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['REGIME', 'CONFIDENCE', 'SCALP_SUITABILITY', 'RECOMMENDED_TIMEFRAME', 'RISK_LEVEL', 'REASONING']
2025-07-15 06:33:06,753 - core.llm_response_parsers - INFO - Market regime parsed: RANGING_TIGHT (MEDIUM scalp suitability)
2025-07-15 06:33:06,753 - core.llm_orchestrator - INFO - ✅ Completed prompt: market_regime
2025-07-15 06:33:10,185 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 869 chars
2025-07-15 06:33:10,186 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: SHORT, CONFIDENCE: 70%, TAKE_PROFIT: 12%, STOP_LOSS: -3%, EXPLANATION: The current market data shows a neutral sector momentum and normal overall volatility with an average volume profile. Given the account's healthy status, it is suitable to engage in trading but within conservative param...
2025-07-15 06:33:10,186 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 933, Completion: 209, Total: 1142
2025-07-15 06:33:10,186 - core.llm_response_parsers - INFO - 🔄 No JSON found, trying structured text parsing
2025-07-15 06:33:10,186 - core.llm_response_parsers - INFO - Parsed structured text: {'DECISION': 'SHORT', 'CONFIDENCE': 70.0, 'TAKE_PROFIT': 12.0, 'STOP_LOSS': -3.0, 'EXPLANATION': "THE CURRENT MARKET DATA SHOWS A NEUTRAL SECTOR MOMENTUM AND NORMAL OVERALL VOLATILITY WITH AN AVERAGE VOLUME PROFILE. GIVEN THE ACCOUNT'S HEALTHY STATUS, IT IS SUITABLE TO ENGAGE IN TRADING BUT WITHIN CONSERVATIVE PARAMETERS DUE TO MODERATE RISK ENVIRONMENT CONSIDERATIONS. DOGE/USDT APPEARS AS THE BEST OPPORTUNITY BASED ON HISTORICAL CONTEXT; HOWEVER, THERE ARE NO RECENT PRICE OR SIGNAL CHANGES INDICATING A STRONG BULLISH TREND THAT WOULD JUSTIFY AN LONG POSITION WITH HIGH CONFIDENCE (80%). THE RECOMMENDED SHORT STRATEGY IS CONSERVATIVE AND ALIGNS WELL WITHIN ACCOUNT HEALTH-ADJUSTED RISK PARAMETERS. A TAKE PROFIT OF 12% ENSURES REASONABLE GAINS WITHOUT OVEREXPOSURE, WHILE THE STOP LOSS AT -3% PROVIDES A SAFETY NET AGAINST POTENTIAL LOSSES IN THIS VOLATILE CRYPTO MARKET SEGMENT.", 'ACTION': 'ENTER_NOW'}
2025-07-15 06:33:10,187 - core.llm_response_parsers - INFO - ✅ Structured text parsing successful: ['DECISION', 'CONFIDENCE', 'TAKE_PROFIT', 'STOP_LOSS', 'EXPLANATION', 'ACTION']
2025-07-15 06:33:10,187 - core.llm_response_parsers - INFO - Opportunity scanner parsed: MOMENTUM (REVERSAL)
2025-07-15 06:33:10,187 - core.llm_orchestrator - INFO - ✅ Completed prompt: opportunity_scanner
2025-07-15 06:33:11,461 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 199 chars
2025-07-15 06:33:11,462 - llama.lmstudio_runner - INFO - 📄 Response Preview: ```json
{
  "action": "LONG",
  "confidence": 90,
  "entry_reason": "Market broke with a sudden upward trend.",
  "take_profit": 2.5,
  "stop_loss": 1.0,
  "hold_time": "3min",
  "leverage": 40
}
```...
2025-07-15 06:33:11,462 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 174, Completion: 91, Total: 265
2025-07-15 06:33:11,462 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['action', 'confidence', 'entry_reason', 'take_profit', 'stop_loss', 'hold_time', 'leverage']
2025-07-15 06:33:11,463 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['ACTION', 'CONFIDENCE', 'ENTRY_REASON', 'TAKE_PROFIT', 'STOP_LOSS', 'HOLD_TIME', 'LEVERAGE']
2025-07-15 06:33:11,463 - core.llm_response_parsers - INFO - Risk assessment parsed: REJECTED (Risk: 50)
2025-07-15 06:33:11,463 - core.llm_orchestrator - INFO - ✅ Completed prompt: risk_assessment
2025-07-15 06:33:11,464 - core.llm_orchestrator - INFO - 🚀 PARALLEL LLM cycle completed in 10.30s - 4 prompts executed concurrently
2025-07-15 06:33:11,464 - core.vote_aggregator - INFO - 🗳️ Vote aggregation: WAIT (65.2%) - Strong WAIT signal (3.3) → WAIT
2025-07-15 06:33:11,465 - core.llm_orchestrator - INFO - 🗳️ Aggregated Decision: WAIT (65.2%) - Strong WAIT signal (3.3) → WAIT
2025-07-15 06:33:11,465 - core.llm_orchestrator - INFO - 🗳️ Vote Breakdown: SHORT: 1.4, WAIT: 3.3
2025-07-15 06:33:31,199 - core.llm_orchestrator - INFO - 🔍 FULL MODE: Executing all 8 prompts in PARALLEL for comprehensive analysis
2025-07-15 06:33:31,201 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-15 06:33:31,202 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-15 06:33:31,202 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-15 06:33:31,202 - core.llm_orchestrator - INFO - 🚀 Submitted 2 prompts for parallel execution
2025-07-15 06:33:31,202 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-15 06:33:31,203 - llama.lmstudio_runner - INFO - 💬 User Prompt: 📊 MARKET REGIME ANALYST - DOGE/USDT:USDT

🔍 MULTI-TIMEFRAME ANALYSIS:
1m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
5m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
15m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%

📈 VOLATILITY METRICS:
ATR: 0.001000 | Spread: 0.100%
Volume Ratio: 1.0x | Price Range: 1.00%

🎯 REGIME CLASSIFICATION:
- TRENDING_BULL: Strong upward momentum, high volume, clear direction
- TRENDING_BEAR: Strong downward momentum, high volume, clear direction
- RANGING_TIGHT: Low volatility, narrow pric...
2025-07-15 06:33:31,204 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🎯 ENTRY TIMING SPECIALIST - DOGE/USDT:USDT

🎯 SIGNAL ANALYSIS:
ML Ensemble: NEUTRAL (50.0%)
Technical: NEUTRAL | Momentum: 0.00
Volume: PENDING | Orderflow: NEUTRAL

📊 MARKET MICROSTRUCTURE:
Bid/Ask: $0.170000/$0.170000
Spread: 0.100% | L2 Imbalance: 0.0%
Recent Flow: BALANCED | Volume Spike: False

📈 KEY LEVELS:
Support: $0.170000
Resistance: $0.170000
Distance to Support: 0.00%

⏰ TIMING FACTORS:
- Price near key levels: NEUTRAL
- Volume confirmation: PENDING
- Spread conditions: FAVORABLE
- M...
2025-07-15 06:33:31,204 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-15 06:33:31,204 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-15 06:33:38,405 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 1462 chars
2025-07-15 06:33:38,406 - llama.lmstudio_runner - INFO - 📄 Response Preview: {
  "action": "ENTER", // Decision to enter the market immediately based on favorable spread conditions and a positive risk/reward ratio, despite neutral technical analysis and volume confirmation pending. The scalper is willing to take this calculated risk for potential profit within their set para...
2025-07-15 06:33:38,406 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 610, Completion: 331, Total: 941
2025-07-15 06:33:38,407 - core.llm_response_parsers - WARNING - 🚨 JSON decode error: Expecting ',' delimiter: line 1 column 309 (char 308)
2025-07-15 06:33:38,407 - core.llm_response_parsers - WARNING - 🔍 Problematic JSON: { "action": "ENTER", "entry_type": "LIMIT", "confidence": 85, "wait_for": null, "max_wait_seconds": -1, "reasoning": "The decision for an immediate market entry was made considering that while technic...
2025-07-15 06:33:38,407 - core.llm_response_parsers - INFO - ✅ Aggressive cleanup extracted: ['action', 'confidence', 'reasoning']
2025-07-15 06:33:38,407 - core.llm_response_parsers - INFO - ✅ JSON fixed with aggressive cleanup
2025-07-15 06:33:38,408 - core.llm_response_parsers - INFO - Entry timing parsed: WAIT (LIMIT order)
2025-07-15 06:33:38,408 - core.llm_orchestrator - INFO - ✅ Completed prompt: entry_timing
2025-07-15 06:33:40,722 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 452 chars
2025-07-15 06:33:40,722 - llama.lmstudio_runner - INFO - 📄 Response Preview: {
  "REGIME": "RANGING_TIGHT",
  "CONFIDENCE": 75,
  "SCALP_SUITABILITY": "MEDIUM",
  "RECOMMENDED_TIMEFRAME": "1m",
  "RISK_LEVEL": "MEDIUM",
  "REASONING": "The market is currently in a consolidation phase with moderate volatility, which presents some challenges for scalping. However, the medium c...
2025-07-15 06:33:40,723 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 705, Completion: 149, Total: 854
2025-07-15 06:33:40,723 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['REGIME', 'CONFIDENCE', 'SCALP_SUITABILITY', 'RECOMMENDED_TIMEFRAME', 'RISK_LEVEL', 'REASONING']
2025-07-15 06:33:40,724 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['REGIME', 'CONFIDENCE', 'SCALP_SUITABILITY', 'RECOMMENDED_TIMEFRAME', 'RISK_LEVEL', 'REASONING']
2025-07-15 06:33:40,724 - core.llm_response_parsers - INFO - Market regime parsed: RANGING_TIGHT (MEDIUM scalp suitability)
2025-07-15 06:33:40,725 - core.llm_orchestrator - INFO - ✅ Completed prompt: market_regime
2025-07-15 06:33:40,726 - core.llm_orchestrator - INFO - 🚀 PARALLEL LLM cycle completed in 9.53s - 2 prompts executed concurrently
2025-07-15 06:33:40,726 - core.vote_aggregator - INFO - 🗳️ Vote aggregation: WAIT (95.0%) - Clear WAIT signal (weight: 1.1, consensus: 100.0%)
2025-07-15 06:33:40,726 - core.llm_orchestrator - INFO - 🗳️ Aggregated Decision: WAIT (95.0%) - Clear WAIT signal (weight: 1.1, consensus: 100.0%)
2025-07-15 06:33:40,726 - core.llm_orchestrator - INFO - 🗳️ Vote Breakdown: WAIT: 1.1
2025-07-15 06:34:01,136 - core.llm_orchestrator - INFO - 🔍 FULL MODE: Executing all 8 prompts in PARALLEL for comprehensive analysis
2025-07-15 06:34:01,137 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-15 06:34:01,138 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-15 06:34:01,138 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-15 06:34:01,139 - core.llm_orchestrator - INFO - 🚀 Submitted 2 prompts for parallel execution
2025-07-15 06:34:01,140 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-15 06:34:01,139 - llama.lmstudio_runner - INFO - 💬 User Prompt: ...
2025-07-15 06:34:01,140 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🎯 ENTRY TIMING SPECIALIST - DOGE/USDT:USDT

🎯 SIGNAL ANALYSIS:
ML Ensemble: NEUTRAL (50.0%)
Technical: NEUTRAL | Momentum: 0.00
Volume: PENDING | Orderflow: NEUTRAL

📊 MARKET MICROSTRUCTURE:
Bid/Ask: $0.170000/$0.170000
Spread: 0.100% | L2 Imbalance: 0.0%
Recent Flow: BALANCED | Volume Spike: False

📈 KEY LEVELS:
Support: $0.170000
Resistance: $0.170000
Distance to Support: 0.00%

⏰ TIMING FACTORS:
- Price near key levels: NEUTRAL
- Volume confirmation: PENDING
- Spread conditions: FAVORABLE
- M...
2025-07-15 06:34:01,140 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-15 06:34:01,141 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-15 06:34:04,770 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 213 chars
2025-07-15 06:34:04,771 - llama.lmstudio_runner - INFO - 📄 Response Preview: ```json
{
  "action": "LONG",
  "confidence": 90,
  "entry_reason": "Market broke through the resistance level at +12 pips.",
  "take_profit": 2.5,
  "stop_loss": 1.7,
  "hold_time": "3min",
  "leverage": 40
}
```...
2025-07-15 06:34:04,771 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 174, Completion: 94, Total: 268
2025-07-15 06:34:04,772 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['action', 'confidence', 'entry_reason', 'take_profit', 'stop_loss', 'hold_time', 'leverage']
2025-07-15 06:34:04,772 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['ACTION', 'CONFIDENCE', 'ENTRY_REASON', 'TAKE_PROFIT', 'STOP_LOSS', 'HOLD_TIME', 'LEVERAGE']
2025-07-15 06:34:04,772 - core.llm_response_parsers - INFO - Risk assessment parsed: REJECTED (Risk: 50)
2025-07-15 06:34:04,773 - core.llm_orchestrator - INFO - ✅ Completed prompt: risk_assessment
2025-07-15 06:34:06,355 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 293 chars
2025-07-15 06:34:06,355 - llama.lmstudio_runner - INFO - 📄 Response Preview: {
  "action": "ENTER",
  "entry_type": "LIMIT",
  "confidence": 92,
  "wait_for": null,
  "max_wait_seconds": 0,
  "reasoning": "Market conditions are favorable with a narrow spread and balanced recent flow. Momentum is neutral but the risk/reward ratio of 2:1 justifies an immediate entry."
}...
2025-07-15 06:34:06,355 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 610, Completion: 100, Total: 710
2025-07-15 06:34:06,356 - core.llm_response_parsers - WARNING - 🚨 JSON decode error: Expecting ',' delimiter: line 1 column 247 (char 246)
2025-07-15 06:34:06,356 - core.llm_response_parsers - WARNING - 🔍 Problematic JSON: { "action": "ENTER", "entry_type": "LIMIT", "confidence": 92, "wait_for": null, "max_wait_seconds": 0, "reasoning": "Market conditions are favorable with a narrow spread and balanced recent flow. Mome...
2025-07-15 06:34:06,357 - core.llm_response_parsers - INFO - ✅ Aggressive cleanup extracted: ['action', 'confidence', 'reasoning']
2025-07-15 06:34:06,357 - core.llm_response_parsers - INFO - ✅ JSON fixed with aggressive cleanup
2025-07-15 06:34:06,357 - core.llm_response_parsers - INFO - Entry timing parsed: WAIT (LIMIT order)
2025-07-15 06:34:06,358 - core.llm_orchestrator - INFO - ✅ Completed prompt: entry_timing
2025-07-15 06:34:06,359 - core.llm_orchestrator - INFO - 🚀 PARALLEL LLM cycle completed in 5.22s - 2 prompts executed concurrently
2025-07-15 06:34:06,360 - core.vote_aggregator - INFO - 🗳️ Vote aggregation: WAIT (46.0%) - Strong WAIT signal (2.3) → WAIT
2025-07-15 06:34:06,361 - core.llm_orchestrator - INFO - 🗳️ Aggregated Decision: WAIT (46.0%) - Strong WAIT signal (2.3) → WAIT
2025-07-15 06:34:06,361 - core.llm_orchestrator - INFO - 🗳️ Vote Breakdown: WAIT: 2.3
2025-07-15 06:34:31,619 - core.llm_orchestrator - INFO - 🔍 FULL MODE: Executing all 8 prompts in PARALLEL for comprehensive analysis
2025-07-15 06:34:31,620 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-15 06:34:31,621 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-15 06:34:31,621 - llama.lmstudio_runner - INFO - 💬 User Prompt: 📊 MARKET REGIME ANALYST - DOGE/USDT:USDT

🔍 MULTI-TIMEFRAME ANALYSIS:
1m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
5m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
15m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%

📈 VOLATILITY METRICS:
ATR: 0.001000 | Spread: 0.100%
Volume Ratio: 1.0x | Price Range: 1.00%

🎯 REGIME CLASSIFICATION:
- TRENDING_BULL: Strong upward momentum, high volume, clear direction
- TRENDING_BEAR: Strong downward momentum, high volume, clear direction
- RANGING_TIGHT: Low volatility, narrow pric...
2025-07-15 06:34:31,621 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-15 06:34:31,622 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-15 06:34:31,622 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-15 06:34:31,624 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🎯 ENTRY TIMING SPECIALIST - DOGE/USDT:USDT

🎯 SIGNAL ANALYSIS:
ML Ensemble: NEUTRAL (50.0%)
Technical: NEUTRAL | Momentum: 0.00
Volume: PENDING | Orderflow: NEUTRAL

📊 MARKET MICROSTRUCTURE:
Bid/Ask: $0.170000/$0.170000
Spread: 0.100% | L2 Imbalance: 0.0%
Recent Flow: BALANCED | Volume Spike: False

📈 KEY LEVELS:
Support: $0.170000
Resistance: $0.170000
Distance to Support: 0.00%

⏰ TIMING FACTORS:
- Price near key levels: NEUTRAL
- Volume confirmation: PENDING
- Spread conditions: FAVORABLE
- M...
2025-07-15 06:34:31,623 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-15 06:34:31,623 - core.llm_orchestrator - INFO - 🚀 Submitted 4 prompts for parallel execution
2025-07-15 06:34:31,622 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-15 06:34:31,625 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a conservative futures trading analyst. Analyze market data carefully and provide measured recommendations. Use format: DECISION: [LONG/SHORT/WAIT], CONFIDENCE: [50-100]%, TAKE_PROFIT: [%], ST...
2025-07-15 06:34:31,624 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-15 06:34:31,625 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🔄 STRATEGY ADAPTATION SPECIALIST

📊 PERFORMANCE ANALYSIS (Last 24h):
Trades: 0 | Win Rate: 50.0%
Avg Profit: 0.80% | Avg Loss: -0.30%
Sharpe Ratio: 1.00 | Max Drawdown: 0.0%
Total PnL: $0.00 | ROI: 0.0%

🎯 CURRENT STRATEGY:
Risk per Trade: 2.0% | Avg Hold Time: 8.0min
Entry Threshold: 70% | Exit Threshold: 60%
Position Size Method: FIXED_RISK | Max Positions: 3

📈 MARKET REGIME: UNKNOWN
Regime Confidence: 50.0%
Scalp Suitability: MEDIUM

🔧 ADAPTATION FACTORS:
- Win rate trending: DOWN
- Drawdown...
2025-07-15 06:34:31,625 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🔍 INTELLIGENT OPPORTUNITY SCANNER - Account Health Aware

🚨 ACCOUNT HEALTH STATUS: HEALTHY
💰 Balance: $1000.00 | Health: Healthy - Normal Trading
📊 Current Exposure: $0.00 (0.0% of account)
🎯 Trading Allowed: YES

📊 SYMBOL ANALYSIS:
• DOGE/USDT:USDT: $0.000000 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• BTC/USDT:USDT: $95000.000000 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• ETH/USDT:USDT: $3500.000000 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• SOL/USDT:USDT: $200.000000 | Mom: +0.0% | Vol: 1.0x |...
2025-07-15 06:34:31,624 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-15 06:34:31,625 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-15 06:34:31,626 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-15 06:34:35,753 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 218 chars
2025-07-15 06:34:35,753 - llama.lmstudio_runner - INFO - 📄 Response Preview: ```json
{
  "REGIME": "RANGING_TIGHT",
  "CONFIDENCE": 75,
  "SCALP_SUITABILITY": "MEDIUM",
  "RECOMMENDED_TIMEFRAME": "1m",
  "RISK_LEVEL": "MEDIUM",
  "REASONING": "Consolidation phase with moderate volatility"
}
```...
2025-07-15 06:34:35,753 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 705, Completion: 108, Total: 813
2025-07-15 06:34:35,754 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['REGIME', 'CONFIDENCE', 'SCALP_SUITABILITY', 'RECOMMENDED_TIMEFRAME', 'RISK_LEVEL', 'REASONING']
2025-07-15 06:34:35,754 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['REGIME', 'CONFIDENCE', 'SCALP_SUITABILITY', 'RECOMMENDED_TIMEFRAME', 'RISK_LEVEL', 'REASONING']
2025-07-15 06:34:35,754 - core.llm_response_parsers - INFO - Market regime parsed: RANGING_TIGHT (MEDIUM scalp suitability)
2025-07-15 06:34:35,754 - core.llm_orchestrator - INFO - ✅ Completed prompt: market_regime
2025-07-15 06:34:36,970 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 249 chars
2025-07-15 06:34:36,970 - llama.lmstudio_runner - INFO - 📄 Response Preview: ```json
{"action":"ENTER_NOW","entry_type":"LIMIT","confidence":90,"wait_for":"SPREAD_CONDITIONS","max_wait_seconds":30,"reasoning":"Spread conditions are favorable, and the risk/reward ratio is optimal for a market entry with high confidence."}
```...
2025-07-15 06:34:36,971 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 610, Completion: 76, Total: 686
2025-07-15 06:34:36,971 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['action', 'entry_type', 'confidence', 'wait_for', 'max_wait_seconds', 'reasoning']
2025-07-15 06:34:36,971 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['ACTION', 'ENTRY_TYPE', 'CONFIDENCE', 'WAIT_FOR', 'MAX_WAIT_SECONDS', 'REASONING']
2025-07-15 06:34:36,972 - core.llm_response_parsers - INFO - Entry timing parsed: ENTER_NOW (LIMIT order)
2025-07-15 06:34:36,972 - core.llm_action_executors - INFO - Executing entry action: ENTER_NOW (LIMIT order)
2025-07-15 06:34:36,972 - core.llm_action_executors - WARNING - No position capacity available - skipping entry
2025-07-15 06:34:36,972 - core.llm_orchestrator - INFO - ✅ Completed prompt: entry_timing
2025-07-15 06:34:38,507 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 255 chars
2025-07-15 06:34:38,507 - llama.lmstudio_runner - INFO - 📄 Response Preview: ```json
{
  "risk_adjustment": [0.5, 2.0],
  "hold_time_target": ["30s", "5min"],
  "entry_threshold": [70, 60],
  "reasoning": "Maintain current strategy - performing well with a moderate win rate and acceptable drawdown level.",
  "confidence": 75
}
```...
2025-07-15 06:34:38,507 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 619, Completion: 98, Total: 717
2025-07-15 06:34:38,508 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['risk_adjustment', 'hold_time_target', 'entry_threshold', 'reasoning', 'confidence']
2025-07-15 06:34:38,508 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['RISK_ADJUSTMENT', 'HOLD_TIME_TARGET', 'ENTRY_THRESHOLD', 'REASONING', 'CONFIDENCE']
2025-07-15 06:34:38,508 - core.llm_response_parsers - INFO - Strategy adaptation parsed: Risk adj 1.0x
2025-07-15 06:34:38,508 - core.llm_action_executors - INFO - Applying strategy adaptations: Risk 1.0x, Hold time 8min
2025-07-15 06:34:38,508 - core.llm_orchestrator - INFO - ✅ Completed prompt: strategy_adaptation
2025-07-15 06:34:42,213 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 993 chars
2025-07-15 06:34:42,213 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: SHORT, CONFIDENCE: 75%, TAKE_PROFIT: 60%, STOP_LOSS: 90%, EXPLANATION: Given the neutral sector momentum and normal volatility levels with an average volume profile indicating a stable market condition. The account health is good, but to preserve capital while still taking advantage of pot...
2025-07-15 06:34:42,213 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 933, Completion: 230, Total: 1163
2025-07-15 06:34:42,214 - core.llm_response_parsers - INFO - 🔄 No JSON found, trying structured text parsing
2025-07-15 06:34:42,214 - core.llm_response_parsers - INFO - Parsed structured text: {'DECISION': 'SHORT', 'CONFIDENCE': 75.0, 'TAKE_PROFIT': 60.0, 'STOP_LOSS': 90.0, 'EXPLANATION': "GIVEN THE NEUTRAL SECTOR MOMENTUM AND NORMAL VOLATILITY LEVELS WITH AN AVERAGE VOLUME PROFILE INDICATING A STABLE MARKET CONDITION. THE ACCOUNT HEALTH IS GOOD, BUT TO PRESERVE CAPITAL WHILE STILL TAKING ADVANTAGE OF POTENTIAL DOWNWARD MOVEMENTS IN CRYPTOCURRENCIES LIKE DOGE/USDT WHICH CURRENTLY SHOWS NO SIGNIFICANT CHANGE (MOM +0.0%), IT'S PRUDENT TO SHORT THE ASSET WITH HIGH SETUP QUALITY AND LIQUIDITY AS INDICATED BY ITS MEDIUM-LEVEL MARKET DEPTH, ENSURING A RISK REWARD RATIO THAT IS SLIGHTLY HIGHER THAN USUAL DUE TO ACCOUNT HEALTH CONSIDERATIONS BUT STILL WITHIN CONSERVATIVE LIMITS FOR THIS STABLE ENVIRONMENT. THE TAKE PROFIT AT 60% WOULD CAPITALIZE ON ANY POTENTIAL UPSIDE WHILE THE STOP LOSS SET AT 90% PROVIDES AMPLE PROTECTION AGAINST SIGNIFICANT LOSSES IN CASE OF AN UNEXPECTED MARKET TURN, ALIGNING WITH A MODERATE RISK PROFILE AND MAINTAINING ACCOUNT HEALTH AS PRIORITY OVER AGGRESSIVE PROFITS.", 'ACTION': 'ENTER_NOW'}
2025-07-15 06:34:42,214 - core.llm_response_parsers - INFO - ✅ Structured text parsing successful: ['DECISION', 'CONFIDENCE', 'TAKE_PROFIT', 'STOP_LOSS', 'EXPLANATION', 'ACTION']
2025-07-15 06:34:42,214 - core.llm_response_parsers - INFO - Opportunity scanner parsed: MOMENTUM (REVERSAL)
2025-07-15 06:34:42,215 - core.llm_orchestrator - INFO - ✅ Completed prompt: opportunity_scanner
2025-07-15 06:34:42,215 - core.llm_orchestrator - INFO - 🚀 PARALLEL LLM cycle completed in 10.60s - 4 prompts executed concurrently
2025-07-15 06:34:42,216 - core.vote_aggregator - INFO - 🗳️ Vote aggregation: WAIT (33.0%) - Strong WAIT signal (1.6) → WAIT
2025-07-15 06:34:42,216 - core.llm_orchestrator - INFO - 🗳️ Aggregated Decision: WAIT (33.0%) - Strong WAIT signal (1.6) → WAIT
2025-07-15 06:34:42,216 - core.llm_orchestrator - INFO - 🗳️ Vote Breakdown: SHORT: 1.5, WAIT: 1.6
2025-07-15 06:35:01,137 - core.llm_orchestrator - INFO - 🔍 FULL MODE: Executing all 8 prompts in PARALLEL for comprehensive analysis
2025-07-15 06:35:01,138 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-15 06:35:01,139 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-15 06:35:01,139 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-15 06:35:01,139 - core.llm_orchestrator - INFO - 🚀 Submitted 2 prompts for parallel execution
2025-07-15 06:35:01,140 - llama.lmstudio_runner - INFO - 💬 User Prompt: ...
2025-07-15 06:35:01,140 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-15 06:35:01,141 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-15 06:35:01,141 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🎯 ENTRY TIMING SPECIALIST - DOGE/USDT:USDT

🎯 SIGNAL ANALYSIS:
ML Ensemble: NEUTRAL (50.0%)
Technical: NEUTRAL | Momentum: 0.00
Volume: PENDING | Orderflow: NEUTRAL

📊 MARKET MICROSTRUCTURE:
Bid/Ask: $0.170000/$0.170000
Spread: 0.100% | L2 Imbalance: 0.0%
Recent Flow: BALANCED | Volume Spike: False

📈 KEY LEVELS:
Support: $0.170000
Resistance: $0.170000
Distance to Support: 0.00%

⏰ TIMING FACTORS:
- Price near key levels: NEUTRAL
- Volume confirmation: PENDING
- Spread conditions: FAVORABLE
- M...
2025-07-15 06:35:01,142 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-15 06:35:04,804 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 230 chars
2025-07-15 06:35:04,804 - llama.lmstudio_runner - INFO - 📄 Response Preview: ```json
{
  "action": "LONG",
  "confidence": 90,
  "entry_reason": "Breakout above resistance level at $1350 with strong volume indicators.",
  "take_profit": 2.5,
  "stop_loss": 1.7,
  "hold_time": "4min",
  "leverage": 30
}
```...
2025-07-15 06:35:04,805 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 174, Completion: 98, Total: 272
2025-07-15 06:35:04,805 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['action', 'confidence', 'entry_reason', 'take_profit', 'stop_loss', 'hold_time', 'leverage']
2025-07-15 06:35:04,805 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['ACTION', 'CONFIDENCE', 'ENTRY_REASON', 'TAKE_PROFIT', 'STOP_LOSS', 'HOLD_TIME', 'LEVERAGE']
2025-07-15 06:35:04,806 - core.llm_response_parsers - INFO - Risk assessment parsed: REJECTED (Risk: 50)
2025-07-15 06:35:04,806 - core.llm_orchestrator - INFO - ✅ Completed prompt: risk_assessment
2025-07-15 06:35:07,102 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 585 chars
2025-07-15 06:35:07,102 - llama.lmstudio_runner - INFO - 📄 Response Preview: {
  "action": "ENTER_NOW",
  "entry_type": "LIMIT",
  "confidence": 92,
  "wait_for": null,
  "max_wait_seconds": 0,
  "reasoning": "Based on the current market microstructure and historical context showing a pattern of WAIT signals with no recent price movement or volume spike to confirm an entry o...
2025-07-15 06:35:07,102 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 610, Completion: 152, Total: 762
2025-07-15 06:35:07,102 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['action', 'entry_type', 'confidence', 'wait_for', 'max_wait_seconds', 'reasoning']
2025-07-15 06:35:07,103 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['ACTION', 'ENTRY_TYPE', 'CONFIDENCE', 'WAIT_FOR', 'MAX_WAIT_SECONDS', 'REASONING']
2025-07-15 06:35:07,103 - core.llm_response_parsers - INFO - Entry timing parsed: ENTER_NOW (LIMIT order)
2025-07-15 06:35:07,103 - core.llm_action_executors - INFO - Executing entry action: ENTER_NOW (LIMIT order)
2025-07-15 06:35:07,103 - core.llm_action_executors - WARNING - No position capacity available - skipping entry
2025-07-15 06:35:07,103 - core.llm_orchestrator - INFO - ✅ Completed prompt: entry_timing
2025-07-15 06:35:07,104 - core.llm_orchestrator - INFO - 🚀 PARALLEL LLM cycle completed in 5.97s - 2 prompts executed concurrently
2025-07-15 06:35:07,104 - core.vote_aggregator - INFO - 🗳️ Vote aggregation: WAIT (54.4%) - Strong WAIT signal (2.7) → WAIT
2025-07-15 06:35:07,104 - core.llm_orchestrator - INFO - 🗳️ Aggregated Decision: WAIT (54.4%) - Strong WAIT signal (2.7) → WAIT
2025-07-15 06:35:07,105 - core.llm_orchestrator - INFO - 🗳️ Vote Breakdown: WAIT: 2.7
2025-07-15 06:35:31,128 - core.llm_orchestrator - INFO - 🔍 FULL MODE: Executing all 8 prompts in PARALLEL for comprehensive analysis
2025-07-15 06:35:31,130 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-15 06:35:31,130 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-15 06:35:31,131 - llama.lmstudio_runner - INFO - 💬 User Prompt: 📊 MARKET REGIME ANALYST - DOGE/USDT:USDT

🔍 MULTI-TIMEFRAME ANALYSIS:
1m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
5m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
15m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%

📈 VOLATILITY METRICS:
ATR: 0.001000 | Spread: 0.100%
Volume Ratio: 1.0x | Price Range: 1.00%

🎯 REGIME CLASSIFICATION:
- TRENDING_BULL: Strong upward momentum, high volume, clear direction
- TRENDING_BEAR: Strong downward momentum, high volume, clear direction
- RANGING_TIGHT: Low volatility, narrow pric...
2025-07-15 06:35:31,131 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-15 06:35:31,131 - core.llm_orchestrator - INFO - 🚀 Submitted 2 prompts for parallel execution
2025-07-15 06:35:31,131 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-15 06:35:31,132 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-15 06:35:31,133 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🎯 ENTRY TIMING SPECIALIST - DOGE/USDT:USDT

🎯 SIGNAL ANALYSIS:
ML Ensemble: NEUTRAL (50.0%)
Technical: NEUTRAL | Momentum: 0.00
Volume: PENDING | Orderflow: NEUTRAL

📊 MARKET MICROSTRUCTURE:
Bid/Ask: $0.170000/$0.170000
Spread: 0.100% | L2 Imbalance: 0.0%
Recent Flow: BALANCED | Volume Spike: False

📈 KEY LEVELS:
Support: $0.170000
Resistance: $0.170000
Distance to Support: 0.00%

⏰ TIMING FACTORS:
- Price near key levels: NEUTRAL
- Volume confirmation: PENDING
- Spread conditions: FAVORABLE
- M...
2025-07-15 06:35:31,134 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-15 06:35:35,136 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 218 chars
2025-07-15 06:35:35,137 - llama.lmstudio_runner - INFO - 📄 Response Preview: ```json
{
  "REGIME": "RANGING_TIGHT",
  "CONFIDENCE": 75,
  "SCALP_SUITABILITY": "MEDIUM",
  "RECOMMENDED_TIMEFRAME": "1m",
  "RISK_LEVEL": "MEDIUM",
  "REASONING": "Consolidation phase with moderate volatility"
}
```...
2025-07-15 06:35:35,137 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 705, Completion: 108, Total: 813
2025-07-15 06:35:35,137 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['REGIME', 'CONFIDENCE', 'SCALP_SUITABILITY', 'RECOMMENDED_TIMEFRAME', 'RISK_LEVEL', 'REASONING']
2025-07-15 06:35:35,137 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['REGIME', 'CONFIDENCE', 'SCALP_SUITABILITY', 'RECOMMENDED_TIMEFRAME', 'RISK_LEVEL', 'REASONING']
2025-07-15 06:35:35,137 - core.llm_response_parsers - INFO - Market regime parsed: RANGING_TIGHT (MEDIUM scalp suitability)
2025-07-15 06:35:35,138 - core.llm_orchestrator - INFO - ✅ Completed prompt: market_regime
2025-07-15 06:35:36,624 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 275 chars
2025-07-15 06:35:36,624 - llama.lmstudio_runner - INFO - 📄 Response Preview: ```json
{
  "action": "ENTER_NOW",
  "entry_type": "LIMIT",
  "confidence": 85,
  "wait_for": null,
  "max_wait_seconds": 0,
  "reasoning": "Market conditions are neutral with a favorable spread and no significant volume spike. Momentum is aligned for potential entry."
}
```...
2025-07-15 06:35:36,624 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 610, Completion: 95, Total: 705
2025-07-15 06:35:36,625 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['action', 'entry_type', 'confidence', 'wait_for', 'max_wait_seconds', 'reasoning']
2025-07-15 06:35:36,625 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['ACTION', 'ENTRY_TYPE', 'CONFIDENCE', 'WAIT_FOR', 'MAX_WAIT_SECONDS', 'REASONING']
2025-07-15 06:35:36,625 - core.llm_response_parsers - INFO - Entry timing parsed: ENTER_NOW (LIMIT order)
2025-07-15 06:35:36,625 - core.llm_action_executors - INFO - Executing entry action: ENTER_NOW (LIMIT order)
2025-07-15 06:35:36,625 - core.llm_action_executors - WARNING - No position capacity available - skipping entry
2025-07-15 06:35:36,625 - core.llm_orchestrator - INFO - ✅ Completed prompt: entry_timing
2025-07-15 06:35:36,626 - core.llm_orchestrator - INFO - 🚀 PARALLEL LLM cycle completed in 5.50s - 2 prompts executed concurrently
2025-07-15 06:35:36,626 - core.vote_aggregator - INFO - 🗳️ Vote aggregation: WAIT (95.0%) - Clear WAIT signal (weight: 1.4, consensus: 100.0%)
2025-07-15 06:35:36,627 - core.llm_orchestrator - INFO - 🗳️ Aggregated Decision: WAIT (95.0%) - Clear WAIT signal (weight: 1.4, consensus: 100.0%)
2025-07-15 06:35:36,627 - core.llm_orchestrator - INFO - 🗳️ Vote Breakdown: WAIT: 1.4
2025-07-15 06:36:01,122 - core.llm_orchestrator - INFO - 🔍 FULL MODE: Executing all 8 prompts in PARALLEL for comprehensive analysis
2025-07-15 06:36:01,123 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-15 06:36:01,124 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-15 06:36:01,124 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-15 06:36:01,125 - llama.lmstudio_runner - INFO - 💬 User Prompt: ...
2025-07-15 06:36:01,125 - core.llm_orchestrator - INFO - 🚀 Submitted 3 prompts for parallel execution
2025-07-15 06:36:01,125 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-15 06:36:01,125 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-15 06:36:01,125 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-15 06:36:01,125 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🎯 ENTRY TIMING SPECIALIST - DOGE/USDT:USDT

🎯 SIGNAL ANALYSIS:
ML Ensemble: NEUTRAL (50.0%)
Technical: NEUTRAL | Momentum: 0.00
Volume: PENDING | Orderflow: NEUTRAL

📊 MARKET MICROSTRUCTURE:
Bid/Ask: $0.170000/$0.170000
Spread: 0.100% | L2 Imbalance: 0.0%
Recent Flow: BALANCED | Volume Spike: False

📈 KEY LEVELS:
Support: $0.170000
Resistance: $0.170000
Distance to Support: 0.00%

⏰ TIMING FACTORS:
- Price near key levels: NEUTRAL
- Volume confirmation: PENDING
- Spread conditions: FAVORABLE
- M...
2025-07-15 06:36:01,126 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a conservative futures trading analyst. Analyze market data carefully and provide measured recommendations. Use format: DECISION: [LONG/SHORT/WAIT], CONFIDENCE: [50-100]%, TAKE_PROFIT: [%], ST...
2025-07-15 06:36:01,126 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-15 06:36:01,127 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🔍 INTELLIGENT OPPORTUNITY SCANNER - Account Health Aware

🚨 ACCOUNT HEALTH STATUS: HEALTHY
💰 Balance: $1000.00 | Health: Healthy - Normal Trading
📊 Current Exposure: $0.00 (0.0% of account)
🎯 Trading Allowed: YES

📊 SYMBOL ANALYSIS:
• DOGE/USDT:USDT: $0.000000 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• BTC/USDT:USDT: $95000.000000 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• ETH/USDT:USDT: $3500.000000 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• SOL/USDT:USDT: $200.000000 | Mom: +0.0% | Vol: 1.0x |...
2025-07-15 06:36:01,127 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-15 06:36:07,296 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 1069 chars
2025-07-15 06:36:07,296 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: SHORT, CONFIDENCE: 70%, TAKE_PROFIT: 2%, STOP_LOSS: -3%, EXPLANATION: The market is currently in a neutral sector momentum phase with normal overall volatility and average volume profile. Given the account's healthy status, it allows for moderate risk exposure while adhering to preservatio...
2025-07-15 06:36:07,296 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 933, Completion: 244, Total: 1177
2025-07-15 06:36:07,297 - core.llm_response_parsers - INFO - 🔄 No JSON found, trying structured text parsing
2025-07-15 06:36:07,297 - core.llm_response_parsers - INFO - Parsed structured text: {'DECISION': 'SHORT', 'CONFIDENCE': 70.0, 'TAKE_PROFIT': 2.0, 'STOP_LOSS': -3.0, 'EXPLANATION': "THE MARKET IS CURRENTLY IN A NEUTRAL SECTOR MOMENTUM PHASE WITH NORMAL OVERALL VOLATILITY AND AVERAGE VOLUME PROFILE. GIVEN THE ACCOUNT'S HEALTHY STATUS, IT ALLOWS FOR MODERATE RISK EXPOSURE WHILE ADHERING TO PRESERVATION CRITERIA OF NOT EXCEEDING 2% PER TRADE OR MORE THAN 70% TOTAL EXPOSURE. THE DOGE/USDT SYMBOL SHOWS A SLIGHT POSITIVE MOM (MOVING AVERAGE) BUT WITH NO SIGNIFICANT MOMENTUM INDICATORS THAT JUSTIFY AN ENTRY POSITION, THUS THE CONSERVATIVE DECISION IS MADE TOWARDS SHORT SELLING TO CAPITALIZE ON POTENTIAL DOWNWARD PRICE MOVEMENTS WITHIN THIS STABLE MARKET ENVIRONMENT WITHOUT OVEREXPOSING RISK LEVELS AS PER ACCOUNT HEALTH-ADJUSTED CRITERIA. THE TAKE PROFIT AND STOP LOSS ARE SET AT 2% (TO PROTECT AGAINST MINOR UNEXPECTED UPWARDS MOVEMENT) AND -3% RESPECTIVELY, ENSURING A CONSERVATIVE APPROACH TO CAPITALIZE ON THE SLIGHT NEGATIVE MOM WHILE MAINTAINING CONTROL OF POTENTIAL LOSSES WITHIN ACCEPTABLE BOUNDS FOR AN OVERALL RISK BUDGET ALIGNED WITH ACCOUNT HEALTH STATUS.", 'ACTION': 'ENTER_NOW'}
2025-07-15 06:36:07,297 - core.llm_response_parsers - INFO - ✅ Structured text parsing successful: ['DECISION', 'CONFIDENCE', 'TAKE_PROFIT', 'STOP_LOSS', 'EXPLANATION', 'ACTION']
2025-07-15 06:36:07,297 - core.llm_response_parsers - INFO - Opportunity scanner parsed: MOMENTUM (REVERSAL)
2025-07-15 06:36:07,298 - core.llm_orchestrator - INFO - ✅ Completed prompt: opportunity_scanner
2025-07-15 06:36:10,231 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 640 chars
2025-07-15 06:36:10,232 - llama.lmstudio_runner - INFO - 📄 Response Preview: {
  "action": "ENTER_NOW",
  "entry_type": "LIMIT",
  "confidence": 90,
  "wait_for": null,
  "max_wait_seconds": 30,
  "reasoning": "Despite the market being NEUTRAL and spread conditions favorable with a risk/reward ratio of 2:1, there is no significant volume confirmation. However, given that we ...
2025-07-15 06:36:10,232 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 610, Completion: 192, Total: 802
2025-07-15 06:36:10,232 - core.llm_response_parsers - WARNING - 🚨 JSON decode error: Expecting ',' delimiter: line 1 column 217 (char 216)
2025-07-15 06:36:10,233 - core.llm_response_parsers - WARNING - 🔍 Problematic JSON: { "action": "ENTER_NOW", "entry_type": "LIMIT", "confidence": 90, "wait_for": null, "max_wait_seconds": 30, "reasoning": "Despite the market being NEUTRAL and spread conditions favorable with a risk/r...
2025-07-15 06:36:10,233 - core.llm_response_parsers - INFO - ✅ Aggressive cleanup extracted: ['action', 'confidence', 'reasoning']
2025-07-15 06:36:10,233 - core.llm_response_parsers - INFO - ✅ JSON fixed with aggressive cleanup
2025-07-15 06:36:10,234 - core.llm_response_parsers - INFO - Entry timing parsed: WAIT (LIMIT order)
2025-07-15 06:36:10,234 - core.llm_orchestrator - INFO - ✅ Completed prompt: entry_timing
2025-07-15 06:36:11,492 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 225 chars
2025-07-15 06:36:11,493 - llama.lmstudio_runner - INFO - 📄 Response Preview: ```json
{
  "action": "LONG",
  "confidence": 90,
  "entry_reason": "Breakout above the resistance level with a strong volume increase.",
  "take_profit": 2.5,
  "stop_loss": 1.0,
  "hold_time": "3min",
  "leverage": 40
}
```...
2025-07-15 06:36:11,493 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 174, Completion: 93, Total: 267
2025-07-15 06:36:11,493 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['action', 'confidence', 'entry_reason', 'take_profit', 'stop_loss', 'hold_time', 'leverage']
2025-07-15 06:36:11,493 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['ACTION', 'CONFIDENCE', 'ENTRY_REASON', 'TAKE_PROFIT', 'STOP_LOSS', 'HOLD_TIME', 'LEVERAGE']
2025-07-15 06:36:11,494 - core.llm_response_parsers - INFO - Risk assessment parsed: REJECTED (Risk: 50)
2025-07-15 06:36:11,494 - core.llm_orchestrator - INFO - ✅ Completed prompt: risk_assessment
2025-07-15 06:36:11,495 - core.llm_orchestrator - INFO - 🚀 PARALLEL LLM cycle completed in 10.37s - 3 prompts executed concurrently
2025-07-15 06:36:11,496 - core.vote_aggregator - INFO - 🗳️ Vote aggregation: WAIT (46.0%) - Strong WAIT signal (2.3) → WAIT
2025-07-15 06:36:11,496 - core.llm_orchestrator - INFO - 🗳️ Aggregated Decision: WAIT (46.0%) - Strong WAIT signal (2.3) → WAIT
2025-07-15 06:36:11,496 - core.llm_orchestrator - INFO - 🗳️ Vote Breakdown: SHORT: 1.4, WAIT: 2.3
2025-07-15 06:36:31,152 - core.llm_orchestrator - INFO - 🔍 FULL MODE: Executing all 8 prompts in PARALLEL for comprehensive analysis
2025-07-15 06:36:31,153 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-15 06:36:31,154 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-15 06:36:31,154 - llama.lmstudio_runner - INFO - 💬 User Prompt: 📊 MARKET REGIME ANALYST - DOGE/USDT:USDT

🔍 MULTI-TIMEFRAME ANALYSIS:
1m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
5m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
15m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%

📈 VOLATILITY METRICS:
ATR: 0.001000 | Spread: 0.100%
Volume Ratio: 1.0x | Price Range: 1.00%

🎯 REGIME CLASSIFICATION:
- TRENDING_BULL: Strong upward momentum, high volume, clear direction
- TRENDING_BEAR: Strong downward momentum, high volume, clear direction
- RANGING_TIGHT: Low volatility, narrow pric...
2025-07-15 06:36:31,154 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-15 06:36:31,155 - core.llm_orchestrator - INFO - 🚀 Submitted 2 prompts for parallel execution
2025-07-15 06:36:31,155 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-15 06:36:31,155 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-15 06:36:31,156 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🎯 ENTRY TIMING SPECIALIST - DOGE/USDT:USDT

🎯 SIGNAL ANALYSIS:
ML Ensemble: NEUTRAL (50.0%)
Technical: NEUTRAL | Momentum: 0.00
Volume: PENDING | Orderflow: NEUTRAL

📊 MARKET MICROSTRUCTURE:
Bid/Ask: $0.170000/$0.170000
Spread: 0.100% | L2 Imbalance: 0.0%
Recent Flow: BALANCED | Volume Spike: False

📈 KEY LEVELS:
Support: $0.170000
Resistance: $0.170000
Distance to Support: 0.00%

⏰ TIMING FACTORS:
- Price near key levels: NEUTRAL
- Volume confirmation: PENDING
- Spread conditions: FAVORABLE
- M...
2025-07-15 06:36:31,157 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-15 06:36:35,232 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 368 chars
2025-07-15 06:36:35,233 - llama.lmstudio_runner - INFO - 📄 Response Preview: {
  "action": "ENTER_NOW",
  "entry_type": "LIMIT",
  "confidence": 90,
  "wait_for": null,
  "max_wait_seconds": 30,
  "reasoning": "Market conditions are favorable with a NEUTRAL signal from ML Ensemble and Technical analysis. The spread is FAVORABLE at 0.100%, volume confirmation pending but expe...
2025-07-15 06:36:35,233 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 610, Completion: 119, Total: 729
2025-07-15 06:36:35,233 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['action', 'entry_type', 'confidence', 'wait_for', 'max_wait_seconds', 'reasoning']
2025-07-15 06:36:35,233 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['ACTION', 'ENTRY_TYPE', 'CONFIDENCE', 'WAIT_FOR', 'MAX_WAIT_SECONDS', 'REASONING']
2025-07-15 06:36:35,233 - core.llm_response_parsers - INFO - Entry timing parsed: ENTER_NOW (LIMIT order)
2025-07-15 06:36:35,233 - core.llm_action_executors - INFO - Executing entry action: ENTER_NOW (LIMIT order)
2025-07-15 06:36:35,234 - core.llm_action_executors - WARNING - No position capacity available - skipping entry
2025-07-15 06:36:35,234 - core.llm_orchestrator - INFO - ✅ Completed prompt: entry_timing
2025-07-15 06:36:37,230 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 307 chars
2025-07-15 06:36:37,231 - llama.lmstudio_runner - INFO - 📄 Response Preview: ```json
{
  "REGIME": "RANGING_TIGHT",
  "CONFIDENCE": 75,
  "SCALP_SUITABILITY": "MEDIUM",
  "RECOMMENDED_TIMEFRAME": "1m",
  "RISK_LEVEL": "MEDIUM",
  "REASONING": "Consolidation phase with moderate volatility, suitable for medium-risk scalping due to some challenges in clear trend identification....
2025-07-15 06:36:37,231 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 705, Completion: 127, Total: 832
2025-07-15 06:36:37,231 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['REGIME', 'CONFIDENCE', 'SCALP_SUITABILITY', 'RECOMMENDED_TIMEFRAME', 'RISK_LEVEL', 'REASONING']
2025-07-15 06:36:37,231 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['REGIME', 'CONFIDENCE', 'SCALP_SUITABILITY', 'RECOMMENDED_TIMEFRAME', 'RISK_LEVEL', 'REASONING']
2025-07-15 06:36:37,232 - core.llm_response_parsers - INFO - Market regime parsed: RANGING_TIGHT (MEDIUM scalp suitability)
2025-07-15 06:36:37,232 - core.llm_orchestrator - INFO - ✅ Completed prompt: market_regime
2025-07-15 06:36:37,233 - core.llm_orchestrator - INFO - 🚀 PARALLEL LLM cycle completed in 6.08s - 2 prompts executed concurrently
2025-07-15 06:36:37,233 - core.vote_aggregator - INFO - 🗳️ Vote aggregation: WAIT (95.0%) - Clear WAIT signal (weight: 1.5, consensus: 100.0%)
2025-07-15 06:36:37,233 - core.llm_orchestrator - INFO - 🗳️ Aggregated Decision: WAIT (95.0%) - Clear WAIT signal (weight: 1.5, consensus: 100.0%)
2025-07-15 06:36:37,233 - core.llm_orchestrator - INFO - 🗳️ Vote Breakdown: WAIT: 1.5
2025-07-15 06:37:01,151 - core.llm_orchestrator - INFO - 🔍 FULL MODE: Executing all 8 prompts in PARALLEL for comprehensive analysis
2025-07-15 06:37:01,152 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-15 06:37:01,153 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-15 06:37:01,153 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-15 06:37:01,153 - llama.lmstudio_runner - INFO - 💬 User Prompt: 📊 MARKET REGIME ANALYST - DOGE/USDT:USDT

🔍 MULTI-TIMEFRAME ANALYSIS:
1m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
5m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
15m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%

📈 VOLATILITY METRICS:
ATR: 0.001000 | Spread: 0.100%
Volume Ratio: 1.0x | Price Range: 1.00%

🎯 REGIME CLASSIFICATION:
- TRENDING_BULL: Strong upward momentum, high volume, clear direction
- TRENDING_BEAR: Strong downward momentum, high volume, clear direction
- RANGING_TIGHT: Low volatility, narrow pric...
2025-07-15 06:37:01,153 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-15 06:37:01,154 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-15 06:37:01,154 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-15 06:37:01,154 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-15 06:37:01,154 - core.llm_orchestrator - INFO - 🚀 Submitted 5 prompts for parallel execution
2025-07-15 06:37:01,154 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-15 06:37:01,155 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-15 06:37:01,155 - llama.lmstudio_runner - INFO - 💬 User Prompt: ...
2025-07-15 06:37:01,155 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a conservative futures trading analyst. Analyze market data carefully and provide measured recommendations. Use format: DECISION: [LONG/SHORT/WAIT], CONFIDENCE: [50-100]%, TAKE_PROFIT: [%], ST...
2025-07-15 06:37:01,155 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-15 06:37:01,156 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🎯 ENTRY TIMING SPECIALIST - DOGE/USDT:USDT

🎯 SIGNAL ANALYSIS:
ML Ensemble: NEUTRAL (50.0%)
Technical: NEUTRAL | Momentum: 0.00
Volume: PENDING | Orderflow: NEUTRAL

📊 MARKET MICROSTRUCTURE:
Bid/Ask: $0.170000/$0.170000
Spread: 0.100% | L2 Imbalance: 0.0%
Recent Flow: BALANCED | Volume Spike: False

📈 KEY LEVELS:
Support: $0.170000
Resistance: $0.170000
Distance to Support: 0.00%

⏰ TIMING FACTORS:
- Price near key levels: NEUTRAL
- Volume confirmation: PENDING
- Spread conditions: FAVORABLE
- M...
2025-07-15 06:37:01,157 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-15 06:37:01,157 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🔍 INTELLIGENT OPPORTUNITY SCANNER - Account Health Aware

🚨 ACCOUNT HEALTH STATUS: HEALTHY
💰 Balance: $1000.00 | Health: Healthy - Normal Trading
📊 Current Exposure: $0.00 (0.0% of account)
🎯 Trading Allowed: YES

📊 SYMBOL ANALYSIS:
• DOGE/USDT:USDT: $0.000000 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• BTC/USDT:USDT: $95000.000000 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• ETH/USDT:USDT: $3500.000000 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• SOL/USDT:USDT: $200.000000 | Mom: +0.0% | Vol: 1.0x |...
2025-07-15 06:37:01,157 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🔄 STRATEGY ADAPTATION SPECIALIST

📊 PERFORMANCE ANALYSIS (Last 24h):
Trades: 0 | Win Rate: 50.0%
Avg Profit: 0.80% | Avg Loss: -0.30%
Sharpe Ratio: 1.00 | Max Drawdown: 0.0%
Total PnL: $0.00 | ROI: 0.0%

🎯 CURRENT STRATEGY:
Risk per Trade: 2.0% | Avg Hold Time: 8.0min
Entry Threshold: 70% | Exit Threshold: 60%
Position Size Method: FIXED_RISK | Max Positions: 3

📈 MARKET REGIME: UNKNOWN
Regime Confidence: 50.0%
Scalp Suitability: MEDIUM

🔧 ADAPTATION FACTORS:
- Win rate trending: DOWN
- Drawdown...
2025-07-15 06:37:01,157 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-15 06:37:01,158 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-15 06:37:01,158 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-15 06:37:09,517 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 1626 chars
2025-07-15 06:37:09,517 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: SHORT, CONFIDENCE: 70%, TAKE_PROFIT: 2%, STOP_LOSS: 4%, EXPLANATION: The market appears to be in a neutral sector momentum phase with normal volatility and average volume profile. Given the account's healthy status, it is advisable not to take on excessive risk while still capitalizing on ...
2025-07-15 06:37:09,517 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 933, Completion: 377, Total: 1310
2025-07-15 06:37:09,517 - core.llm_response_parsers - INFO - 🔄 No JSON found, trying structured text parsing
2025-07-15 06:37:09,518 - core.llm_response_parsers - INFO - Parsed structured text: {'DECISION': 'SHORT', 'CONFIDENCE': 70.0, 'TAKE_PROFIT': 2.0, 'STOP_LOSS': 4.0, 'EXPLANATION': "THE MARKET APPEARS TO BE IN A NEUTRAL SECTOR MOMENTUM PHASE WITH NORMAL VOLATILITY AND AVERAGE VOLUME PROFILE. GIVEN THE ACCOUNT'S HEALTHY STATUS, IT IS ADVISABLE NOT TO TAKE ON EXCESSIVE RISK WHILE STILL CAPITALIZING ON POTENTIAL DOWNTRENDS FOR PROFITABILITY WITHIN CONSERVATIVE PARAMETERS.", 'ACTION': 'ENTER_NOW'}
2025-07-15 06:37:09,518 - core.llm_response_parsers - INFO - ✅ Structured text parsing successful: ['DECISION', 'CONFIDENCE', 'TAKE_PROFIT', 'STOP_LOSS', 'EXPLANATION', 'ACTION']
2025-07-15 06:37:09,518 - core.llm_response_parsers - INFO - Opportunity scanner parsed: MOMENTUM (REVERSAL)
2025-07-15 06:37:09,518 - core.llm_orchestrator - INFO - ✅ Completed prompt: opportunity_scanner
2025-07-15 06:37:11,007 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 155 chars
2025-07-15 06:37:11,007 - llama.lmstudio_runner - INFO - 📄 Response Preview: ```json
{
  "REGIME": "RANGING_TIGHT",
  "CONFIDENCE": 75,
  "SCALP_SUITABILITY": "MEDIUM",
  "RECOMMENDED_TIMEFRAME": "1m",
  "RISK_LEVEL": "MEDIUM"
}
```...
2025-07-15 06:37:11,007 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 705, Completion: 88, Total: 793
2025-07-15 06:37:11,008 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['REGIME', 'CONFIDENCE', 'SCALP_SUITABILITY', 'RECOMMENDED_TIMEFRAME', 'RISK_LEVEL']
2025-07-15 06:37:11,008 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['REGIME', 'CONFIDENCE', 'SCALP_SUITABILITY', 'RECOMMENDED_TIMEFRAME', 'RISK_LEVEL']
2025-07-15 06:37:11,008 - core.llm_response_parsers - INFO - Market regime parsed: RANGING_TIGHT (MEDIUM scalp suitability)
2025-07-15 06:37:11,009 - core.llm_orchestrator - INFO - ✅ Completed prompt: market_regime
2025-07-15 06:37:12,266 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 213 chars
2025-07-15 06:37:12,267 - llama.lmstudio_runner - INFO - 📄 Response Preview: ```json
{
  "action": "LONG",
  "confidence": 90,
  "entry_reason": "Market broke through resistance level at +14 pip move.",
  "take_profit": 2.5,
  "stop_loss": 1.3,
  "hold_time": "3min",
  "leverage": 40
}
```...
2025-07-15 06:37:12,267 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 174, Completion: 93, Total: 267
2025-07-15 06:37:12,268 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['action', 'confidence', 'entry_reason', 'take_profit', 'stop_loss', 'hold_time', 'leverage']
2025-07-15 06:37:12,268 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['ACTION', 'CONFIDENCE', 'ENTRY_REASON', 'TAKE_PROFIT', 'STOP_LOSS', 'HOLD_TIME', 'LEVERAGE']
2025-07-15 06:37:12,268 - core.llm_response_parsers - INFO - Risk assessment parsed: REJECTED (Risk: 50)
2025-07-15 06:37:12,268 - core.llm_orchestrator - INFO - ✅ Completed prompt: risk_assessment
2025-07-15 06:37:13,632 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 259 chars
2025-07-15 06:37:13,632 - llama.lmstudio_runner - INFO - 📄 Response Preview: {"risk_adjustment":1.5, "hold_time_target":30, "entry_threshold":75, "exit_threshold":65, "sizing_method":"VARIABLE_RISK", "reasoning":"Increase risk to capitalize on current win rate trend and market regime confidence. Adjust hold time for faster scalping."}...
2025-07-15 06:37:13,632 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 619, Completion: 86, Total: 705
2025-07-15 06:37:13,633 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['risk_adjustment', 'hold_time_target', 'entry_threshold', 'exit_threshold', 'sizing_method', 'reasoning']
2025-07-15 06:37:13,633 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['RISK_ADJUSTMENT', 'HOLD_TIME_TARGET', 'ENTRY_THRESHOLD', 'EXIT_THRESHOLD', 'SIZING_METHOD', 'REASONING', 'CONFIDENCE']
2025-07-15 06:37:13,633 - core.llm_response_parsers - INFO - Strategy adaptation parsed: Risk adj 1.5x
2025-07-15 06:37:13,633 - core.llm_action_executors - INFO - Applying strategy adaptations: Risk 1.5x, Hold time 30min
2025-07-15 06:37:13,633 - core.llm_orchestrator - INFO - ✅ Completed prompt: strategy_adaptation
2025-07-15 06:37:15,541 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 491 chars
2025-07-15 06:37:15,542 - llama.lmstudio_runner - INFO - 📄 Response Preview: {
  "action": "ENTER_NOW",
  "entry_type": "LIMIT",
  "confidence": 90,
  "wait_for": null,
  "max_wait_seconds": 30,
  "reasoning": "With the current market conditions being neutral across all technical and fundamental indicators with a favorable spread condition as per our scalping strategy requir...
2025-07-15 06:37:15,542 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 610, Completion: 125, Total: 735
2025-07-15 06:37:15,542 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['action', 'entry_type', 'confidence', 'wait_for', 'max_wait_seconds', 'reasoning']
2025-07-15 06:37:15,542 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['ACTION', 'ENTRY_TYPE', 'CONFIDENCE', 'WAIT_FOR', 'MAX_WAIT_SECONDS', 'REASONING']
2025-07-15 06:37:15,543 - core.llm_response_parsers - INFO - Entry timing parsed: ENTER_NOW (LIMIT order)
2025-07-15 06:37:15,543 - core.llm_action_executors - INFO - Executing entry action: ENTER_NOW (LIMIT order)
2025-07-15 06:37:15,543 - core.llm_action_executors - WARNING - No position capacity available - skipping entry
2025-07-15 06:37:15,543 - core.llm_orchestrator - INFO - ✅ Completed prompt: entry_timing
2025-07-15 06:37:15,544 - core.llm_orchestrator - INFO - 🚀 PARALLEL LLM cycle completed in 14.39s - 5 prompts executed concurrently
2025-07-15 06:37:15,544 - core.vote_aggregator - INFO - 🗳️ Vote aggregation: WAIT (67.8%) - Strong WAIT signal (3.4) → WAIT
2025-07-15 06:37:15,545 - core.llm_orchestrator - INFO - 🗳️ Aggregated Decision: WAIT (67.8%) - Strong WAIT signal (3.4) → WAIT
2025-07-15 06:37:15,545 - core.llm_orchestrator - INFO - 🗳️ Vote Breakdown: SHORT: 1.4, WAIT: 3.4
2025-07-15 06:37:31,153 - core.llm_orchestrator - INFO - 🔍 FULL MODE: Executing all 8 prompts in PARALLEL for comprehensive analysis
2025-07-15 06:37:31,154 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-15 06:37:31,154 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-15 06:37:31,154 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-15 06:37:31,154 - core.llm_orchestrator - INFO - 🚀 Submitted 2 prompts for parallel execution
2025-07-15 06:37:31,155 - llama.lmstudio_runner - INFO - 💬 User Prompt: 📊 MARKET REGIME ANALYST - DOGE/USDT:USDT

🔍 MULTI-TIMEFRAME ANALYSIS:
1m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
5m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
15m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%

📈 VOLATILITY METRICS:
ATR: 0.001000 | Spread: 0.100%
Volume Ratio: 1.0x | Price Range: 1.00%

🎯 REGIME CLASSIFICATION:
- TRENDING_BULL: Strong upward momentum, high volume, clear direction
- TRENDING_BEAR: Strong downward momentum, high volume, clear direction
- RANGING_TIGHT: Low volatility, narrow pric...
2025-07-15 06:37:31,157 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-15 06:37:31,155 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-15 06:37:31,157 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🎯 ENTRY TIMING SPECIALIST - DOGE/USDT:USDT

🎯 SIGNAL ANALYSIS:
ML Ensemble: NEUTRAL (50.0%)
Technical: NEUTRAL | Momentum: 0.00
Volume: PENDING | Orderflow: NEUTRAL

📊 MARKET MICROSTRUCTURE:
Bid/Ask: $0.170000/$0.170000
Spread: 0.100% | L2 Imbalance: 0.0%
Recent Flow: BALANCED | Volume Spike: False

📈 KEY LEVELS:
Support: $0.170000
Resistance: $0.170000
Distance to Support: 0.00%

⏰ TIMING FACTORS:
- Price near key levels: NEUTRAL
- Volume confirmation: PENDING
- Spread conditions: FAVORABLE
- M...
2025-07-15 06:37:31,157 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-15 06:37:35,466 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 275 chars
2025-07-15 06:37:35,466 - llama.lmstudio_runner - INFO - 📄 Response Preview: ```json
{
  "REGIME": "RANGING_TIGHT",
  "CONFIDENCE": 75,
  "SCALP_SUITABILITY": "MEDIUM",
  "RECOMMENDED_TIMEFRAME": "1m",
  "RISK_LEVEL": "MEDIUM",
  "REASONING": "Consolidation phase with moderate volatility, suitable for scalping under controlled risk conditions."
}
```...
2025-07-15 06:37:35,466 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 705, Completion: 117, Total: 822
2025-07-15 06:37:35,466 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['REGIME', 'CONFIDENCE', 'SCALP_SUITABILITY', 'RECOMMENDED_TIMEFRAME', 'RISK_LEVEL', 'REASONING']
2025-07-15 06:37:35,467 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['REGIME', 'CONFIDENCE', 'SCALP_SUITABILITY', 'RECOMMENDED_TIMEFRAME', 'RISK_LEVEL', 'REASONING']
2025-07-15 06:37:35,467 - core.llm_response_parsers - INFO - Market regime parsed: RANGING_TIGHT (MEDIUM scalp suitability)
2025-07-15 06:37:35,467 - core.llm_orchestrator - INFO - ✅ Completed prompt: market_regime
2025-07-15 06:37:37,188 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 412 chars
2025-07-15 06:37:37,189 - llama.lmstudio_runner - INFO - 📄 Response Preview: ```json
{
  "action": "ENTER",
  "entry_type": "LIMIT",
  "confidence": 70,
  "wait_for": null,
  "max_wait_seconds": null,
  "reasoning": "Market conditions are favorable with a narrow spread and balanced recent flow. Despite the neutral signals from ML Ensemble and technical indicators, there is p...
2025-07-15 06:37:37,190 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 610, Completion: 112, Total: 722
2025-07-15 06:37:37,191 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['action', 'entry_type', 'confidence', 'wait_for', 'max_wait_seconds', 'reasoning']
2025-07-15 06:37:37,192 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['ACTION', 'ENTRY_TYPE', 'CONFIDENCE', 'WAIT_FOR', 'MAX_WAIT_SECONDS', 'REASONING']
2025-07-15 06:37:37,193 - core.llm_response_parsers - INFO - Entry timing parsed: WAIT (LIMIT order)
2025-07-15 06:37:37,193 - core.llm_orchestrator - INFO - ✅ Completed prompt: entry_timing
2025-07-15 06:37:37,194 - core.llm_orchestrator - INFO - 🚀 PARALLEL LLM cycle completed in 6.04s - 2 prompts executed concurrently
2025-07-15 06:37:37,195 - core.vote_aggregator - INFO - 🗳️ Vote aggregation: WAIT (95.0%) - Clear WAIT signal (weight: 1.3, consensus: 100.0%)
2025-07-15 06:37:37,195 - core.llm_orchestrator - INFO - 🗳️ Aggregated Decision: WAIT (95.0%) - Clear WAIT signal (weight: 1.3, consensus: 100.0%)
2025-07-15 06:37:37,195 - core.llm_orchestrator - INFO - 🗳️ Vote Breakdown: WAIT: 1.3
2025-07-15 06:38:01,237 - core.llm_orchestrator - INFO - 🔍 FULL MODE: Executing all 8 prompts in PARALLEL for comprehensive analysis
2025-07-15 06:38:01,238 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-15 06:38:01,239 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-15 06:38:01,239 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-15 06:38:01,240 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-15 06:38:01,240 - llama.lmstudio_runner - INFO - 💬 User Prompt: 📊 MARKET REGIME ANALYST - DOGE/USDT:USDT

🔍 MULTI-TIMEFRAME ANALYSIS:
1m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
5m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
15m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%

📈 VOLATILITY METRICS:
ATR: 0.001000 | Spread: 0.100%
Volume Ratio: 1.0x | Price Range: 1.00%

🎯 REGIME CLASSIFICATION:
- TRENDING_BULL: Strong upward momentum, high volume, clear direction
- TRENDING_BEAR: Strong downward momentum, high volume, clear direction
- RANGING_TIGHT: Low volatility, narrow pric...
2025-07-15 06:38:01,240 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-15 06:38:01,240 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-15 06:38:01,241 - core.llm_orchestrator - INFO - 🚀 Submitted 4 prompts for parallel execution
2025-07-15 06:38:01,241 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-15 06:38:01,241 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-15 06:38:01,241 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a conservative futures trading analyst. Analyze market data carefully and provide measured recommendations. Use format: DECISION: [LONG/SHORT/WAIT], CONFIDENCE: [50-100]%, TAKE_PROFIT: [%], ST...
2025-07-15 06:38:01,242 - llama.lmstudio_runner - INFO - 💬 User Prompt: ...
2025-07-15 06:38:01,242 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🎯 ENTRY TIMING SPECIALIST - DOGE/USDT:USDT

🎯 SIGNAL ANALYSIS:
ML Ensemble: NEUTRAL (50.0%)
Technical: NEUTRAL | Momentum: 0.00
Volume: PENDING | Orderflow: NEUTRAL

📊 MARKET MICROSTRUCTURE:
Bid/Ask: $0.170000/$0.170000
Spread: 0.100% | L2 Imbalance: 0.0%
Recent Flow: BALANCED | Volume Spike: False

📈 KEY LEVELS:
Support: $0.170000
Resistance: $0.170000
Distance to Support: 0.00%

⏰ TIMING FACTORS:
- Price near key levels: NEUTRAL
- Volume confirmation: PENDING
- Spread conditions: FAVORABLE
- M...
2025-07-15 06:38:01,243 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🔍 INTELLIGENT OPPORTUNITY SCANNER - Account Health Aware

🚨 ACCOUNT HEALTH STATUS: HEALTHY
💰 Balance: $1000.00 | Health: Healthy - Normal Trading
📊 Current Exposure: $0.00 (0.0% of account)
🎯 Trading Allowed: YES

📊 SYMBOL ANALYSIS:
• DOGE/USDT:USDT: $0.000000 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• BTC/USDT:USDT: $95000.000000 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• ETH/USDT:USDT: $3500.000000 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• SOL/USDT:USDT: $200.000000 | Mom: +0.0% | Vol: 1.0x |...
2025-07-15 06:38:01,243 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-15 06:38:01,243 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-15 06:38:01,244 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-15 06:38:04,978 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 310 chars
2025-07-15 06:38:04,978 - llama.lmstudio_runner - INFO - 📄 Response Preview: {
  "action": "ENTER",
  "entry_type": "LIMIT",
  "confidence": 90,
  "wait_for": null,
  "max_wait_seconds": 30,
  "reasoning": "Market conditions are favorable with a narrow spread and no significant volume spike. The price is near key support levels which could indicate potential for an upward mo...
2025-07-15 06:38:04,979 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 610, Completion: 96, Total: 706
2025-07-15 06:38:04,979 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['action', 'entry_type', 'confidence', 'wait_for', 'max_wait_seconds', 'reasoning']
2025-07-15 06:38:04,979 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['ACTION', 'ENTRY_TYPE', 'CONFIDENCE', 'WAIT_FOR', 'MAX_WAIT_SECONDS', 'REASONING']
2025-07-15 06:38:04,979 - core.llm_response_parsers - INFO - Entry timing parsed: WAIT (LIMIT order)
2025-07-15 06:38:04,980 - core.llm_orchestrator - INFO - ✅ Completed prompt: entry_timing
2025-07-15 06:38:06,244 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 221 chars
2025-07-15 06:38:06,244 - llama.lmstudio_runner - INFO - 📄 Response Preview: ```json
{
  "action": "LONG",
  "confidence": 90,
  "entry_reason": "Breakout above resistance level with strong volume indicators.",
  "take_profit": 2.5,
  "stop_loss": 1.0,
  "hold_time": "3min",
  "leverage": 40
}
```...
2025-07-15 06:38:06,244 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 174, Completion: 92, Total: 266
2025-07-15 06:38:06,244 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['action', 'confidence', 'entry_reason', 'take_profit', 'stop_loss', 'hold_time', 'leverage']
2025-07-15 06:38:06,245 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['ACTION', 'CONFIDENCE', 'ENTRY_REASON', 'TAKE_PROFIT', 'STOP_LOSS', 'HOLD_TIME', 'LEVERAGE']
2025-07-15 06:38:06,245 - core.llm_response_parsers - INFO - Risk assessment parsed: REJECTED (Risk: 50)
2025-07-15 06:38:06,245 - core.llm_orchestrator - INFO - ✅ Completed prompt: risk_assessment
2025-07-15 06:38:09,139 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 750 chars
2025-07-15 06:38:09,139 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: LONG, CONFIDENCE: 85%, TAKE_PROFIT: 10%, STOP_LOSS: -20%, EXPLANATION: The account is in a healthy state with sufficient balance and risk capacity. Given the neutral sector momentum but normal overall volatility, there's an opportunity to capitalize on potential price movements without exc...
2025-07-15 06:38:09,139 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 933, Completion: 175, Total: 1108
2025-07-15 06:38:09,140 - core.llm_response_parsers - INFO - 🔄 No JSON found, trying structured text parsing
2025-07-15 06:38:09,140 - core.llm_response_parsers - INFO - Parsed structured text: {'DECISION': 'LONG', 'CONFIDENCE': 85.0, 'TAKE_PROFIT': 10.0, 'STOP_LOSS': -20.0, 'EXPLANATION': "THE ACCOUNT IS IN A HEALTHY STATE WITH SUFFICIENT BALANCE AND RISK CAPACITY. GIVEN THE NEUTRAL SECTOR MOMENTUM BUT NORMAL OVERALL VOLATILITY, THERE'S AN OPPORTUNITY TO CAPITALIZE ON POTENTIAL PRICE MOVEMENTS WITHOUT EXCESSIVE EXPOSURE. DOGE/USDT SHOWS MEDIUM SETUP QUALITY WHICH ALIGNS WELL WITHIN OUR CONSERVATIVE TRADING STRATEGY PARAMETERS CONSIDERING ITS LIQUIDITY SPREAD IS LESS THAN 0.2%. THE HISTORICAL CONTEXT INDICATES A 'WAIT' SIGNAL BUT WITH NO RECENT MOVEMENT, SUGGESTING THAT ENTERING NOW COULD BE OPPORTUNE BEFORE ANY POTENTIAL PRICE CHANGE OCCURS WHILE STILL ADHERING TO THE ACCOUNT PRESERVATION CRITERIA AND OPPORTUNITY THRESHOLDS SET FOR THIS ANALYSIS.", 'ACTION': 'ENTER_NOW'}
2025-07-15 06:38:09,140 - core.llm_response_parsers - INFO - ✅ Structured text parsing successful: ['DECISION', 'CONFIDENCE', 'TAKE_PROFIT', 'STOP_LOSS', 'EXPLANATION', 'ACTION']
2025-07-15 06:38:09,140 - core.llm_response_parsers - INFO - Opportunity scanner parsed: BREAKOUT (MOMENTUM)
2025-07-15 06:38:09,140 - core.llm_orchestrator - INFO - ✅ Completed prompt: opportunity_scanner
2025-07-15 06:38:10,947 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 218 chars
2025-07-15 06:38:10,948 - llama.lmstudio_runner - INFO - 📄 Response Preview: ```json
{
  "REGIME": "RANGING_TIGHT",
  "CONFIDENCE": 75,
  "SCALP_SUITABILITY": "MEDIUM",
  "RECOMMENDED_TIMEFRAME": "1m",
  "RISK_LEVEL": "MEDIUM",
  "REASONING": "Consolidation phase with moderate volatility"
}
```...
2025-07-15 06:38:10,948 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 705, Completion: 108, Total: 813
2025-07-15 06:38:10,948 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['REGIME', 'CONFIDENCE', 'SCALP_SUITABILITY', 'RECOMMENDED_TIMEFRAME', 'RISK_LEVEL', 'REASONING']
2025-07-15 06:38:10,948 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['REGIME', 'CONFIDENCE', 'SCALP_SUITABILITY', 'RECOMMENDED_TIMEFRAME', 'RISK_LEVEL', 'REASONING']
2025-07-15 06:38:10,949 - core.llm_response_parsers - INFO - Market regime parsed: RANGING_TIGHT (MEDIUM scalp suitability)
2025-07-15 06:38:10,949 - core.llm_orchestrator - INFO - ✅ Completed prompt: market_regime
2025-07-15 06:38:10,950 - core.llm_orchestrator - INFO - 🚀 PARALLEL LLM cycle completed in 9.71s - 4 prompts executed concurrently
2025-07-15 06:38:10,951 - core.vote_aggregator - INFO - 🗳️ Vote aggregation: WAIT (65.2%) - Strong WAIT signal (3.3) → WAIT
2025-07-15 06:38:10,951 - core.llm_orchestrator - INFO - 🗳️ Aggregated Decision: WAIT (65.2%) - Strong WAIT signal (3.3) → WAIT
2025-07-15 06:38:10,951 - core.llm_orchestrator - INFO - 🗳️ Vote Breakdown: LONG: 1.7, WAIT: 3.3
2025-07-15 06:38:31,612 - core.llm_orchestrator - INFO - 🔍 FULL MODE: Executing all 8 prompts in PARALLEL for comprehensive analysis
2025-07-15 06:38:31,614 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-15 06:38:31,614 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-15 06:38:31,614 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-15 06:38:31,614 - core.llm_orchestrator - INFO - 🚀 Submitted 2 prompts for parallel execution
2025-07-15 06:38:31,615 - llama.lmstudio_runner - INFO - 💬 User Prompt: 📊 MARKET REGIME ANALYST - DOGE/USDT:USDT

🔍 MULTI-TIMEFRAME ANALYSIS:
1m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
5m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
15m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%

📈 VOLATILITY METRICS:
ATR: 0.001000 | Spread: 0.100%
Volume Ratio: 1.0x | Price Range: 1.00%

🎯 REGIME CLASSIFICATION:
- TRENDING_BULL: Strong upward momentum, high volume, clear direction
- TRENDING_BEAR: Strong downward momentum, high volume, clear direction
- RANGING_TIGHT: Low volatility, narrow pric...
2025-07-15 06:38:31,615 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-15 06:38:31,615 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-15 06:38:31,615 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🎯 ENTRY TIMING SPECIALIST - DOGE/USDT:USDT

🎯 SIGNAL ANALYSIS:
ML Ensemble: NEUTRAL (50.0%)
Technical: NEUTRAL | Momentum: 0.00
Volume: PENDING | Orderflow: NEUTRAL

📊 MARKET MICROSTRUCTURE:
Bid/Ask: $0.170000/$0.170000
Spread: 0.100% | L2 Imbalance: 0.0%
Recent Flow: BALANCED | Volume Spike: False

📈 KEY LEVELS:
Support: $0.170000
Resistance: $0.170000
Distance to Support: 0.00%

⏰ TIMING FACTORS:
- Price near key levels: NEUTRAL
- Volume confirmation: PENDING
- Spread conditions: FAVORABLE
- M...
2025-07-15 06:38:31,616 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-15 06:38:36,032 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 407 chars
2025-07-15 06:38:36,032 - llama.lmstudio_runner - INFO - 📄 Response Preview: ```json
{
  "REGIME": "RANGING_TIGHT",
  "CONFIDENCE": 75,
  "SCALP_SUITABILITY": "MEDIUM",
  "RECOMMENDED_TIMEFRAME": "1m",
  "RISK_LEVEL": "MEDIUM",
  "REASONING": "Consolidation phase with moderate volatility is suitable for medium-risk scalping strategies. The market shows a neutral trend across...
2025-07-15 06:38:36,032 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 705, Completion: 144, Total: 849
2025-07-15 06:38:36,032 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['REGIME', 'CONFIDENCE', 'SCALP_SUITABILITY', 'RECOMMENDED_TIMEFRAME', 'RISK_LEVEL', 'REASONING']
2025-07-15 06:38:36,032 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['REGIME', 'CONFIDENCE', 'SCALP_SUITABILITY', 'RECOMMENDED_TIMEFRAME', 'RISK_LEVEL', 'REASONING']
2025-07-15 06:38:36,033 - core.llm_response_parsers - INFO - Market regime parsed: RANGING_TIGHT (MEDIUM scalp suitability)
2025-07-15 06:38:36,033 - core.llm_orchestrator - INFO - ✅ Completed prompt: market_regime
2025-07-15 06:38:37,823 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 388 chars
2025-07-15 06:38:37,823 - llama.lmstudio_runner - INFO - 📄 Response Preview: {
  "action": "ENTER",
  "entry_type": "LIMIT",
  "confidence": 85,
  "wait_for": null,
  "max_wait_seconds": 0,
  "reasoning": "Given the favorable spread conditions and neutral technical indicators with a balanced recent flow not showing any significant volume spikes or momentum shifts. The risk/r...
2025-07-15 06:38:37,823 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 610, Completion: 116, Total: 726
2025-07-15 06:38:37,824 - core.llm_response_parsers - WARNING - 🚨 JSON decode error: Expecting ',' delimiter: line 1 column 305 (char 304)
2025-07-15 06:38:37,824 - core.llm_response_parsers - WARNING - 🔍 Problematic JSON: { "action": "ENTER", "entry_type": "LIMIT", "confidence": 85, "wait_for": null, "max_wait_seconds": 0, "reasoning": "Given the favorable spread conditions and neutral technical indicators with a balan...
2025-07-15 06:38:37,824 - core.llm_response_parsers - INFO - ✅ Aggressive cleanup extracted: ['action', 'confidence', 'reasoning']
2025-07-15 06:38:37,824 - core.llm_response_parsers - INFO - ✅ JSON fixed with aggressive cleanup
2025-07-15 06:38:37,824 - core.llm_response_parsers - INFO - Entry timing parsed: WAIT (LIMIT order)
2025-07-15 06:38:37,824 - core.llm_orchestrator - INFO - ✅ Completed prompt: entry_timing
2025-07-15 06:38:37,825 - core.llm_orchestrator - INFO - 🚀 PARALLEL LLM cycle completed in 6.21s - 2 prompts executed concurrently
2025-07-15 06:38:37,826 - core.vote_aggregator - INFO - 🗳️ Vote aggregation: WAIT (95.0%) - Clear WAIT signal (weight: 1.1, consensus: 100.0%)
2025-07-15 06:38:37,826 - core.llm_orchestrator - INFO - 🗳️ Aggregated Decision: WAIT (95.0%) - Clear WAIT signal (weight: 1.1, consensus: 100.0%)
2025-07-15 06:38:37,826 - core.llm_orchestrator - INFO - 🗳️ Vote Breakdown: WAIT: 1.1
2025-07-15 06:39:02,108 - core.llm_orchestrator - INFO - 🔍 FULL MODE: Executing all 8 prompts in PARALLEL for comprehensive analysis
2025-07-15 06:39:02,110 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-15 06:39:02,110 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-15 06:39:02,110 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-15 06:39:02,111 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-15 06:39:02,111 - llama.lmstudio_runner - INFO - 💬 User Prompt: 📊 MARKET REGIME ANALYST - DOGE/USDT:USDT

🔍 MULTI-TIMEFRAME ANALYSIS:
1m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
5m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
15m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%

📈 VOLATILITY METRICS:
ATR: 0.001000 | Spread: 0.100%
Volume Ratio: 1.0x | Price Range: 1.00%

🎯 REGIME CLASSIFICATION:
- TRENDING_BULL: Strong upward momentum, high volume, clear direction
- TRENDING_BEAR: Strong downward momentum, high volume, clear direction
- RANGING_TIGHT: Low volatility, narrow pric...
2025-07-15 06:39:02,111 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-15 06:39:02,112 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-15 06:39:02,112 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-15 06:39:02,112 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-15 06:39:02,112 - core.llm_orchestrator - INFO - 🚀 Submitted 5 prompts for parallel execution
2025-07-15 06:39:02,112 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-15 06:39:02,112 - llama.lmstudio_runner - INFO - 💬 User Prompt: ...
2025-07-15 06:39:02,112 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a conservative futures trading analyst. Analyze market data carefully and provide measured recommendations. Use format: DECISION: [LONG/SHORT/WAIT], CONFIDENCE: [50-100]%, TAKE_PROFIT: [%], ST...
2025-07-15 06:39:02,113 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-15 06:39:02,113 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🎯 ENTRY TIMING SPECIALIST - DOGE/USDT:USDT

🎯 SIGNAL ANALYSIS:
ML Ensemble: NEUTRAL (50.0%)
Technical: NEUTRAL | Momentum: 0.00
Volume: PENDING | Orderflow: NEUTRAL

📊 MARKET MICROSTRUCTURE:
Bid/Ask: $0.170000/$0.170000
Spread: 0.100% | L2 Imbalance: 0.0%
Recent Flow: BALANCED | Volume Spike: False

📈 KEY LEVELS:
Support: $0.170000
Resistance: $0.170000
Distance to Support: 0.00%

⏰ TIMING FACTORS:
- Price near key levels: NEUTRAL
- Volume confirmation: PENDING
- Spread conditions: FAVORABLE
- M...
2025-07-15 06:39:02,114 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-15 06:39:02,114 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🔍 INTELLIGENT OPPORTUNITY SCANNER - Account Health Aware

🚨 ACCOUNT HEALTH STATUS: HEALTHY
💰 Balance: $1000.00 | Health: Healthy - Normal Trading
📊 Current Exposure: $0.00 (0.0% of account)
🎯 Trading Allowed: YES

📊 SYMBOL ANALYSIS:
• DOGE/USDT:USDT: $0.000000 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• BTC/USDT:USDT: $95000.000000 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• ETH/USDT:USDT: $3500.000000 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• SOL/USDT:USDT: $200.000000 | Mom: +0.0% | Vol: 1.0x |...
2025-07-15 06:39:02,114 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🔄 STRATEGY ADAPTATION SPECIALIST

📊 PERFORMANCE ANALYSIS (Last 24h):
Trades: 0 | Win Rate: 50.0%
Avg Profit: 0.80% | Avg Loss: -0.30%
Sharpe Ratio: 1.00 | Max Drawdown: 0.0%
Total PnL: $0.00 | ROI: 0.0%

🎯 CURRENT STRATEGY:
Risk per Trade: 2.0% | Avg Hold Time: 8.0min
Entry Threshold: 70% | Exit Threshold: 60%
Position Size Method: FIXED_RISK | Max Positions: 3

📈 MARKET REGIME: UNKNOWN
Regime Confidence: 50.0%
Scalp Suitability: MEDIUM

🔧 ADAPTATION FACTORS:
- Win rate trending: DOWN
- Drawdown...
2025-07-15 06:39:02,115 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-15 06:39:02,115 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-15 06:39:02,116 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-15 06:39:05,646 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 222 chars
2025-07-15 06:39:05,646 - llama.lmstudio_runner - INFO - 📄 Response Preview: ```json
{
  "action": "LONG",
  "confidence": 90,
  "entry_reason": "Market broke above resistance level with a sudden volume spike.",
  "take_profit": 2.5,
  "stop_loss": 1.0,
  "hold_time": "3min",
  "leverage": 40
}
```...
2025-07-15 06:39:05,646 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 174, Completion: 93, Total: 267
2025-07-15 06:39:05,647 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['action', 'confidence', 'entry_reason', 'take_profit', 'stop_loss', 'hold_time', 'leverage']
2025-07-15 06:39:05,647 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['ACTION', 'CONFIDENCE', 'ENTRY_REASON', 'TAKE_PROFIT', 'STOP_LOSS', 'HOLD_TIME', 'LEVERAGE']
2025-07-15 06:39:05,647 - core.llm_response_parsers - INFO - Risk assessment parsed: REJECTED (Risk: 50)
2025-07-15 06:39:05,647 - core.llm_orchestrator - INFO - ✅ Completed prompt: risk_assessment
2025-07-15 06:39:07,492 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 402 chars
2025-07-15 06:39:07,493 - llama.lmstudio_runner - INFO - 📄 Response Preview: {
  "action": "ENTER_NOW",
  "entry_type": "LIMIT",
  "confidence": 92,
  "wait_for": null,
  "max_wait_seconds": null,
  "reasoning": "The market is currently in a NEUTRAL state with favorable spread conditions and no pending volume confirmation. The risk/reward ratio of 2:1 aligns well for an imme...
2025-07-15 06:39:07,493 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 610, Completion: 120, Total: 730
2025-07-15 06:39:07,493 - core.llm_response_parsers - WARNING - 🚨 JSON decode error: Expecting ',' delimiter: line 1 column 263 (char 262)
2025-07-15 06:39:07,493 - core.llm_response_parsers - WARNING - 🔍 Problematic JSON: { "action": "ENTER_NOW", "entry_type": "LIMIT", "confidence": 92, "wait_for": null, "max_wait_seconds": null, "reasoning": "The market is currently in a NEUTRAL state with favorable spread conditions ...
2025-07-15 06:39:07,494 - core.llm_response_parsers - INFO - ✅ Aggressive cleanup extracted: ['action', 'confidence', 'reasoning']
2025-07-15 06:39:07,494 - core.llm_response_parsers - INFO - ✅ JSON fixed with aggressive cleanup
2025-07-15 06:39:07,494 - core.llm_response_parsers - INFO - Entry timing parsed: WAIT (LIMIT order)
2025-07-15 06:39:07,494 - core.llm_orchestrator - INFO - ✅ Completed prompt: entry_timing
2025-07-15 06:39:09,378 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 265 chars
2025-07-15 06:39:09,379 - llama.lmstudio_runner - INFO - 📄 Response Preview: ```json
{
  "REGIME": "RANGING_TIGHT",
  "CONFIDENCE": 75,
  "SCALP_SUITABILITY": "MEDIUM",
  "RECOMMENDED_TIMEFRAME": "1m",
  "RISK_LEVEL": "MEDIUM",
  "REASONING": "Consolidation phase with moderate volatility, suitable for medium-risk scalping strategies."
}
```...
2025-07-15 06:39:09,379 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 705, Completion: 119, Total: 824
2025-07-15 06:39:09,379 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['REGIME', 'CONFIDENCE', 'SCALP_SUITABILITY', 'RECOMMENDED_TIMEFRAME', 'RISK_LEVEL', 'REASONING']
2025-07-15 06:39:09,380 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['REGIME', 'CONFIDENCE', 'SCALP_SUITABILITY', 'RECOMMENDED_TIMEFRAME', 'RISK_LEVEL', 'REASONING']
2025-07-15 06:39:09,380 - core.llm_response_parsers - INFO - Market regime parsed: RANGING_TIGHT (MEDIUM scalp suitability)
2025-07-15 06:39:09,380 - core.llm_orchestrator - INFO - ✅ Completed prompt: market_regime
2025-07-15 06:39:14,031 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 1276 chars
2025-07-15 06:39:14,032 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: SHORT, CONFIDENCE: 70%, TAKE_PROFIT: 25%, STOP_LOSS: -3%, EXPLANATION: The market data indicates a neutral sector momentum with normal volatility and average volume profile. Given the account's healthy status and risk-adjusted capacity, it is prudent to consider shorting DOGE/USDT due to i...
2025-07-15 06:39:14,032 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 933, Completion: 295, Total: 1228
2025-07-15 06:39:14,032 - core.llm_response_parsers - INFO - 🔄 No JSON found, trying structured text parsing
2025-07-15 06:39:14,033 - core.llm_response_parsers - INFO - Parsed structured text: {'DECISION': 'SHORT', 'CONFIDENCE': 70.0, 'TAKE_PROFIT': 25.0, 'STOP_LOSS': -3.0, 'EXPLANATION': "THE MARKET DATA INDICATES A NEUTRAL SECTOR MOMENTUM WITH NORMAL VOLATILITY AND AVERAGE VOLUME PROFILE. GIVEN THE ACCOUNT'S HEALTHY STATUS AND RISK-ADJUSTED CAPACITY, IT IS PRUDENT TO CONSIDER SHORTING DOGE/USDT DUE TO ITS CURRENT SETUP BEING MEDIUM WITHOUT ANY SIGNIFICANT PRICE MOVEMENT OR TRADING SIGNALS IN RECENT HISTORY THAT SUGGEST AN IMMEDIATE UPWARD TREND REVERSAL. THE CONSERVATIVE ENTRY STRATEGY ALIGNS WITH THE ACCOUNT'S PRESERVATION CRITERIA AND OPPORTUNITY THRESHOLDS WHICH PRIORITIZE SAFETY OVER HIGH RISK-REWARD SCENARIOS, ESPECIALLY CONSIDERING THERE IS NO STRONG MOMENTUM ALIGNMENT OBSERVED YET (MOM: +0.0%). HOWEVER, TO MITIGATE POTENTIAL DOWNSIDE WHILE STILL CAPITALIZING ON A POSSIBLE SHORT POSITION IF MARKET CONDITIONS CHANGE SLIGHTLY IN THE NEXT 5-15 MINUTES WINDOW, WE SET TAKE PROFIT AT 25% AND STOP LOSS AT -3%. THIS APPROACH ENSURES THAT EVEN WITH CONSERVATIVE VOLATILITY EXPECTATIONS (<0.2% SPREAD), THERE IS ROOM FOR POTENTIAL UPSIDE WHILE MAINTAINING A CAUTIOUS STANCE DUE TO THE LACK OF CLEAR SIGNALS OR PATTERNS INDICATING AN IMMINENT PRICE INCREASE, THUS RESPECTING BOTH ACCOUNT HEALTH AND OPPORTUNITY CRITERIA WITHOUT COMPROMISING ON RISK MANAGEMENT PRINCIPLES.", 'ACTION': 'ENTER_NOW'}
2025-07-15 06:39:14,033 - core.llm_response_parsers - INFO - ✅ Structured text parsing successful: ['DECISION', 'CONFIDENCE', 'TAKE_PROFIT', 'STOP_LOSS', 'EXPLANATION', 'ACTION']
2025-07-15 06:39:14,033 - core.llm_response_parsers - INFO - Opportunity scanner parsed: MOMENTUM (REVERSAL)
2025-07-15 06:39:14,034 - core.llm_orchestrator - INFO - ✅ Completed prompt: opportunity_scanner
2025-07-15 06:39:15,367 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 259 chars
2025-07-15 06:39:15,367 - llama.lmstudio_runner - INFO - 📄 Response Preview: {"risk_adjustment":1.5, "hold_time_target":6,"entry_threshold":75, "exit_threshold":65, "sizing_method":"VARIABLE", "reasoning":"Increase risk slightly to capitalize on high confidence regime and adjust hold time for optimal scalping windows","confidence":90}...
2025-07-15 06:39:15,368 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 619, Completion: 80, Total: 699
2025-07-15 06:39:15,368 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['risk_adjustment', 'hold_time_target', 'entry_threshold', 'exit_threshold', 'sizing_method', 'reasoning', 'confidence']
2025-07-15 06:39:15,368 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['RISK_ADJUSTMENT', 'HOLD_TIME_TARGET', 'ENTRY_THRESHOLD', 'EXIT_THRESHOLD', 'SIZING_METHOD', 'REASONING', 'CONFIDENCE']
2025-07-15 06:39:15,368 - core.llm_response_parsers - INFO - Strategy adaptation parsed: Risk adj 1.5x
2025-07-15 06:39:15,369 - core.llm_action_executors - INFO - Applying strategy adaptations: Risk 1.5x, Hold time 6min
2025-07-15 06:39:15,369 - core.llm_orchestrator - INFO - ✅ Completed prompt: strategy_adaptation
2025-07-15 06:39:15,370 - core.llm_orchestrator - INFO - 🚀 PARALLEL LLM cycle completed in 13.26s - 5 prompts executed concurrently
2025-07-15 06:39:15,370 - core.vote_aggregator - INFO - 🗳️ Vote aggregation: WAIT (61.8%) - Strong WAIT signal (3.1) → WAIT
2025-07-15 06:39:15,370 - core.llm_orchestrator - INFO - 🗳️ Aggregated Decision: WAIT (61.8%) - Strong WAIT signal (3.1) → WAIT
2025-07-15 06:39:15,371 - core.llm_orchestrator - INFO - 🗳️ Vote Breakdown: SHORT: 1.4, WAIT: 3.1
2025-07-15 06:39:31,131 - core.llm_orchestrator - INFO - 🔍 FULL MODE: Executing all 8 prompts in PARALLEL for comprehensive analysis
2025-07-15 06:39:31,132 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-15 06:39:31,132 - core.llm_orchestrator - INFO - 🚀 Submitted 1 prompts for parallel execution
2025-07-15 06:39:31,133 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-15 06:39:31,133 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🎯 ENTRY TIMING SPECIALIST - DOGE/USDT:USDT

🎯 SIGNAL ANALYSIS:
ML Ensemble: NEUTRAL (50.0%)
Technical: NEUTRAL | Momentum: 0.00
Volume: PENDING | Orderflow: NEUTRAL

📊 MARKET MICROSTRUCTURE:
Bid/Ask: $0.170000/$0.170000
Spread: 0.100% | L2 Imbalance: 0.0%
Recent Flow: BALANCED | Volume Spike: False

📈 KEY LEVELS:
Support: $0.170000
Resistance: $0.170000
Distance to Support: 0.00%

⏰ TIMING FACTORS:
- Price near key levels: NEUTRAL
- Volume confirmation: PENDING
- Spread conditions: FAVORABLE
- M...
2025-07-15 06:39:31,133 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-15 06:39:35,383 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 394 chars
2025-07-15 06:39:35,383 - llama.lmstudio_runner - INFO - 📄 Response Preview: {
  "action": "ENTER",
  "entry_type": "LIMIT",
  "confidence": 80,
  "wait_for": null,
  "max_wait_seconds": 30,
  "reasoning": "Given the favorable spread conditions and a risk/reward ratio of 2:1 with current market microstructure indicating balanced recent flow without volume spikes or significa...
2025-07-15 06:39:35,383 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 610, Completion: 118, Total: 728
2025-07-15 06:39:35,384 - core.llm_response_parsers - WARNING - 🚨 JSON decode error: Expecting ',' delimiter: line 1 column 185 (char 184)
2025-07-15 06:39:35,384 - core.llm_response_parsers - WARNING - 🔍 Problematic JSON: { "action": "ENTER", "entry_type": "LIMIT", "confidence": 80, "wait_for": null, "max_wait_seconds": 30, "reasoning": "Given the favorable spread conditions and a risk/reward ratio of "2":1 with curren...
2025-07-15 06:39:35,384 - core.llm_response_parsers - INFO - ✅ Aggressive cleanup extracted: ['action', 'confidence', 'reasoning']
2025-07-15 06:39:35,385 - core.llm_response_parsers - INFO - ✅ JSON fixed with aggressive cleanup
2025-07-15 06:39:35,385 - core.llm_response_parsers - INFO - Entry timing parsed: WAIT (LIMIT order)
2025-07-15 06:39:35,385 - core.llm_orchestrator - INFO - ✅ Completed prompt: entry_timing
2025-07-15 06:39:35,386 - core.llm_orchestrator - INFO - 🚀 PARALLEL LLM cycle completed in 4.25s - 1 prompts executed concurrently
2025-07-15 06:39:35,386 - core.vote_aggregator - INFO - 🗳️ Vote aggregation: WAIT (40.0%) - Insufficient vote weight (0.5) → WAIT
2025-07-15 06:39:35,386 - core.llm_orchestrator - INFO - 🗳️ Aggregated Decision: WAIT (40.0%) - Insufficient vote weight (0.5) → WAIT
2025-07-15 06:39:35,386 - core.llm_orchestrator - INFO - 🗳️ Vote Breakdown: WAIT: 0.5
2025-07-15 06:40:01,135 - core.llm_orchestrator - INFO - 🔍 FULL MODE: Executing all 8 prompts in PARALLEL for comprehensive analysis
2025-07-15 06:40:01,136 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-15 06:40:01,137 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-15 06:40:01,137 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-15 06:40:01,137 - llama.lmstudio_runner - INFO - 💬 User Prompt: 📊 MARKET REGIME ANALYST - DOGE/USDT:USDT

🔍 MULTI-TIMEFRAME ANALYSIS:
1m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
5m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
15m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%

📈 VOLATILITY METRICS:
ATR: 0.001000 | Spread: 0.100%
Volume Ratio: 1.0x | Price Range: 1.00%

🎯 REGIME CLASSIFICATION:
- TRENDING_BULL: Strong upward momentum, high volume, clear direction
- TRENDING_BEAR: Strong downward momentum, high volume, clear direction
- RANGING_TIGHT: Low volatility, narrow pric...
2025-07-15 06:40:01,138 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-15 06:40:01,138 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-15 06:40:01,138 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-15 06:40:01,138 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-15 06:40:01,138 - core.llm_orchestrator - INFO - 🚀 Submitted 3 prompts for parallel execution
2025-07-15 06:40:01,138 - llama.lmstudio_runner - INFO - 💬 User Prompt: ...
2025-07-15 06:40:01,139 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🎯 ENTRY TIMING SPECIALIST - DOGE/USDT:USDT

🎯 SIGNAL ANALYSIS:
ML Ensemble: NEUTRAL (50.0%)
Technical: NEUTRAL | Momentum: 0.00
Volume: PENDING | Orderflow: NEUTRAL

📊 MARKET MICROSTRUCTURE:
Bid/Ask: $0.170000/$0.170000
Spread: 0.100% | L2 Imbalance: 0.0%
Recent Flow: BALANCED | Volume Spike: False

📈 KEY LEVELS:
Support: $0.170000
Resistance: $0.170000
Distance to Support: 0.00%

⏰ TIMING FACTORS:
- Price near key levels: NEUTRAL
- Volume confirmation: PENDING
- Spread conditions: FAVORABLE
- M...
2025-07-15 06:40:01,140 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-15 06:40:01,140 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-15 06:40:05,863 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 458 chars
2025-07-15 06:40:05,864 - llama.lmstudio_runner - INFO - 📄 Response Preview: {
  "REGIME": "RANGING_TIGHT",
  "CONFIDENCE": 75,
  "SCALP_SUITABILITY": "MEDIUM",
  "RECOMMENDED_TIMEFRAME": "1m",
  "RISK_LEVEL": "MEDIUM",
  "REASONING": "The market is currently in a consolidation phase with moderate volatility. The trend indicators across multiple timeframes are neutral, sugge...
2025-07-15 06:40:05,864 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 705, Completion: 150, Total: 855
2025-07-15 06:40:05,864 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['REGIME', 'CONFIDENCE', 'SCALP_SUITABILITY', 'RECOMMENDED_TIMEFRAME', 'RISK_LEVEL', 'REASONING']
2025-07-15 06:40:05,865 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['REGIME', 'CONFIDENCE', 'SCALP_SUITABILITY', 'RECOMMENDED_TIMEFRAME', 'RISK_LEVEL', 'REASONING']
2025-07-15 06:40:05,865 - core.llm_response_parsers - INFO - Market regime parsed: RANGING_TIGHT (MEDIUM scalp suitability)
2025-07-15 06:40:05,865 - core.llm_orchestrator - INFO - ✅ Completed prompt: market_regime
2025-07-15 06:40:07,139 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 221 chars
2025-07-15 06:40:07,140 - llama.lmstudio_runner - INFO - 📄 Response Preview: ```json
{
  "action": "LONG",
  "confidence": 90,
  "entry_reason": "Breakout above resistance level with strong volume indication.",
  "take_profit": 2.5,
  "stop_loss": 1.0,
  "hold_time": "3min",
  "leverage": 40
}
```...
2025-07-15 06:40:07,140 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 174, Completion: 92, Total: 266
2025-07-15 06:40:07,140 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['action', 'confidence', 'entry_reason', 'take_profit', 'stop_loss', 'hold_time', 'leverage']
2025-07-15 06:40:07,140 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['ACTION', 'CONFIDENCE', 'ENTRY_REASON', 'TAKE_PROFIT', 'STOP_LOSS', 'HOLD_TIME', 'LEVERAGE']
2025-07-15 06:40:07,141 - core.llm_response_parsers - INFO - Risk assessment parsed: REJECTED (Risk: 50)
2025-07-15 06:40:07,141 - core.llm_orchestrator - INFO - ✅ Completed prompt: risk_assessment
2025-07-15 06:40:08,766 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 312 chars
2025-07-15 06:40:08,766 - llama.lmstudio_runner - INFO - 📄 Response Preview: ```json
{
  "action": "ENTER",
  "entry_type": "LIMIT",
  "confidence": 90,
  "wait_for": null,
  "max_wait_seconds": 30,
  "reasoning": "Market conditions are favorable with a NEUTRAL signal from ML Ensemble and Spread. Volume confirmation pending but market is historically reactive to similar patt...
2025-07-15 06:40:08,767 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 610, Completion: 103, Total: 713
2025-07-15 06:40:08,768 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['action', 'entry_type', 'confidence', 'wait_for', 'max_wait_seconds', 'reasoning']
2025-07-15 06:40:08,769 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['ACTION', 'ENTRY_TYPE', 'CONFIDENCE', 'WAIT_FOR', 'MAX_WAIT_SECONDS', 'REASONING']
2025-07-15 06:40:08,769 - core.llm_response_parsers - INFO - Entry timing parsed: WAIT (LIMIT order)
2025-07-15 06:40:08,769 - core.llm_orchestrator - INFO - ✅ Completed prompt: entry_timing
2025-07-15 06:40:08,772 - core.llm_orchestrator - INFO - 🚀 PARALLEL LLM cycle completed in 7.64s - 3 prompts executed concurrently
2025-07-15 06:40:08,773 - core.vote_aggregator - INFO - 🗳️ Vote aggregation: WAIT (65.2%) - Strong WAIT signal (3.3) → WAIT
2025-07-15 06:40:08,774 - core.llm_orchestrator - INFO - 🗳️ Aggregated Decision: WAIT (65.2%) - Strong WAIT signal (3.3) → WAIT
2025-07-15 06:40:08,774 - core.llm_orchestrator - INFO - 🗳️ Vote Breakdown: WAIT: 3.3
2025-07-15 06:40:31,190 - core.llm_orchestrator - INFO - 🔍 FULL MODE: Executing all 8 prompts in PARALLEL for comprehensive analysis
2025-07-15 06:40:31,191 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-15 06:40:31,192 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-15 06:40:31,192 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-15 06:40:31,192 - llama.lmstudio_runner - INFO - 💬 User Prompt: 📊 MARKET REGIME ANALYST - DOGE/USDT:USDT

🔍 MULTI-TIMEFRAME ANALYSIS:
1m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
5m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
15m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%

📈 VOLATILITY METRICS:
ATR: 0.001000 | Spread: 0.100%
Volume Ratio: 1.0x | Price Range: 1.00%

🎯 REGIME CLASSIFICATION:
- TRENDING_BULL: Strong upward momentum, high volume, clear direction
- TRENDING_BEAR: Strong downward momentum, high volume, clear direction
- RANGING_TIGHT: Low volatility, narrow pric...
2025-07-15 06:40:31,193 - core.llm_orchestrator - INFO - 🚀 Submitted 3 prompts for parallel execution
2025-07-15 06:40:31,193 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-15 06:40:31,193 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-15 06:40:31,193 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-15 06:40:31,194 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🎯 ENTRY TIMING SPECIALIST - DOGE/USDT:USDT

🎯 SIGNAL ANALYSIS:
ML Ensemble: NEUTRAL (50.0%)
Technical: NEUTRAL | Momentum: 0.00
Volume: PENDING | Orderflow: NEUTRAL

📊 MARKET MICROSTRUCTURE:
Bid/Ask: $0.170000/$0.170000
Spread: 0.100% | L2 Imbalance: 0.0%
Recent Flow: BALANCED | Volume Spike: False

📈 KEY LEVELS:
Support: $0.170000
Resistance: $0.170000
Distance to Support: 0.00%

⏰ TIMING FACTORS:
- Price near key levels: NEUTRAL
- Volume confirmation: PENDING
- Spread conditions: FAVORABLE
- M...
2025-07-15 06:40:31,194 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a conservative futures trading analyst. Analyze market data carefully and provide measured recommendations. Use format: DECISION: [LONG/SHORT/WAIT], CONFIDENCE: [50-100]%, TAKE_PROFIT: [%], ST...
2025-07-15 06:40:31,194 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-15 06:40:31,195 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🔍 INTELLIGENT OPPORTUNITY SCANNER - Account Health Aware

🚨 ACCOUNT HEALTH STATUS: HEALTHY
💰 Balance: $1000.00 | Health: Healthy - Normal Trading
📊 Current Exposure: $0.00 (0.0% of account)
🎯 Trading Allowed: YES

📊 SYMBOL ANALYSIS:
• DOGE/USDT:USDT: $0.000000 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• BTC/USDT:USDT: $95000.000000 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• ETH/USDT:USDT: $3500.000000 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• SOL/USDT:USDT: $200.000000 | Mom: +0.0% | Vol: 1.0x |...
2025-07-15 06:40:31,195 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-15 06:40:36,555 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 775 chars
2025-07-15 06:40:36,555 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: LONG, CONFIDENCE: 75%, TAKE_PROFIT: 12%, STOP_LOSS: -4%, EXPLANATION: The market data indicates a neutral sector momentum with normal volatility and average volume profile. Given the account's healthy status and adherence to preservation criteria, there is an opportunity for conservative e...
2025-07-15 06:40:36,556 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 933, Completion: 184, Total: 1117
2025-07-15 06:40:36,556 - core.llm_response_parsers - INFO - 🔄 No JSON found, trying structured text parsing
2025-07-15 06:40:36,556 - core.llm_response_parsers - INFO - Parsed structured text: {'DECISION': 'LONG', 'CONFIDENCE': 75.0, 'TAKE_PROFIT': 12.0, 'STOP_LOSS': -4.0, 'EXPLANATION': "THE MARKET DATA INDICATES A NEUTRAL SECTOR MOMENTUM WITH NORMAL VOLATILITY AND AVERAGE VOLUME PROFILE. GIVEN THE ACCOUNT'S HEALTHY STATUS AND ADHERENCE TO PRESERVATION CRITERIA, THERE IS AN OPPORTUNITY FOR CONSERVATIVE ENTRY INTO DOGE/USDT DUE TO ITS MEDIUM SETUP QUALITY WITHOUT VIOLATING RISK LIMITS OR IMPACTING ACCOUNT STABILITY SIGNIFICANTLY. THE HISTORICAL CONTEXT OF 'WAIT' SIGNALS SUGGESTS A CAUTIOUS APPROACH BUT DOES NOT PRECLUDE POTENTIAL GAINS FROM THE CURRENT MARKET CONDITIONS WITH APPROPRIATE STOP-LOSS AND TAKE-PROFIT LEVELS SET CONSERVATIVELY, CONSIDERING BOTH VOLATILITY RANGE PREFERENCE AND RECENT PRICE STAGNATION IN DOGE/USDT WHICH MAY INDICATE AN UPCOMING BULLISH MOVEMENT.", 'ACTION': 'ENTER_NOW'}
2025-07-15 06:40:36,556 - core.llm_response_parsers - INFO - ✅ Structured text parsing successful: ['DECISION', 'CONFIDENCE', 'TAKE_PROFIT', 'STOP_LOSS', 'EXPLANATION', 'ACTION']
2025-07-15 06:40:36,557 - core.llm_response_parsers - INFO - Opportunity scanner parsed: MOMENTUM (MOMENTUM)
2025-07-15 06:40:36,557 - core.llm_orchestrator - INFO - ✅ Completed prompt: opportunity_scanner
2025-07-15 06:40:38,246 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 371 chars
2025-07-15 06:40:38,247 - llama.lmstudio_runner - INFO - 📄 Response Preview: {
  "action": "ENTER_NOW",
  "entry_type": "LIMIT",
  "confidence": 85,
  "wait_for": null,
  "max_wait_seconds": 0,
  "reasoning": "Market conditions are favorable with a narrow spread and no significant imbalance. Despite the neutral technical indicators, historical context shows consistent price ...
2025-07-15 06:40:38,247 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 610, Completion: 104, Total: 714
2025-07-15 06:40:38,247 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['action', 'entry_type', 'confidence', 'wait_for', 'max_wait_seconds', 'reasoning']
2025-07-15 06:40:38,247 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['ACTION', 'ENTRY_TYPE', 'CONFIDENCE', 'WAIT_FOR', 'MAX_WAIT_SECONDS', 'REASONING']
2025-07-15 06:40:38,247 - core.llm_response_parsers - INFO - Entry timing parsed: ENTER_NOW (LIMIT order)
2025-07-15 06:40:38,248 - core.llm_action_executors - INFO - Executing entry action: ENTER_NOW (LIMIT order)
2025-07-15 06:40:38,248 - core.llm_action_executors - WARNING - No position capacity available - skipping entry
2025-07-15 06:40:38,248 - core.llm_orchestrator - INFO - ✅ Completed prompt: entry_timing
2025-07-15 06:40:39,974 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 230 chars
2025-07-15 06:40:39,974 - llama.lmstudio_runner - INFO - 📄 Response Preview: ```json
{
    "REGIME": "RANGING_TIGHT",
    "CONFIDENCE": 75,
    "SCALP_SUITABILITY": "MEDIUM",
    "RECOMMENDED_TIMEFRAME": "1m",
    "RISK_LEVEL": "MEDIUM",
    "REASONING": "Consolidation phase with moderate volatility"
}
```...
2025-07-15 06:40:39,975 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 705, Completion: 108, Total: 813
2025-07-15 06:40:39,975 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['REGIME', 'CONFIDENCE', 'SCALP_SUITABILITY', 'RECOMMENDED_TIMEFRAME', 'RISK_LEVEL', 'REASONING']
2025-07-15 06:40:39,976 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['REGIME', 'CONFIDENCE', 'SCALP_SUITABILITY', 'RECOMMENDED_TIMEFRAME', 'RISK_LEVEL', 'REASONING']
2025-07-15 06:40:39,976 - core.llm_response_parsers - INFO - Market regime parsed: RANGING_TIGHT (MEDIUM scalp suitability)
2025-07-15 06:40:39,976 - core.llm_orchestrator - INFO - ✅ Completed prompt: market_regime
2025-07-15 06:40:39,977 - core.llm_orchestrator - INFO - 🚀 PARALLEL LLM cycle completed in 8.79s - 3 prompts executed concurrently
2025-07-15 06:40:39,977 - core.vote_aggregator - INFO - 🗳️ Vote aggregation: LONG (51.5%) - Clear LONG signal (weight: 1.5, consensus: 51.5%)
2025-07-15 06:40:39,978 - core.llm_orchestrator - INFO - 🗳️ Aggregated Decision: LONG (51.5%) - Clear LONG signal (weight: 1.5, consensus: 51.5%)
2025-07-15 06:40:39,978 - core.llm_orchestrator - INFO - 🗳️ Vote Breakdown: LONG: 1.5, WAIT: 1.4
2025-07-15 06:41:01,161 - core.llm_orchestrator - INFO - 🔍 FULL MODE: Executing all 8 prompts in PARALLEL for comprehensive analysis
2025-07-15 06:41:01,162 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-15 06:41:01,163 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-15 06:41:01,163 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-15 06:41:01,163 - core.llm_orchestrator - INFO - 🚀 Submitted 2 prompts for parallel execution
2025-07-15 06:41:01,163 - llama.lmstudio_runner - INFO - 💬 User Prompt: ...
2025-07-15 06:41:01,164 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-15 06:41:01,164 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-15 06:41:01,164 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🎯 ENTRY TIMING SPECIALIST - DOGE/USDT:USDT

🎯 SIGNAL ANALYSIS:
ML Ensemble: NEUTRAL (50.0%)
Technical: NEUTRAL | Momentum: 0.00
Volume: PENDING | Orderflow: NEUTRAL

📊 MARKET MICROSTRUCTURE:
Bid/Ask: $0.170000/$0.170000
Spread: 0.100% | L2 Imbalance: 0.0%
Recent Flow: BALANCED | Volume Spike: False

📈 KEY LEVELS:
Support: $0.170000
Resistance: $0.170000
Distance to Support: 0.00%

⏰ TIMING FACTORS:
- Price near key levels: NEUTRAL
- Volume confirmation: PENDING
- Spread conditions: FAVORABLE
- M...
2025-07-15 06:41:01,165 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-15 06:41:04,851 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 239 chars
2025-07-15 06:41:04,851 - llama.lmstudio_runner - INFO - 📄 Response Preview: ```json
{
  "action": "LONG",
  "confidence": 90,
  "entry_reason": "Breakout above resistance level with high volume and strong momentum indicators.",
  "take_profit": 2.5,
  "stop_loss": 1.0,
  "hold_time": "3min",
  "leverage": 40
}
```...
2025-07-15 06:41:04,851 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 174, Completion: 95, Total: 269
2025-07-15 06:41:04,852 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['action', 'confidence', 'entry_reason', 'take_profit', 'stop_loss', 'hold_time', 'leverage']
2025-07-15 06:41:04,852 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['ACTION', 'CONFIDENCE', 'ENTRY_REASON', 'TAKE_PROFIT', 'STOP_LOSS', 'HOLD_TIME', 'LEVERAGE']
2025-07-15 06:41:04,852 - core.llm_response_parsers - INFO - Risk assessment parsed: REJECTED (Risk: 50)
2025-07-15 06:41:04,852 - core.llm_orchestrator - INFO - ✅ Completed prompt: risk_assessment
2025-07-15 06:41:06,463 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 335 chars
2025-07-15 06:41:06,464 - llama.lmstudio_runner - INFO - 📄 Response Preview: {
  "action": "ENTER_NOW",
  "entry_type": "LIMIT",
  "confidence": 85,
  "wait_for": null,
  "max_wait_seconds": 0,
  "reasoning": "Market conditions are favorable with a narrow spread and no significant volume spike. The price is near key support levels indicating potential for an upward move with...
2025-07-15 06:41:06,464 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 610, Completion: 102, Total: 712
2025-07-15 06:41:06,464 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['action', 'entry_type', 'confidence', 'wait_for', 'max_wait_seconds', 'reasoning']
2025-07-15 06:41:06,464 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['ACTION', 'ENTRY_TYPE', 'CONFIDENCE', 'WAIT_FOR', 'MAX_WAIT_SECONDS', 'REASONING']
2025-07-15 06:41:06,464 - core.llm_response_parsers - INFO - Entry timing parsed: ENTER_NOW (LIMIT order)
2025-07-15 06:41:06,465 - core.llm_action_executors - INFO - Executing entry action: ENTER_NOW (LIMIT order)
2025-07-15 06:41:06,465 - core.llm_action_executors - WARNING - No position capacity available - skipping entry
2025-07-15 06:41:06,465 - core.llm_orchestrator - INFO - ✅ Completed prompt: entry_timing
2025-07-15 06:41:06,466 - core.llm_orchestrator - INFO - 🚀 PARALLEL LLM cycle completed in 5.30s - 2 prompts executed concurrently
2025-07-15 06:41:06,466 - core.vote_aggregator - INFO - 🗳️ Vote aggregation: WAIT (53.0%) - Strong WAIT signal (2.6) → WAIT
2025-07-15 06:41:06,466 - core.llm_orchestrator - INFO - 🗳️ Aggregated Decision: WAIT (53.0%) - Strong WAIT signal (2.6) → WAIT
2025-07-15 06:41:06,467 - core.llm_orchestrator - INFO - 🗳️ Vote Breakdown: WAIT: 2.6
2025-07-15 06:41:31,175 - core.llm_orchestrator - INFO - 🔍 FULL MODE: Executing all 8 prompts in PARALLEL for comprehensive analysis
2025-07-15 06:41:31,176 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-15 06:41:31,177 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-15 06:41:31,177 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-15 06:41:31,177 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-15 06:41:31,178 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-15 06:41:31,178 - llama.lmstudio_runner - INFO - 💬 User Prompt: 📊 MARKET REGIME ANALYST - DOGE/USDT:USDT

🔍 MULTI-TIMEFRAME ANALYSIS:
1m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
5m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
15m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%

📈 VOLATILITY METRICS:
ATR: 0.001000 | Spread: 0.100%
Volume Ratio: 1.0x | Price Range: 1.00%

🎯 REGIME CLASSIFICATION:
- TRENDING_BULL: Strong upward momentum, high volume, clear direction
- TRENDING_BEAR: Strong downward momentum, high volume, clear direction
- RANGING_TIGHT: Low volatility, narrow pric...
2025-07-15 06:41:31,178 - core.llm_orchestrator - INFO - 🚀 Submitted 3 prompts for parallel execution
2025-07-15 06:41:31,178 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🎯 ENTRY TIMING SPECIALIST - DOGE/USDT:USDT

🎯 SIGNAL ANALYSIS:
ML Ensemble: NEUTRAL (50.0%)
Technical: NEUTRAL | Momentum: 0.00
Volume: PENDING | Orderflow: NEUTRAL

📊 MARKET MICROSTRUCTURE:
Bid/Ask: $0.170000/$0.170000
Spread: 0.100% | L2 Imbalance: 0.0%
Recent Flow: BALANCED | Volume Spike: False

📈 KEY LEVELS:
Support: $0.170000
Resistance: $0.170000
Distance to Support: 0.00%

⏰ TIMING FACTORS:
- Price near key levels: NEUTRAL
- Volume confirmation: PENDING
- Spread conditions: FAVORABLE
- M...
2025-07-15 06:41:31,178 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-15 06:41:31,178 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-15 06:41:31,179 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-15 06:41:31,179 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🔄 STRATEGY ADAPTATION SPECIALIST

📊 PERFORMANCE ANALYSIS (Last 24h):
Trades: 0 | Win Rate: 50.0%
Avg Profit: 0.80% | Avg Loss: -0.30%
Sharpe Ratio: 1.00 | Max Drawdown: 0.0%
Total PnL: $0.00 | ROI: 0.0%

🎯 CURRENT STRATEGY:
Risk per Trade: 2.0% | Avg Hold Time: 8.0min
Entry Threshold: 70% | Exit Threshold: 60%
Position Size Method: FIXED_RISK | Max Positions: 3

📈 MARKET REGIME: UNKNOWN
Regime Confidence: 50.0%
Scalp Suitability: MEDIUM

🔧 ADAPTATION FACTORS:
- Win rate trending: DOWN
- Drawdown...
2025-07-15 06:41:31,180 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-15 06:41:34,947 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 155 chars
2025-07-15 06:41:34,948 - llama.lmstudio_runner - INFO - 📄 Response Preview: ```json
{
  "REGIME": "RANGING_TIGHT",
  "CONFIDENCE": 75,
  "SCALP_SUITABILITY": "MEDIUM",
  "RECOMMENDED_TIMEFRAME": "1m",
  "RISK_LEVEL": "MEDIUM"
}
```...
2025-07-15 06:41:34,948 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 705, Completion: 88, Total: 793
2025-07-15 06:41:34,948 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['REGIME', 'CONFIDENCE', 'SCALP_SUITABILITY', 'RECOMMENDED_TIMEFRAME', 'RISK_LEVEL']
2025-07-15 06:41:34,949 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['REGIME', 'CONFIDENCE', 'SCALP_SUITABILITY', 'RECOMMENDED_TIMEFRAME', 'RISK_LEVEL']
2025-07-15 06:41:34,949 - core.llm_response_parsers - INFO - Market regime parsed: RANGING_TIGHT (MEDIUM scalp suitability)
2025-07-15 06:41:34,949 - core.llm_orchestrator - INFO - ✅ Completed prompt: market_regime
2025-07-15 06:41:36,528 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 316 chars
2025-07-15 06:41:36,528 - llama.lmstudio_runner - INFO - 📄 Response Preview: ```json
{
  "action": "ENTER",
  "entry_type": "LIMIT",
  "confidence": 80,
  "wait_for": null,
  "max_wait_seconds": 30,
  "reasoning": "Market conditions are favorable with a narrow spread and no significant volume spike. The price is near key levels which may indicate potential movement in the sh...
2025-07-15 06:41:36,528 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 610, Completion: 100, Total: 710
2025-07-15 06:41:36,529 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['action', 'entry_type', 'confidence', 'wait_for', 'max_wait_seconds', 'reasoning']
2025-07-15 06:41:36,529 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['ACTION', 'ENTRY_TYPE', 'CONFIDENCE', 'WAIT_FOR', 'MAX_WAIT_SECONDS', 'REASONING']
2025-07-15 06:41:36,529 - core.llm_response_parsers - INFO - Entry timing parsed: WAIT (LIMIT order)
2025-07-15 06:41:36,529 - core.llm_orchestrator - INFO - ✅ Completed prompt: entry_timing
2025-07-15 06:41:38,087 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 295 chars
2025-07-15 06:41:38,088 - llama.lmstudio_runner - INFO - 📄 Response Preview: ```json
{
    "risk_adjustment": [0.5, 2.0],
    "hold_time_target": "8",
    "entry_threshold": [70, 60],
    "reasoning": "Maintain current strategy - performing well with a moderate win rate and acceptable drawdown level under normal market volatility conditions.",
    "confidence": 75
}
```...
2025-07-15 06:41:38,088 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 619, Completion: 99, Total: 718
2025-07-15 06:41:38,088 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['risk_adjustment', 'hold_time_target', 'entry_threshold', 'reasoning', 'confidence']
2025-07-15 06:41:38,088 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['RISK_ADJUSTMENT', 'HOLD_TIME_TARGET', 'ENTRY_THRESHOLD', 'REASONING', 'CONFIDENCE']
2025-07-15 06:41:38,088 - core.llm_response_parsers - INFO - Strategy adaptation parsed: Risk adj 1.0x
2025-07-15 06:41:38,088 - core.llm_action_executors - INFO - Applying strategy adaptations: Risk 1.0x, Hold time 8min
2025-07-15 06:41:38,089 - core.llm_orchestrator - INFO - ✅ Completed prompt: strategy_adaptation
2025-07-15 06:41:38,090 - core.llm_orchestrator - INFO - 🚀 PARALLEL LLM cycle completed in 6.91s - 3 prompts executed concurrently
2025-07-15 06:41:38,090 - core.vote_aggregator - INFO - 🗳️ Vote aggregation: WAIT (31.0%) - Strong WAIT signal (1.6) → WAIT
2025-07-15 06:41:38,090 - core.llm_orchestrator - INFO - 🗳️ Aggregated Decision: WAIT (31.0%) - Strong WAIT signal (1.6) → WAIT
2025-07-15 06:41:38,090 - core.llm_orchestrator - INFO - 🗳️ Vote Breakdown: WAIT: 1.6
2025-07-15 06:42:01,173 - core.llm_orchestrator - INFO - 🔍 FULL MODE: Executing all 8 prompts in PARALLEL for comprehensive analysis
2025-07-15 06:42:01,175 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-15 06:42:01,176 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-15 06:42:01,176 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-15 06:42:01,177 - llama.lmstudio_runner - INFO - 💬 User Prompt: ...
2025-07-15 06:42:01,177 - core.llm_orchestrator - INFO - 🚀 Submitted 3 prompts for parallel execution
2025-07-15 06:42:01,177 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-15 06:42:01,178 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-15 06:42:01,178 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-15 06:42:01,179 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a conservative futures trading analyst. Analyze market data carefully and provide measured recommendations. Use format: DECISION: [LONG/SHORT/WAIT], CONFIDENCE: [50-100]%, TAKE_PROFIT: [%], ST...
2025-07-15 06:42:01,179 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🎯 ENTRY TIMING SPECIALIST - DOGE/USDT:USDT

🎯 SIGNAL ANALYSIS:
ML Ensemble: NEUTRAL (50.0%)
Technical: NEUTRAL | Momentum: 0.00
Volume: PENDING | Orderflow: NEUTRAL

📊 MARKET MICROSTRUCTURE:
Bid/Ask: $0.170000/$0.170000
Spread: 0.100% | L2 Imbalance: 0.0%
Recent Flow: BALANCED | Volume Spike: False

📈 KEY LEVELS:
Support: $0.170000
Resistance: $0.170000
Distance to Support: 0.00%

⏰ TIMING FACTORS:
- Price near key levels: NEUTRAL
- Volume confirmation: PENDING
- Spread conditions: FAVORABLE
- M...
2025-07-15 06:42:01,180 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🔍 INTELLIGENT OPPORTUNITY SCANNER - Account Health Aware

🚨 ACCOUNT HEALTH STATUS: HEALTHY
💰 Balance: $1000.00 | Health: Healthy - Normal Trading
📊 Current Exposure: $0.00 (0.0% of account)
🎯 Trading Allowed: YES

📊 SYMBOL ANALYSIS:
• DOGE/USDT:USDT: $0.000000 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• BTC/USDT:USDT: $95000.000000 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• ETH/USDT:USDT: $3500.000000 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• SOL/USDT:USDT: $200.000000 | Mom: +0.0% | Vol: 1.0x |...
2025-07-15 06:42:01,181 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-15 06:42:01,181 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-15 06:42:04,972 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 294 chars
2025-07-15 06:42:04,972 - llama.lmstudio_runner - INFO - 📄 Response Preview: {
  "action": "ENTER",
  "entry_type": "LIMIT",
  "confidence": 78,
  "wait_for": null,
  "max_wait_seconds": 0,
  "reasoning": "Market conditions are favorable with a low spread and balanced recent flow. Confirmation of volume spike is pending but expected soon based on historical context."
}...
2025-07-15 06:42:04,973 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 610, Completion: 94, Total: 704
2025-07-15 06:42:04,974 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['action', 'entry_type', 'confidence', 'wait_for', 'max_wait_seconds', 'reasoning']
2025-07-15 06:42:04,974 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['ACTION', 'ENTRY_TYPE', 'CONFIDENCE', 'WAIT_FOR', 'MAX_WAIT_SECONDS', 'REASONING']
2025-07-15 06:42:04,974 - core.llm_response_parsers - INFO - Entry timing parsed: WAIT (LIMIT order)
2025-07-15 06:42:04,974 - core.llm_orchestrator - INFO - ✅ Completed prompt: entry_timing
2025-07-15 06:42:06,248 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 213 chars
2025-07-15 06:42:06,248 - llama.lmstudio_runner - INFO - 📄 Response Preview: ```json
{
  "action": "LONG",
  "confidence": 90,
  "entry_reason": "Breakout above the resistance level with volume spike.",
  "take_profit": 2.5,
  "stop_loss": 1.0,
  "hold_time": "3min",
  "leverage": 40
}
```...
2025-07-15 06:42:06,248 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 174, Completion: 92, Total: 266
2025-07-15 06:42:06,248 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['action', 'confidence', 'entry_reason', 'take_profit', 'stop_loss', 'hold_time', 'leverage']
2025-07-15 06:42:06,248 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['ACTION', 'CONFIDENCE', 'ENTRY_REASON', 'TAKE_PROFIT', 'STOP_LOSS', 'HOLD_TIME', 'LEVERAGE']
2025-07-15 06:42:06,249 - core.llm_response_parsers - INFO - Risk assessment parsed: REJECTED (Risk: 50)
2025-07-15 06:42:06,249 - core.llm_orchestrator - INFO - ✅ Completed prompt: risk_assessment
2025-07-15 06:42:09,231 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 781 chars
2025-07-15 06:42:09,231 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: SHORT, CONFIDENCE: 75%, TAKE_PROFIT: 2%, STOP_LOSS: -3%, EXPLANATION: The current market data indicates a neutral sector momentum with normal volatility and average volume profile. Given the account's healthy status and risk-adjusted capacity, it is advisable to consider short positions th...
2025-07-15 06:42:09,232 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 933, Completion: 181, Total: 1114
2025-07-15 06:42:09,232 - core.llm_response_parsers - INFO - 🔄 No JSON found, trying structured text parsing
2025-07-15 06:42:09,232 - core.llm_response_parsers - INFO - Parsed structured text: {'DECISION': 'SHORT', 'CONFIDENCE': 75.0, 'TAKE_PROFIT': 2.0, 'STOP_LOSS': -3.0, 'EXPLANATION': "THE CURRENT MARKET DATA INDICATES A NEUTRAL SECTOR MOMENTUM WITH NORMAL VOLATILITY AND AVERAGE VOLUME PROFILE. GIVEN THE ACCOUNT'S HEALTHY STATUS AND RISK-ADJUSTED CAPACITY, IT IS ADVISABLE TO CONSIDER SHORT POSITIONS THAT ALIGN WITH CONSERVATIVE TRADING STRATEGIES DUE TO POTENTIAL OVERVALUATION RISKS IN CRYPTOCURRENCIES LIKE DOGE/USDT WHICH HAS SHOWN A SLIGHT UPWARD TREND BUT LACKS STRONG MOMENTUM INDICATORS. THE HISTORICAL CONTEXT OF LAST 5 PRICES BEING FLAT AND SIGNALS CONSISTENTLY 'WAIT' SUGGESTS CAUTION, REINFORCING THE DECISION FOR SHORT POSITIONS WITH TIGHT RISK MANAGEMENT PARAMETERS TO PRESERVE ACCOUNT HEALTH WHILE SEEKING POTENTIAL PROFIT OPPORTUNITIES WITHIN CONSERVATIVE BOUNDARIES.", 'ACTION': 'ENTER_NOW'}
2025-07-15 06:42:09,232 - core.llm_response_parsers - INFO - ✅ Structured text parsing successful: ['DECISION', 'CONFIDENCE', 'TAKE_PROFIT', 'STOP_LOSS', 'EXPLANATION', 'ACTION']
2025-07-15 06:42:09,232 - core.llm_response_parsers - INFO - Opportunity scanner parsed: MOMENTUM (REVERSAL)
2025-07-15 06:42:09,232 - core.llm_orchestrator - INFO - ✅ Completed prompt: opportunity_scanner
2025-07-15 06:42:09,233 - core.llm_orchestrator - INFO - 🚀 PARALLEL LLM cycle completed in 8.06s - 3 prompts executed concurrently
2025-07-15 06:42:09,234 - core.vote_aggregator - INFO - 🗳️ Vote aggregation: WAIT (51.6%) - Strong WAIT signal (2.6) → WAIT
2025-07-15 06:42:09,234 - core.llm_orchestrator - INFO - 🗳️ Aggregated Decision: WAIT (51.6%) - Strong WAIT signal (2.6) → WAIT
2025-07-15 06:42:09,234 - core.llm_orchestrator - INFO - 🗳️ Vote Breakdown: SHORT: 1.5, WAIT: 2.6
2025-07-15 06:42:31,391 - core.llm_orchestrator - INFO - 🔍 FULL MODE: Executing all 8 prompts in PARALLEL for comprehensive analysis
2025-07-15 06:42:31,392 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-15 06:42:31,393 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-15 06:42:31,393 - llama.lmstudio_runner - INFO - 💬 User Prompt: 📊 MARKET REGIME ANALYST - DOGE/USDT:USDT

🔍 MULTI-TIMEFRAME ANALYSIS:
1m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
5m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
15m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%

📈 VOLATILITY METRICS:
ATR: 0.001000 | Spread: 0.100%
Volume Ratio: 1.0x | Price Range: 1.00%

🎯 REGIME CLASSIFICATION:
- TRENDING_BULL: Strong upward momentum, high volume, clear direction
- TRENDING_BEAR: Strong downward momentum, high volume, clear direction
- RANGING_TIGHT: Low volatility, narrow pric...
2025-07-15 06:42:31,393 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-15 06:42:31,393 - core.llm_orchestrator - INFO - 🚀 Submitted 2 prompts for parallel execution
2025-07-15 06:42:31,393 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-15 06:42:31,393 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-15 06:42:31,394 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🎯 ENTRY TIMING SPECIALIST - DOGE/USDT:USDT

🎯 SIGNAL ANALYSIS:
ML Ensemble: NEUTRAL (50.0%)
Technical: NEUTRAL | Momentum: 0.00
Volume: PENDING | Orderflow: NEUTRAL

📊 MARKET MICROSTRUCTURE:
Bid/Ask: $0.170000/$0.170000
Spread: 0.100% | L2 Imbalance: 0.0%
Recent Flow: BALANCED | Volume Spike: False

📈 KEY LEVELS:
Support: $0.170000
Resistance: $0.170000
Distance to Support: 0.00%

⏰ TIMING FACTORS:
- Price near key levels: NEUTRAL
- Volume confirmation: PENDING
- Spread conditions: FAVORABLE
- M...
2025-07-15 06:42:31,395 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-15 06:42:35,219 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 155 chars
2025-07-15 06:42:35,220 - llama.lmstudio_runner - INFO - 📄 Response Preview: ```json
{
  "REGIME": "RANGING_TIGHT",
  "CONFIDENCE": 75,
  "SCALP_SUITABILITY": "MEDIUM",
  "RECOMMENDED_TIMEFRAME": "1m",
  "RISK_LEVEL": "MEDIUM"
}
```...
2025-07-15 06:42:35,220 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 705, Completion: 88, Total: 793
2025-07-15 06:42:35,220 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['REGIME', 'CONFIDENCE', 'SCALP_SUITABILITY', 'RECOMMENDED_TIMEFRAME', 'RISK_LEVEL']
2025-07-15 06:42:35,220 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['REGIME', 'CONFIDENCE', 'SCALP_SUITABILITY', 'RECOMMENDED_TIMEFRAME', 'RISK_LEVEL']
2025-07-15 06:42:35,221 - core.llm_response_parsers - INFO - Market regime parsed: RANGING_TIGHT (MEDIUM scalp suitability)
2025-07-15 06:42:35,221 - core.llm_orchestrator - INFO - ✅ Completed prompt: market_regime
2025-07-15 06:42:36,887 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 307 chars
2025-07-15 06:42:36,888 - llama.lmstudio_runner - INFO - 📄 Response Preview: {
  "action": "ENTER_NOW",
  "entry_type": "LIMIT",
  "confidence": 90,
  "wait_for": null,
  "max_wait_seconds": null,
  "reasoning": "Market conditions are favorable with a narrow spread and no significant volume spike. Momentum is neutral but the risk/reward ratio of 2:1 justifies an immediate en...
2025-07-15 06:42:36,889 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 610, Completion: 103, Total: 713
2025-07-15 06:42:36,889 - core.llm_response_parsers - WARNING - 🚨 JSON decode error: Expecting ',' delimiter: line 1 column 261 (char 260)
2025-07-15 06:42:36,890 - core.llm_response_parsers - WARNING - 🔍 Problematic JSON: { "action": "ENTER_NOW", "entry_type": "LIMIT", "confidence": 90, "wait_for": null, "max_wait_seconds": null, "reasoning": "Market conditions are favorable with a narrow spread and no significant volu...
2025-07-15 06:42:36,891 - core.llm_response_parsers - INFO - ✅ Aggressive cleanup extracted: ['action', 'confidence', 'reasoning']
2025-07-15 06:42:36,891 - core.llm_response_parsers - INFO - ✅ JSON fixed with aggressive cleanup
2025-07-15 06:42:36,892 - core.llm_response_parsers - INFO - Entry timing parsed: WAIT (LIMIT order)
2025-07-15 06:42:36,892 - core.llm_orchestrator - INFO - ✅ Completed prompt: entry_timing
2025-07-15 06:42:36,894 - core.llm_orchestrator - INFO - 🚀 PARALLEL LLM cycle completed in 5.50s - 2 prompts executed concurrently
2025-07-15 06:42:36,895 - core.vote_aggregator - INFO - 🗳️ Vote aggregation: WAIT (95.0%) - Clear WAIT signal (weight: 1.1, consensus: 100.0%)
2025-07-15 06:42:36,895 - core.llm_orchestrator - INFO - 🗳️ Aggregated Decision: WAIT (95.0%) - Clear WAIT signal (weight: 1.1, consensus: 100.0%)
2025-07-15 06:42:36,895 - core.llm_orchestrator - INFO - 🗳️ Vote Breakdown: WAIT: 1.1
2025-07-15 06:43:01,135 - core.llm_orchestrator - INFO - 🔍 FULL MODE: Executing all 8 prompts in PARALLEL for comprehensive analysis
2025-07-15 06:43:01,136 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-15 06:43:01,136 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-15 06:43:01,136 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-15 06:43:01,136 - core.llm_orchestrator - INFO - 🚀 Submitted 2 prompts for parallel execution
2025-07-15 06:43:01,136 - llama.lmstudio_runner - INFO - 💬 User Prompt: ...
2025-07-15 06:43:01,137 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-15 06:43:01,137 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-15 06:43:01,137 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🎯 ENTRY TIMING SPECIALIST - DOGE/USDT:USDT

🎯 SIGNAL ANALYSIS:
ML Ensemble: NEUTRAL (50.0%)
Technical: NEUTRAL | Momentum: 0.00
Volume: PENDING | Orderflow: NEUTRAL

📊 MARKET MICROSTRUCTURE:
Bid/Ask: $0.170000/$0.170000
Spread: 0.100% | L2 Imbalance: 0.0%
Recent Flow: BALANCED | Volume Spike: False

📈 KEY LEVELS:
Support: $0.170000
Resistance: $0.170000
Distance to Support: 0.00%

⏰ TIMING FACTORS:
- Price near key levels: NEUTRAL
- Volume confirmation: PENDING
- Spread conditions: FAVORABLE
- M...
2025-07-15 06:43:01,137 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-15 06:43:04,842 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 247 chars
2025-07-15 06:43:04,842 - llama.lmstudio_runner - INFO - 📄 Response Preview: ```json
{
  "action": "LONG",
  "confidence": 90,
  "entry_reason": "Breakout above resistance level with high volume and positive candlestick patterns.",
  "take_profit": 2.5,
  "stop_loss": 1.0,
  "hold_time": "3 minutes",
  "leverage": 40
}
```...
2025-07-15 06:43:04,842 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 174, Completion: 97, Total: 271
2025-07-15 06:43:04,842 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['action', 'confidence', 'entry_reason', 'take_profit', 'stop_loss', 'hold_time', 'leverage']
2025-07-15 06:43:04,843 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['ACTION', 'CONFIDENCE', 'ENTRY_REASON', 'TAKE_PROFIT', 'STOP_LOSS', 'HOLD_TIME', 'LEVERAGE']
2025-07-15 06:43:04,843 - core.llm_response_parsers - INFO - Risk assessment parsed: REJECTED (Risk: 50)
2025-07-15 06:43:04,843 - core.llm_orchestrator - INFO - ✅ Completed prompt: risk_assessment
2025-07-15 06:43:06,647 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 383 chars
2025-07-15 06:43:06,648 - llama.lmstudio_runner - INFO - 📄 Response Preview: ```json
{
  "action": "ENTER_NOW",
  "entry_type": "LIMIT",
  "confidence": 85,
  "wait_for": null,
  "max_wait_seconds": 0,
  "reasoning": "Market conditions are favorable with a neutral signal from ML Ensemble and no pending volume confirmation. The spread is narrow at current levels which indicat...
2025-07-15 06:43:06,648 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 610, Completion: 113, Total: 723
2025-07-15 06:43:06,649 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['action', 'entry_type', 'confidence', 'wait_for', 'max_wait_seconds', 'reasoning']
2025-07-15 06:43:06,649 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['ACTION', 'ENTRY_TYPE', 'CONFIDENCE', 'WAIT_FOR', 'MAX_WAIT_SECONDS', 'REASONING']
2025-07-15 06:43:06,649 - core.llm_response_parsers - INFO - Entry timing parsed: ENTER_NOW (LIMIT order)
2025-07-15 06:43:06,649 - core.llm_action_executors - INFO - Executing entry action: ENTER_NOW (LIMIT order)
2025-07-15 06:43:06,649 - core.llm_action_executors - WARNING - No position capacity available - skipping entry
2025-07-15 06:43:06,650 - core.llm_orchestrator - INFO - ✅ Completed prompt: entry_timing
2025-07-15 06:43:06,650 - core.llm_orchestrator - INFO - 🚀 PARALLEL LLM cycle completed in 5.52s - 2 prompts executed concurrently
2025-07-15 06:43:06,652 - core.vote_aggregator - INFO - 🗳️ Vote aggregation: WAIT (53.0%) - Strong WAIT signal (2.6) → WAIT
2025-07-15 06:43:06,652 - core.llm_orchestrator - INFO - 🗳️ Aggregated Decision: WAIT (53.0%) - Strong WAIT signal (2.6) → WAIT
2025-07-15 06:43:06,653 - core.llm_orchestrator - INFO - 🗳️ Vote Breakdown: WAIT: 2.6
2025-07-15 06:43:31,147 - core.llm_orchestrator - INFO - 🔍 FULL MODE: Executing all 8 prompts in PARALLEL for comprehensive analysis
2025-07-15 06:43:31,148 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-15 06:43:31,149 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-15 06:43:31,149 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-15 06:43:31,149 - core.llm_orchestrator - INFO - 🚀 Submitted 3 prompts for parallel execution
2025-07-15 06:43:31,149 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-15 06:43:31,149 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-15 06:43:31,149 - llama.lmstudio_runner - INFO - 💬 User Prompt: 📊 MARKET REGIME ANALYST - DOGE/USDT:USDT

🔍 MULTI-TIMEFRAME ANALYSIS:
1m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
5m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
15m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%

📈 VOLATILITY METRICS:
ATR: 0.001000 | Spread: 0.100%
Volume Ratio: 1.0x | Price Range: 1.00%

🎯 REGIME CLASSIFICATION:
- TRENDING_BULL: Strong upward momentum, high volume, clear direction
- TRENDING_BEAR: Strong downward momentum, high volume, clear direction
- RANGING_TIGHT: Low volatility, narrow pric...
2025-07-15 06:43:31,150 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-15 06:43:31,150 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a conservative futures trading analyst. Analyze market data carefully and provide measured recommendations. Use format: DECISION: [LONG/SHORT/WAIT], CONFIDENCE: [50-100]%, TAKE_PROFIT: [%], ST...
2025-07-15 06:43:31,150 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🎯 ENTRY TIMING SPECIALIST - DOGE/USDT:USDT

🎯 SIGNAL ANALYSIS:
ML Ensemble: NEUTRAL (50.0%)
Technical: NEUTRAL | Momentum: 0.00
Volume: PENDING | Orderflow: NEUTRAL

📊 MARKET MICROSTRUCTURE:
Bid/Ask: $0.170000/$0.170000
Spread: 0.100% | L2 Imbalance: 0.0%
Recent Flow: BALANCED | Volume Spike: False

📈 KEY LEVELS:
Support: $0.170000
Resistance: $0.170000
Distance to Support: 0.00%

⏰ TIMING FACTORS:
- Price near key levels: NEUTRAL
- Volume confirmation: PENDING
- Spread conditions: FAVORABLE
- M...
2025-07-15 06:43:31,151 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🔍 INTELLIGENT OPPORTUNITY SCANNER - Account Health Aware

🚨 ACCOUNT HEALTH STATUS: HEALTHY
💰 Balance: $1000.00 | Health: Healthy - Normal Trading
📊 Current Exposure: $0.00 (0.0% of account)
🎯 Trading Allowed: YES

📊 SYMBOL ANALYSIS:
• DOGE/USDT:USDT: $0.000000 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• BTC/USDT:USDT: $95000.000000 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• ETH/USDT:USDT: $3500.000000 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• SOL/USDT:USDT: $200.000000 | Mom: +0.0% | Vol: 1.0x |...
2025-07-15 06:43:31,151 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-15 06:43:31,152 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-15 06:43:35,095 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 359 chars
2025-07-15 06:43:35,095 - llama.lmstudio_runner - INFO - 📄 Response Preview: {
  "action": "ENTER_NOW",
  "entry_type": "LIMIT",
  "confidence": 92,
  "wait_for": null,
  "max_wait_seconds": 0,
  "reasoning": "Market conditions are favorable with a NEUTRAL signal from the ML Ensemble and FAVORABLE spread. The volume is confirmed as balanced which indicates liquidity without ...
2025-07-15 06:43:35,095 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 610, Completion: 111, Total: 721
2025-07-15 06:43:35,096 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['action', 'entry_type', 'confidence', 'wait_for', 'max_wait_seconds', 'reasoning']
2025-07-15 06:43:35,096 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['ACTION', 'ENTRY_TYPE', 'CONFIDENCE', 'WAIT_FOR', 'MAX_WAIT_SECONDS', 'REASONING']
2025-07-15 06:43:35,096 - core.llm_response_parsers - INFO - Entry timing parsed: ENTER_NOW (LIMIT order)
2025-07-15 06:43:35,096 - core.llm_action_executors - INFO - Executing entry action: ENTER_NOW (LIMIT order)
2025-07-15 06:43:35,096 - core.llm_action_executors - WARNING - No position capacity available - skipping entry
2025-07-15 06:43:35,097 - core.llm_orchestrator - INFO - ✅ Completed prompt: entry_timing
2025-07-15 06:43:38,387 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 869 chars
2025-07-15 06:43:38,388 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: LONG, CONFIDENCE: 80%, TAKE_PROFIT: 75%, STOP_LOSS: -125%, EXPLANATION: The current account health is good with a balanced and normal trading environment. Given the neutral sector momentum but average volume profile indicating stable market conditions, there's an opportunity to take advant...
2025-07-15 06:43:38,388 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 933, Completion: 197, Total: 1130
2025-07-15 06:43:38,389 - core.llm_response_parsers - INFO - 🔄 No JSON found, trying structured text parsing
2025-07-15 06:43:38,389 - core.llm_response_parsers - INFO - Parsed structured text: {'DECISION': 'LONG', 'CONFIDENCE': 80.0, 'TAKE_PROFIT': 75.0, 'STOP_LOSS': -125.0, 'EXPLANATION': "THE CURRENT ACCOUNT HEALTH IS GOOD WITH A BALANCED AND NORMAL TRADING ENVIRONMENT. GIVEN THE NEUTRAL SECTOR MOMENTUM BUT AVERAGE VOLUME PROFILE INDICATING STABLE MARKET CONDITIONS, THERE'S AN OPPORTUNITY TO TAKE ADVANTAGE OF POTENTIAL SHORT-TERM GAINS WITHOUT EXPOSING TOO MUCH RISK. DOGE/USDT SHOWS MEDIUM SETUP QUALITY WHICH ALIGNS WELL WITH OUR CONSERVATIVE APPROACH WHILE ALSO PROVIDING A CLEAR ENTRY POINT DUE TO ITS LIQUIDITY AND LOW VOLATILITY WITHIN THE DESIRED RANGE (1-3%). THE HISTORICAL CONTEXT SUGGESTS THAT WE SHOULD WAIT FOR CONFIRMATION OF AN UPWARD TREND, BUT GIVEN THE HIGH CONFIDENCE IN THIS OPPORTUNITY'S POTENTIAL BASED ON CURRENT MARKET DATA ANALYSIS, IT IS RECOMMENDED TO TAKE ADVANTAGE OF A CONSERVATIVE ENTRY POSITION WITH APPROPRIATE RISK MANAGEMENT STRATEGIES.", 'ACTION': 'ENTER_NOW'}
2025-07-15 06:43:38,390 - core.llm_response_parsers - INFO - ✅ Structured text parsing successful: ['DECISION', 'CONFIDENCE', 'TAKE_PROFIT', 'STOP_LOSS', 'EXPLANATION', 'ACTION']
2025-07-15 06:43:38,390 - core.llm_response_parsers - INFO - Opportunity scanner parsed: BREAKOUT (MOMENTUM)
2025-07-15 06:43:38,391 - core.llm_orchestrator - INFO - ✅ Completed prompt: opportunity_scanner
2025-07-15 06:43:39,885 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 155 chars
2025-07-15 06:43:39,885 - llama.lmstudio_runner - INFO - 📄 Response Preview: ```json
{
  "REGIME": "RANGING_TIGHT",
  "CONFIDENCE": 75,
  "SCALP_SUITABILITY": "MEDIUM",
  "RECOMMENDED_TIMEFRAME": "1m",
  "RISK_LEVEL": "MEDIUM"
}
```...
2025-07-15 06:43:39,885 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 705, Completion: 88, Total: 793
2025-07-15 06:43:39,885 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['REGIME', 'CONFIDENCE', 'SCALP_SUITABILITY', 'RECOMMENDED_TIMEFRAME', 'RISK_LEVEL']
2025-07-15 06:43:39,886 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['REGIME', 'CONFIDENCE', 'SCALP_SUITABILITY', 'RECOMMENDED_TIMEFRAME', 'RISK_LEVEL']
2025-07-15 06:43:39,886 - core.llm_response_parsers - INFO - Market regime parsed: RANGING_TIGHT (MEDIUM scalp suitability)
2025-07-15 06:43:39,886 - core.llm_orchestrator - INFO - ✅ Completed prompt: market_regime
2025-07-15 06:43:39,887 - core.llm_orchestrator - INFO - 🚀 PARALLEL LLM cycle completed in 8.74s - 3 prompts executed concurrently
2025-07-15 06:43:39,887 - core.vote_aggregator - INFO - 🗳️ Vote aggregation: LONG (51.9%) - Clear LONG signal (weight: 1.6, consensus: 51.9%)
2025-07-15 06:43:39,887 - core.llm_orchestrator - INFO - 🗳️ Aggregated Decision: LONG (51.9%) - Clear LONG signal (weight: 1.6, consensus: 51.9%)
2025-07-15 06:43:39,888 - core.llm_orchestrator - INFO - 🗳️ Vote Breakdown: LONG: 1.6, WAIT: 1.5
2025-07-15 06:44:01,146 - core.llm_orchestrator - INFO - 🔍 FULL MODE: Executing all 8 prompts in PARALLEL for comprehensive analysis
2025-07-15 06:44:01,147 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-15 06:44:01,147 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-15 06:44:01,147 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-15 06:44:01,147 - llama.lmstudio_runner - INFO - 💬 User Prompt: ...
2025-07-15 06:44:01,147 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-15 06:44:01,147 - core.llm_orchestrator - INFO - 🚀 Submitted 3 prompts for parallel execution
2025-07-15 06:44:01,147 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-15 06:44:01,148 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-15 06:44:01,148 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-15 06:44:01,148 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🎯 ENTRY TIMING SPECIALIST - DOGE/USDT:USDT

🎯 SIGNAL ANALYSIS:
ML Ensemble: NEUTRAL (50.0%)
Technical: NEUTRAL | Momentum: 0.00
Volume: PENDING | Orderflow: NEUTRAL

📊 MARKET MICROSTRUCTURE:
Bid/Ask: $0.170000/$0.170000
Spread: 0.100% | L2 Imbalance: 0.0%
Recent Flow: BALANCED | Volume Spike: False

📈 KEY LEVELS:
Support: $0.170000
Resistance: $0.170000
Distance to Support: 0.00%

⏰ TIMING FACTORS:
- Price near key levels: NEUTRAL
- Volume confirmation: PENDING
- Spread conditions: FAVORABLE
- M...
2025-07-15 06:44:01,148 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🔄 STRATEGY ADAPTATION SPECIALIST

📊 PERFORMANCE ANALYSIS (Last 24h):
Trades: 0 | Win Rate: 50.0%
Avg Profit: 0.80% | Avg Loss: -0.30%
Sharpe Ratio: 1.00 | Max Drawdown: 0.0%
Total PnL: $0.00 | ROI: 0.0%

🎯 CURRENT STRATEGY:
Risk per Trade: 2.0% | Avg Hold Time: 8.0min
Entry Threshold: 70% | Exit Threshold: 60%
Position Size Method: FIXED_RISK | Max Positions: 3

📈 MARKET REGIME: UNKNOWN
Regime Confidence: 50.0%
Scalp Suitability: MEDIUM

🔧 ADAPTATION FACTORS:
- Win rate trending: DOWN
- Drawdown...
2025-07-15 06:44:01,149 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-15 06:44:01,149 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-15 06:44:04,843 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 283 chars
2025-07-15 06:44:04,844 - llama.lmstudio_runner - INFO - 📄 Response Preview: {"risk_adjustment":1.5, "hold_time_target":30, "entry_threshold":75, "exit_threshold":65, "sizing_method":"VARIABLE", "reasoning":"Increase risk slightly due to recent poor performance while tightening hold time and adjusting thresholds for better entry/exit timing","confidence":80}...
2025-07-15 06:44:04,844 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 619, Completion: 88, Total: 707
2025-07-15 06:44:04,844 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['risk_adjustment', 'hold_time_target', 'entry_threshold', 'exit_threshold', 'sizing_method', 'reasoning', 'confidence']
2025-07-15 06:44:04,845 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['RISK_ADJUSTMENT', 'HOLD_TIME_TARGET', 'ENTRY_THRESHOLD', 'EXIT_THRESHOLD', 'SIZING_METHOD', 'REASONING', 'CONFIDENCE']
2025-07-15 06:44:04,845 - core.llm_response_parsers - INFO - Strategy adaptation parsed: Risk adj 1.5x
2025-07-15 06:44:04,845 - core.llm_action_executors - INFO - Applying strategy adaptations: Risk 1.5x, Hold time 30min
2025-07-15 06:44:04,845 - core.llm_orchestrator - INFO - ✅ Completed prompt: strategy_adaptation
2025-07-15 06:44:06,428 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 310 chars
2025-07-15 06:44:06,428 - llama.lmstudio_runner - INFO - 📄 Response Preview: {
  "action": "ENTER_NOW",
  "entry_type": "LIMIT",
  "confidence": 85,
  "wait_for": null,
  "max_wait_seconds": 0,
  "reasoning": "Market conditions are favorable with a FAVORABLE spread and price near key levels. The historical context shows no recent volatility which aligns well for an immediate...
2025-07-15 06:44:06,428 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 610, Completion: 101, Total: 711
2025-07-15 06:44:06,429 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['action', 'entry_type', 'confidence', 'wait_for', 'max_wait_seconds', 'reasoning']
2025-07-15 06:44:06,429 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['ACTION', 'ENTRY_TYPE', 'CONFIDENCE', 'WAIT_FOR', 'MAX_WAIT_SECONDS', 'REASONING']
2025-07-15 06:44:06,429 - core.llm_response_parsers - INFO - Entry timing parsed: ENTER_NOW (LIMIT order)
2025-07-15 06:44:06,429 - core.llm_action_executors - INFO - Executing entry action: ENTER_NOW (LIMIT order)
2025-07-15 06:44:06,430 - core.llm_action_executors - WARNING - No position capacity available - skipping entry
2025-07-15 06:44:06,430 - core.llm_orchestrator - INFO - ✅ Completed prompt: entry_timing
2025-07-15 06:44:07,876 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 234 chars
2025-07-15 06:44:07,876 - llama.lmstudio_runner - INFO - 📄 Response Preview: ```json
{
  "action": "LONG",
  "confidence": 90,
  "entry_reason": "Market broke through support level at $51.75 with a sudden spike to $52.30.",
  "take_profit": 2.5,
  "stop_loss": 1.8,
  "hold_time": "4min",
  "leverage": 30
}
```...
2025-07-15 06:44:07,876 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 174, Completion: 106, Total: 280
2025-07-15 06:44:07,877 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['action', 'confidence', 'entry_reason', 'take_profit', 'stop_loss', 'hold_time', 'leverage']
2025-07-15 06:44:07,877 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['ACTION', 'CONFIDENCE', 'ENTRY_REASON', 'TAKE_PROFIT', 'STOP_LOSS', 'HOLD_TIME', 'LEVERAGE']
2025-07-15 06:44:07,877 - core.llm_response_parsers - INFO - Risk assessment parsed: REJECTED (Risk: 50)
2025-07-15 06:44:07,878 - core.llm_orchestrator - INFO - ✅ Completed prompt: risk_assessment
2025-07-15 06:44:07,878 - core.llm_orchestrator - INFO - 🚀 PARALLEL LLM cycle completed in 6.73s - 3 prompts executed concurrently
2025-07-15 06:44:07,879 - core.vote_aggregator - INFO - 🗳️ Vote aggregation: WAIT (57.0%) - Strong WAIT signal (2.9) → WAIT
2025-07-15 06:44:07,879 - core.llm_orchestrator - INFO - 🗳️ Aggregated Decision: WAIT (57.0%) - Strong WAIT signal (2.9) → WAIT
2025-07-15 06:44:07,879 - core.llm_orchestrator - INFO - 🗳️ Vote Breakdown: WAIT: 2.9
2025-07-15 06:44:31,209 - core.llm_orchestrator - INFO - 🔍 FULL MODE: Executing all 8 prompts in PARALLEL for comprehensive analysis
2025-07-15 06:44:31,210 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-15 06:44:31,210 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-15 06:44:31,211 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-15 06:44:31,211 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-15 06:44:31,211 - llama.lmstudio_runner - INFO - 💬 User Prompt: 📊 MARKET REGIME ANALYST - DOGE/USDT:USDT

🔍 MULTI-TIMEFRAME ANALYSIS:
1m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
5m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
15m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%

📈 VOLATILITY METRICS:
ATR: 0.001000 | Spread: 0.100%
Volume Ratio: 1.0x | Price Range: 1.00%

🎯 REGIME CLASSIFICATION:
- TRENDING_BULL: Strong upward momentum, high volume, clear direction
- TRENDING_BEAR: Strong downward momentum, high volume, clear direction
- RANGING_TIGHT: Low volatility, narrow pric...
2025-07-15 06:44:31,212 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-15 06:44:31,211 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🎯 ENTRY TIMING SPECIALIST - DOGE/USDT:USDT

🎯 SIGNAL ANALYSIS:
ML Ensemble: NEUTRAL (50.0%)
Technical: NEUTRAL | Momentum: 0.00
Volume: PENDING | Orderflow: NEUTRAL

📊 MARKET MICROSTRUCTURE:
Bid/Ask: $0.170000/$0.170000
Spread: 0.100% | L2 Imbalance: 0.0%
Recent Flow: BALANCED | Volume Spike: False

📈 KEY LEVELS:
Support: $0.170000
Resistance: $0.170000
Distance to Support: 0.00%

⏰ TIMING FACTORS:
- Price near key levels: NEUTRAL
- Volume confirmation: PENDING
- Spread conditions: FAVORABLE
- M...
2025-07-15 06:44:31,211 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-15 06:44:31,212 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a conservative futures trading analyst. Analyze market data carefully and provide measured recommendations. Use format: DECISION: [LONG/SHORT/WAIT], CONFIDENCE: [50-100]%, TAKE_PROFIT: [%], ST...
2025-07-15 06:44:31,212 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-15 06:44:31,211 - core.llm_orchestrator - INFO - 🚀 Submitted 3 prompts for parallel execution
2025-07-15 06:44:31,213 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🔍 INTELLIGENT OPPORTUNITY SCANNER - Account Health Aware

🚨 ACCOUNT HEALTH STATUS: HEALTHY
💰 Balance: $1000.00 | Health: Healthy - Normal Trading
📊 Current Exposure: $0.00 (0.0% of account)
🎯 Trading Allowed: YES

📊 SYMBOL ANALYSIS:
• DOGE/USDT:USDT: $0.000000 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• BTC/USDT:USDT: $95000.000000 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• ETH/USDT:USDT: $3500.000000 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• SOL/USDT:USDT: $200.000000 | Mom: +0.0% | Vol: 1.0x |...
2025-07-15 06:44:31,214 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-15 06:44:37,412 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 1015 chars
2025-07-15 06:44:37,412 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: SHORT, CONFIDENCE: 60%, TAKE_PROFIT: -2.0%, STOP_LOSS: -3.0%, EXPLANATION: Given the current market conditions with neutral sector momentum and normal volatility levels alongside an account in a healthy state that allows for trading, it is prudent to consider short positions on cryptocurre...
2025-07-15 06:44:37,412 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 933, Completion: 243, Total: 1176
2025-07-15 06:44:37,413 - core.llm_response_parsers - INFO - 🔄 No JSON found, trying structured text parsing
2025-07-15 06:44:37,413 - core.llm_response_parsers - INFO - Parsed structured text: {'DECISION': 'SHORT', 'CONFIDENCE': 60.0, 'STOP_LOSS': -3.0, 'EXPLANATION': "GIVEN THE CURRENT MARKET CONDITIONS WITH NEUTRAL SECTOR MOMENTUM AND NORMAL VOLATILITY LEVELS ALONGSIDE AN ACCOUNT IN A HEALTHY STATE THAT ALLOWS FOR TRADING, IT IS PRUDENT TO CONSIDER SHORT POSITIONS ON CRYPTOCURRENCIES LIKE DOGE/USDT WHICH HAVE SHOWN NO SIGNIFICANT MOVEMENT (MOM +0.0%) BUT STILL MAINTAIN MEDIUM SETUP QUALITY DUE TO THE LACK OF CLEAR PATTERNS OR STRONG SIGNALS FROM HISTORICAL DATA AND CURRENT MARKET ANALYSIS. THE CONSERVATIVE ENTRY STRATEGY ALIGNS WITH OUR ACCOUNT PRESERVATION CRITERIA, ENSURING THAT WE DO NOT EXCEED 2% RISK PER TRADE RELATIVE TO THE HEALTH-ADJUSTED BALANCE WHILE KEEPING TOTAL EXPOSURE WITHIN A SAFE LIMIT (70%). A TAKE PROFIT AT -2.0% PROVIDES AN EARLY EXIT POINT IF PRICES START MOVING AGAINST US BEFORE REACHING STOP LOSS LEVELS SET AT -3.0%, WHICH IS SLIGHTLY HIGHER THAN OUR PREFERRED CONSERVATIVE VOLATILITY RANGE, BUT STILL MANAGEABLE AND PROTECTIVE OF THE ACCOUNT'S HEALTH STATUS.", 'ACTION': 'ENTER_NOW'}
2025-07-15 06:44:37,413 - core.llm_response_parsers - INFO - ✅ Structured text parsing successful: ['DECISION', 'CONFIDENCE', 'STOP_LOSS', 'EXPLANATION', 'ACTION']
2025-07-15 06:44:37,413 - core.llm_response_parsers - INFO - Opportunity scanner parsed: MOMENTUM (REVERSAL)
2025-07-15 06:44:37,414 - core.llm_orchestrator - INFO - ✅ Completed prompt: opportunity_scanner
2025-07-15 06:44:39,167 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 324 chars
2025-07-15 06:44:39,167 - llama.lmstudio_runner - INFO - 📄 Response Preview: {
  "action": "ENTER",
  "entry_type": "LIMIT",
  "confidence": 80,
  "wait_for": null,
  "max_wait_seconds": 30,
  "reasoning": "Market conditions are favorable with a NEUTRAL signal from ML Ensemble and L2 Imbalance at zero. The spread is narrow (favorable), suggesting high liquidity without signi...
2025-07-15 06:44:39,168 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 610, Completion: 108, Total: 718
2025-07-15 06:44:39,168 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['action', 'entry_type', 'confidence', 'wait_for', 'max_wait_seconds', 'reasoning']
2025-07-15 06:44:39,168 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['ACTION', 'ENTRY_TYPE', 'CONFIDENCE', 'WAIT_FOR', 'MAX_WAIT_SECONDS', 'REASONING']
2025-07-15 06:44:39,168 - core.llm_response_parsers - INFO - Entry timing parsed: WAIT (LIMIT order)
2025-07-15 06:44:39,169 - core.llm_orchestrator - INFO - ✅ Completed prompt: entry_timing
2025-07-15 06:44:40,920 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 218 chars
2025-07-15 06:44:40,920 - llama.lmstudio_runner - INFO - 📄 Response Preview: ```json
{
  "REGIME": "RANGING_TIGHT",
  "CONFIDENCE": 75,
  "SCALP_SUITABILITY": "MEDIUM",
  "RECOMMENDED_TIMEFRAME": "1m",
  "RISK_LEVEL": "MEDIUM",
  "REASONING": "Consolidation phase with moderate volatility"
}
```...
2025-07-15 06:44:40,921 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 705, Completion: 108, Total: 813
2025-07-15 06:44:40,921 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['REGIME', 'CONFIDENCE', 'SCALP_SUITABILITY', 'RECOMMENDED_TIMEFRAME', 'RISK_LEVEL', 'REASONING']
2025-07-15 06:44:40,921 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['REGIME', 'CONFIDENCE', 'SCALP_SUITABILITY', 'RECOMMENDED_TIMEFRAME', 'RISK_LEVEL', 'REASONING']
2025-07-15 06:44:40,921 - core.llm_response_parsers - INFO - Market regime parsed: RANGING_TIGHT (MEDIUM scalp suitability)
2025-07-15 06:44:40,921 - core.llm_orchestrator - INFO - ✅ Completed prompt: market_regime
2025-07-15 06:44:40,922 - core.llm_orchestrator - INFO - 🚀 PARALLEL LLM cycle completed in 9.71s - 3 prompts executed concurrently
2025-07-15 06:44:40,923 - core.vote_aggregator - INFO - 🗳️ Vote aggregation: WAIT (53.2%) - Clear WAIT signal (weight: 1.4, consensus: 53.2%)
2025-07-15 06:44:40,923 - core.llm_orchestrator - INFO - 🗳️ Aggregated Decision: WAIT (53.2%) - Clear WAIT signal (weight: 1.4, consensus: 53.2%)
2025-07-15 06:44:40,923 - core.llm_orchestrator - INFO - 🗳️ Vote Breakdown: SHORT: 1.2, WAIT: 1.4
2025-07-15 06:45:01,735 - core.llm_orchestrator - INFO - 🔍 FULL MODE: Executing all 8 prompts in PARALLEL for comprehensive analysis
2025-07-15 06:45:01,736 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-15 06:45:01,736 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-15 06:45:01,736 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-15 06:45:01,736 - llama.lmstudio_runner - INFO - 💬 User Prompt: 📊 MARKET REGIME ANALYST - DOGE/USDT:USDT

🔍 MULTI-TIMEFRAME ANALYSIS:
1m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
5m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
15m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%

📈 VOLATILITY METRICS:
ATR: 0.001000 | Spread: 0.100%
Volume Ratio: 1.0x | Price Range: 1.00%

🎯 REGIME CLASSIFICATION:
- TRENDING_BULL: Strong upward momentum, high volume, clear direction
- TRENDING_BEAR: Strong downward momentum, high volume, clear direction
- RANGING_TIGHT: Low volatility, narrow pric...
2025-07-15 06:45:01,737 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-15 06:45:01,737 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-15 06:45:01,737 - core.llm_orchestrator - INFO - 🚀 Submitted 3 prompts for parallel execution
2025-07-15 06:45:01,737 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-15 06:45:01,737 - llama.lmstudio_runner - INFO - 💬 User Prompt: ...
2025-07-15 06:45:01,739 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-15 06:45:01,740 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-15 06:45:01,740 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🎯 ENTRY TIMING SPECIALIST - DOGE/USDT:USDT

🎯 SIGNAL ANALYSIS:
ML Ensemble: NEUTRAL (50.0%)
Technical: NEUTRAL | Momentum: 0.00
Volume: PENDING | Orderflow: NEUTRAL

📊 MARKET MICROSTRUCTURE:
Bid/Ask: $0.170000/$0.170000
Spread: 0.100% | L2 Imbalance: 0.0%
Recent Flow: BALANCED | Volume Spike: False

📈 KEY LEVELS:
Support: $0.170000
Resistance: $0.170000
Distance to Support: 0.00%

⏰ TIMING FACTORS:
- Price near key levels: NEUTRAL
- Volume confirmation: PENDING
- Spread conditions: FAVORABLE
- M...
2025-07-15 06:45:01,740 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-15 06:45:05,371 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 227 chars
2025-07-15 06:45:05,371 - llama.lmstudio_runner - INFO - 📄 Response Preview: ```json
{
  "action": "LONG",
  "confidence": 90,
  "entry_reason": "Market broke through resistance level with a sudden surge in volume.",
  "take_profit": 1.5,
  "stop_loss": 0.3,
  "hold_time": "3min",
  "leverage": 20
}
```...
2025-07-15 06:45:05,371 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 174, Completion: 94, Total: 268
2025-07-15 06:45:05,371 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['action', 'confidence', 'entry_reason', 'take_profit', 'stop_loss', 'hold_time', 'leverage']
2025-07-15 06:45:05,371 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['ACTION', 'CONFIDENCE', 'ENTRY_REASON', 'TAKE_PROFIT', 'STOP_LOSS', 'HOLD_TIME', 'LEVERAGE']
2025-07-15 06:45:05,372 - core.llm_response_parsers - INFO - Risk assessment parsed: REJECTED (Risk: 50)
2025-07-15 06:45:05,372 - core.llm_orchestrator - INFO - ✅ Completed prompt: risk_assessment
2025-07-15 06:45:07,625 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 393 chars
2025-07-15 06:45:07,626 - llama.lmstudio_runner - INFO - 📄 Response Preview: ```json
{
  "REGIME": "RANGING_TIGHT",
  "CONFIDENCE": 75,
  "SCALP_SUITABILITY": "MEDIUM",
  "RECOMMENDED_TIMEFRAME": "1m",
  "RISK_LEVEL": "MEDIUM",
  "REASONING": "Consolidation phase with moderate volatility indicates a stable market, suitable for scalping if managed carefully. The medium risk l...
2025-07-15 06:45:07,626 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 705, Completion: 141, Total: 846
2025-07-15 06:45:07,626 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['REGIME', 'CONFIDENCE', 'SCALP_SUITABILITY', 'RECOMMENDED_TIMEFRAME', 'RISK_LEVEL', 'REASONING']
2025-07-15 06:45:07,627 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['REGIME', 'CONFIDENCE', 'SCALP_SUITABILITY', 'RECOMMENDED_TIMEFRAME', 'RISK_LEVEL', 'REASONING']
2025-07-15 06:45:07,627 - core.llm_response_parsers - INFO - Market regime parsed: RANGING_TIGHT (MEDIUM scalp suitability)
2025-07-15 06:45:07,627 - core.llm_orchestrator - INFO - ✅ Completed prompt: market_regime
2025-07-15 06:45:09,207 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 318 chars
2025-07-15 06:45:09,207 - llama.lmstudio_runner - INFO - 📄 Response Preview: {
  "action": "ENTER_NOW",
  "entry_type": "LIMIT",
  "confidence": 85,
  "wait_for": null,
  "max_wait_seconds": 0,
  "reasoning": "Market conditions are favorable with a narrow spread and no significant imbalance. The price is near key levels indicating potential for movement without immediate ris...
2025-07-15 06:45:09,208 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 610, Completion: 98, Total: 708
2025-07-15 06:45:09,208 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['action', 'entry_type', 'confidence', 'wait_for', 'max_wait_seconds', 'reasoning']
2025-07-15 06:45:09,208 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['ACTION', 'ENTRY_TYPE', 'CONFIDENCE', 'WAIT_FOR', 'MAX_WAIT_SECONDS', 'REASONING']
2025-07-15 06:45:09,209 - core.llm_response_parsers - INFO - Entry timing parsed: ENTER_NOW (LIMIT order)
2025-07-15 06:45:09,209 - core.llm_action_executors - INFO - Executing entry action: ENTER_NOW (LIMIT order)
2025-07-15 06:45:09,209 - core.llm_action_executors - WARNING - No position capacity available - skipping entry
2025-07-15 06:45:09,210 - core.llm_orchestrator - INFO - ✅ Completed prompt: entry_timing
2025-07-15 06:45:09,211 - core.llm_orchestrator - INFO - 🚀 PARALLEL LLM cycle completed in 7.48s - 3 prompts executed concurrently
2025-07-15 06:45:09,212 - core.vote_aggregator - INFO - 🗳️ Vote aggregation: WAIT (64.2%) - Strong WAIT signal (3.2) → WAIT
2025-07-15 06:45:09,212 - core.llm_orchestrator - INFO - 🗳️ Aggregated Decision: WAIT (64.2%) - Strong WAIT signal (3.2) → WAIT
2025-07-15 06:45:09,212 - core.llm_orchestrator - INFO - 🗳️ Vote Breakdown: WAIT: 3.2
2025-07-15 06:45:31,172 - core.llm_orchestrator - INFO - 🔍 FULL MODE: Executing all 8 prompts in PARALLEL for comprehensive analysis
2025-07-15 06:45:31,174 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-15 06:45:31,174 - core.llm_orchestrator - INFO - 🚀 Submitted 1 prompts for parallel execution
2025-07-15 06:45:31,174 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-15 06:45:31,175 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🎯 ENTRY TIMING SPECIALIST - DOGE/USDT:USDT

🎯 SIGNAL ANALYSIS:
ML Ensemble: NEUTRAL (50.0%)
Technical: NEUTRAL | Momentum: 0.00
Volume: PENDING | Orderflow: NEUTRAL

📊 MARKET MICROSTRUCTURE:
Bid/Ask: $0.170000/$0.170000
Spread: 0.100% | L2 Imbalance: 0.0%
Recent Flow: BALANCED | Volume Spike: False

📈 KEY LEVELS:
Support: $0.170000
Resistance: $0.170000
Distance to Support: 0.00%

⏰ TIMING FACTORS:
- Price near key levels: NEUTRAL
- Volume confirmation: PENDING
- Spread conditions: FAVORABLE
- M...
2025-07-15 06:45:31,175 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-15 06:45:35,060 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 352 chars
2025-07-15 06:45:35,060 - llama.lmstudio_runner - INFO - 📄 Response Preview: ```json
{
  "action": "ENTER_NOW",
  "entry_type": "LIMIT",
  "confidence": 80,
  "wait_for": null,
  "max_wait_seconds": -1,
  "reasoning": "Market conditions are favorable with a narrow spread and no significant imbalance. The price is near key levels indicating potential for movement without imme...
2025-07-15 06:45:35,061 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 610, Completion: 105, Total: 715
2025-07-15 06:45:35,061 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['action', 'entry_type', 'confidence', 'wait_for', 'max_wait_seconds', 'reasoning']
2025-07-15 06:45:35,061 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['ACTION', 'ENTRY_TYPE', 'CONFIDENCE', 'WAIT_FOR', 'MAX_WAIT_SECONDS', 'REASONING']
2025-07-15 06:45:35,062 - core.llm_response_parsers - INFO - Entry timing parsed: ENTER_NOW (LIMIT order)
2025-07-15 06:45:35,062 - core.llm_action_executors - INFO - Executing entry action: ENTER_NOW (LIMIT order)
2025-07-15 06:45:35,062 - core.llm_action_executors - WARNING - No position capacity available - skipping entry
2025-07-15 06:45:35,063 - core.llm_orchestrator - INFO - ✅ Completed prompt: entry_timing
2025-07-15 06:45:35,063 - core.llm_orchestrator - INFO - 🚀 PARALLEL LLM cycle completed in 3.89s - 1 prompts executed concurrently
2025-07-15 06:45:35,064 - core.vote_aggregator - INFO - 🗳️ Vote aggregation: WAIT (40.0%) - Insufficient vote weight (0.8) → WAIT
2025-07-15 06:45:35,064 - core.llm_orchestrator - INFO - 🗳️ Aggregated Decision: WAIT (40.0%) - Insufficient vote weight (0.8) → WAIT
2025-07-15 06:45:35,064 - core.llm_orchestrator - INFO - 🗳️ Vote Breakdown: WAIT: 0.8
2025-07-15 06:46:01,675 - core.llm_orchestrator - INFO - 🔍 FULL MODE: Executing all 8 prompts in PARALLEL for comprehensive analysis
2025-07-15 06:46:01,676 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-15 06:46:01,678 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-15 06:46:01,678 - llama.lmstudio_runner - INFO - 💬 User Prompt: 📊 MARKET REGIME ANALYST - DOGE/USDT:USDT

🔍 MULTI-TIMEFRAME ANALYSIS:
1m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
5m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
15m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%

📈 VOLATILITY METRICS:
ATR: 0.001000 | Spread: 0.100%
Volume Ratio: 1.0x | Price Range: 1.00%

🎯 REGIME CLASSIFICATION:
- TRENDING_BULL: Strong upward momentum, high volume, clear direction
- TRENDING_BEAR: Strong downward momentum, high volume, clear direction
- RANGING_TIGHT: Low volatility, narrow pric...
2025-07-15 06:46:01,678 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-15 06:46:01,679 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-15 06:46:01,679 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-15 06:46:01,679 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-15 06:46:01,680 - core.llm_orchestrator - INFO - 🚀 Submitted 5 prompts for parallel execution
2025-07-15 06:46:01,680 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-15 06:46:01,680 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-15 06:46:01,679 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-15 06:46:01,681 - llama.lmstudio_runner - INFO - 💬 User Prompt: ...
2025-07-15 06:46:01,682 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-15 06:46:01,682 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a conservative futures trading analyst. Analyze market data carefully and provide measured recommendations. Use format: DECISION: [LONG/SHORT/WAIT], CONFIDENCE: [50-100]%, TAKE_PROFIT: [%], ST...
2025-07-15 06:46:01,682 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🎯 ENTRY TIMING SPECIALIST - DOGE/USDT:USDT

🎯 SIGNAL ANALYSIS:
ML Ensemble: NEUTRAL (50.0%)
Technical: NEUTRAL | Momentum: 0.00
Volume: PENDING | Orderflow: NEUTRAL

📊 MARKET MICROSTRUCTURE:
Bid/Ask: $0.170000/$0.170000
Spread: 0.100% | L2 Imbalance: 0.0%
Recent Flow: BALANCED | Volume Spike: False

📈 KEY LEVELS:
Support: $0.170000
Resistance: $0.170000
Distance to Support: 0.00%

⏰ TIMING FACTORS:
- Price near key levels: NEUTRAL
- Volume confirmation: PENDING
- Spread conditions: FAVORABLE
- M...
2025-07-15 06:46:01,682 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-15 06:46:01,682 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🔄 STRATEGY ADAPTATION SPECIALIST

📊 PERFORMANCE ANALYSIS (Last 24h):
Trades: 0 | Win Rate: 50.0%
Avg Profit: 0.80% | Avg Loss: -0.30%
Sharpe Ratio: 1.00 | Max Drawdown: 0.0%
Total PnL: $0.00 | ROI: 0.0%

🎯 CURRENT STRATEGY:
Risk per Trade: 2.0% | Avg Hold Time: 8.0min
Entry Threshold: 70% | Exit Threshold: 60%
Position Size Method: FIXED_RISK | Max Positions: 3

📈 MARKET REGIME: UNKNOWN
Regime Confidence: 50.0%
Scalp Suitability: MEDIUM

🔧 ADAPTATION FACTORS:
- Win rate trending: DOWN
- Drawdown...
2025-07-15 06:46:01,684 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-15 06:46:01,683 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-15 06:46:01,683 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🔍 INTELLIGENT OPPORTUNITY SCANNER - Account Health Aware

🚨 ACCOUNT HEALTH STATUS: HEALTHY
💰 Balance: $1000.00 | Health: Healthy - Normal Trading
📊 Current Exposure: $0.00 (0.0% of account)
🎯 Trading Allowed: YES

📊 SYMBOL ANALYSIS:
• DOGE/USDT:USDT: $0.000000 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• BTC/USDT:USDT: $95000.000000 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• ETH/USDT:USDT: $3500.000000 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• SOL/USDT:USDT: $200.000000 | Mom: +0.0% | Vol: 1.0x |...
2025-07-15 06:46:01,686 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-15 06:46:05,511 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 352 chars
2025-07-15 06:46:05,512 - llama.lmstudio_runner - INFO - 📄 Response Preview: ```json
{
  "action": "ENTER",
  "entry_type": "LIMIT",
  "confidence": 90,
  "wait_for": null,
  "max_wait_seconds": 30,
  "reasoning": "Market conditions are favorable with a narrow spread and no significant volume spikes. The price is near key levels indicating potential for movement without imme...
2025-07-15 06:46:05,512 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 610, Completion: 103, Total: 713
2025-07-15 06:46:05,512 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['action', 'entry_type', 'confidence', 'wait_for', 'max_wait_seconds', 'reasoning']
2025-07-15 06:46:05,513 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['ACTION', 'ENTRY_TYPE', 'CONFIDENCE', 'WAIT_FOR', 'MAX_WAIT_SECONDS', 'REASONING']
2025-07-15 06:46:05,513 - core.llm_response_parsers - INFO - Entry timing parsed: WAIT (LIMIT order)
2025-07-15 06:46:05,513 - core.llm_orchestrator - INFO - ✅ Completed prompt: entry_timing
2025-07-15 06:46:08,588 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 715 chars
2025-07-15 06:46:08,588 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: LONG, CONFIDENCE: 85%, TAKE_PROFIT: 10%, STOP_LOSS: -7%, EXPLANATION: The account is in a healthy state with normal trading parameters and the historical context shows no recent price or signal changes. Given that all symbols have similar setup qualities, DOGE/USDT stands out due to its lo...
2025-07-15 06:46:08,589 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 933, Completion: 177, Total: 1110
2025-07-15 06:46:08,589 - core.llm_response_parsers - INFO - 🔄 No JSON found, trying structured text parsing
2025-07-15 06:46:08,589 - core.llm_response_parsers - INFO - Parsed structured text: {'DECISION': 'LONG', 'CONFIDENCE': 85.0, 'TAKE_PROFIT': 10.0, 'STOP_LOSS': -7.0, 'EXPLANATION': 'THE ACCOUNT IS IN A HEALTHY STATE WITH NORMAL TRADING PARAMETERS AND THE HISTORICAL CONTEXT SHOWS NO RECENT PRICE OR SIGNAL CHANGES. GIVEN THAT ALL SYMBOLS HAVE SIMILAR SETUP QUALITIES, DOGE/USDT STANDS OUT DUE TO ITS LOWER VOLATILITY COMPARED TO BTC WHICH HAS HIGHER RISK BUT ALSO POTENTIALLY GREATER REWARD; THUS IT ALIGNS BETTER WITH A CONSERVATIVE STRATEGY AIMED AT PRESERVING ACCOUNT HEALTH WHILE STILL CAPTURING PROFITS WITHIN THE SET CRITERIA. THE TAKE-PROFIT AND STOP-LOSS PERCENTAGES ARE CHOSEN BASED ON TYPICAL RANGES FOR SUCH OPPORTUNITIES, ENSURING THAT RISKS REMAIN CONTROLLED IN LINE WITH OUR RISK BUDGET OF 2% PER TRADE.', 'ACTION': 'ENTER_NOW'}
2025-07-15 06:46:08,590 - core.llm_response_parsers - INFO - ✅ Structured text parsing successful: ['DECISION', 'CONFIDENCE', 'TAKE_PROFIT', 'STOP_LOSS', 'EXPLANATION', 'ACTION']
2025-07-15 06:46:08,590 - core.llm_response_parsers - INFO - Opportunity scanner parsed: BREAKOUT (MOMENTUM)
2025-07-15 06:46:08,590 - core.llm_orchestrator - INFO - ✅ Completed prompt: opportunity_scanner
2025-07-15 06:46:11,033 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 469 chars
2025-07-15 06:46:11,033 - llama.lmstudio_runner - INFO - 📄 Response Preview: {
  "REGIME": "RANGING_TIGHT",
  "CONFIDENCE": 75,
  "SCALP_SUITABILITY": "MEDIUM",
  "RECOMMENDED_TIMEFRAME": "1m",
  "RISK_LEVEL": "MEDIUM",
  "REASONING": "Current market conditions indicate a consolidation phase with moderate volatility. This environment is suitable for scalping strategies that ...
2025-07-15 06:46:11,034 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 705, Completion: 148, Total: 853
2025-07-15 06:46:11,034 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['REGIME', 'CONFIDENCE', 'SCALP_SUITABILITY', 'RECOMMENDED_TIMEFRAME', 'RISK_LEVEL', 'REASONING']
2025-07-15 06:46:11,034 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['REGIME', 'CONFIDENCE', 'SCALP_SUITABILITY', 'RECOMMENDED_TIMEFRAME', 'RISK_LEVEL', 'REASONING']
2025-07-15 06:46:11,035 - core.llm_response_parsers - INFO - Market regime parsed: RANGING_TIGHT (MEDIUM scalp suitability)
2025-07-15 06:46:11,035 - core.llm_orchestrator - INFO - ✅ Completed prompt: market_regime
2025-07-15 06:46:13,107 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 391 chars
2025-07-15 06:46:13,108 - llama.lmstudio_runner - INFO - 📄 Response Preview: {
  "risk_adjustment": {"min": 0.5, "max": 2.0},
  "hold_time_target": "8", // in minutes
  "entry_threshold": 70, // as percentage confidence level
  "exit_threshold": 60, // as percentage confidence level
  "sizing_method": "FIXED_RISK",
  "reasoning": "Maintain current strategy - performing well ...
2025-07-15 06:46:13,108 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 619, Completion: 130, Total: 749
2025-07-15 06:46:13,108 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['risk_adjustment', 'hold_time_target', 'entry_threshold', 'exit_threshold', 'sizing_method', 'reasoning', 'confidence']
2025-07-15 06:46:13,109 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['RISK_ADJUSTMENT', 'HOLD_TIME_TARGET', 'ENTRY_THRESHOLD', 'EXIT_THRESHOLD', 'SIZING_METHOD', 'REASONING', 'CONFIDENCE']
2025-07-15 06:46:13,109 - core.llm_response_parsers - INFO - Strategy adaptation parsed: Risk adj 1.0x
2025-07-15 06:46:13,109 - core.llm_action_executors - INFO - Applying strategy adaptations: Risk 1.0x, Hold time 8min
2025-07-15 06:46:13,109 - core.llm_orchestrator - INFO - ✅ Completed prompt: strategy_adaptation
2025-07-15 06:46:14,501 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 234 chars
2025-07-15 06:46:14,502 - llama.lmstudio_runner - INFO - 📄 Response Preview: ```json
{
  "action": "LONG",
  "confidence": 90,
  "entry_reason": "Market broke through resistance level with a rapid move of more than 2 pip.",
  "take_profit": 1.5,
  "stop_loss": 0.3,
  "hold_time": "3min",
  "leverage": 40
}
```...
2025-07-15 06:46:14,502 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 174, Completion: 97, Total: 271
2025-07-15 06:46:14,503 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['action', 'confidence', 'entry_reason', 'take_profit', 'stop_loss', 'hold_time', 'leverage']
2025-07-15 06:46:14,503 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['ACTION', 'CONFIDENCE', 'ENTRY_REASON', 'TAKE_PROFIT', 'STOP_LOSS', 'HOLD_TIME', 'LEVERAGE']
2025-07-15 06:46:14,503 - core.llm_response_parsers - INFO - Risk assessment parsed: REJECTED (Risk: 50)
2025-07-15 06:46:14,503 - core.llm_orchestrator - INFO - ✅ Completed prompt: risk_assessment
2025-07-15 06:46:14,505 - core.llm_orchestrator - INFO - 🚀 PARALLEL LLM cycle completed in 12.83s - 5 prompts executed concurrently
2025-07-15 06:46:14,506 - core.vote_aggregator - INFO - 🗳️ Vote aggregation: WAIT (69.0%) - Strong WAIT signal (3.5) → WAIT
2025-07-15 06:46:14,506 - core.llm_orchestrator - INFO - 🗳️ Aggregated Decision: WAIT (69.0%) - Strong WAIT signal (3.5) → WAIT
2025-07-15 06:46:14,506 - core.llm_orchestrator - INFO - 🗳️ Vote Breakdown: LONG: 1.7, WAIT: 3.5
2025-07-15 06:46:31,403 - core.llm_orchestrator - INFO - 🔍 FULL MODE: Executing all 8 prompts in PARALLEL for comprehensive analysis
2025-07-15 06:46:31,404 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-15 06:46:31,404 - core.llm_orchestrator - INFO - 🚀 Submitted 1 prompts for parallel execution
2025-07-15 06:46:31,404 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-15 06:46:31,405 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🎯 ENTRY TIMING SPECIALIST - DOGE/USDT:USDT

🎯 SIGNAL ANALYSIS:
ML Ensemble: NEUTRAL (50.0%)
Technical: NEUTRAL | Momentum: 0.00
Volume: PENDING | Orderflow: NEUTRAL

📊 MARKET MICROSTRUCTURE:
Bid/Ask: $0.170000/$0.170000
Spread: 0.100% | L2 Imbalance: 0.0%
Recent Flow: BALANCED | Volume Spike: False

📈 KEY LEVELS:
Support: $0.170000
Resistance: $0.170000
Distance to Support: 0.00%

⏰ TIMING FACTORS:
- Price near key levels: NEUTRAL
- Volume confirmation: PENDING
- Spread conditions: FAVORABLE
- M...
2025-07-15 06:46:31,405 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-15 06:46:35,267 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 338 chars
2025-07-15 06:46:35,267 - llama.lmstudio_runner - INFO - 📄 Response Preview: {
  "action": "ENTER",
  "entry_type": "LIMIT",
  "confidence": 85,
  "wait_for": null,
  "max_wait_seconds": 0,
  "reasoning": "Market conditions are favorable with a neutral signal from ML Ensemble and no pending volume confirmation. The spread is at its lowest indicating high liquidity which alig...
2025-07-15 06:46:35,268 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 610, Completion: 102, Total: 712
2025-07-15 06:46:35,269 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['action', 'entry_type', 'confidence', 'wait_for', 'max_wait_seconds', 'reasoning']
2025-07-15 06:46:35,269 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['ACTION', 'ENTRY_TYPE', 'CONFIDENCE', 'WAIT_FOR', 'MAX_WAIT_SECONDS', 'REASONING']
2025-07-15 06:46:35,269 - core.llm_response_parsers - INFO - Entry timing parsed: WAIT (LIMIT order)
2025-07-15 06:46:35,270 - core.llm_orchestrator - INFO - ✅ Completed prompt: entry_timing
2025-07-15 06:46:35,271 - core.llm_orchestrator - INFO - 🚀 PARALLEL LLM cycle completed in 3.87s - 1 prompts executed concurrently
2025-07-15 06:46:35,272 - core.vote_aggregator - INFO - 🗳️ Vote aggregation: WAIT (40.0%) - Insufficient vote weight (0.8) → WAIT
2025-07-15 06:46:35,273 - core.llm_orchestrator - INFO - 🗳️ Aggregated Decision: WAIT (40.0%) - Insufficient vote weight (0.8) → WAIT
2025-07-15 06:46:35,273 - core.llm_orchestrator - INFO - 🗳️ Vote Breakdown: WAIT: 0.8
2025-07-15 06:47:01,173 - core.llm_orchestrator - INFO - 🔍 FULL MODE: Executing all 8 prompts in PARALLEL for comprehensive analysis
2025-07-15 06:47:01,174 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-15 06:47:01,174 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-15 06:47:01,174 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-15 06:47:01,174 - llama.lmstudio_runner - INFO - 💬 User Prompt: 📊 MARKET REGIME ANALYST - DOGE/USDT:USDT

🔍 MULTI-TIMEFRAME ANALYSIS:
1m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
5m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
15m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%

📈 VOLATILITY METRICS:
ATR: 0.001000 | Spread: 0.100%
Volume Ratio: 1.0x | Price Range: 1.00%

🎯 REGIME CLASSIFICATION:
- TRENDING_BULL: Strong upward momentum, high volume, clear direction
- TRENDING_BEAR: Strong downward momentum, high volume, clear direction
- RANGING_TIGHT: Low volatility, narrow pric...
2025-07-15 06:47:01,174 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-15 06:47:01,175 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-15 06:47:01,175 - llama.lmstudio_runner - INFO - 💬 User Prompt: ...
2025-07-15 06:47:01,175 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-15 06:47:01,175 - core.llm_orchestrator - INFO - 🚀 Submitted 3 prompts for parallel execution
2025-07-15 06:47:01,177 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-15 06:47:01,176 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-15 06:47:01,177 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🎯 ENTRY TIMING SPECIALIST - DOGE/USDT:USDT

🎯 SIGNAL ANALYSIS:
ML Ensemble: NEUTRAL (50.0%)
Technical: NEUTRAL | Momentum: 0.00
Volume: PENDING | Orderflow: NEUTRAL

📊 MARKET MICROSTRUCTURE:
Bid/Ask: $0.170000/$0.170000
Spread: 0.100% | L2 Imbalance: 0.0%
Recent Flow: BALANCED | Volume Spike: False

📈 KEY LEVELS:
Support: $0.170000
Resistance: $0.170000
Distance to Support: 0.00%

⏰ TIMING FACTORS:
- Price near key levels: NEUTRAL
- Volume confirmation: PENDING
- Spread conditions: FAVORABLE
- M...
2025-07-15 06:47:01,179 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-15 06:47:04,768 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 227 chars
2025-07-15 06:47:04,769 - llama.lmstudio_runner - INFO - 📄 Response Preview: ```json
{
  "action": "LONG",
  "confidence": 90,
  "entry_reason": "Market broke through resistance level with a sudden spike in volume.",
  "take_profit": 2.5,
  "stop_loss": 1.0,
  "hold_time": "3min",
  "leverage": 40
}
```...
2025-07-15 06:47:04,769 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 174, Completion: 94, Total: 268
2025-07-15 06:47:04,769 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['action', 'confidence', 'entry_reason', 'take_profit', 'stop_loss', 'hold_time', 'leverage']
2025-07-15 06:47:04,769 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['ACTION', 'CONFIDENCE', 'ENTRY_REASON', 'TAKE_PROFIT', 'STOP_LOSS', 'HOLD_TIME', 'LEVERAGE']
2025-07-15 06:47:04,770 - core.llm_response_parsers - INFO - Risk assessment parsed: REJECTED (Risk: 50)
2025-07-15 06:47:04,770 - core.llm_orchestrator - INFO - ✅ Completed prompt: risk_assessment
2025-07-15 06:47:06,769 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 278 chars
2025-07-15 06:47:06,769 - llama.lmstudio_runner - INFO - 📄 Response Preview: ```json
{
  "REGIME": "RANGING_TIGHT",
  "CONFIDENCE": 75,
  "SCALP_SUITABILITY": "MEDIUM",
  "RECOMMENDED_TIMEFRAME": "1m",
  "RISK_LEVEL": "MEDIUM",
  "REASONING": "Consolidation phase with moderate volatility, suitable for scalping if executed quickly and efficiently."
}
```...
2025-07-15 06:47:06,770 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 705, Completion: 118, Total: 823
2025-07-15 06:47:06,770 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['REGIME', 'CONFIDENCE', 'SCALP_SUITABILITY', 'RECOMMENDED_TIMEFRAME', 'RISK_LEVEL', 'REASONING']
2025-07-15 06:47:06,770 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['REGIME', 'CONFIDENCE', 'SCALP_SUITABILITY', 'RECOMMENDED_TIMEFRAME', 'RISK_LEVEL', 'REASONING']
2025-07-15 06:47:06,770 - core.llm_response_parsers - INFO - Market regime parsed: RANGING_TIGHT (MEDIUM scalp suitability)
2025-07-15 06:47:06,771 - core.llm_orchestrator - INFO - ✅ Completed prompt: market_regime
2025-07-15 06:47:08,516 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 304 chars
2025-07-15 06:47:08,517 - llama.lmstudio_runner - INFO - 📄 Response Preview: {
  "action": "ENTER_NOW",
  "entry_type": "LIMIT",
  "confidence": 85,
  "wait_for": null,
  "max_wait_seconds": 0,
  "reasoning": "Market conditions are favorable with a narrow spread and no significant volume spike. Momentum is neutral but the risk/reward ratio of 2:1 justifies an immediate entry...
2025-07-15 06:47:08,517 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 610, Completion: 104, Total: 714
2025-07-15 06:47:08,518 - core.llm_response_parsers - WARNING - 🚨 JSON decode error: Expecting ',' delimiter: line 1 column 258 (char 257)
2025-07-15 06:47:08,518 - core.llm_response_parsers - WARNING - 🔍 Problematic JSON: { "action": "ENTER_NOW", "entry_type": "LIMIT", "confidence": 85, "wait_for": null, "max_wait_seconds": 0, "reasoning": "Market conditions are favorable with a narrow spread and no significant volume ...
2025-07-15 06:47:08,519 - core.llm_response_parsers - INFO - ✅ Aggressive cleanup extracted: ['action', 'confidence', 'reasoning']
2025-07-15 06:47:08,519 - core.llm_response_parsers - INFO - ✅ JSON fixed with aggressive cleanup
2025-07-15 06:47:08,519 - core.llm_response_parsers - INFO - Entry timing parsed: WAIT (LIMIT order)
2025-07-15 06:47:08,520 - core.llm_orchestrator - INFO - ✅ Completed prompt: entry_timing
2025-07-15 06:47:08,522 - core.llm_orchestrator - INFO - 🚀 PARALLEL LLM cycle completed in 7.35s - 3 prompts executed concurrently
2025-07-15 06:47:08,523 - core.vote_aggregator - INFO - 🗳️ Vote aggregation: WAIT (57.2%) - Strong WAIT signal (2.9) → WAIT
2025-07-15 06:47:08,524 - core.llm_orchestrator - INFO - 🗳️ Aggregated Decision: WAIT (57.2%) - Strong WAIT signal (2.9) → WAIT
2025-07-15 06:47:08,524 - core.llm_orchestrator - INFO - 🗳️ Vote Breakdown: WAIT: 2.9
