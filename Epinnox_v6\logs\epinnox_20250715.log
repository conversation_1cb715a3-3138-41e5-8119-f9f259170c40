2025-07-15 06:19:05,667 - main - INFO - Epinnox v6 starting up...
2025-07-15 06:19:05,717 - core.performance_monitor - INFO - Performance monitoring started
2025-07-15 06:19:05,717 - core.performance_monitor - INFO - Performance monitor initialized with 10s interval
2025-07-15 06:19:05,717 - main - INFO - Performance monitoring initialized
2025-07-15 06:19:05,728 - utils.cache_manager - INFO - Cache manager initialized: cache, max_age=24h, max_size=100MB
2025-07-15 06:19:05,737 - utils.cache_manager - INFO - Starting automatic cache cleanup
2025-07-15 06:19:05,738 - utils.cache_manager - INFO - Auto cleanup complete: 0 files removed, 0.00 MB freed
2025-07-15 06:19:09,867 - core.timer_coordinator - INFO - [TIMER_COORDINATOR] Initialized unified timer coordinator
2025-07-15 06:19:14,809 - config.autonomous_config - INFO - Configuration loaded from configs/autonomous_trading.yaml
2025-07-15 06:19:15,724 - data.live_data_manager - INFO - Successfully subscribed to live data for BTC/USDT:USDT on timeframes: ['1m', '5m', '15m']
2025-07-15 06:19:17,031 - websocket - INFO - Websocket connected
2025-07-15 06:19:19,753 - trading.position_tracker - INFO - Position Tracker initialized
2025-07-15 06:19:20,171 - llama.runner - INFO - Loaded OpenAI API key from models_config.yaml
2025-07-15 06:19:20,171 - llama.runner - INFO - Using model path: C:\Users\<USER>\.lmstudio\models\lmstudio-community\Phi-3.1-mini-128k-instruct-GGUF\Phi-3.1-mini-128k-instruct-Q4_K_M.gguf
2025-07-15 06:19:20,172 - llama.runner - INFO - Using guard path: C:\Users\<USER>\Documents\dev\Epinnox_v6\models\Llama-Guard-3-8B.gguf
2025-07-15 06:19:20,172 - llama.runner - INFO - Using binary path: C:\Users\<USER>\Documents\dev\llama.cpp\main
2025-07-15 06:19:20,185 - llama.lmstudio_runner - INFO - Loaded LMStudio configuration from models_config.yaml
2025-07-15 06:19:22,236 - llama.lmstudio_runner - INFO - Discovered 8 models: ['phi-3.1-mini-128k-instruct', 'openthinker3-7b', 'mistral-7b-claude-chat', 'fine_tuned_qwen1.7b', 'tsfc-model-0.0.1', 'codestral-22b-v0.1', 'deepseek-r1-distill-qwen-7b', 'text-embedding-nomic-embed-text-v1.5']
2025-07-15 06:19:22,236 - llama.lmstudio_runner - INFO - Selected preferred model from config: phi-3.1-mini-128k-instruct
2025-07-15 06:19:22,236 - llama.lmstudio_runner - INFO - Initialized LMStudio with model: phi-3.1-mini-128k-instruct
2025-07-15 06:19:22,262 - trading.intelligent_limit_order_manager - INFO - Intelligent Limit Order Manager initialized for professional scalping
2025-07-15 06:19:22,263 - core.llm_action_executors - INFO - ✅ Intelligent Limit Order Manager initialized
2025-07-15 06:19:22,263 - core.llm_action_executors - INFO - 🚀 LLM Action Executors initialized with intelligent limit order system
2025-07-15 06:19:22,263 - utils.cache_manager - INFO - Cache manager initialized: cache, max_age=24h, max_size=100MB
2025-07-15 06:19:22,264 - core.llm_orchestrator - INFO - LLM Prompt Orchestrator initialized
2025-07-15 06:19:22,267 - core.signal_hierarchy - INFO - Signal Hierarchy initialized
2025-07-15 06:19:22,335 - storage.database_manager - INFO - Database tables initialized successfully with performance optimizations
2025-07-15 06:19:22,336 - storage.database_manager - INFO - Database manager initialized: data\epinnox_trading.db
2025-07-15 06:19:22,336 - storage.session_manager - INFO - Session Manager initialized
2025-07-15 06:19:22,341 - storage.database_manager - INFO - Created session: live_BTCUSDTUSDT_20250715_061922_801e4598
2025-07-15 06:19:22,341 - storage.session_manager - INFO - Started session: live_BTCUSDTUSDT_20250715_061922_801e4598
2025-07-15 06:19:22,511 - core.risk_management_system - INFO - 🛡️ Risk Management System initialized
2025-07-15 06:19:22,520 - core.error_handling_system - INFO - 🛡️ Error Handling System initialized
2025-07-15 06:19:22,521 - core.error_handling_system - INFO - 📊 Component registered: exchange_connection
2025-07-15 06:19:22,521 - core.error_handling_system - INFO - 📊 Component registered: trading_interface
2025-07-15 06:19:22,521 - core.error_handling_system - INFO - 📊 Component registered: market_data
2025-07-15 06:19:22,521 - core.error_handling_system - INFO - 📊 Component registered: llm_orchestrator
2025-07-15 06:19:22,522 - core.error_handling_system - INFO - 🔍 Health monitoring started
2025-07-15 06:19:22,533 - core.monitoring_dashboard - INFO - 📊 Monitoring Dashboard initialized
2025-07-15 06:19:22,534 - core.symbol_scanner - INFO - SymbolScanner initialized with 8 symbols
2025-07-15 06:19:22,534 - core.timer_coordinator - INFO - [TIMER_COORDINATOR] Registered timer 'symbol_scanner_update': interval=30.0s, priority=MEDIUM
2025-07-15 06:19:22,534 - core.symbol_scanner - INFO - [SYMBOL_SCANNER] Registered with timer coordinator (interval: 30.0s)
2025-07-15 06:19:22,535 - core.llm_action_executors - WARNING - ⚠️ Intelligent Limit Order Manager not available
2025-07-15 06:19:22,535 - core.llm_action_executors - INFO - 🚀 LLM Action Executors initialized with intelligent limit order system
2025-07-15 06:19:59,009 - data.live_data_manager - INFO - Successfully subscribed to live data for DOGE/USDT:USDT on timeframes: ['1m', '5m', '15m']
2025-07-15 06:20:38,947 - core.llm_orchestrator - INFO - 🔍 FULL MODE: Executing all 8 prompts in PARALLEL for comprehensive analysis
2025-07-15 06:20:38,952 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-15 06:20:38,952 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-15 06:20:38,952 - llama.lmstudio_runner - INFO - 💬 User Prompt: 📊 MARKET REGIME ANALYST - DOGE/USDT:USDT

🔍 MULTI-TIMEFRAME ANALYSIS:
1m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
5m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
15m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%

📈 VOLATILITY METRICS:
ATR: 0.001000 | Spread: 0.100%
Volume Ratio: 1.0x | Price Range: 1.00%

🎯 REGIME CLASSIFICATION:
- TRENDING_BULL: Strong upward momentum, high volume, clear direction
- TRENDING_BEAR: Strong downward momentum, high volume, clear direction
- RANGING_TIGHT: Low volatility, narrow pric...
2025-07-15 06:20:38,952 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-15 06:20:38,953 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-15 06:20:38,953 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-15 06:20:38,953 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-15 06:20:38,953 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-15 06:20:38,954 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-15 06:20:38,954 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-15 06:20:38,954 - core.llm_orchestrator - INFO - 🚀 Submitted 5 prompts for parallel execution
2025-07-15 06:20:38,954 - llama.lmstudio_runner - INFO - 💬 User Prompt: ...
2025-07-15 06:20:38,955 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🎯 ENTRY TIMING SPECIALIST - DOGE/USDT:USDT

🎯 SIGNAL ANALYSIS:
ML Ensemble: NEUTRAL (50.0%)
Technical: NEUTRAL | Momentum: 0.00
Volume: PENDING | Orderflow: NEUTRAL

📊 MARKET MICROSTRUCTURE:
Bid/Ask: $0.170000/$0.170000
Spread: 0.100% | L2 Imbalance: 0.0%
Recent Flow: BALANCED | Volume Spike: False

📈 KEY LEVELS:
Support: $0.170000
Resistance: $0.170000
Distance to Support: 0.00%

⏰ TIMING FACTORS:
- Price near key levels: NEUTRAL
- Volume confirmation: PENDING
- Spread conditions: FAVORABLE
- M...
2025-07-15 06:20:38,955 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-15 06:20:38,956 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a conservative futures trading analyst. Analyze market data carefully and provide measured recommendations. Use format: DECISION: [LONG/SHORT/WAIT], CONFIDENCE: [50-100]%, TAKE_PROFIT: [%], ST...
2025-07-15 06:20:38,956 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-15 06:20:38,956 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-15 06:20:38,956 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🔄 STRATEGY ADAPTATION SPECIALIST

📊 PERFORMANCE ANALYSIS (Last 24h):
Trades: 0 | Win Rate: 50.0%
Avg Profit: 0.80% | Avg Loss: -0.30%
Sharpe Ratio: 1.00 | Max Drawdown: 0.0%
Total PnL: $0.00 | ROI: 0.0%

🎯 CURRENT STRATEGY:
Risk per Trade: 2.0% | Avg Hold Time: 8.0min
Entry Threshold: 70% | Exit Threshold: 60%
Position Size Method: FIXED_RISK | Max Positions: 3

📈 MARKET REGIME: UNKNOWN
Regime Confidence: 50.0%
Scalp Suitability: MEDIUM

🔧 ADAPTATION FACTORS:
- Win rate trending: DOWN
- Drawdown...
2025-07-15 06:20:38,957 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🔍 INTELLIGENT OPPORTUNITY SCANNER - Account Health Aware

🚨 ACCOUNT HEALTH STATUS: HEALTHY
💰 Balance: $1000.00 | Health: Healthy - Normal Trading
📊 Current Exposure: $0.00 (0.0% of account)
🎯 Trading Allowed: YES

📊 SYMBOL ANALYSIS:
• DOGE/USDT:USDT: $0.000000 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• BTC/USDT:USDT: $95000.000000 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• ETH/USDT:USDT: $3500.000000 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• SOL/USDT:USDT: $200.000000 | Mom: +0.0% | Vol: 1.0x |...
2025-07-15 06:20:38,957 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-15 06:20:38,958 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-15 06:20:42,554 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 229 chars
2025-07-15 06:20:42,554 - llama.lmstudio_runner - INFO - 📄 Response Preview: ```json
{
  "action": "LONG",
  "confidence": 90,
  "entry_reason": "Market broke above the resistance level with a strong volume increase.",
  "take_profit": 2.5,
  "stop_loss": 1.0,
  "hold_time": "3min",
  "leverage": 40
}
```...
2025-07-15 06:20:42,554 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 174, Completion: 93, Total: 267
2025-07-15 06:20:42,555 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['action', 'confidence', 'entry_reason', 'take_profit', 'stop_loss', 'hold_time', 'leverage']
2025-07-15 06:20:42,556 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['ACTION', 'CONFIDENCE', 'ENTRY_REASON', 'TAKE_PROFIT', 'STOP_LOSS', 'HOLD_TIME', 'LEVERAGE']
2025-07-15 06:20:42,556 - core.llm_response_parsers - INFO - Risk assessment parsed: REJECTED (Risk: 50)
2025-07-15 06:20:42,556 - core.llm_orchestrator - INFO - ✅ Completed prompt: risk_assessment
2025-07-15 06:20:44,293 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 218 chars
2025-07-15 06:20:44,293 - llama.lmstudio_runner - INFO - 📄 Response Preview: ```json
{
  "REGIME": "RANGING_TIGHT",
  "CONFIDENCE": 75,
  "SCALP_SUITABILITY": "MEDIUM",
  "RECOMMENDED_TIMEFRAME": "1m",
  "RISK_LEVEL": "MEDIUM",
  "REASONING": "Consolidation phase with moderate volatility"
}
```...
2025-07-15 06:20:44,294 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 705, Completion: 108, Total: 813
2025-07-15 06:20:44,294 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['REGIME', 'CONFIDENCE', 'SCALP_SUITABILITY', 'RECOMMENDED_TIMEFRAME', 'RISK_LEVEL', 'REASONING']
2025-07-15 06:20:44,294 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['REGIME', 'CONFIDENCE', 'SCALP_SUITABILITY', 'RECOMMENDED_TIMEFRAME', 'RISK_LEVEL', 'REASONING']
2025-07-15 06:20:44,295 - core.llm_response_parsers - INFO - Market regime parsed: RANGING_TIGHT (MEDIUM scalp suitability)
2025-07-15 06:20:44,295 - core.llm_orchestrator - INFO - ✅ Completed prompt: market_regime
2025-07-15 06:20:45,853 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 229 chars
2025-07-15 06:20:45,853 - llama.lmstudio_runner - INFO - 📄 Response Preview: ```json
{
  "risk_adjustment": 1.0,
  "hold_time_target": 8,
  "entry_threshold": 70,
  "exit_threshold": 60,
  "sizing_method": "FIXED_RISK",
  "reasoning": "Maintain current strategy - performing well",
  "confidence": 85
}
```...
2025-07-15 06:20:45,854 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 619, Completion: 100, Total: 719
2025-07-15 06:20:45,854 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['risk_adjustment', 'hold_time_target', 'entry_threshold', 'exit_threshold', 'sizing_method', 'reasoning', 'confidence']
2025-07-15 06:20:45,855 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['RISK_ADJUSTMENT', 'HOLD_TIME_TARGET', 'ENTRY_THRESHOLD', 'EXIT_THRESHOLD', 'SIZING_METHOD', 'REASONING', 'CONFIDENCE']
2025-07-15 06:20:45,855 - core.llm_response_parsers - INFO - Strategy adaptation parsed: Risk adj 1.0x
2025-07-15 06:20:45,855 - core.llm_action_executors - INFO - Applying strategy adaptations: Risk 1.0x, Hold time 8min
2025-07-15 06:20:45,856 - core.llm_orchestrator - INFO - ✅ Completed prompt: strategy_adaptation
2025-07-15 06:20:47,369 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 311 chars
2025-07-15 06:20:47,370 - llama.lmstudio_runner - INFO - 📄 Response Preview: {
  "action": "ENTER_NOW",
  "entry_type": "LIMIT",
  "confidence": 90,
  "wait_for": null,
  "max_wait_seconds": null,
  "reasoning": "Market conditions are favorable with a narrow spread and no significant imbalance. The price is near key levels indicating potential for movement within the next ti...
2025-07-15 06:20:47,370 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 610, Completion: 96, Total: 706
2025-07-15 06:20:47,371 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['action', 'entry_type', 'confidence', 'wait_for', 'max_wait_seconds', 'reasoning']
2025-07-15 06:20:47,371 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['ACTION', 'ENTRY_TYPE', 'CONFIDENCE', 'WAIT_FOR', 'MAX_WAIT_SECONDS', 'REASONING']
2025-07-15 06:20:47,372 - core.llm_response_parsers - INFO - Entry timing parsed: ENTER_NOW (LIMIT order)
2025-07-15 06:20:47,372 - core.llm_action_executors - INFO - Executing entry action: ENTER_NOW (LIMIT order)
2025-07-15 06:20:47,372 - core.llm_action_executors - WARNING - No position capacity available - skipping entry
2025-07-15 06:20:47,373 - core.llm_orchestrator - INFO - ✅ Completed prompt: entry_timing
2025-07-15 06:20:51,679 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 1147 chars
2025-07-15 06:20:51,679 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: SHORT, CONFIDENCE: 85%, TAKE_PROFIT: 2%, STOP_LOSS: -3%, EXPLANATION: The current market data shows a neutral sector momentum and normal volatility with an average volume profile. Given the account's healthy status and risk-adjusted capacity, there is room for conservative trading strategi...
2025-07-15 06:20:51,680 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 933, Completion: 271, Total: 1204
2025-07-15 06:20:51,680 - core.llm_response_parsers - INFO - 🔄 No JSON found, trying structured text parsing
2025-07-15 06:20:51,682 - core.llm_response_parsers - INFO - Parsed structured text: {'DECISION': 'SHORT', 'CONFIDENCE': 85.0, 'TAKE_PROFIT': 2.0, 'STOP_LOSS': -3.0, 'EXPLANATION': "THE CURRENT MARKET DATA SHOWS A NEUTRAL SECTOR MOMENTUM AND NORMAL VOLATILITY WITH AN AVERAGE VOLUME PROFILE. GIVEN THE ACCOUNT'S HEALTHY STATUS AND RISK-ADJUSTED CAPACITY, THERE IS ROOM FOR CONSERVATIVE TRADING STRATEGIES THAT AIM TO CAPITALIZE ON POTENTIAL SHORT POSITIONS IN CRYPTOCURRENCIES LIKE DOGE/USDT WHICH CURRENTLY HAVE NO SIGNIFICANT PRICE MOVEMENT (MOM: +0.0%). THE HISTORICAL CONTEXT OF LAST 5 PRICES BEING STATIC AT $0.0 AND SIGNALS ALL INDICATING 'WAIT' SUGGESTS A LACK OF IMMEDIATE TRADING OPPORTUNITIES, BUT ALSO THE POTENTIAL FOR FUTURE DOWNWARD MOVEMENTS DUE TO MARKET INERTIA OR NEGATIVE NEWS AFFECTING SENTIMENT TOWARDS ALTCOINS LIKE DOGE/USDT. A CONSERVATIVE SHORT POSITION WITH HIGH CONFIDENCE IS RECOMMENDED AS IT ALIGNS WELL WITH BOTH OPPORTUNITY CRITERIA AND ACCOUNT PRESERVATION PARAMETERS WHILE MAINTAINING A HEALTHY RISK BUDGET PER TRADE AT 2%. THE TAKE-PROFIT LEVEL OF 2% ENSURES PROFITABILITY WITHIN THE EXPECTED VOLATILITY RANGE, WHEREAS THE STOP LOSS SET TO -3% PROVIDES ADEQUATE PROTECTION AGAINST POTENTIAL ADVERSE PRICE MOVEMENTS.", 'ACTION': 'ENTER_NOW'}
2025-07-15 06:20:51,682 - core.llm_response_parsers - INFO - ✅ Structured text parsing successful: ['DECISION', 'CONFIDENCE', 'TAKE_PROFIT', 'STOP_LOSS', 'EXPLANATION', 'ACTION']
2025-07-15 06:20:51,682 - core.llm_response_parsers - INFO - Opportunity scanner parsed: BREAKOUT (REVERSAL)
2025-07-15 06:20:51,682 - core.llm_orchestrator - INFO - ✅ Completed prompt: opportunity_scanner
2025-07-15 06:20:51,683 - core.llm_orchestrator - INFO - 🚀 PARALLEL LLM cycle completed in 12.74s - 5 prompts executed concurrently
2025-07-15 06:21:08,555 - core.llm_orchestrator - INFO - 🔍 FULL MODE: Executing all 8 prompts in PARALLEL for comprehensive analysis
2025-07-15 06:21:08,556 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-15 06:21:08,556 - core.llm_orchestrator - INFO - 🚀 Submitted 1 prompts for parallel execution
2025-07-15 06:21:08,556 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-15 06:21:08,557 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🎯 ENTRY TIMING SPECIALIST - DOGE/USDT:USDT

🎯 SIGNAL ANALYSIS:
ML Ensemble: NEUTRAL (50.0%)
Technical: NEUTRAL | Momentum: 0.00
Volume: PENDING | Orderflow: NEUTRAL

📊 MARKET MICROSTRUCTURE:
Bid/Ask: $0.170000/$0.170000
Spread: 0.100% | L2 Imbalance: 0.0%
Recent Flow: BALANCED | Volume Spike: False

📈 KEY LEVELS:
Support: $0.170000
Resistance: $0.170000
Distance to Support: 0.00%

⏰ TIMING FACTORS:
- Price near key levels: NEUTRAL
- Volume confirmation: PENDING
- Spread conditions: FAVORABLE
- M...
2025-07-15 06:21:08,557 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-15 06:21:12,529 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 354 chars
2025-07-15 06:21:12,529 - llama.lmstudio_runner - INFO - 📄 Response Preview: {
  "action": "ENTER",
  "entry_type": "LIMIT",
  "confidence": 85,
  "wait_for": null,
  "max_wait_seconds": 0,
  "reasoning": "Market conditions are favorable with a NEUTRAL signal analysis and the spread is FAVORABLE. Although volume confirmation is pending, historical context shows no recent pri...
2025-07-15 06:21:12,530 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 610, Completion: 104, Total: 714
2025-07-15 06:21:12,530 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['action', 'entry_type', 'confidence', 'wait_for', 'max_wait_seconds', 'reasoning']
2025-07-15 06:21:12,530 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['ACTION', 'ENTRY_TYPE', 'CONFIDENCE', 'WAIT_FOR', 'MAX_WAIT_SECONDS', 'REASONING']
2025-07-15 06:21:12,531 - core.llm_response_parsers - INFO - Entry timing parsed: WAIT (LIMIT order)
2025-07-15 06:21:12,531 - core.llm_orchestrator - INFO - ✅ Completed prompt: entry_timing
2025-07-15 06:21:12,531 - core.llm_orchestrator - INFO - 🚀 PARALLEL LLM cycle completed in 3.98s - 1 prompts executed concurrently
2025-07-15 06:21:38,948 - core.llm_orchestrator - INFO - 🔍 FULL MODE: Executing all 8 prompts in PARALLEL for comprehensive analysis
2025-07-15 06:21:38,950 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-15 06:21:38,950 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-15 06:21:38,951 - llama.lmstudio_runner - INFO - 💬 User Prompt: 📊 MARKET REGIME ANALYST - DOGE/USDT:USDT

🔍 MULTI-TIMEFRAME ANALYSIS:
1m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
5m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
15m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%

📈 VOLATILITY METRICS:
ATR: 0.001000 | Spread: 0.100%
Volume Ratio: 1.0x | Price Range: 1.00%

🎯 REGIME CLASSIFICATION:
- TRENDING_BULL: Strong upward momentum, high volume, clear direction
- TRENDING_BEAR: Strong downward momentum, high volume, clear direction
- RANGING_TIGHT: Low volatility, narrow pric...
2025-07-15 06:21:38,951 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-15 06:21:38,951 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-15 06:21:38,952 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-15 06:21:38,952 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-15 06:21:38,952 - core.llm_orchestrator - INFO - 🚀 Submitted 4 prompts for parallel execution
2025-07-15 06:21:38,953 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🎯 ENTRY TIMING SPECIALIST - DOGE/USDT:USDT

🎯 SIGNAL ANALYSIS:
ML Ensemble: NEUTRAL (50.0%)
Technical: NEUTRAL | Momentum: 0.00
Volume: PENDING | Orderflow: NEUTRAL

📊 MARKET MICROSTRUCTURE:
Bid/Ask: $0.170000/$0.170000
Spread: 0.100% | L2 Imbalance: 0.0%
Recent Flow: BALANCED | Volume Spike: False

📈 KEY LEVELS:
Support: $0.170000
Resistance: $0.170000
Distance to Support: 0.00%

⏰ TIMING FACTORS:
- Price near key levels: NEUTRAL
- Volume confirmation: PENDING
- Spread conditions: FAVORABLE
- M...
2025-07-15 06:21:38,952 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-15 06:21:38,952 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-15 06:21:38,953 - llama.lmstudio_runner - INFO - 💬 User Prompt: ...
2025-07-15 06:21:38,953 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-15 06:21:38,953 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a conservative futures trading analyst. Analyze market data carefully and provide measured recommendations. Use format: DECISION: [LONG/SHORT/WAIT], CONFIDENCE: [50-100]%, TAKE_PROFIT: [%], ST...
2025-07-15 06:21:38,954 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-15 06:21:38,954 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🔍 INTELLIGENT OPPORTUNITY SCANNER - Account Health Aware

🚨 ACCOUNT HEALTH STATUS: HEALTHY
💰 Balance: $1000.00 | Health: Healthy - Normal Trading
📊 Current Exposure: $0.00 (0.0% of account)
🎯 Trading Allowed: YES

📊 SYMBOL ANALYSIS:
• DOGE/USDT:USDT: $0.000000 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• BTC/USDT:USDT: $95000.000000 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• ETH/USDT:USDT: $3500.000000 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• SOL/USDT:USDT: $200.000000 | Mom: +0.0% | Vol: 1.0x |...
2025-07-15 06:21:38,955 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-15 06:21:42,680 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 306 chars
2025-07-15 06:21:42,681 - llama.lmstudio_runner - INFO - 📄 Response Preview: {
  "action": "ENTER",
  "entry_type": "LIMIT",
  "confidence": 90,
  "wait_for": null,
  "max_wait_seconds": 30,
  "reasoning": "Market conditions are favorable with a narrow spread and no significant imbalance. The price is near key support levels which historically have been strong points for ent...
2025-07-15 06:21:42,681 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 610, Completion: 96, Total: 706
2025-07-15 06:21:42,681 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['action', 'entry_type', 'confidence', 'wait_for', 'max_wait_seconds', 'reasoning']
2025-07-15 06:21:42,682 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['ACTION', 'ENTRY_TYPE', 'CONFIDENCE', 'WAIT_FOR', 'MAX_WAIT_SECONDS', 'REASONING']
2025-07-15 06:21:42,682 - core.llm_response_parsers - INFO - Entry timing parsed: WAIT (LIMIT order)
2025-07-15 06:21:42,682 - core.llm_orchestrator - INFO - ✅ Completed prompt: entry_timing
2025-07-15 06:21:44,402 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 218 chars
2025-07-15 06:21:44,402 - llama.lmstudio_runner - INFO - 📄 Response Preview: ```json
{
  "REGIME": "RANGING_TIGHT",
  "CONFIDENCE": 75,
  "SCALP_SUITABILITY": "MEDIUM",
  "RECOMMENDED_TIMEFRAME": "1m",
  "RISK_LEVEL": "MEDIUM",
  "REASONING": "Consolidation phase with moderate volatility"
}
```...
2025-07-15 06:21:44,402 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 705, Completion: 108, Total: 813
2025-07-15 06:21:44,402 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['REGIME', 'CONFIDENCE', 'SCALP_SUITABILITY', 'RECOMMENDED_TIMEFRAME', 'RISK_LEVEL', 'REASONING']
2025-07-15 06:21:44,403 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['REGIME', 'CONFIDENCE', 'SCALP_SUITABILITY', 'RECOMMENDED_TIMEFRAME', 'RISK_LEVEL', 'REASONING']
2025-07-15 06:21:44,403 - core.llm_response_parsers - INFO - Market regime parsed: RANGING_TIGHT (MEDIUM scalp suitability)
2025-07-15 06:21:44,403 - core.llm_orchestrator - INFO - ✅ Completed prompt: market_regime
2025-07-15 06:21:48,312 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 967 chars
2025-07-15 06:21:48,312 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: LONG, CONFIDENCE: 85%, TAKE_PROFIT: 2%, STOP_LOSS: -3%, EXPLANATION: The account is in a healthy state with normal trading parameters and the current setup for DOGE/USDT shows high quality signals. With an acceptable risk-reward ratio of >3:1, excellent liquidity indicated by low spreads (...
2025-07-15 06:21:48,313 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 933, Completion: 242, Total: 1175
2025-07-15 06:21:48,313 - core.llm_response_parsers - INFO - 🔄 No JSON found, trying structured text parsing
2025-07-15 06:21:48,313 - core.llm_response_parsers - INFO - Parsed structured text: {'DECISION': 'LONG', 'CONFIDENCE': 85.0, 'TAKE_PROFIT': 2.0, 'STOP_LOSS': -3.0, 'EXPLANATION': "THE ACCOUNT IS IN A HEALTHY STATE WITH NORMAL TRADING PARAMETERS AND THE CURRENT SETUP FOR DOGE/USDT SHOWS HIGH QUALITY SIGNALS. WITH AN ACCEPTABLE RISK-REWARD RATIO OF >3:1, EXCELLENT LIQUIDITY INDICATED BY LOW SPREADS (<0.2%), STRONG MOMENTUM ALIGNMENT, AND CONSERVATIVE VOLATILITY RANGE PREFERRED AT 1-3%, IT IS A STRATEGIC MOVE TO ENTER THE MARKET LONG ON DOGE/USDT WITH MODERATE CONFIDENCE LEVELS BUT CAUTIOUS PROFIT TARGETS SET FOR TAKE PROFITS AT +2%. THE STOP LOSS WILL BE -3% OF ENTRY PRICE AS PER ACCOUNT PRESERVATION CRITERIA AND OPPORTUNITY RANKING. THIS APPROACH BALANCES POTENTIAL GAINS WHILE MANAGING RISK EFFECTIVELY IN LINE WITH THE HEALTH-ADJUSTED TRADING STRATEGY, ACKNOWLEDGING THAT HISTORICAL DATA SHOWS A 'WAIT' SIGNAL WHICH SUGGESTS CAUTION BUT DOES NOT RULE OUT FUTURE POSITIVE MOVEMENTS GIVEN CURRENT MARKET CONDITIONS ARE FAVORABLE FOR DOGE/USDT AS PER ANALYSIS.", 'ACTION': 'ENTER_NOW'}
2025-07-15 06:21:48,313 - core.llm_response_parsers - INFO - ✅ Structured text parsing successful: ['DECISION', 'CONFIDENCE', 'TAKE_PROFIT', 'STOP_LOSS', 'EXPLANATION', 'ACTION']
2025-07-15 06:21:48,314 - core.llm_response_parsers - INFO - Opportunity scanner parsed: BREAKOUT (MOMENTUM)
2025-07-15 06:21:48,314 - core.llm_orchestrator - INFO - ✅ Completed prompt: opportunity_scanner
2025-07-15 06:21:49,695 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 223 chars
2025-07-15 06:21:49,695 - llama.lmstudio_runner - INFO - 📄 Response Preview: ```json
{
  "action": "LONG",
  "confidence": 90,
  "entry_reason": "Breakout above the resistance level at $125 with a volume spike.",
  "take_profit": 3.0,
  "stop_loss": 1.5,
  "hold_time": "3min",
  "leverage": 40
}
```...
2025-07-15 06:21:49,695 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 174, Completion: 98, Total: 272
2025-07-15 06:21:49,696 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['action', 'confidence', 'entry_reason', 'take_profit', 'stop_loss', 'hold_time', 'leverage']
2025-07-15 06:21:49,696 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['ACTION', 'CONFIDENCE', 'ENTRY_REASON', 'TAKE_PROFIT', 'STOP_LOSS', 'HOLD_TIME', 'LEVERAGE']
2025-07-15 06:21:49,696 - core.llm_response_parsers - INFO - Risk assessment parsed: REJECTED (Risk: 50)
2025-07-15 06:21:49,697 - core.llm_orchestrator - INFO - ✅ Completed prompt: risk_assessment
2025-07-15 06:21:49,698 - core.llm_orchestrator - INFO - 🚀 PARALLEL LLM cycle completed in 10.75s - 4 prompts executed concurrently
2025-07-15 06:22:09,467 - core.llm_orchestrator - INFO - 🔍 FULL MODE: Executing all 8 prompts in PARALLEL for comprehensive analysis
2025-07-15 06:22:09,468 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-15 06:22:09,468 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-15 06:22:09,468 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-15 06:22:09,469 - llama.lmstudio_runner - INFO - 💬 User Prompt: 📊 MARKET REGIME ANALYST - DOGE/USDT:USDT

🔍 MULTI-TIMEFRAME ANALYSIS:
1m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
5m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
15m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%

📈 VOLATILITY METRICS:
ATR: 0.001000 | Spread: 0.100%
Volume Ratio: 1.0x | Price Range: 1.00%

🎯 REGIME CLASSIFICATION:
- TRENDING_BULL: Strong upward momentum, high volume, clear direction
- TRENDING_BEAR: Strong downward momentum, high volume, clear direction
- RANGING_TIGHT: Low volatility, narrow pric...
2025-07-15 06:22:09,468 - core.llm_orchestrator - INFO - 🚀 Submitted 2 prompts for parallel execution
2025-07-15 06:22:09,469 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-15 06:22:09,469 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-15 06:22:09,469 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🎯 ENTRY TIMING SPECIALIST - DOGE/USDT:USDT

🎯 SIGNAL ANALYSIS:
ML Ensemble: NEUTRAL (50.0%)
Technical: NEUTRAL | Momentum: 0.00
Volume: PENDING | Orderflow: NEUTRAL

📊 MARKET MICROSTRUCTURE:
Bid/Ask: $0.170000/$0.170000
Spread: 0.100% | L2 Imbalance: 0.0%
Recent Flow: BALANCED | Volume Spike: False

📈 KEY LEVELS:
Support: $0.170000
Resistance: $0.170000
Distance to Support: 0.00%

⏰ TIMING FACTORS:
- Price near key levels: NEUTRAL
- Volume confirmation: PENDING
- Spread conditions: FAVORABLE
- M...
2025-07-15 06:22:09,470 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-15 06:22:13,351 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 262 chars
2025-07-15 06:22:13,352 - llama.lmstudio_runner - INFO - 📄 Response Preview: {
  "action": "ENTER",
  "entry_type": "LIMIT",
  "confidence": 90,
  "wait_for": null,
  "max_wait_seconds": 30,
  "reasoning": "Market conditions are favorable with a narrow spread and no significant volume spike. The risk/reward ratio is optimal for entry."
}...
2025-07-15 06:22:13,352 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 610, Completion: 90, Total: 700
2025-07-15 06:22:13,353 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['action', 'entry_type', 'confidence', 'wait_for', 'max_wait_seconds', 'reasoning']
2025-07-15 06:22:13,353 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['ACTION', 'ENTRY_TYPE', 'CONFIDENCE', 'WAIT_FOR', 'MAX_WAIT_SECONDS', 'REASONING']
2025-07-15 06:22:13,353 - core.llm_response_parsers - INFO - Entry timing parsed: WAIT (LIMIT order)
2025-07-15 06:22:13,354 - core.llm_orchestrator - INFO - ✅ Completed prompt: entry_timing
2025-07-15 06:22:15,800 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 415 chars
2025-07-15 06:22:15,801 - llama.lmstudio_runner - INFO - 📄 Response Preview: ```json
{
  "REGIME": "RANGING_TIGHT",
  "CONFIDENCE": 75,
  "SCALP_SUITABILITY": "MEDIUM",
  "RECOMMENDED_TIMEFRAME": "1m",
  "RISK_LEVEL": "MEDIUM",
  "REASONING": "Consolidation phase with moderate volatility, suitable for scalping during clear trends and good volume. However, due to the tight ra...
2025-07-15 06:22:15,801 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 705, Completion: 152, Total: 857
2025-07-15 06:22:15,801 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['REGIME', 'CONFIDENCE', 'SCALP_SUITABILITY', 'RECOMMENDED_TIMEFRAME', 'RISK_LEVEL', 'REASONING']
2025-07-15 06:22:15,801 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['REGIME', 'CONFIDENCE', 'SCALP_SUITABILITY', 'RECOMMENDED_TIMEFRAME', 'RISK_LEVEL', 'REASONING']
2025-07-15 06:22:15,802 - core.llm_response_parsers - INFO - Market regime parsed: RANGING_TIGHT (MEDIUM scalp suitability)
2025-07-15 06:22:15,802 - core.llm_orchestrator - INFO - ✅ Completed prompt: market_regime
2025-07-15 06:22:15,803 - core.llm_orchestrator - INFO - 🚀 PARALLEL LLM cycle completed in 6.34s - 2 prompts executed concurrently
2025-07-15 06:22:38,494 - core.llm_orchestrator - INFO - 🔍 FULL MODE: Executing all 8 prompts in PARALLEL for comprehensive analysis
2025-07-15 06:22:38,495 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-15 06:22:38,495 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-15 06:22:38,495 - llama.lmstudio_runner - INFO - 💬 User Prompt: ...
2025-07-15 06:22:38,495 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-15 06:22:38,495 - core.llm_orchestrator - INFO - 🚀 Submitted 2 prompts for parallel execution
2025-07-15 06:22:38,495 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-15 06:22:38,495 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-15 06:22:38,496 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🎯 ENTRY TIMING SPECIALIST - DOGE/USDT:USDT

🎯 SIGNAL ANALYSIS:
ML Ensemble: NEUTRAL (50.0%)
Technical: NEUTRAL | Momentum: 0.00
Volume: PENDING | Orderflow: NEUTRAL

📊 MARKET MICROSTRUCTURE:
Bid/Ask: $0.170000/$0.170000
Spread: 0.100% | L2 Imbalance: 0.0%
Recent Flow: BALANCED | Volume Spike: False

📈 KEY LEVELS:
Support: $0.170000
Resistance: $0.170000
Distance to Support: 0.00%

⏰ TIMING FACTORS:
- Price near key levels: NEUTRAL
- Volume confirmation: PENDING
- Spread conditions: FAVORABLE
- M...
2025-07-15 06:22:38,496 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-15 06:22:42,188 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 224 chars
2025-07-15 06:22:42,188 - llama.lmstudio_runner - INFO - 📄 Response Preview: ```json
{
  "action": "LONG",
  "confidence": 90,
  "entry_reason": "Market broke through resistance level with a strong volume surge.",
  "take_profit": 2.5,
  "stop_loss": 1.7,
  "hold_time": "3min",
  "leverage": 40
}
```...
2025-07-15 06:22:42,188 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 174, Completion: 93, Total: 267
2025-07-15 06:22:42,189 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['action', 'confidence', 'entry_reason', 'take_profit', 'stop_loss', 'hold_time', 'leverage']
2025-07-15 06:22:42,189 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['ACTION', 'CONFIDENCE', 'ENTRY_REASON', 'TAKE_PROFIT', 'STOP_LOSS', 'HOLD_TIME', 'LEVERAGE']
2025-07-15 06:22:42,189 - core.llm_response_parsers - INFO - Risk assessment parsed: REJECTED (Risk: 50)
2025-07-15 06:22:42,189 - core.llm_orchestrator - INFO - ✅ Completed prompt: risk_assessment
2025-07-15 06:22:47,662 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 1830 chars
2025-07-15 06:22:47,663 - llama.lmstudio_runner - INFO - 📄 Response Preview: {
  "action": "ENTER", // Decision to enter the market based on favorable spread conditions, despite other factors being neutral or pending confirmation.
  "entry_type": "LIMIT", // Preference for a limit order over an open outcry system in high-frequency trading environments where precision is key ...
2025-07-15 06:22:47,663 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 610, Completion: 374, Total: 984
2025-07-15 06:22:47,664 - core.llm_response_parsers - WARNING - 🚨 JSON decode error: Expecting ',' delimiter: line 1 column 180 (char 179)
2025-07-15 06:22:47,664 - core.llm_response_parsers - WARNING - 🔍 Problematic JSON: { "action": "ENTER", "entry_type": "LIMIT", "confidence": 85, "wait_for": null, "reasoning": "The decision is driven by a combination of factors that align well for an immediate "entry": favorable spr...
2025-07-15 06:22:47,665 - core.llm_response_parsers - INFO - ✅ Aggressive cleanup extracted: ['action', 'confidence', 'reasoning']
2025-07-15 06:22:47,666 - core.llm_response_parsers - INFO - ✅ JSON fixed with aggressive cleanup
2025-07-15 06:22:47,666 - core.llm_response_parsers - INFO - Entry timing parsed: WAIT (LIMIT order)
2025-07-15 06:22:47,666 - core.llm_orchestrator - INFO - ✅ Completed prompt: entry_timing
2025-07-15 06:22:47,667 - core.llm_orchestrator - INFO - 🚀 PARALLEL LLM cycle completed in 9.17s - 2 prompts executed concurrently
2025-07-15 06:23:08,499 - core.llm_orchestrator - INFO - 🔍 FULL MODE: Executing all 8 prompts in PARALLEL for comprehensive analysis
2025-07-15 06:23:08,501 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-15 06:23:08,502 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-15 06:23:08,503 - llama.lmstudio_runner - INFO - 💬 User Prompt: 📊 MARKET REGIME ANALYST - DOGE/USDT:USDT

🔍 MULTI-TIMEFRAME ANALYSIS:
1m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
5m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
15m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%

📈 VOLATILITY METRICS:
ATR: 0.001000 | Spread: 0.100%
Volume Ratio: 1.0x | Price Range: 1.00%

🎯 REGIME CLASSIFICATION:
- TRENDING_BULL: Strong upward momentum, high volume, clear direction
- TRENDING_BEAR: Strong downward momentum, high volume, clear direction
- RANGING_TIGHT: Low volatility, narrow pric...
2025-07-15 06:23:08,503 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-15 06:23:08,504 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-15 06:23:08,504 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-15 06:23:08,504 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-15 06:23:08,505 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-15 06:23:08,505 - core.llm_orchestrator - INFO - 🚀 Submitted 4 prompts for parallel execution
2025-07-15 06:23:08,505 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🎯 ENTRY TIMING SPECIALIST - DOGE/USDT:USDT

🎯 SIGNAL ANALYSIS:
ML Ensemble: NEUTRAL (50.0%)
Technical: NEUTRAL | Momentum: 0.00
Volume: PENDING | Orderflow: NEUTRAL

📊 MARKET MICROSTRUCTURE:
Bid/Ask: $0.170000/$0.170000
Spread: 0.100% | L2 Imbalance: 0.0%
Recent Flow: BALANCED | Volume Spike: False

📈 KEY LEVELS:
Support: $0.170000
Resistance: $0.170000
Distance to Support: 0.00%

⏰ TIMING FACTORS:
- Price near key levels: NEUTRAL
- Volume confirmation: PENDING
- Spread conditions: FAVORABLE
- M...
2025-07-15 06:23:08,507 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a conservative futures trading analyst. Analyze market data carefully and provide measured recommendations. Use format: DECISION: [LONG/SHORT/WAIT], CONFIDENCE: [50-100]%, TAKE_PROFIT: [%], ST...
2025-07-15 06:23:08,507 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-15 06:23:08,508 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-15 06:23:08,508 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🔍 INTELLIGENT OPPORTUNITY SCANNER - Account Health Aware

🚨 ACCOUNT HEALTH STATUS: HEALTHY
💰 Balance: $1000.00 | Health: Healthy - Normal Trading
📊 Current Exposure: $0.00 (0.0% of account)
🎯 Trading Allowed: YES

📊 SYMBOL ANALYSIS:
• DOGE/USDT:USDT: $0.000000 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• BTC/USDT:USDT: $95000.000000 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• ETH/USDT:USDT: $3500.000000 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• SOL/USDT:USDT: $200.000000 | Mom: +0.0% | Vol: 1.0x |...
2025-07-15 06:23:08,509 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🔄 STRATEGY ADAPTATION SPECIALIST

📊 PERFORMANCE ANALYSIS (Last 24h):
Trades: 0 | Win Rate: 50.0%
Avg Profit: 0.80% | Avg Loss: -0.30%
Sharpe Ratio: 1.00 | Max Drawdown: 0.0%
Total PnL: $0.00 | ROI: 0.0%

🎯 CURRENT STRATEGY:
Risk per Trade: 2.0% | Avg Hold Time: 8.0min
Entry Threshold: 70% | Exit Threshold: 60%
Position Size Method: FIXED_RISK | Max Positions: 3

📈 MARKET REGIME: UNKNOWN
Regime Confidence: 50.0%
Scalp Suitability: MEDIUM

🔧 ADAPTATION FACTORS:
- Win rate trending: DOWN
- Drawdown...
2025-07-15 06:23:08,510 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-15 06:23:08,510 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-15 06:23:13,340 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 497 chars
2025-07-15 06:23:13,341 - llama.lmstudio_runner - INFO - 📄 Response Preview: ```json
{
  "REGIME": "RANGING_TIGHT",
  "CONFIDENCE": 75,
  "SCALP_SUITABILITY": "MEDIUM",
  "RECOMMENDED_TIMEFRAME": "1m",
  "RISK_LEVEL": "MEDIUM",
  "REASONING": "Consolidation phase with moderate volatility indicates a period where price action is tight and ranges are narrow. This environment p...
2025-07-15 06:23:13,341 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 705, Completion: 162, Total: 867
2025-07-15 06:23:13,341 - core.llm_response_parsers - WARNING - 🚨 JSON decode error: Expecting ',' delimiter: line 1 column 434 (char 433)
2025-07-15 06:23:13,341 - core.llm_response_parsers - WARNING - 🔍 Problematic JSON: { "REGIME": "RANGING_TIGHT", "CONFIDENCE": 75, "SCALP_SUITABILITY": "MEDIUM", "RECOMMENDED_TIMEFRAME": "1m", "RISK_LEVEL": "MEDIUM", "REASONING": "Consolidation phase with moderate volatility indicate...
2025-07-15 06:23:13,342 - core.llm_response_parsers - INFO - ✅ Aggressive cleanup extracted: ['confidence', 'reasoning']
2025-07-15 06:23:13,342 - core.llm_response_parsers - INFO - ✅ JSON fixed with aggressive cleanup
2025-07-15 06:23:13,342 - core.llm_response_parsers - INFO - Market regime parsed: RANGING_TIGHT (MEDIUM scalp suitability)
2025-07-15 06:23:13,342 - core.llm_orchestrator - INFO - ✅ Completed prompt: market_regime
2025-07-15 06:23:14,890 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 322 chars
2025-07-15 06:23:14,890 - llama.lmstudio_runner - INFO - 📄 Response Preview: {"risk_adjustment":1.5, "hold_time_target":30, "entry_threshold":75, "exit_threshold":65, "sizing_method":"VARIABLE", "reasoning":"Increase risk to capitalize on current downtrend and improve win rate while maintaining a manageable drawdown level. Adjust entry threshold for higher confidence in tren...
2025-07-15 06:23:14,891 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 619, Completion: 98, Total: 717
2025-07-15 06:23:14,891 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['risk_adjustment', 'hold_time_target', 'entry_threshold', 'exit_threshold', 'sizing_method', 'reasoning', 'confidence']
2025-07-15 06:23:14,891 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['RISK_ADJUSTMENT', 'HOLD_TIME_TARGET', 'ENTRY_THRESHOLD', 'EXIT_THRESHOLD', 'SIZING_METHOD', 'REASONING', 'CONFIDENCE']
2025-07-15 06:23:14,892 - core.llm_response_parsers - INFO - Strategy adaptation parsed: Risk adj 1.5x
2025-07-15 06:23:14,892 - core.llm_action_executors - INFO - Applying strategy adaptations: Risk 1.5x, Hold time 30min
2025-07-15 06:23:14,892 - core.llm_orchestrator - INFO - ✅ Completed prompt: strategy_adaptation
2025-07-15 06:23:16,580 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 318 chars
2025-07-15 06:23:16,581 - llama.lmstudio_runner - INFO - 📄 Response Preview: ```json
{
  "action": "ENTER_NOW",
  "entry_type": "LIMIT",
  "confidence": 90,
  "wait_for": null,
  "max_wait_seconds": 30,
  "reasoning": "Market conditions are favorable with a low spread and balanced recent flow. The risk/reward ratio is optimal at 2:1 considering the current bid price near sup...
2025-07-15 06:23:16,581 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 610, Completion: 108, Total: 718
2025-07-15 06:23:16,582 - core.llm_response_parsers - WARNING - 🚨 JSON decode error: Expecting ',' delimiter: line 1 column 236 (char 235)
2025-07-15 06:23:16,582 - core.llm_response_parsers - WARNING - 🔍 Problematic JSON: { "action": "ENTER_NOW", "entry_type": "LIMIT", "confidence": 90, "wait_for": null, "max_wait_seconds": 30, "reasoning": "Market conditions are favorable with a low spread and balanced recent flow. Th...
2025-07-15 06:23:16,582 - core.llm_response_parsers - INFO - ✅ Aggressive cleanup extracted: ['action', 'confidence', 'reasoning']
2025-07-15 06:23:16,582 - core.llm_response_parsers - INFO - ✅ JSON fixed with aggressive cleanup
2025-07-15 06:23:16,583 - core.llm_response_parsers - INFO - Entry timing parsed: WAIT (LIMIT order)
2025-07-15 06:23:16,583 - core.llm_orchestrator - INFO - ✅ Completed prompt: entry_timing
2025-07-15 06:23:21,454 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 1250 chars
2025-07-15 06:23:21,454 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: SHORT, CONFIDENCE: 70%, TAKE_PROFIT: 2%, STOP_LOSS: -3%, EXPLANATION: Given the current market data and account health status which is marked as 'HEALTHY', there are no significant price movements or strong signals to suggest an immediate long position. The historical context shows a lack ...
2025-07-15 06:23:21,455 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 933, Completion: 290, Total: 1223
2025-07-15 06:23:21,455 - core.llm_response_parsers - INFO - 🔄 No JSON found, trying structured text parsing
2025-07-15 06:23:21,455 - core.llm_response_parsers - INFO - Parsed structured text: {'DECISION': 'SHORT', 'CONFIDENCE': 70.0, 'TAKE_PROFIT': 2.0, 'STOP_LOSS': -3.0, 'EXPLANATION': "GIVEN THE CURRENT MARKET DATA AND ACCOUNT HEALTH STATUS WHICH IS MARKED AS 'HEALTHY', THERE ARE NO SIGNIFICANT PRICE MOVEMENTS OR STRONG SIGNALS TO SUGGEST AN IMMEDIATE LONG POSITION. THE HISTORICAL CONTEXT SHOWS A LACK OF RECENT VOLATILITY, WITH LAST 5 PRICES REMAINING AT $0.00 FOR DOGE/USDT, INDICATING STABILITY IN THE CURRENT MARKET CONDITIONS. HOWEVER, CONSIDERING THAT WE HAVE ROOM WITHIN OUR RISK BUDGET AND TOTAL EXPOSURE LIMITS DUE TO ACCOUNT HEALTH ADJUSTMENTS, IT IS PRUDENT TO INITIATE A SHORT POSITION ON BTC/USDT WITH HIGH SETUP QUALITY AS INDICATED BY ITS MEDIUM-LEVEL MOTHER (MOM) CHANGE OF +0.0% WHICH SUGGESTS POTENTIAL FOR PRICE DECLINE WITHOUT SIGNIFICANT IMMEDIATE MOVEMENT BUT STILL WITHIN THE CONSERVATIVE RANGE PREFERRED DUE TO ACCOUNT HEALTH CONSIDERATIONS AND CURRENT MARKET VOLATILITY BEING NORMAL, NOT EXCESSIVE OR LOW. THE TAKE PROFIT AT 2% ENSURES WE CAPITALIZE ON ANY MINOR DOWNWARD TREND WHILE A STOP LOSS OF -3% PROVIDES ADEQUATE PROTECTION AGAINST SUDDEN ADVERSE MOVEMENTS IN LINE WITH THE CONSERVATIVE RISK/REWARD RATIO REQUIRED FOR OUR ACCOUNT'S HEALTH-ADJUSTED CAPACITY, THUS MAINTAINING BALANCE BETWEEN POTENTIAL PROFIT AND SAFETY.", 'ACTION': 'ENTER_NOW'}
2025-07-15 06:23:21,456 - core.llm_response_parsers - INFO - ✅ Structured text parsing successful: ['DECISION', 'CONFIDENCE', 'TAKE_PROFIT', 'STOP_LOSS', 'EXPLANATION', 'ACTION']
2025-07-15 06:23:21,456 - core.llm_response_parsers - INFO - Opportunity scanner parsed: MOMENTUM (REVERSAL)
2025-07-15 06:23:21,457 - core.llm_orchestrator - INFO - ✅ Completed prompt: opportunity_scanner
2025-07-15 06:23:21,458 - core.llm_orchestrator - INFO - 🚀 PARALLEL LLM cycle completed in 12.96s - 4 prompts executed concurrently
2025-07-15 06:23:38,466 - core.llm_orchestrator - INFO - 🔍 FULL MODE: Executing all 8 prompts in PARALLEL for comprehensive analysis
2025-07-15 06:23:38,468 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-15 06:23:38,469 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-15 06:23:38,469 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-15 06:23:38,469 - core.llm_orchestrator - INFO - 🚀 Submitted 2 prompts for parallel execution
2025-07-15 06:23:38,470 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-15 06:23:38,470 - llama.lmstudio_runner - INFO - 💬 User Prompt: ...
2025-07-15 06:23:38,470 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🎯 ENTRY TIMING SPECIALIST - DOGE/USDT:USDT

🎯 SIGNAL ANALYSIS:
ML Ensemble: NEUTRAL (50.0%)
Technical: NEUTRAL | Momentum: 0.00
Volume: PENDING | Orderflow: NEUTRAL

📊 MARKET MICROSTRUCTURE:
Bid/Ask: $0.170000/$0.170000
Spread: 0.100% | L2 Imbalance: 0.0%
Recent Flow: BALANCED | Volume Spike: False

📈 KEY LEVELS:
Support: $0.170000
Resistance: $0.170000
Distance to Support: 0.00%

⏰ TIMING FACTORS:
- Price near key levels: NEUTRAL
- Volume confirmation: PENDING
- Spread conditions: FAVORABLE
- M...
2025-07-15 06:23:38,471 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-15 06:23:38,471 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-15 06:23:42,485 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 321 chars
2025-07-15 06:23:42,485 - llama.lmstudio_runner - INFO - 📄 Response Preview: {
  "action": "ENTER_NOW",
  "entry_type": "LIMIT",
  "confidence": 85,
  "wait_for": null,
  "max_wait_seconds": -1,
  "reasoning": "Market conditions are favorable with a narrow spread and no significant imbalance. The price is near key support levels which historically have been reliable entry po...
2025-07-15 06:23:42,486 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 610, Completion: 100, Total: 710
2025-07-15 06:23:42,486 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['action', 'entry_type', 'confidence', 'wait_for', 'max_wait_seconds', 'reasoning']
2025-07-15 06:23:42,486 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['ACTION', 'ENTRY_TYPE', 'CONFIDENCE', 'WAIT_FOR', 'MAX_WAIT_SECONDS', 'REASONING']
2025-07-15 06:23:42,487 - core.llm_response_parsers - INFO - Entry timing parsed: ENTER_NOW (LIMIT order)
2025-07-15 06:23:42,487 - core.llm_action_executors - INFO - Executing entry action: ENTER_NOW (LIMIT order)
2025-07-15 06:23:42,487 - core.llm_action_executors - WARNING - No position capacity available - skipping entry
2025-07-15 06:23:42,487 - core.llm_orchestrator - INFO - ✅ Completed prompt: entry_timing
2025-07-15 06:23:43,770 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 209 chars
2025-07-15 06:23:43,770 - llama.lmstudio_runner - INFO - 📄 Response Preview: ```json
{
  "action": "LONG",
  "confidence": 90,
  "entry_reason": "Breakout above resistance level with volume spike.",
  "take_profit": 2.5,
  "stop_loss": 1.0,
  "hold_time": "3min",
  "leverage": 40
}
```...
2025-07-15 06:23:43,770 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 174, Completion: 91, Total: 265
2025-07-15 06:23:43,771 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['action', 'confidence', 'entry_reason', 'take_profit', 'stop_loss', 'hold_time', 'leverage']
2025-07-15 06:23:43,771 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['ACTION', 'CONFIDENCE', 'ENTRY_REASON', 'TAKE_PROFIT', 'STOP_LOSS', 'HOLD_TIME', 'LEVERAGE']
2025-07-15 06:23:43,771 - core.llm_response_parsers - INFO - Risk assessment parsed: REJECTED (Risk: 50)
2025-07-15 06:23:43,771 - core.llm_orchestrator - INFO - ✅ Completed prompt: risk_assessment
2025-07-15 06:23:43,772 - core.llm_orchestrator - INFO - 🚀 PARALLEL LLM cycle completed in 5.31s - 2 prompts executed concurrently
2025-07-15 06:24:08,866 - core.llm_orchestrator - INFO - 🔍 FULL MODE: Executing all 8 prompts in PARALLEL for comprehensive analysis
2025-07-15 06:24:08,866 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-15 06:24:08,867 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-15 06:24:08,867 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-15 06:24:08,867 - llama.lmstudio_runner - INFO - 💬 User Prompt: 📊 MARKET REGIME ANALYST - DOGE/USDT:USDT

🔍 MULTI-TIMEFRAME ANALYSIS:
1m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
5m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
15m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%

📈 VOLATILITY METRICS:
ATR: 0.001000 | Spread: 0.100%
Volume Ratio: 1.0x | Price Range: 1.00%

🎯 REGIME CLASSIFICATION:
- TRENDING_BULL: Strong upward momentum, high volume, clear direction
- TRENDING_BEAR: Strong downward momentum, high volume, clear direction
- RANGING_TIGHT: Low volatility, narrow pric...
2025-07-15 06:24:08,867 - core.llm_orchestrator - INFO - 🚀 Submitted 3 prompts for parallel execution
2025-07-15 06:24:08,867 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-15 06:24:08,868 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-15 06:24:08,868 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-15 06:24:08,869 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🎯 ENTRY TIMING SPECIALIST - DOGE/USDT:USDT

🎯 SIGNAL ANALYSIS:
ML Ensemble: NEUTRAL (50.0%)
Technical: NEUTRAL | Momentum: 0.00
Volume: PENDING | Orderflow: NEUTRAL

📊 MARKET MICROSTRUCTURE:
Bid/Ask: $0.170000/$0.170000
Spread: 0.100% | L2 Imbalance: 0.0%
Recent Flow: BALANCED | Volume Spike: False

📈 KEY LEVELS:
Support: $0.170000
Resistance: $0.170000
Distance to Support: 0.00%

⏰ TIMING FACTORS:
- Price near key levels: NEUTRAL
- Volume confirmation: PENDING
- Spread conditions: FAVORABLE
- M...
2025-07-15 06:24:08,869 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a conservative futures trading analyst. Analyze market data carefully and provide measured recommendations. Use format: DECISION: [LONG/SHORT/WAIT], CONFIDENCE: [50-100]%, TAKE_PROFIT: [%], ST...
2025-07-15 06:24:08,870 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-15 06:24:08,870 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🔍 INTELLIGENT OPPORTUNITY SCANNER - Account Health Aware

🚨 ACCOUNT HEALTH STATUS: HEALTHY
💰 Balance: $1000.00 | Health: Healthy - Normal Trading
📊 Current Exposure: $0.00 (0.0% of account)
🎯 Trading Allowed: YES

📊 SYMBOL ANALYSIS:
• DOGE/USDT:USDT: $0.000000 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• BTC/USDT:USDT: $95000.000000 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• ETH/USDT:USDT: $3500.000000 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• SOL/USDT:USDT: $200.000000 | Mom: +0.0% | Vol: 1.0x |...
2025-07-15 06:24:08,871 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-15 06:24:12,923 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 311 chars
2025-07-15 06:24:12,924 - llama.lmstudio_runner - INFO - 📄 Response Preview: ```json
{
  "action": "ENTER_NOW",
  "entry_type": "LIMIT",
  "confidence": 85,
  "wait_for": null,
  "max_wait_seconds": 0,
  "reasoning": "Market conditions are favorable with a narrow spread and no significant volume spike. The price is near key support levels indicating potential for an upward m...
2025-07-15 06:24:12,924 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 610, Completion: 101, Total: 711
2025-07-15 06:24:12,924 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['action', 'entry_type', 'confidence', 'wait_for', 'max_wait_seconds', 'reasoning']
2025-07-15 06:24:12,925 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['ACTION', 'ENTRY_TYPE', 'CONFIDENCE', 'WAIT_FOR', 'MAX_WAIT_SECONDS', 'REASONING']
2025-07-15 06:24:12,925 - core.llm_response_parsers - INFO - Entry timing parsed: ENTER_NOW (LIMIT order)
2025-07-15 06:24:12,925 - core.llm_action_executors - INFO - Executing entry action: ENTER_NOW (LIMIT order)
2025-07-15 06:24:12,925 - core.llm_action_executors - WARNING - No position capacity available - skipping entry
2025-07-15 06:24:12,926 - core.llm_orchestrator - INFO - ✅ Completed prompt: entry_timing
2025-07-15 06:24:14,440 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 155 chars
2025-07-15 06:24:14,441 - llama.lmstudio_runner - INFO - 📄 Response Preview: ```json
{
  "REGIME": "RANGING_TIGHT",
  "CONFIDENCE": 75,
  "SCALP_SUITABILITY": "MEDIUM",
  "RECOMMENDED_TIMEFRAME": "1m",
  "RISK_LEVEL": "MEDIUM"
}
```...
2025-07-15 06:24:14,441 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 705, Completion: 88, Total: 793
2025-07-15 06:24:14,442 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['REGIME', 'CONFIDENCE', 'SCALP_SUITABILITY', 'RECOMMENDED_TIMEFRAME', 'RISK_LEVEL']
2025-07-15 06:24:14,442 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['REGIME', 'CONFIDENCE', 'SCALP_SUITABILITY', 'RECOMMENDED_TIMEFRAME', 'RISK_LEVEL']
2025-07-15 06:24:14,442 - core.llm_response_parsers - INFO - Market regime parsed: RANGING_TIGHT (MEDIUM scalp suitability)
2025-07-15 06:24:14,443 - core.llm_orchestrator - INFO - ✅ Completed prompt: market_regime
2025-07-15 06:24:17,328 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 678 chars
2025-07-15 06:24:17,328 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: SHORT, CONFIDENCE: 75%, TAKE_PROFIT: 2%, STOP_LOSS: 4%, EXPLANATION: Given the neutral sector momentum and normal overall volatility with a healthy account status, there's no immediate indication of an uptrend in DOGE/USDT. The historical context shows stagnant prices without clear signals...
2025-07-15 06:24:17,329 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 933, Completion: 167, Total: 1100
2025-07-15 06:24:17,329 - core.llm_response_parsers - INFO - 🔄 No JSON found, trying structured text parsing
2025-07-15 06:24:17,329 - core.llm_response_parsers - INFO - Parsed structured text: {'DECISION': 'SHORT', 'CONFIDENCE': 75.0, 'TAKE_PROFIT': 2.0, 'STOP_LOSS': 4.0, 'EXPLANATION': "GIVEN THE NEUTRAL SECTOR MOMENTUM AND NORMAL OVERALL VOLATILITY WITH A HEALTHY ACCOUNT STATUS, THERE'S NO IMMEDIATE INDICATION OF AN UPTREND IN DOGE/USDT. THE HISTORICAL CONTEXT SHOWS STAGNANT PRICES WITHOUT CLEAR SIGNALS FOR ENTRY OR EXIT POINTS OVER THE PAST FIVE TRADES. A CONSERVATIVE SHORT POSITION IS RECOMMENDED TO CAPITALIZE ON POTENTIAL DOWNWARD PRICE MOVEMENTS WHILE ADHERING TO ACCOUNT PRESERVATION CRITERIA AND MAINTAINING A RISK-REWARD RATIO THAT ALIGNS WITH OUR HEALTHY TRADING ENVIRONMENT, WHICH ALLOWS MORE AGGRESSIVE POSITIONS THAN TYPICALLY REQUIRED FOR AN UNHEALTHY BALANCE SHEET.", 'ACTION': 'ENTER_NOW'}
2025-07-15 06:24:17,330 - core.llm_response_parsers - INFO - ✅ Structured text parsing successful: ['DECISION', 'CONFIDENCE', 'TAKE_PROFIT', 'STOP_LOSS', 'EXPLANATION', 'ACTION']
2025-07-15 06:24:17,330 - core.llm_response_parsers - INFO - Opportunity scanner parsed: MOMENTUM (REVERSAL)
2025-07-15 06:24:17,330 - core.llm_orchestrator - INFO - ✅ Completed prompt: opportunity_scanner
2025-07-15 06:24:17,331 - core.llm_orchestrator - INFO - 🚀 PARALLEL LLM cycle completed in 8.47s - 3 prompts executed concurrently
2025-07-15 06:24:38,489 - core.llm_orchestrator - INFO - 🔍 FULL MODE: Executing all 8 prompts in PARALLEL for comprehensive analysis
2025-07-15 06:24:38,490 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-15 06:24:38,490 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-15 06:24:38,490 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-15 06:24:38,490 - core.llm_orchestrator - INFO - 🚀 Submitted 2 prompts for parallel execution
2025-07-15 06:24:38,490 - llama.lmstudio_runner - INFO - 💬 User Prompt: ...
2025-07-15 06:24:38,491 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-15 06:24:38,491 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-15 06:24:38,491 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🎯 ENTRY TIMING SPECIALIST - DOGE/USDT:USDT

🎯 SIGNAL ANALYSIS:
ML Ensemble: NEUTRAL (50.0%)
Technical: NEUTRAL | Momentum: 0.00
Volume: PENDING | Orderflow: NEUTRAL

📊 MARKET MICROSTRUCTURE:
Bid/Ask: $0.170000/$0.170000
Spread: 0.100% | L2 Imbalance: 0.0%
Recent Flow: BALANCED | Volume Spike: False

📈 KEY LEVELS:
Support: $0.170000
Resistance: $0.170000
Distance to Support: 0.00%

⏰ TIMING FACTORS:
- Price near key levels: NEUTRAL
- Volume confirmation: PENDING
- Spread conditions: FAVORABLE
- M...
2025-07-15 06:24:38,492 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-15 06:24:42,283 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 234 chars
2025-07-15 06:24:42,283 - llama.lmstudio_runner - INFO - 📄 Response Preview: ```json
{
  "action": "LONG",
  "confidence": 90,
  "entry_reason": "Market broke through resistance level with a rapid move of more than 2 pip.",
  "take_profit": 1.5,
  "stop_loss": 0.3,
  "hold_time": "3min",
  "leverage": 40
}
```...
2025-07-15 06:24:42,283 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 174, Completion: 97, Total: 271
2025-07-15 06:24:42,284 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['action', 'confidence', 'entry_reason', 'take_profit', 'stop_loss', 'hold_time', 'leverage']
2025-07-15 06:24:42,284 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['ACTION', 'CONFIDENCE', 'ENTRY_REASON', 'TAKE_PROFIT', 'STOP_LOSS', 'HOLD_TIME', 'LEVERAGE']
2025-07-15 06:24:42,284 - core.llm_response_parsers - INFO - Risk assessment parsed: REJECTED (Risk: 50)
2025-07-15 06:24:42,284 - core.llm_orchestrator - INFO - ✅ Completed prompt: risk_assessment
2025-07-15 06:24:44,136 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 358 chars
2025-07-15 06:24:44,136 - llama.lmstudio_runner - INFO - 📄 Response Preview: {
  "action": "ENTER_NOW",
  "entry_type": "LIMIT",
  "confidence": 90,
  "wait_for": null,
  "max_wait_seconds": 30,
  "reasoning": "Market conditions are favorable with a narrow bid/ask spread and no significant volume spikes. Momentum is neutral but the risk to reward ratio of 2:1 justifies an im...
2025-07-15 06:24:44,136 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 610, Completion: 114, Total: 724
2025-07-15 06:24:44,137 - core.llm_response_parsers - WARNING - 🚨 JSON decode error: Expecting ',' delimiter: line 1 column 271 (char 270)
2025-07-15 06:24:44,137 - core.llm_response_parsers - WARNING - 🔍 Problematic JSON: { "action": "ENTER_NOW", "entry_type": "LIMIT", "confidence": 90, "wait_for": null, "max_wait_seconds": 30, "reasoning": "Market conditions are favorable with a narrow bid/ask spread and no significan...
2025-07-15 06:24:44,137 - core.llm_response_parsers - INFO - ✅ Aggressive cleanup extracted: ['action', 'confidence', 'reasoning']
2025-07-15 06:24:44,137 - core.llm_response_parsers - INFO - ✅ JSON fixed with aggressive cleanup
2025-07-15 06:24:44,137 - core.llm_response_parsers - INFO - Entry timing parsed: WAIT (LIMIT order)
2025-07-15 06:24:44,137 - core.llm_orchestrator - INFO - ✅ Completed prompt: entry_timing
2025-07-15 06:24:44,138 - core.llm_orchestrator - INFO - 🚀 PARALLEL LLM cycle completed in 5.65s - 2 prompts executed concurrently
2025-07-15 06:25:09,685 - core.llm_orchestrator - INFO - 🔍 FULL MODE: Executing all 8 prompts in PARALLEL for comprehensive analysis
2025-07-15 06:25:09,686 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-15 06:25:09,686 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-15 06:25:09,686 - llama.lmstudio_runner - INFO - 💬 User Prompt: 📊 MARKET REGIME ANALYST - DOGE/USDT:USDT

🔍 MULTI-TIMEFRAME ANALYSIS:
1m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
5m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
15m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%

📈 VOLATILITY METRICS:
ATR: 0.001000 | Spread: 0.100%
Volume Ratio: 1.0x | Price Range: 1.00%

🎯 REGIME CLASSIFICATION:
- TRENDING_BULL: Strong upward momentum, high volume, clear direction
- TRENDING_BEAR: Strong downward momentum, high volume, clear direction
- RANGING_TIGHT: Low volatility, narrow pric...
2025-07-15 06:25:09,686 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-15 06:25:09,687 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-15 06:25:09,687 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-15 06:25:09,688 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🎯 ENTRY TIMING SPECIALIST - DOGE/USDT:USDT

🎯 SIGNAL ANALYSIS:
ML Ensemble: NEUTRAL (50.0%)
Technical: NEUTRAL | Momentum: 0.00
Volume: PENDING | Orderflow: NEUTRAL

📊 MARKET MICROSTRUCTURE:
Bid/Ask: $0.170000/$0.170000
Spread: 0.100% | L2 Imbalance: 0.0%
Recent Flow: BALANCED | Volume Spike: False

📈 KEY LEVELS:
Support: $0.170000
Resistance: $0.170000
Distance to Support: 0.00%

⏰ TIMING FACTORS:
- Price near key levels: NEUTRAL
- Volume confirmation: PENDING
- Spread conditions: FAVORABLE
- M...
2025-07-15 06:25:09,687 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-15 06:25:09,689 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-15 06:25:09,689 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a conservative futures trading analyst. Analyze market data carefully and provide measured recommendations. Use format: DECISION: [LONG/SHORT/WAIT], CONFIDENCE: [50-100]%, TAKE_PROFIT: [%], ST...
2025-07-15 06:25:09,689 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🔍 INTELLIGENT OPPORTUNITY SCANNER - Account Health Aware

🚨 ACCOUNT HEALTH STATUS: HEALTHY
💰 Balance: $1000.00 | Health: Healthy - Normal Trading
📊 Current Exposure: $0.00 (0.0% of account)
🎯 Trading Allowed: YES

📊 SYMBOL ANALYSIS:
• DOGE/USDT:USDT: $0.000000 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• BTC/USDT:USDT: $95000.000000 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• ETH/USDT:USDT: $3500.000000 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• SOL/USDT:USDT: $200.000000 | Mom: +0.0% | Vol: 1.0x |...
2025-07-15 06:25:09,690 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-15 06:25:09,694 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-15 06:25:09,694 - core.llm_orchestrator - INFO - 🚀 Submitted 4 prompts for parallel execution
2025-07-15 06:25:09,695 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-15 06:25:09,695 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🔄 STRATEGY ADAPTATION SPECIALIST

📊 PERFORMANCE ANALYSIS (Last 24h):
Trades: 0 | Win Rate: 50.0%
Avg Profit: 0.80% | Avg Loss: -0.30%
Sharpe Ratio: 1.00 | Max Drawdown: 0.0%
Total PnL: $0.00 | ROI: 0.0%

🎯 CURRENT STRATEGY:
Risk per Trade: 2.0% | Avg Hold Time: 8.0min
Entry Threshold: 70% | Exit Threshold: 60%
Position Size Method: FIXED_RISK | Max Positions: 3

📈 MARKET REGIME: UNKNOWN
Regime Confidence: 50.0%
Scalp Suitability: MEDIUM

🔧 ADAPTATION FACTORS:
- Win rate trending: DOWN
- Drawdown...
2025-07-15 06:25:09,695 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-15 06:25:15,150 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 717 chars
2025-07-15 06:25:15,150 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: LONG, CONFIDENCE: 85%, TAKE_PROFIT: 12%, STOP_LOSS: -7%, EXPLANATION: The account is in a healthy state with normal trading parameters and the historical context shows no recent price movement or signals. Given that all four symbols have similar setup quality, I recommend going long on DOG...
2025-07-15 06:25:15,151 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 933, Completion: 181, Total: 1114
2025-07-15 06:25:15,151 - core.llm_response_parsers - INFO - 🔄 No JSON found, trying structured text parsing
2025-07-15 06:25:15,152 - core.llm_response_parsers - INFO - Parsed structured text: {'DECISION': 'LONG', 'CONFIDENCE': 85.0, 'TAKE_PROFIT': 12.0, 'STOP_LOSS': -7.0, 'EXPLANATION': "THE ACCOUNT IS IN A HEALTHY STATE WITH NORMAL TRADING PARAMETERS AND THE HISTORICAL CONTEXT SHOWS NO RECENT PRICE MOVEMENT OR SIGNALS. GIVEN THAT ALL FOUR SYMBOLS HAVE SIMILAR SETUP QUALITY, I RECOMMEND GOING LONG ON DOGE/USDT DUE TO ITS LOWER CURRENT EXPOSURE COMPARED TO BTC WHICH HAS HIGHER VOLATILITY BUT ALSO OFFERS POTENTIALLY GREATER REWARDS IN A CONSERVATIVE STRATEGY ALIGNED WITH ACCOUNT HEALTH AND RISK CRITERIA. THE TAKE-PROFIT IS SET AT 12% ABOVE THE ENTRY PRICE CONSIDERING NORMAL MARKET FLUCTUATIONS, WHILE STOP LOSS WILL BE TRIGGERED IF LOSSES EXCEED -7%, ENSURING WE DON'T BREACH OUR MAXIMUM TOTAL EXPOSURE LIMIT OF $70%.", 'ACTION': 'ENTER_NOW'}
2025-07-15 06:25:15,153 - core.llm_response_parsers - INFO - ✅ Structured text parsing successful: ['DECISION', 'CONFIDENCE', 'TAKE_PROFIT', 'STOP_LOSS', 'EXPLANATION', 'ACTION']
2025-07-15 06:25:15,153 - core.llm_response_parsers - INFO - Opportunity scanner parsed: BREAKOUT (MOMENTUM)
2025-07-15 06:25:15,154 - core.llm_orchestrator - INFO - ✅ Completed prompt: opportunity_scanner
2025-07-15 06:25:17,267 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 425 chars
2025-07-15 06:25:17,268 - llama.lmstudio_runner - INFO - 📄 Response Preview: {
  "action": "ENTER",
  "entry_type": "LIMIT",
  "confidence": 85,
  "wait_for": null,
  "max_wait_seconds": 0,
  "reasoning": "Market conditions are favorable with a tight spread and balanced recent flow. Despite the neutral signals from ML Ensemble and technical indicators, historical context sho...
2025-07-15 06:25:17,268 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 610, Completion: 117, Total: 727
2025-07-15 06:25:17,269 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['action', 'entry_type', 'confidence', 'wait_for', 'max_wait_seconds', 'reasoning']
2025-07-15 06:25:17,269 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['ACTION', 'ENTRY_TYPE', 'CONFIDENCE', 'WAIT_FOR', 'MAX_WAIT_SECONDS', 'REASONING']
2025-07-15 06:25:17,270 - core.llm_response_parsers - INFO - Entry timing parsed: WAIT (LIMIT order)
2025-07-15 06:25:17,270 - core.llm_orchestrator - INFO - ✅ Completed prompt: entry_timing
2025-07-15 06:25:22,579 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 1437 chars
2025-07-15 06:25:22,580 - llama.lmstudio_runner - INFO - 📄 Response Preview: {
  "risk_adjustment": [0.5, 2.0],
  "hold_time_target": "8", // in minutes as specified by the original instruction to maintain current hold time target of 8min
  "entry_threshold": 70, // remains at a high confidence level for entry threshold
  "exit_threshold": 60, // slightly lower exit threshol...
2025-07-15 06:25:22,580 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 619, Completion: 336, Total: 955
2025-07-15 06:25:22,580 - core.llm_response_parsers - WARNING - 🚨 JSON decode error: Expecting ',' delimiter: line 1 column 287 (char 286)
2025-07-15 06:25:22,581 - core.llm_response_parsers - WARNING - 🔍 Problematic JSON: { "risk_adjustment": [0.5, 2.0], "hold_time_target": "8", "entry_threshold": 70, "exit_threshold": 60, "sizing_method": "FIXED_RISK", "reasoning": "Maintain current strategy - although there has been ...
2025-07-15 06:25:22,581 - core.llm_response_parsers - INFO - ✅ Aggressive cleanup extracted: ['reasoning']
2025-07-15 06:25:22,581 - core.llm_response_parsers - INFO - ✅ JSON fixed with aggressive cleanup
2025-07-15 06:25:22,581 - core.llm_response_parsers - INFO - Strategy adaptation parsed: Risk adj 1.0x
2025-07-15 06:25:22,581 - core.llm_action_executors - INFO - Applying strategy adaptations: Risk 1.0x, Hold time 8min
2025-07-15 06:25:22,582 - core.llm_orchestrator - INFO - ✅ Completed prompt: strategy_adaptation
2025-07-15 06:25:25,073 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 427 chars
2025-07-15 06:25:25,074 - llama.lmstudio_runner - INFO - 📄 Response Preview: ```json
{
  "REGIME": "RANGING_TIGHT",
  "CONFIDENCE": 75,
  "SCALP_SUITABILITY": "MEDIUM",
  "RECOMMENDED_TIMEFRAME": "1m",
  "RISK_LEVEL": "MEDIUM",
  "REASONING": "The market is currently in a consolidation phase with moderate volatility, which presents some challenges for scalping but still offe...
2025-07-15 06:25:25,074 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 705, Completion: 149, Total: 854
2025-07-15 06:25:25,074 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['REGIME', 'CONFIDENCE', 'SCALP_SUITABILITY', 'RECOMMENDED_TIMEFRAME', 'RISK_LEVEL', 'REASONING']
2025-07-15 06:25:25,075 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['REGIME', 'CONFIDENCE', 'SCALP_SUITABILITY', 'RECOMMENDED_TIMEFRAME', 'RISK_LEVEL', 'REASONING']
2025-07-15 06:25:25,075 - core.llm_response_parsers - INFO - Market regime parsed: RANGING_TIGHT (MEDIUM scalp suitability)
2025-07-15 06:25:25,075 - core.llm_orchestrator - INFO - ✅ Completed prompt: market_regime
2025-07-15 06:25:25,076 - core.llm_orchestrator - INFO - 🚀 PARALLEL LLM cycle completed in 15.39s - 4 prompts executed concurrently
2025-07-15 06:25:38,475 - core.llm_orchestrator - INFO - 🔍 FULL MODE: Executing all 8 prompts in PARALLEL for comprehensive analysis
2025-07-15 06:25:38,476 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-15 06:25:38,477 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-15 06:25:38,477 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-15 06:25:38,477 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-15 06:25:38,477 - llama.lmstudio_runner - INFO - 💬 User Prompt: ...
2025-07-15 06:25:38,477 - core.llm_orchestrator - INFO - 🚀 Submitted 2 prompts for parallel execution
2025-07-15 06:25:38,477 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🎯 ENTRY TIMING SPECIALIST - DOGE/USDT:USDT

🎯 SIGNAL ANALYSIS:
ML Ensemble: NEUTRAL (50.0%)
Technical: NEUTRAL | Momentum: 0.00
Volume: PENDING | Orderflow: NEUTRAL

📊 MARKET MICROSTRUCTURE:
Bid/Ask: $0.170000/$0.170000
Spread: 0.100% | L2 Imbalance: 0.0%
Recent Flow: BALANCED | Volume Spike: False

📈 KEY LEVELS:
Support: $0.170000
Resistance: $0.170000
Distance to Support: 0.00%

⏰ TIMING FACTORS:
- Price near key levels: NEUTRAL
- Volume confirmation: PENDING
- Spread conditions: FAVORABLE
- M...
2025-07-15 06:25:38,478 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-15 06:25:38,478 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-15 06:25:42,449 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 341 chars
2025-07-15 06:25:42,450 - llama.lmstudio_runner - INFO - 📄 Response Preview: {
  "action": "ENTER_NOW",
  "entry_type": "LIMIT",
  "confidence": 92,
  "wait_for": null,
  "max_wait_seconds": -1,
  "reasoning": "Market conditions are favorable with a low spread and balanced recent flow. The price is near key levels indicating potential for movement without immediate risk of b...
2025-07-15 06:25:42,450 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 610, Completion: 100, Total: 710
2025-07-15 06:25:42,450 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['action', 'entry_type', 'confidence', 'wait_for', 'max_wait_seconds', 'reasoning']
2025-07-15 06:25:42,451 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['ACTION', 'ENTRY_TYPE', 'CONFIDENCE', 'WAIT_FOR', 'MAX_WAIT_SECONDS', 'REASONING']
2025-07-15 06:25:42,451 - core.llm_response_parsers - INFO - Entry timing parsed: ENTER_NOW (LIMIT order)
2025-07-15 06:25:42,451 - core.llm_action_executors - INFO - Executing entry action: ENTER_NOW (LIMIT order)
2025-07-15 06:25:42,451 - core.llm_action_executors - WARNING - No position capacity available - skipping entry
2025-07-15 06:25:42,451 - core.llm_orchestrator - INFO - ✅ Completed prompt: entry_timing
2025-07-15 06:25:43,790 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 209 chars
2025-07-15 06:25:43,790 - llama.lmstudio_runner - INFO - 📄 Response Preview: ```json
{
  "action": "LONG",
  "confidence": 90,
  "entry_reason": "Breakout above resistance level with volume spike.",
  "take_profit": 2.5,
  "stop_loss": 1.0,
  "hold_time": "3min",
  "leverage": 40
}
```...
2025-07-15 06:25:43,791 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 174, Completion: 91, Total: 265
2025-07-15 06:25:43,791 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['action', 'confidence', 'entry_reason', 'take_profit', 'stop_loss', 'hold_time', 'leverage']
2025-07-15 06:25:43,791 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['ACTION', 'CONFIDENCE', 'ENTRY_REASON', 'TAKE_PROFIT', 'STOP_LOSS', 'HOLD_TIME', 'LEVERAGE']
2025-07-15 06:25:43,791 - core.llm_response_parsers - INFO - Risk assessment parsed: REJECTED (Risk: 50)
2025-07-15 06:25:43,792 - core.llm_orchestrator - INFO - ✅ Completed prompt: risk_assessment
2025-07-15 06:25:43,792 - core.llm_orchestrator - INFO - 🚀 PARALLEL LLM cycle completed in 5.32s - 2 prompts executed concurrently
2025-07-15 06:26:08,450 - core.llm_orchestrator - INFO - 🔍 FULL MODE: Executing all 8 prompts in PARALLEL for comprehensive analysis
2025-07-15 06:26:08,451 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-15 06:26:08,452 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-15 06:26:08,452 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-15 06:26:08,452 - core.llm_orchestrator - INFO - 🚀 Submitted 2 prompts for parallel execution
2025-07-15 06:26:08,452 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-15 06:26:08,452 - llama.lmstudio_runner - INFO - 💬 User Prompt: 📊 MARKET REGIME ANALYST - DOGE/USDT:USDT

🔍 MULTI-TIMEFRAME ANALYSIS:
1m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
5m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
15m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%

📈 VOLATILITY METRICS:
ATR: 0.001000 | Spread: 0.100%
Volume Ratio: 1.0x | Price Range: 1.00%

🎯 REGIME CLASSIFICATION:
- TRENDING_BULL: Strong upward momentum, high volume, clear direction
- TRENDING_BEAR: Strong downward momentum, high volume, clear direction
- RANGING_TIGHT: Low volatility, narrow pric...
2025-07-15 06:26:08,453 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🎯 ENTRY TIMING SPECIALIST - DOGE/USDT:USDT

🎯 SIGNAL ANALYSIS:
ML Ensemble: NEUTRAL (50.0%)
Technical: NEUTRAL | Momentum: 0.00
Volume: PENDING | Orderflow: NEUTRAL

📊 MARKET MICROSTRUCTURE:
Bid/Ask: $0.170000/$0.170000
Spread: 0.100% | L2 Imbalance: 0.0%
Recent Flow: BALANCED | Volume Spike: False

📈 KEY LEVELS:
Support: $0.170000
Resistance: $0.170000
Distance to Support: 0.00%

⏰ TIMING FACTORS:
- Price near key levels: NEUTRAL
- Volume confirmation: PENDING
- Spread conditions: FAVORABLE
- M...
2025-07-15 06:26:08,453 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-15 06:26:08,454 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-15 06:26:12,454 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 295 chars
2025-07-15 06:26:12,454 - llama.lmstudio_runner - INFO - 📄 Response Preview: {
  "action": "ENTER",
  "entry_type": "LIMIT",
  "confidence": 90,
  "wait_for": null,
  "max_wait_seconds": 30,
  "reasoning": "Market conditions are favorable with a narrow spread and no significant volume spike. Momentum is neutral but the risk/reward ratio of 2:1 justifies entering now."
}...
2025-07-15 06:26:12,455 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 610, Completion: 101, Total: 711
2025-07-15 06:26:12,455 - core.llm_response_parsers - WARNING - 🚨 JSON decode error: Expecting ',' delimiter: line 1 column 255 (char 254)
2025-07-15 06:26:12,456 - core.llm_response_parsers - WARNING - 🔍 Problematic JSON: { "action": "ENTER", "entry_type": "LIMIT", "confidence": 90, "wait_for": null, "max_wait_seconds": 30, "reasoning": "Market conditions are favorable with a narrow spread and no significant volume spi...
2025-07-15 06:26:12,456 - core.llm_response_parsers - INFO - ✅ Aggressive cleanup extracted: ['action', 'confidence', 'reasoning']
2025-07-15 06:26:12,456 - core.llm_response_parsers - INFO - ✅ JSON fixed with aggressive cleanup
2025-07-15 06:26:12,456 - core.llm_response_parsers - INFO - Entry timing parsed: WAIT (LIMIT order)
2025-07-15 06:26:12,457 - core.llm_orchestrator - INFO - ✅ Completed prompt: entry_timing
2025-07-15 06:26:14,235 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 218 chars
2025-07-15 06:26:14,235 - llama.lmstudio_runner - INFO - 📄 Response Preview: ```json
{
  "REGIME": "RANGING_TIGHT",
  "CONFIDENCE": 75,
  "SCALP_SUITABILITY": "MEDIUM",
  "RECOMMENDED_TIMEFRAME": "1m",
  "RISK_LEVEL": "MEDIUM",
  "REASONING": "Consolidation phase with moderate volatility"
}
```...
2025-07-15 06:26:14,235 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 705, Completion: 108, Total: 813
2025-07-15 06:26:14,236 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['REGIME', 'CONFIDENCE', 'SCALP_SUITABILITY', 'RECOMMENDED_TIMEFRAME', 'RISK_LEVEL', 'REASONING']
2025-07-15 06:26:14,236 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['REGIME', 'CONFIDENCE', 'SCALP_SUITABILITY', 'RECOMMENDED_TIMEFRAME', 'RISK_LEVEL', 'REASONING']
2025-07-15 06:26:14,236 - core.llm_response_parsers - INFO - Market regime parsed: RANGING_TIGHT (MEDIUM scalp suitability)
2025-07-15 06:26:14,236 - core.llm_orchestrator - INFO - ✅ Completed prompt: market_regime
2025-07-15 06:26:14,237 - core.llm_orchestrator - INFO - 🚀 PARALLEL LLM cycle completed in 5.79s - 2 prompts executed concurrently
2025-07-15 06:26:38,736 - core.llm_orchestrator - INFO - 🔍 FULL MODE: Executing all 8 prompts in PARALLEL for comprehensive analysis
2025-07-15 06:26:38,737 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-15 06:26:38,737 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-15 06:26:38,738 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-15 06:26:38,738 - llama.lmstudio_runner - INFO - 💬 User Prompt: 📊 MARKET REGIME ANALYST - DOGE/USDT:USDT

🔍 MULTI-TIMEFRAME ANALYSIS:
1m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
5m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
15m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%

📈 VOLATILITY METRICS:
ATR: 0.001000 | Spread: 0.100%
Volume Ratio: 1.0x | Price Range: 1.00%

🎯 REGIME CLASSIFICATION:
- TRENDING_BULL: Strong upward momentum, high volume, clear direction
- TRENDING_BEAR: Strong downward momentum, high volume, clear direction
- RANGING_TIGHT: Low volatility, narrow pric...
2025-07-15 06:26:38,738 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-15 06:26:38,738 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-15 06:26:38,739 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-15 06:26:38,739 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-15 06:26:38,739 - core.llm_orchestrator - INFO - 🚀 Submitted 4 prompts for parallel execution
2025-07-15 06:26:38,739 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🎯 ENTRY TIMING SPECIALIST - DOGE/USDT:USDT

🎯 SIGNAL ANALYSIS:
ML Ensemble: NEUTRAL (50.0%)
Technical: NEUTRAL | Momentum: 0.00
Volume: PENDING | Orderflow: NEUTRAL

📊 MARKET MICROSTRUCTURE:
Bid/Ask: $0.170000/$0.170000
Spread: 0.100% | L2 Imbalance: 0.0%
Recent Flow: BALANCED | Volume Spike: False

📈 KEY LEVELS:
Support: $0.170000
Resistance: $0.170000
Distance to Support: 0.00%

⏰ TIMING FACTORS:
- Price near key levels: NEUTRAL
- Volume confirmation: PENDING
- Spread conditions: FAVORABLE
- M...
2025-07-15 06:26:38,739 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-15 06:26:38,739 - llama.lmstudio_runner - INFO - 💬 User Prompt: ...
2025-07-15 06:26:38,741 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a conservative futures trading analyst. Analyze market data carefully and provide measured recommendations. Use format: DECISION: [LONG/SHORT/WAIT], CONFIDENCE: [50-100]%, TAKE_PROFIT: [%], ST...
2025-07-15 06:26:38,740 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-15 06:26:38,741 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-15 06:26:38,741 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🔍 INTELLIGENT OPPORTUNITY SCANNER - Account Health Aware

🚨 ACCOUNT HEALTH STATUS: HEALTHY
💰 Balance: $1000.00 | Health: Healthy - Normal Trading
📊 Current Exposure: $0.00 (0.0% of account)
🎯 Trading Allowed: YES

📊 SYMBOL ANALYSIS:
• DOGE/USDT:USDT: $0.000000 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• BTC/USDT:USDT: $95000.000000 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• ETH/USDT:USDT: $3500.000000 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• SOL/USDT:USDT: $200.000000 | Mom: +0.0% | Vol: 1.0x |...
2025-07-15 06:26:38,742 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-15 06:26:42,854 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 282 chars
2025-07-15 06:26:42,855 - llama.lmstudio_runner - INFO - 📄 Response Preview: ```json
{
  "REGIME": "RANGING_TIGHT",
  "CONFIDENCE": 75,
  "SCALP_SUITABILITY": "MEDIUM",
  "RECOMMENDED_TIMEFRAME": "1m",
  "RISK_LEVEL": "MEDIUM",
  "REASONING": "Consolidation phase with moderate volatility, suitable for scalping due to potential breakout opportunities."
}
```...
2025-07-15 06:26:42,855 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 705, Completion: 120, Total: 825
2025-07-15 06:26:42,856 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['REGIME', 'CONFIDENCE', 'SCALP_SUITABILITY', 'RECOMMENDED_TIMEFRAME', 'RISK_LEVEL', 'REASONING']
2025-07-15 06:26:42,857 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['REGIME', 'CONFIDENCE', 'SCALP_SUITABILITY', 'RECOMMENDED_TIMEFRAME', 'RISK_LEVEL', 'REASONING']
2025-07-15 06:26:42,857 - core.llm_response_parsers - INFO - Market regime parsed: RANGING_TIGHT (MEDIUM scalp suitability)
2025-07-15 06:26:42,858 - core.llm_orchestrator - INFO - ✅ Completed prompt: market_regime
2025-07-15 06:26:44,781 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 373 chars
2025-07-15 06:26:44,781 - llama.lmstudio_runner - INFO - 📄 Response Preview: {
  "action": "ENTER_NOW",
  "entry_type": "LIMIT",
  "confidence": 85,
  "wait_for": null,
  "max_wait_seconds": -1,
  "reasoning": "Market conditions are currently favorable with a narrow spread and balanced recent flow. Although volume confirmation is pending, the risk/reward ratio of 2:1 justifi...
2025-07-15 06:26:44,781 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 610, Completion: 115, Total: 725
2025-07-15 06:26:44,782 - core.llm_response_parsers - WARNING - 🚨 JSON decode error: Expecting ',' delimiter: line 1 column 279 (char 278)
2025-07-15 06:26:44,783 - core.llm_response_parsers - WARNING - 🔍 Problematic JSON: { "action": "ENTER_NOW", "entry_type": "LIMIT", "confidence": 85, "wait_for": null, "max_wait_seconds": -1, "reasoning": "Market conditions are currently favorable with a narrow spread and balanced re...
2025-07-15 06:26:44,783 - core.llm_response_parsers - INFO - ✅ Aggressive cleanup extracted: ['action', 'confidence', 'reasoning']
2025-07-15 06:26:44,784 - core.llm_response_parsers - INFO - ✅ JSON fixed with aggressive cleanup
2025-07-15 06:26:44,784 - core.llm_response_parsers - INFO - Entry timing parsed: WAIT (LIMIT order)
2025-07-15 06:26:44,784 - core.llm_orchestrator - INFO - ✅ Completed prompt: entry_timing
2025-07-15 06:26:46,147 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 209 chars
2025-07-15 06:26:46,147 - llama.lmstudio_runner - INFO - 📄 Response Preview: ```json
{
  "action": "LONG",
  "confidence": 90,
  "entry_reason": "Breakout above resistance level with volume surge.",
  "take_profit": 2.5,
  "stop_loss": 1.0,
  "hold_time": "3min",
  "leverage": 40
}
```...
2025-07-15 06:26:46,147 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 174, Completion: 91, Total: 265
2025-07-15 06:26:46,148 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['action', 'confidence', 'entry_reason', 'take_profit', 'stop_loss', 'hold_time', 'leverage']
2025-07-15 06:26:46,148 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['ACTION', 'CONFIDENCE', 'ENTRY_REASON', 'TAKE_PROFIT', 'STOP_LOSS', 'HOLD_TIME', 'LEVERAGE']
2025-07-15 06:26:46,148 - core.llm_response_parsers - INFO - Risk assessment parsed: REJECTED (Risk: 50)
2025-07-15 06:26:46,148 - core.llm_orchestrator - INFO - ✅ Completed prompt: risk_assessment
2025-07-15 06:26:49,283 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 771 chars
2025-07-15 06:26:49,284 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: LONG, CONFIDENCE: 85%, TAKE_PROFIT: 2%, STOP_LOSS: -3%, EXPLANATION: The market data indicates a neutral sector momentum with normal volatility and average volume profile. Given the account's healthy status and adherence to preservation criteria, there is an opportunity for conservative en...
2025-07-15 06:26:49,284 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 933, Completion: 176, Total: 1109
2025-07-15 06:26:49,284 - core.llm_response_parsers - INFO - 🔄 No JSON found, trying structured text parsing
2025-07-15 06:26:49,285 - core.llm_response_parsers - INFO - Parsed structured text: {'DECISION': 'LONG', 'CONFIDENCE': 85.0, 'TAKE_PROFIT': 2.0, 'STOP_LOSS': -3.0, 'EXPLANATION': "THE MARKET DATA INDICATES A NEUTRAL SECTOR MOMENTUM WITH NORMAL VOLATILITY AND AVERAGE VOLUME PROFILE. GIVEN THE ACCOUNT'S HEALTHY STATUS AND ADHERENCE TO PRESERVATION CRITERIA, THERE IS AN OPPORTUNITY FOR CONSERVATIVE ENTRY INTO DOGE/USDT DUE TO ITS MEDIUM SETUP QUALITY WITHOUT COMPROMISING RISK MANAGEMENT PRINCIPLES OR EXCEEDING LIQUIDITY REQUIREMENTS. THE HISTORICAL CONTEXT SHOWS NO RECENT PRICE MOVEMENT IN THIS CRYPTOCURRENCY PAIR; HOWEVER, THE LACK OF VOLATILITY SUGGESTS A STABLE ENVIRONMENT SUITABLE FOR LONG POSITIONING WITH MODERATE STOP LOSS AND TAKE PROFIT LEVELS SET BASED ON ACCOUNT'S HEALTH-ADJUSTED PARAMETERS TO ENSURE CAPITAL PRESERVATION WHILE CAPTURING POTENTIAL GAINS.", 'ACTION': 'ENTER_NOW'}
2025-07-15 06:26:49,285 - core.llm_response_parsers - INFO - ✅ Structured text parsing successful: ['DECISION', 'CONFIDENCE', 'TAKE_PROFIT', 'STOP_LOSS', 'EXPLANATION', 'ACTION']
2025-07-15 06:26:49,285 - core.llm_response_parsers - INFO - Opportunity scanner parsed: BREAKOUT (MOMENTUM)
2025-07-15 06:26:49,285 - core.llm_orchestrator - INFO - ✅ Completed prompt: opportunity_scanner
2025-07-15 06:26:49,287 - core.llm_orchestrator - INFO - 🚀 PARALLEL LLM cycle completed in 10.55s - 4 prompts executed concurrently
2025-07-15 06:27:08,504 - core.llm_orchestrator - INFO - 🔍 FULL MODE: Executing all 8 prompts in PARALLEL for comprehensive analysis
2025-07-15 06:27:08,506 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-15 06:27:08,506 - core.llm_orchestrator - INFO - 🚀 Submitted 1 prompts for parallel execution
2025-07-15 06:27:08,506 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-15 06:27:08,507 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🎯 ENTRY TIMING SPECIALIST - DOGE/USDT:USDT

🎯 SIGNAL ANALYSIS:
ML Ensemble: NEUTRAL (50.0%)
Technical: NEUTRAL | Momentum: 0.00
Volume: PENDING | Orderflow: NEUTRAL

📊 MARKET MICROSTRUCTURE:
Bid/Ask: $0.170000/$0.170000
Spread: 0.100% | L2 Imbalance: 0.0%
Recent Flow: BALANCED | Volume Spike: False

📈 KEY LEVELS:
Support: $0.170000
Resistance: $0.170000
Distance to Support: 0.00%

⏰ TIMING FACTORS:
- Price near key levels: NEUTRAL
- Volume confirmation: PENDING
- Spread conditions: FAVORABLE
- M...
2025-07-15 06:27:08,508 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-15 06:27:12,356 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 277 chars
2025-07-15 06:27:12,357 - llama.lmstudio_runner - INFO - 📄 Response Preview: {
  "action": "ENTER",
  "entry_type": "LIMIT",
  "confidence": 80,
  "wait_for": null,
  "max_wait_seconds": 30,
  "reasoning": "Market conditions are favorable with a narrow spread and volume spike not yet confirmed. Entry now would capitalize on potential momentum shift."
}...
2025-07-15 06:27:12,357 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 610, Completion: 90, Total: 700
2025-07-15 06:27:12,358 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['action', 'entry_type', 'confidence', 'wait_for', 'max_wait_seconds', 'reasoning']
2025-07-15 06:27:12,358 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['ACTION', 'ENTRY_TYPE', 'CONFIDENCE', 'WAIT_FOR', 'MAX_WAIT_SECONDS', 'REASONING']
2025-07-15 06:27:12,358 - core.llm_response_parsers - INFO - Entry timing parsed: WAIT (LIMIT order)
2025-07-15 06:27:12,358 - core.llm_orchestrator - INFO - ✅ Completed prompt: entry_timing
2025-07-15 06:27:12,359 - core.llm_orchestrator - INFO - 🚀 PARALLEL LLM cycle completed in 3.85s - 1 prompts executed concurrently
2025-07-15 06:27:38,499 - core.llm_orchestrator - INFO - 🔍 FULL MODE: Executing all 8 prompts in PARALLEL for comprehensive analysis
2025-07-15 06:27:38,500 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-15 06:27:38,500 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-15 06:27:38,500 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-15 06:27:38,501 - llama.lmstudio_runner - INFO - 💬 User Prompt: 📊 MARKET REGIME ANALYST - DOGE/USDT:USDT

🔍 MULTI-TIMEFRAME ANALYSIS:
1m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
5m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
15m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%

📈 VOLATILITY METRICS:
ATR: 0.001000 | Spread: 0.100%
Volume Ratio: 1.0x | Price Range: 1.00%

🎯 REGIME CLASSIFICATION:
- TRENDING_BULL: Strong upward momentum, high volume, clear direction
- TRENDING_BEAR: Strong downward momentum, high volume, clear direction
- RANGING_TIGHT: Low volatility, narrow pric...
2025-07-15 06:27:38,501 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-15 06:27:38,501 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-15 06:27:38,501 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-15 06:27:38,501 - core.llm_orchestrator - INFO - 🚀 Submitted 4 prompts for parallel execution
2025-07-15 06:27:38,501 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-15 06:27:38,501 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-15 06:27:38,502 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🎯 ENTRY TIMING SPECIALIST - DOGE/USDT:USDT

🎯 SIGNAL ANALYSIS:
ML Ensemble: NEUTRAL (50.0%)
Technical: NEUTRAL | Momentum: 0.00
Volume: PENDING | Orderflow: NEUTRAL

📊 MARKET MICROSTRUCTURE:
Bid/Ask: $0.170000/$0.170000
Spread: 0.100% | L2 Imbalance: 0.0%
Recent Flow: BALANCED | Volume Spike: False

📈 KEY LEVELS:
Support: $0.170000
Resistance: $0.170000
Distance to Support: 0.00%

⏰ TIMING FACTORS:
- Price near key levels: NEUTRAL
- Volume confirmation: PENDING
- Spread conditions: FAVORABLE
- M...
2025-07-15 06:27:38,502 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-15 06:27:38,502 - llama.lmstudio_runner - INFO - 💬 User Prompt: ...
2025-07-15 06:27:38,503 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-15 06:27:38,504 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🔄 STRATEGY ADAPTATION SPECIALIST

📊 PERFORMANCE ANALYSIS (Last 24h):
Trades: 0 | Win Rate: 50.0%
Avg Profit: 0.80% | Avg Loss: -0.30%
Sharpe Ratio: 1.00 | Max Drawdown: 0.0%
Total PnL: $0.00 | ROI: 0.0%

🎯 CURRENT STRATEGY:
Risk per Trade: 2.0% | Avg Hold Time: 8.0min
Entry Threshold: 70% | Exit Threshold: 60%
Position Size Method: FIXED_RISK | Max Positions: 3

📈 MARKET REGIME: UNKNOWN
Regime Confidence: 50.0%
Scalp Suitability: MEDIUM

🔧 ADAPTATION FACTORS:
- Win rate trending: DOWN
- Drawdown...
2025-07-15 06:27:38,504 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-15 06:27:38,504 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-15 06:27:42,834 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 311 chars
2025-07-15 06:27:42,835 - llama.lmstudio_runner - INFO - 📄 Response Preview: ```json
{
  "REGIME": "RANGING_TIGHT",
  "CONFIDENCE": 75,
  "SCALP_SUITABILITY": "MEDIUM",
  "RECOMMENDED_TIMEFRAME": "1m",
  "RISK_LEVEL": "MEDIUM",
  "REASONING": "Consolidation phase with moderate volatility, suitable for scalping but requires careful monitoring of the market to avoid sudden bre...
2025-07-15 06:27:42,835 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 705, Completion: 124, Total: 829
2025-07-15 06:27:42,835 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['REGIME', 'CONFIDENCE', 'SCALP_SUITABILITY', 'RECOMMENDED_TIMEFRAME', 'RISK_LEVEL', 'REASONING']
2025-07-15 06:27:42,836 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['REGIME', 'CONFIDENCE', 'SCALP_SUITABILITY', 'RECOMMENDED_TIMEFRAME', 'RISK_LEVEL', 'REASONING']
2025-07-15 06:27:42,837 - core.llm_response_parsers - INFO - Market regime parsed: RANGING_TIGHT (MEDIUM scalp suitability)
2025-07-15 06:27:42,837 - core.llm_orchestrator - INFO - ✅ Completed prompt: market_regime
2025-07-15 06:27:49,199 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 1566 chars
2025-07-15 06:27:49,200 - llama.lmstudio_runner - INFO - 📄 Response Preview: {
  "risk_adjustment": {"min": 0.5, "max": 2.0},
  "hold_time_target": "8", // in minutes
  "entry_threshold": 70, // as a percentage confidence level for market movement confirmation
  "exit_threshold": 60, // same format as entry threshold; used to determine when to exit based on the strength of t...
2025-07-15 06:27:49,200 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 619, Completion: 402, Total: 1021
2025-07-15 06:27:49,200 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['min', 'max']
2025-07-15 06:27:49,201 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['MIN', 'MAX', 'CONFIDENCE']
2025-07-15 06:27:49,201 - core.llm_response_parsers - INFO - Strategy adaptation parsed: Risk adj 1.0x
2025-07-15 06:27:49,201 - core.llm_action_executors - INFO - Applying strategy adaptations: Risk 1.0x, Hold time 8min
2025-07-15 06:27:49,201 - core.llm_orchestrator - INFO - ✅ Completed prompt: strategy_adaptation
2025-07-15 06:27:51,110 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 293 chars
2025-07-15 06:27:51,111 - llama.lmstudio_runner - INFO - 📄 Response Preview: {
  "action": "ENTER",
  "entry_type": "LIMIT",
  "confidence": 85,
  "wait_for": null,
  "max_wait_seconds": 0,
  "reasoning": "Market conditions are favorable with a narrow spread and balanced recent flow. Momentum is neutral but the risk/reward ratio of 2:1 justifies an immediate entry."
}...
2025-07-15 06:27:51,111 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 610, Completion: 100, Total: 710
2025-07-15 06:27:51,112 - core.llm_response_parsers - WARNING - 🚨 JSON decode error: Expecting ',' delimiter: line 1 column 247 (char 246)
2025-07-15 06:27:51,113 - core.llm_response_parsers - WARNING - 🔍 Problematic JSON: { "action": "ENTER", "entry_type": "LIMIT", "confidence": 85, "wait_for": null, "max_wait_seconds": 0, "reasoning": "Market conditions are favorable with a narrow spread and balanced recent flow. Mome...
2025-07-15 06:27:51,114 - core.llm_response_parsers - INFO - ✅ Aggressive cleanup extracted: ['action', 'confidence', 'reasoning']
2025-07-15 06:27:51,117 - core.llm_response_parsers - INFO - ✅ JSON fixed with aggressive cleanup
2025-07-15 06:27:51,117 - core.llm_response_parsers - INFO - Entry timing parsed: WAIT (LIMIT order)
2025-07-15 06:27:51,119 - core.llm_orchestrator - INFO - ✅ Completed prompt: entry_timing
2025-07-15 06:27:52,638 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 221 chars
2025-07-15 06:27:52,647 - llama.lmstudio_runner - INFO - 📄 Response Preview: ```json
{
  "action": "LONG",
  "confidence": 90,
  "entry_reason": "Breakout above resistance level with strong volume indicators.",
  "take_profit": 2.5,
  "stop_loss": 1.0,
  "hold_time": "3min",
  "leverage": 40
}
```...
2025-07-15 06:27:52,651 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 174, Completion: 92, Total: 266
2025-07-15 06:27:52,654 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['action', 'confidence', 'entry_reason', 'take_profit', 'stop_loss', 'hold_time', 'leverage']
2025-07-15 06:27:52,658 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['ACTION', 'CONFIDENCE', 'ENTRY_REASON', 'TAKE_PROFIT', 'STOP_LOSS', 'HOLD_TIME', 'LEVERAGE']
2025-07-15 06:27:52,659 - core.llm_response_parsers - INFO - Risk assessment parsed: REJECTED (Risk: 50)
2025-07-15 06:27:52,661 - core.llm_orchestrator - INFO - ✅ Completed prompt: risk_assessment
2025-07-15 06:27:52,671 - core.llm_orchestrator - INFO - 🚀 PARALLEL LLM cycle completed in 14.17s - 4 prompts executed concurrently
