"""
Master Autonomous Controller
Integrates all autonomous trading components into a unified system
"""

import asyncio
from datetime import datetime, time
import logging
from typing import Dict, List, Optional

logger = logging.getLogger(__name__)

class AutonomousController:
    """
    Master controller for fully autonomous trading
    """
    
    def __init__(self, exchange, config: Dict):
        self.exchange = exchange
        self.config = config
        
        # Initialize core components
        self.portfolio_manager = None
        self.performance_tracker = None
        self.trade_executor = None
        self.ml_manager = None
        self.adaptive_updater = None
        self.rl_agent = None
        
        # Control flags
        self.is_running = False
        self.use_rl = config.get('use_rl', False)
        self.trading_hours = config.get('trading_hours', {'start': 0, 'end': 24})  # 24/7 by default
        
        # Performance monitoring
        self.cycle_count = 0
        self.last_performance_check = datetime.now()
        
        logger.info("[AUTONOMOUS] Controller initialized")

    async def _initialize_trade_executor(self):
        """Initialize the trade executor asynchronously"""
        try:
            success = await self.trade_executor.initialize()
            if success:
                logger.info("[AUTONOMOUS] Trade executor initialized successfully")
            else:
                logger.error("[AUTONOMOUS] Failed to initialize trade executor")
        except Exception as e:
            logger.error(f"[AUTONOMOUS] Error initializing trade executor: {e}")

    async def initialize(self):
        """Initialize all components"""
        logger.info("[AUTONOMOUS] Initializing autonomous trading controller...")
        
        # Initialize portfolio manager
        from portfolio.portfolio_manager import PortfolioManager
        self.portfolio_manager = PortfolioManager(
            initial_balance=self.config.get('initial_balance', 1000.0),
            max_positions=self.config.get('max_positions', 5)
        )
        
        # Initialize performance tracker
        from monitoring.performance_tracker import PerformanceTracker
        self.performance_tracker = PerformanceTracker()
        
        # Initialize unified trade executor
        from execution.execution_adapter import ExecutionAdapter

        # Determine trading mode
        trading_mode = "paper"  # Default to paper trading
        exchange_config = None

        # Check if we should use live trading
        if self.config.get('live_trading', False):
            try:
                from credentials import HTX_API_KEY, HTX_SECRET_KEY, validate_credentials

                if validate_credentials():
                    trading_mode = "live"
                    exchange_config = {
                        'exchange': 'htx',
                        'api_key': HTX_API_KEY,
                        'secret': HTX_SECRET_KEY
                    }
                    logger.info("✅ Live trading mode enabled")
                else:
                    logger.warning("⚠️ Invalid credentials, using paper trading")
            except ImportError:
                logger.warning("⚠️ Credentials not available, using paper trading")

        self.trade_executor = ExecutionAdapter(
            mode=trading_mode,
            exchange_config=exchange_config,
            initial_balance=self.config.get('initial_balance', 1000.0)
        )

        # Initialize the trade executor
        asyncio.create_task(self._initialize_trade_executor())
        
        # Initialize ML components
        try:
            from ml.models import MLModelManager
            self.ml_manager = MLModelManager()
            
            from ml.adaptive_updater import AdaptiveModelUpdater
            self.adaptive_updater = AdaptiveModelUpdater(
                self.performance_tracker,
                self.ml_manager
            )
        except Exception as e:
            logger.warning(f"[AUTONOMOUS] ML components not available: {e}")
        
        # Initialize RL agent if enabled
        if self.use_rl:
            try:
                from data.exchange import ExchangeDataFetcher
                from ml.trading_env import TradingEnvironment
                from ml.rl_agent import TradingRLAgent
                
                data_fetcher = ExchangeDataFetcher(exchange_id='htx')
                trading_env = TradingEnvironment(data_fetcher)
                
                self.rl_agent = TradingRLAgent(trading_env)
                if not self.rl_agent.load_model():
                    logger.info("[AUTONOMOUS] Training new RL agent...")
                    await self.rl_agent.train(total_timesteps=50000)
            except Exception as e:
                logger.warning(f"[AUTONOMOUS] RL agent not available: {e}")
                self.use_rl = False
        
        logger.info("[AUTONOMOUS] Initialization complete")
    
    async def run_autonomous_trading(self):
        """Main autonomous trading loop"""
        logger.info("[AUTONOMOUS] Starting fully autonomous trading system...")
        self.is_running = True
        
        try:
            while self.is_running:
                cycle_start = datetime.now()
                
                # Check trading hours
                if not self.is_trading_hours():
                    await asyncio.sleep(300)  # Wait 5 minutes
                    continue
                
                # Check if we need to update models
                if self.adaptive_updater:
                    await self.check_model_updates()
                
                # Get market data and make trading decision
                decision_data = await self.make_trading_decision()
                
                # Portfolio risk management
                if self.portfolio_manager:
                    await self.portfolio_manager.rebalance_portfolio()
                
                # Execute trading decision
                if decision_data['decision'] != 'WAIT':
                    execution_result = await self.execute_trade(decision_data)
                    await self.record_trade_result(execution_result, decision_data)
                
                # Performance monitoring
                await self.monitor_performance()
                
                # Update cycle count
                self.cycle_count += 1
                
                # Display status
                await self.display_status()
                
                # Adaptive sleep based on market activity
                sleep_time = self.calculate_adaptive_sleep_time()
                await asyncio.sleep(sleep_time)
                
        except Exception as e:
            logger.error(f"[AUTONOMOUS] Critical error in trading loop: {e}")
            await self.emergency_shutdown()
        finally:
            self.is_running = False
    
    async def make_trading_decision(self) -> Dict:
        """Make autonomous trading decision"""
        if self.use_rl and self.rl_agent:
            # Use RL agent for decision making
            return await self.make_rl_decision()
        else:
            # Use traditional ML ensemble approach
            return await self.make_ensemble_decision()
    
    async def make_rl_decision(self) -> Dict:
        """Make decision using RL agent"""
        try:
            # Get market observation
            market_obs = await self.get_market_observation()
            
            # Get RL action
            rl_action = self.rl_agent.predict(market_obs)
            
            # Convert to trading decision format
            direction = int(rl_action[0])
            position_size = float(rl_action[1])
            leverage = float(rl_action[2])
            
            decision_map = {0: 'WAIT', 1: 'LONG', 2: 'SHORT'}
            
            return {
                'decision': decision_map[direction],
                'confidence': min(95, 60 + position_size * 35),
                'position_size': position_size,
                'leverage': leverage,
                'source': 'rl_agent'
            }
        except Exception as e:
            logger.error(f"[AUTONOMOUS] RL decision error: {e}")
            return {'decision': 'WAIT', 'confidence': 0, 'source': 'error'}
    
    async def make_ensemble_decision(self) -> Dict:
        """Make decision using traditional ensemble approach"""
        try:
            # This would integrate with your existing run_trading_system_with_dynamic_selection
            from main import run_trading_system_with_dynamic_selection
            
            decision, explanation, parsed_response, selected_symbol = run_trading_system_with_dynamic_selection(
                use_live_data=True,
                enable_dynamic_selection=True
            )
            
            return {
                'decision': decision,
                'confidence': parsed_response.get('confidence', 50),
                'selected_symbol': selected_symbol,
                'leverage_position_sizing': parsed_response.get('leverage_position_sizing', {}),
                'explanation': explanation,
                'source': 'ensemble'
            }
        except Exception as e:
            logger.error(f"[AUTONOMOUS] Ensemble decision error: {e}")
            return {'decision': 'WAIT', 'confidence': 0, 'source': 'error'}
    
    async def execute_trade(self, decision_data: Dict) -> Dict:
        """Execute trading decision through portfolio manager"""
        try:
            # Execute through trade executor
            execution_result = await self.trade_executor.execute_trading_decision(decision_data)
            
            # If successful, update portfolio manager
            if execution_result['status'] == 'FILLED' and self.portfolio_manager:
                await self.portfolio_manager.open_position(
                    symbol=execution_result['symbol'],
                    side=execution_result['side'],
                    size=execution_result['amount'],
                    entry_price=execution_result['price'],
                    leverage=decision_data.get('leverage', 1.0)
                )
            
            return execution_result
        except Exception as e:
            logger.error(f"[AUTONOMOUS] Trade execution error: {e}")
            return {'status': 'ERROR', 'reason': str(e)}
    
    async def record_trade_result(self, execution_result: Dict, decision_data: Dict):
        """Record trade result for performance tracking"""
        if self.performance_tracker and execution_result['status'] == 'FILLED':
            trade_data = {
                'timestamp': datetime.now(),
                'symbol': execution_result['symbol'],
                'decision': decision_data['decision'],
                'confidence': decision_data.get('confidence', 0),
                'entry_price': execution_result['price'],
                'position_size': execution_result['amount'],
                'leverage': decision_data.get('leverage', 1.0),
                'execution_status': execution_result['status'],
                'trade_source': decision_data.get('source', 'autonomous')
            }
            self.performance_tracker.record_trade(trade_data)
    
    async def monitor_performance(self):
        """Monitor and log performance metrics"""
        if self.performance_tracker:
            # Calculate daily metrics every hour
            if (datetime.now() - self.last_performance_check).total_seconds() > 3600:
                metrics = self.performance_tracker.calculate_daily_metrics()
                logger.info(f"[PERFORMANCE] Daily metrics: {metrics}")
                self.last_performance_check = datetime.now()
    
    async def check_model_updates(self):
        """Check if models need updating"""
        if self.adaptive_updater:
            try:
                # Get recent market data for model updates
                # This would need to be implemented based on your data sources
                current_data = await self.get_recent_market_data()
                await self.adaptive_updater.check_and_update_models(current_data)
            except Exception as e:
                logger.error(f"[AUTONOMOUS] Model update check failed: {e}")
    
    async def get_market_observation(self):
        """Get current market observation for RL agent"""
        # This would return market features for the RL agent
        # For now, return dummy observation
        return np.random.random(50).astype(np.float32)
    
    async def get_recent_market_data(self):
        """Get recent market data for model updates"""
        # This would fetch recent market data
        # For now, return dummy data
        import pandas as pd
        return pd.DataFrame()
    
    def is_trading_hours(self) -> bool:
        """Check if current time is within trading hours"""
        current_hour = datetime.now().hour
        start_hour = self.trading_hours['start']
        end_hour = self.trading_hours['end']
        
        if start_hour <= end_hour:
            return start_hour <= current_hour < end_hour
        else:  # Overnight trading
            return current_hour >= start_hour or current_hour < end_hour
    
    def calculate_adaptive_sleep_time(self) -> float:
        """Calculate adaptive sleep time based on market conditions"""
        base_sleep = self.config.get('base_delay', 60)
        
        # Adjust based on portfolio activity
        if self.portfolio_manager and len(self.portfolio_manager.positions) > 0:
            return base_sleep * 0.5  # More frequent checks with open positions
        
        return base_sleep
    
    async def display_status(self):
        """Display current autonomous trading status"""
        if self.cycle_count % 10 == 0:  # Display every 10 cycles
            portfolio_summary = self.portfolio_manager.get_portfolio_summary() if self.portfolio_manager else {}
            
            logger.info(f"[STATUS] Cycle #{self.cycle_count}")
            logger.info(f"[STATUS] Balance: ${portfolio_summary.get('balance', 0):.2f}")
            logger.info(f"[STATUS] Open Positions: {portfolio_summary.get('open_positions', 0)}")
            logger.info(f"[STATUS] Portfolio Risk: {portfolio_summary.get('portfolio_risk', 0):.1%}")
    
    async def emergency_shutdown(self):
        """Emergency shutdown procedure"""
        logger.critical("[AUTONOMOUS] Emergency shutdown initiated")
        
        # Close all positions
        if self.portfolio_manager:
            for symbol in list(self.portfolio_manager.positions.keys()):
                try:
                    current_price = self.portfolio_manager.positions[symbol].current_price
                    await self.portfolio_manager.close_position(symbol, current_price)
                    logger.info(f"[EMERGENCY] Closed position: {symbol}")
                except Exception as e:
                    logger.error(f"[EMERGENCY] Failed to close {symbol}: {e}")
        
        self.is_running = False
        logger.critical("[AUTONOMOUS] Emergency shutdown complete")
    
    def stop(self):
        """Gracefully stop the autonomous controller"""
        logger.info("[AUTONOMOUS] Stopping autonomous trading...")
        self.is_running = False
