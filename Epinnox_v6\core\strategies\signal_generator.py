#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Signal Generator Base Class
--------------------------
Base class for all signal generators with common functionality.
"""

import pandas as pd
import numpy as np
import logging
from abc import ABC, abstractmethod

logger = logging.getLogger('strategy.signal_generator')

class SignalGenerator(ABC):
    """
    Base class for all signal generators.
    
    This class defines the interface for signal generators and provides
    common functionality for calculating indicators and generating signals.
    """
    
    def __init__(self, config=None, name="BaseSignalGenerator"):
        """
        Initialize the signal generator.
        
        Args:
            config (dict): Configuration dictionary.
            name (str): Name of the signal generator.
        """
        self.config = config or {}
        self.name = name
        self.weight = 1.0  # Default weight for this signal generator
        
    @abstractmethod
    def generate_signal(self, df, **kwargs):
        """
        Generate a trading signal based on the input data.
        
        Args:
            df (pd.DataFrame): OHLCV DataFrame.
            **kwargs: Additional arguments.
            
        Returns:
            dict: Signal dictionary with at least 'score' and 'direction' keys.
        """
        pass
    
    def calculate_true_range(self, df):
        """
        Calculate True Range.

        Args:
            df (pd.DataFrame): OHLCV DataFrame.

        Returns:
            pd.Series: True Range series.
        """
        high = df['high']
        low = df['low']
        close_prev = df['close'].shift(1)

        # True Range calculation
        tr1 = high - low
        tr2 = (high - close_prev).abs()
        tr3 = (low - close_prev).abs()

        tr = pd.concat([tr1, tr2, tr3], axis=1).max(axis=1)
        return tr
    
    def calculate_rma(self, series, period):
        """
        Calculate Relative Moving Average (RMA) which is used in TradingView's ATR.

        Args:
            series (pd.Series): Input series.
            period (int): RMA period.

        Returns:
            pd.Series: RMA series.
        """
        alpha = 1.0 / period
        return series.ewm(alpha=alpha, adjust=False).mean()
    
    def calculate_atr(self, df, period):
        """
        Calculate Average True Range (ATR) using RMA.

        Args:
            df (pd.DataFrame): OHLCV DataFrame.
            period (int): ATR period.

        Returns:
            pd.Series: ATR series.
        """
        tr = self.calculate_true_range(df)
        atr = self.calculate_rma(tr, period)
        return atr
    
    def calculate_rsi(self, df, period=14):
        """
        Calculate Relative Strength Index (RSI).

        Args:
            df (pd.DataFrame): OHLCV DataFrame.
            period (int): RSI period.

        Returns:
            pd.Series: RSI series.
        """
        delta = df['close'].diff()
        gain = delta.where(delta > 0, 0)
        loss = -delta.where(delta < 0, 0)

        avg_gain = self.calculate_rma(gain, period)
        avg_loss = self.calculate_rma(loss, period)

        rs = avg_gain / avg_loss.replace(0, 1e-10)  # Avoid division by zero
        rsi = 100 - (100 / (1 + rs))

        return rsi
    
    def calculate_macd(self, df, fast_period=12, slow_period=26, signal_period=9):
        """
        Calculate Moving Average Convergence Divergence (MACD).

        Args:
            df (pd.DataFrame): OHLCV DataFrame.
            fast_period (int): Fast EMA period.
            slow_period (int): Slow EMA period.
            signal_period (int): Signal EMA period.

        Returns:
            dict: Dictionary containing MACD line, signal line, and histogram.
        """
        # Calculate fast and slow EMAs
        fast_ema = df['close'].ewm(span=fast_period, adjust=False).mean()
        slow_ema = df['close'].ewm(span=slow_period, adjust=False).mean()

        # Calculate MACD line
        macd_line = fast_ema - slow_ema

        # Calculate signal line
        signal_line = macd_line.ewm(span=signal_period, adjust=False).mean()

        # Calculate histogram
        histogram = macd_line - signal_line

        return {
            'macd': macd_line,
            'signal': signal_line,
            'histogram': histogram
        }
    
    def normalize_score(self, score):
        """
        Normalize a score to be between 0 and 1.
        
        Args:
            score (float): Raw score.
            
        Returns:
            float: Normalized score between 0 and 1.
        """
        return max(0.0, min(1.0, score))
