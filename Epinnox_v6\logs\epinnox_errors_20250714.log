2025-07-14 00:36:45,527 - websocket - ERROR - error from callback <bound method WebSocketClient._on_message of <data.websocket_client.WebSocketClient object at 0x00000221FFC71EB0>>: wrapped C/C++ object of type WebSocketClient has been deleted
2025-07-14 00:36:45,528 - websocket - ERROR - error from callback <bound method WebSocketClient._on_error of <data.websocket_client.WebSocketClient object at 0x00000221FFC71EB0>>: wrapped C/C++ object of type WebSocketClient has been deleted
2025-07-14 07:59:18,277 - core.llm_action_executors - ERROR - Failed to initialize limit order manager: 'NoneType' object has no attribute 'trading_engine'
2025-07-14 08:00:42,111 - core.llm_action_executors - ERROR - Failed to initialize limit order manager: 'NoneType' object has no attribute 'trading_engine'
2025-07-14 08:05:11,682 - core.llm_action_executors - ERROR - Failed to initialize limit order manager: 'NoneType' object has no attribute 'trading_engine'
2025-07-14 08:05:25,894 - websocket - ERROR - Handshake status 504 Gateway Time-out -+-+- {'content-type': 'text/html', 'content-length': '164', 'connection': 'close', 'server': 'openresty', 'date': 'Mon, 14 Jul 2025 13:05:24 GMT', 'x-request-id': 'ddeb9588c2bca131d49194882d970f27', 'x-cache': 'Error from cloudfront', 'via': '1.1 4de62e0f8bb36f486176ce5d831470b4.cloudfront.net (CloudFront)', 'x-amz-cf-pop': 'MAN51-P3', 'x-amz-cf-id': 'Kk1JIVHXVKjCX7ksbypNttm0nS3XNyAlKoM-u5nW8IEX2Ad06NOStg=='} -+-+- b'<html>\r\n<head><title>504 Gateway Time-out</title></head>\r\n<body>\r\n<center><h1>504 Gateway Time-out</h1></center>\r\n<hr><center>openresty</center>\r\n</body>\r\n</html>\r\n' - goodbye
2025-07-14 08:06:20,582 - core.llm_action_executors - ERROR - Failed to initialize limit order manager: 'NoneType' object has no attribute 'trading_engine'
2025-07-14 08:12:51,178 - core.llm_action_executors - ERROR - Failed to initialize limit order manager: 'NoneType' object has no attribute 'trading_engine'
2025-07-14 08:12:51,181 - core.llm_action_executors - ERROR - Trading engine attribute is missing. Please check the trading interface setup.
2025-07-14 08:12:52,006 - core.llm_action_executors - ERROR - Trading engine attribute is missing. Please check the trading interface setup.
2025-07-14 08:12:59,476 - websocket - ERROR - error from callback <bound method WebSocketClient._on_message of <data.websocket_client.WebSocketClient object at 0x0000016CA21E31F0>>: wrapped C/C++ object of type WebSocketClient has been deleted
2025-07-14 08:12:59,476 - websocket - ERROR - error from callback <bound method WebSocketClient._on_error of <data.websocket_client.WebSocketClient object at 0x0000016CA21E31F0>>: wrapped C/C++ object of type WebSocketClient has been deleted
2025-07-14 08:12:59,825 - websocket - ERROR - error from callback <bound method WebSocketClient._on_close of <data.websocket_client.WebSocketClient object at 0x0000016CA21E31F0>>: wrapped C/C++ object of type WebSocketClient has been deleted
2025-07-14 08:13:17,865 - core.llm_action_executors - ERROR - Failed to initialize limit order manager: 'NoneType' object has no attribute 'trading_engine'
2025-07-14 08:17:14,839 - core.llm_action_executors - ERROR - Failed to initialize limit order manager: 'NoneType' object has no attribute 'trading_engine'
2025-07-14 16:42:56,564 - websocket - ERROR - error from callback <bound method WebSocketClient._on_message of <data.websocket_client.WebSocketClient object at 0x0000023EA986C160>>: wrapped C/C++ object of type WebSocketClient has been deleted
2025-07-14 16:42:56,568 - websocket - ERROR - error from callback <bound method WebSocketClient._on_error of <data.websocket_client.WebSocketClient object at 0x0000023EA986C160>>: wrapped C/C++ object of type WebSocketClient has been deleted
2025-07-14 16:57:28,796 - websocket - ERROR - error from callback <bound method WebSocketClient._on_message of <data.websocket_client.WebSocketClient object at 0x0000016989381BE0>>: wrapped C/C++ object of type WebSocketClient has been deleted
2025-07-14 19:02:43,729 - llama.lmstudio_runner - ERROR - ❌ LMStudio API error: 404
2025-07-14 19:02:43,730 - llama.lmstudio_runner - ERROR - ❌ LMStudio API error: 404
2025-07-14 19:02:43,730 - llama.lmstudio_runner - ERROR - ❌ LMStudio API error: 404
2025-07-14 19:02:43,730 - llama.lmstudio_runner - ERROR - 📄 Error response: {
    "error": {
        "message": "Failed to load model \"phi-3.1-mini-128k-instruct\". Error: Model does not exist.",
        "type": "invalid_request_error",
        "param": "model",
        "code": "model_not_found"
    }
}...
2025-07-14 19:02:43,731 - llama.lmstudio_runner - ERROR - 📄 Error response: {
    "error": {
        "message": "Failed to load model \"phi-3.1-mini-128k-instruct\". Error: Model does not exist.",
        "type": "invalid_request_error",
        "param": "model",
        "code": "model_not_found"
    }
}...
2025-07-14 19:02:43,731 - llama.lmstudio_runner - ERROR - 📄 Error response: {
    "error": {
        "message": "Failed to load model \"phi-3.1-mini-128k-instruct\". Error: Model does not exist.",
        "type": "invalid_request_error",
        "param": "model",
        "code": "model_not_found"
    }
}...
2025-07-14 19:02:43,734 - llama.lmstudio_runner - ERROR - ❌ LMStudio API error: 404
2025-07-14 19:02:43,736 - llama.lmstudio_runner - ERROR - 📄 Error response: {
    "error": {
        "message": "Failed to load model \"phi-3.1-mini-128k-instruct\". Error: Model does not exist.",
        "type": "invalid_request_error",
        "param": "model",
        "code": "model_not_found"
    }
}...
2025-07-14 21:05:15,114 - websocket - ERROR - Connection to remote host was lost. - goodbye
2025-07-14 21:55:58,532 - websocket - ERROR - Connection to remote host was lost. - goodbye
