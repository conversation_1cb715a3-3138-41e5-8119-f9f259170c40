#!/usr/bin/env python3
"""
RESTART AUTONOMOUS TRADING
Restart the autonomous trading system with proper async handling
"""

import sys
import os
import asyncio
import logging
from datetime import datetime

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Configure Unicode-safe logging
try:
    from core.unicode_safe_logging import configure_unicode_safe_logging
    configure_unicode_safe_logging()
except ImportError:
    pass

logger = logging.getLogger(__name__)

async def restart_autonomous_trading():
    """Restart autonomous trading with proper async handling"""
    
    print("🔄 RESTARTING AUTONOMOUS TRADING SYSTEM")
    print("=" * 50)
    
    try:
        # 1. Initialize core components
        print("\n1. INITIALIZING CORE COMPONENTS")
        print("-" * 35)
        
        from core.autonomous_trading_orchestrator import AutonomousTradingOrchestrator, TradingMode
        from core.dynamic_risk_manager import dynamic_risk_manager, RiskLevel
        from core.emergency_stop_coordinator import emergency_coordinator
        from validation.live_trading_validator import live_trading_validator
        
        # Set ultra-conservative risk level
        dynamic_risk_manager.set_risk_level(
            RiskLevel.ULTRA_CONSERVATIVE,
            "Autonomous trading restart"
        )
        
        print("   ✅ Core components initialized")
        
        # 2. Create live trading orchestrator
        print("\n2. CREATING LIVE TRADING ORCHESTRATOR")
        print("-" * 35)
        
        live_config = {
            'trading_mode': TradingMode.LIVE,
            'max_trading_capital': 100.0,
            'position_size_pct': 1.0,
            'portfolio_risk_pct': 2.0,
            'validation_mode': True
        }
        
        orchestrator = AutonomousTradingOrchestrator(TradingMode.LIVE, live_config)
        
        # Initialize orchestrator
        await orchestrator.initialize()
        
        print("   ✅ Live trading orchestrator created and initialized")
        
        # 3. Register orchestrator with validator
        print("\n3. REGISTERING WITH VALIDATOR")
        print("-" * 35)
        
        live_trading_validator.register_orchestrator(orchestrator)
        live_trading_validator.register_dynamic_risk_manager(dynamic_risk_manager)
        live_trading_validator.register_emergency_coordinator(emergency_coordinator)
        
        print("   ✅ Components registered with validator")
        
        # 4. Start autonomous trading
        print("\n4. STARTING AUTONOMOUS TRADING")
        print("-" * 35)
        
        # Start autonomous trading with proper await
        success = await orchestrator.start_autonomous_trading()
        
        if success:
            print("   ✅ Autonomous trading started successfully")
            
            # Mark validation as active
            live_trading_validator.validation_active = True
            live_trading_validator.validation_start_time = datetime.now()
            
            print("   ✅ Live validation marked as active")
        else:
            print("   ❌ Failed to start autonomous trading")
            return False
        
        # 5. Start timer coordination
        print("\n5. STARTING TIMER COORDINATION")
        print("-" * 35)
        
        try:
            from core.timer_coordinator import timer_coordinator
            
            # Start timer coordinator
            if hasattr(timer_coordinator, 'start') and callable(timer_coordinator.start):
                await timer_coordinator.start()
                print("   ✅ Timer coordinator started")
            else:
                print("   ⚠️ Timer coordinator start method not available")
                
        except Exception as e:
            print(f"   ⚠️ Timer coordinator issue: {e}")
        
        # 6. Verify autonomous trading is running
        print("\n6. VERIFYING AUTONOMOUS TRADING STATUS")
        print("-" * 35)
        
        # Check orchestrator status
        if hasattr(orchestrator, 'autonomous_trading_active'):
            if orchestrator.autonomous_trading_active:
                print("   ✅ Autonomous trading: ACTIVE")
            else:
                print("   ❌ Autonomous trading: NOT ACTIVE")
        
        # Check LLM decision loop
        if hasattr(orchestrator, 'llm_decision_loop_active'):
            if orchestrator.llm_decision_loop_active:
                print("   ✅ LLM decision loop: ACTIVE")
            else:
                print("   ❌ LLM decision loop: NOT ACTIVE")
        
        # Check validation status
        if live_trading_validator.validation_active:
            print("   ✅ Live validation: ACTIVE")
        else:
            print("   ❌ Live validation: NOT ACTIVE")
        
        return True
        
    except Exception as e:
        logger.error(f"Error restarting autonomous trading: {e}")
        print(f"❌ Error: {e}")
        return False

async def monitor_trading_activity():
    """Monitor trading activity for a short period"""
    
    print("\n7. MONITORING TRADING ACTIVITY")
    print("-" * 35)
    
    try:
        from validation.live_trading_validator import live_trading_validator
        
        # Monitor for 30 seconds
        for i in range(6):
            await asyncio.sleep(5)
            
            orchestrator = live_trading_validator.orchestrator
            if orchestrator:
                # Check for any trading activity
                if hasattr(orchestrator, 'get_performance_metrics'):
                    metrics = orchestrator.get_performance_metrics()
                    if metrics:
                        trades = metrics.get('total_trades', 0)
                        print(f"   📊 Trades executed: {trades}")
                
                # Check active positions
                if hasattr(orchestrator, 'get_active_positions'):
                    positions = orchestrator.get_active_positions()
                    print(f"   📊 Active positions: {len(positions) if positions else 0}")
            
            print(f"   ⏱️ Monitoring... {(i+1)*5}s")
        
        print("   ✅ Monitoring completed")
        
    except Exception as e:
        print(f"   ❌ Monitoring error: {e}")

async def main():
    """Main restart function"""
    
    try:
        print("🚨 AUTONOMOUS TRADING RESTART SEQUENCE")
        print("⚠️ This will restart live trading with real money")
        print("=" * 60)
        
        # Restart autonomous trading
        success = await restart_autonomous_trading()
        
        if success:
            # Monitor trading activity
            await monitor_trading_activity()
            
            print("\n" + "=" * 60)
            print("RESTART SUMMARY")
            print("=" * 60)
            print("✅ AUTONOMOUS TRADING RESTARTED SUCCESSFULLY")
            print("✅ Live validation active")
            print("✅ LLM decision-making enabled")
            print("✅ Ultra-conservative settings maintained")
            print("✅ Real money trading with $116.59 USDT")
            
            print("\n🎯 WHAT'S HAPPENING NOW:")
            print("• LLM analyzing markets every 30 seconds")
            print("• ScalperGPT providing high-frequency analysis")
            print("• LIMIT orders only for safety")
            print("• Emergency stops ready")
            print("• GUI monitoring active")
            
            print("\n📊 MONITOR THROUGH:")
            print("• GUI dashboard for real-time status")
            print("• Console logs for decision-making")
            print("• HTX exchange for actual trades")
            
            return 0
        else:
            print("\n❌ AUTONOMOUS TRADING RESTART FAILED")
            print("🔧 Check logs for specific errors")
            return 1
        
    except Exception as e:
        print(f"\n❌ RESTART ERROR: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
