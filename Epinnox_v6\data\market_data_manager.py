"""
Market Data Manager with Robust WebSocket Connections
Provides reliable real-time market data with automatic failover
"""

import asyncio
import logging
import time
from typing import Dict, Optional, List, Callable
from datetime import datetime, timedelta
from dataclasses import dataclass
import json

from core.websocket_manager import WebSocketManager

logger = logging.getLogger(__name__)

@dataclass
class MarketData:
    """Market data structure"""
    symbol: str
    price: float
    bid: float
    ask: float
    volume: float
    timestamp: float
    source: str

class MarketDataManager:
    """
    Manages real-time market data with multiple WebSocket connections
    and automatic failover for maximum reliability
    """
    
    def __init__(self, exchange: str = "htx"):
        """
        Initialize market data manager
        
        Args:
            exchange: Exchange name (htx, binance, etc.)
        """
        self.exchange = exchange
        self.websocket_managers = {}
        self.market_data = {}
        self.subscriptions = set()
        
        # Data quality monitoring
        self.last_update_times = {}
        self.update_counts = {}
        self.error_counts = {}
        
        # Failover settings
        self.max_data_age_seconds = 30  # Consider data stale after 30 seconds
        self.health_check_interval = 60  # Check health every minute
        
        # Callbacks
        self.data_callbacks = []
        self.error_callbacks = []
        
        # Control flags
        self.is_running = False
        
        # Initialize WebSocket endpoints based on exchange
        self._initialize_websocket_endpoints()
        
        logger.info(f"Market data manager initialized for {exchange}")
    
    def _initialize_websocket_endpoints(self):
        """Initialize WebSocket endpoints for the exchange"""
        if self.exchange.lower() == "htx":
            # HTX WebSocket endpoints
            endpoints = {
                'market': 'wss://api.huobi.pro/ws',
                'futures': 'wss://api.hbdm.com/swap-ws',
                'backup': 'wss://api-aws.huobi.pro/ws'
            }
        elif self.exchange.lower() == "binance":
            # Binance WebSocket endpoints
            endpoints = {
                'market': 'wss://stream.binance.com:9443/ws',
                'futures': 'wss://fstream.binance.com/ws',
                'backup': 'wss://stream.binance.com:443/ws'
            }
        else:
            # Default/fallback endpoints
            endpoints = {
                'market': f'wss://api.{self.exchange}.com/ws',
                'backup': f'wss://api-backup.{self.exchange}.com/ws'
            }
        
        # Create WebSocket managers for each endpoint
        for name, url in endpoints.items():
            ws_manager = WebSocketManager(url, f"{self.exchange}_{name}")
            
            # Add message handlers
            ws_manager.add_message_handler('default', self._handle_market_message)
            ws_manager.add_connection_handler(self._on_connection)
            ws_manager.add_disconnection_handler(self._on_disconnection)
            ws_manager.add_error_handler(self._on_error)
            
            self.websocket_managers[name] = ws_manager
        
        logger.info(f"Initialized {len(self.websocket_managers)} WebSocket endpoints")
    
    async def start(self):
        """Start market data manager"""
        if self.is_running:
            logger.warning("Market data manager already running")
            return
        
        self.is_running = True
        logger.info("Starting market data manager...")
        
        # Start all WebSocket connections
        connection_tasks = []
        for name, ws_manager in self.websocket_managers.items():
            task = asyncio.create_task(ws_manager.listen())
            connection_tasks.append(task)
            logger.info(f"Started WebSocket connection: {name}")
        
        # Start health monitoring
        health_task = asyncio.create_task(self._health_monitor())
        
        # Wait for at least one connection to succeed
        await asyncio.sleep(2)
        
        # Check if any connections are established
        connected_count = sum(1 for ws in self.websocket_managers.values() 
                             if ws.state.value == "connected")
        
        if connected_count == 0:
            logger.error("Failed to establish any WebSocket connections")
            raise Exception("No WebSocket connections available")
        
        logger.info(f"✅ Market data manager started with {connected_count} active connections")
    
    async def stop(self):
        """Stop market data manager"""
        logger.info("Stopping market data manager...")
        
        self.is_running = False
        
        # Disconnect all WebSocket connections
        disconnect_tasks = []
        for ws_manager in self.websocket_managers.values():
            task = asyncio.create_task(ws_manager.disconnect())
            disconnect_tasks.append(task)
        
        # Wait for all disconnections
        await asyncio.gather(*disconnect_tasks, return_exceptions=True)
        
        logger.info("✅ Market data manager stopped")
    
    async def subscribe_symbol(self, symbol: str) -> bool:
        """
        Subscribe to market data for a symbol
        
        Args:
            symbol: Trading symbol (e.g., 'BTC/USDT:USDT')
            
        Returns:
            bool: True if subscription successful
        """
        if symbol in self.subscriptions:
            logger.debug(f"Already subscribed to {symbol}")
            return True
        
        # Convert symbol to exchange format
        exchange_symbol = self._convert_symbol_format(symbol)
        
        # Create subscription message based on exchange
        if self.exchange.lower() == "htx":
            sub_message = {
                "sub": f"market.{exchange_symbol}.ticker",
                "id": f"ticker_{exchange_symbol}"
            }
        elif self.exchange.lower() == "binance":
            sub_message = {
                "method": "SUBSCRIBE",
                "params": [f"{exchange_symbol.lower()}@ticker"],
                "id": int(time.time())
            }
        else:
            # Generic format
            sub_message = {
                "subscribe": f"{exchange_symbol}_ticker"
            }
        
        # Send subscription to all connected WebSockets
        success_count = 0
        for name, ws_manager in self.websocket_managers.items():
            if await ws_manager.send_message(sub_message):
                success_count += 1
                logger.debug(f"Sent subscription for {symbol} to {name}")
        
        if success_count > 0:
            self.subscriptions.add(symbol)
            self.last_update_times[symbol] = time.time()
            self.update_counts[symbol] = 0
            self.error_counts[symbol] = 0
            
            logger.info(f"✅ Subscribed to {symbol} on {success_count} connections")
            return True
        else:
            logger.error(f"❌ Failed to subscribe to {symbol}")
            return False
    
    async def unsubscribe_symbol(self, symbol: str) -> bool:
        """
        Unsubscribe from market data for a symbol
        
        Args:
            symbol: Trading symbol
            
        Returns:
            bool: True if unsubscription successful
        """
        if symbol not in self.subscriptions:
            logger.debug(f"Not subscribed to {symbol}")
            return True
        
        exchange_symbol = self._convert_symbol_format(symbol)
        
        # Create unsubscription message
        if self.exchange.lower() == "htx":
            unsub_message = {
                "unsub": f"market.{exchange_symbol}.ticker",
                "id": f"unsub_ticker_{exchange_symbol}"
            }
        elif self.exchange.lower() == "binance":
            unsub_message = {
                "method": "UNSUBSCRIBE",
                "params": [f"{exchange_symbol.lower()}@ticker"],
                "id": int(time.time())
            }
        else:
            unsub_message = {
                "unsubscribe": f"{exchange_symbol}_ticker"
            }
        
        # Send unsubscription to all WebSockets
        for ws_manager in self.websocket_managers.values():
            await ws_manager.send_message(unsub_message)
        
        # Remove from tracking
        self.subscriptions.discard(symbol)
        if symbol in self.market_data:
            del self.market_data[symbol]
        if symbol in self.last_update_times:
            del self.last_update_times[symbol]
        
        logger.info(f"✅ Unsubscribed from {symbol}")
        return True
    
    def _convert_symbol_format(self, symbol: str) -> str:
        """Convert symbol to exchange-specific format"""
        if self.exchange.lower() == "htx":
            # HTX format: btcusdt
            if ':' in symbol:
                base_symbol = symbol.split(':')[0]
                return base_symbol.replace('/', '').lower()
            else:
                return symbol.replace('/', '').lower()
        elif self.exchange.lower() == "binance":
            # Binance format: BTCUSDT
            if ':' in symbol:
                base_symbol = symbol.split(':')[0]
                return base_symbol.replace('/', '').upper()
            else:
                return symbol.replace('/', '').upper()
        else:
            # Generic format
            return symbol.replace('/', '_')
    
    async def _handle_market_message(self, data: Dict):
        """Handle incoming market data message"""
        try:
            # Parse message based on exchange format
            if self.exchange.lower() == "htx":
                await self._parse_htx_message(data)
            elif self.exchange.lower() == "binance":
                await self._parse_binance_message(data)
            else:
                await self._parse_generic_message(data)
                
        except Exception as e:
            logger.error(f"Error handling market message: {e}")
    
    async def _parse_htx_message(self, data: Dict):
        """Parse HTX market data message"""
        if 'ch' in data and 'tick' in data:
            channel = data['ch']
            tick = data['tick']
            
            # Extract symbol from channel
            if 'market.' in channel and '.ticker' in channel:
                exchange_symbol = channel.split('.')[1]
                symbol = self._convert_from_exchange_format(exchange_symbol)
                
                # Create market data
                market_data = MarketData(
                    symbol=symbol,
                    price=float(tick.get('close', 0)),
                    bid=float(tick.get('bid', 0)),
                    ask=float(tick.get('ask', 0)),
                    volume=float(tick.get('vol', 0)),
                    timestamp=time.time(),
                    source=f"{self.exchange}_websocket"
                )
                
                await self._update_market_data(symbol, market_data)
    
    async def _parse_binance_message(self, data: Dict):
        """Parse Binance market data message"""
        if 'stream' in data and 'data' in data:
            stream = data['stream']
            tick = data['data']
            
            if '@ticker' in stream:
                symbol = stream.split('@')[0].upper()
                symbol = self._convert_from_exchange_format(symbol)
                
                market_data = MarketData(
                    symbol=symbol,
                    price=float(tick.get('c', 0)),  # Close price
                    bid=float(tick.get('b', 0)),    # Best bid
                    ask=float(tick.get('a', 0)),    # Best ask
                    volume=float(tick.get('v', 0)), # Volume
                    timestamp=time.time(),
                    source=f"{self.exchange}_websocket"
                )
                
                await self._update_market_data(symbol, market_data)
    
    async def _parse_generic_message(self, data: Dict):
        """Parse generic market data message"""
        # Implement generic parsing logic
        pass
    
    def _convert_from_exchange_format(self, exchange_symbol: str) -> str:
        """Convert from exchange format back to standard format"""
        if self.exchange.lower() == "htx":
            # Convert btcusdt to BTC/USDT:USDT
            if exchange_symbol.endswith('usdt'):
                base = exchange_symbol[:-4].upper()
                return f"{base}/USDT:USDT"
        elif self.exchange.lower() == "binance":
            # Convert BTCUSDT to BTC/USDT:USDT
            if exchange_symbol.endswith('USDT'):
                base = exchange_symbol[:-4]
                return f"{base}/USDT:USDT"
        
        return exchange_symbol
    
    async def _update_market_data(self, symbol: str, data: MarketData):
        """Update market data and notify callbacks"""
        self.market_data[symbol] = data
        self.last_update_times[symbol] = time.time()
        self.update_counts[symbol] = self.update_counts.get(symbol, 0) + 1
        
        # Notify callbacks
        for callback in self.data_callbacks:
            try:
                await callback(symbol, data)
            except Exception as e:
                logger.error(f"Error in data callback: {e}")
    
    async def _on_connection(self):
        """Handle WebSocket connection event"""
        logger.debug("WebSocket connected")
    
    async def _on_disconnection(self):
        """Handle WebSocket disconnection event"""
        logger.warning("WebSocket disconnected")
    
    async def _on_error(self, error):
        """Handle WebSocket error event"""
        logger.error(f"WebSocket error: {error}")
    
    async def _health_monitor(self):
        """Monitor connection health and data quality"""
        while self.is_running:
            try:
                await asyncio.sleep(self.health_check_interval)
                
                # Check data freshness
                current_time = time.time()
                stale_symbols = []
                
                for symbol, last_update in self.last_update_times.items():
                    age = current_time - last_update
                    if age > self.max_data_age_seconds:
                        stale_symbols.append(symbol)
                
                if stale_symbols:
                    logger.warning(f"Stale data detected for symbols: {stale_symbols}")
                
                # Check WebSocket health
                healthy_connections = 0
                for name, ws_manager in self.websocket_managers.items():
                    if await ws_manager.health_check():
                        healthy_connections += 1
                    else:
                        logger.warning(f"Unhealthy WebSocket connection: {name}")
                
                logger.debug(f"Health check: {healthy_connections}/{len(self.websocket_managers)} connections healthy")
                
            except Exception as e:
                logger.error(f"Error in health monitor: {e}")
    
    def get_market_data(self, symbol: str) -> Optional[MarketData]:
        """Get latest market data for symbol"""
        return self.market_data.get(symbol)
    
    def get_connection_stats(self) -> Dict:
        """Get connection statistics"""
        stats = {}
        for name, ws_manager in self.websocket_managers.items():
            stats[name] = ws_manager.get_connection_stats()
        
        return {
            'exchange': self.exchange,
            'subscriptions': len(self.subscriptions),
            'symbols': list(self.subscriptions),
            'connections': stats,
            'total_updates': sum(self.update_counts.values()),
            'is_healthy': self.is_healthy()
        }
    
    def is_healthy(self) -> bool:
        """Check if market data manager is healthy"""
        if not self.is_running:
            return False
        
        # Check if at least one connection is healthy
        healthy_connections = sum(1 for ws in self.websocket_managers.values() 
                                 if ws.is_healthy())
        
        return healthy_connections > 0
    
    def add_data_callback(self, callback: Callable):
        """Add callback for market data updates"""
        self.data_callbacks.append(callback)
    
    def add_error_callback(self, callback: Callable):
        """Add callback for error events"""
        self.error_callbacks.append(callback)
