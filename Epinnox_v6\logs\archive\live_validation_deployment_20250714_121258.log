2025-07-14 12:12:58,530 - INFO - __main__ - Live Validation Deployment initialized
2025-07-14 12:12:58,566 - INFO - validation.live_trading_validator - Live Trading Validator initialized
2025-07-14 12:13:02,972 - INFO - core.timer_coordinator - [TIMER_COORDINATOR] Initialized unified timer coordinator
2025-07-14 12:13:02,980 - INFO - core.emergency_stop_coordinator - Emergency Stop Coordinator initialized
2025-07-14 12:13:02,985 - INFO - core.dynamic_risk_manager - Dynamic Risk Manager initialized
2025-07-14 12:13:02,985 - INFO - core.autonomous_trading_orchestrator - Autonomous Trading Orchestrator initialized in paper mode
2025-07-14 12:13:02,986 - INFO - validation.live_trading_validator - Orchestrator registered with live trading validator
2025-07-14 12:13:02,987 - INFO - validation.live_trading_validator - Dynamic risk manager registered with live trading validator
2025-07-14 12:13:02,987 - INFO - validation.live_trading_validator - Emergency coordinator registered with live trading validator
2025-07-14 12:13:02,987 - INFO - validation.live_trading_validator - Running pre-deployment validation...
2025-07-14 12:13:02,990 - INFO - core.dynamic_risk_manager - Registered parameter change callback: on_risk_parameters_changed
2025-07-14 12:13:02,990 - INFO - core.dynamic_risk_integration - Dynamic Risk Integration initialized
2025-07-14 12:13:02,991 - INFO - core.dynamic_risk_integration - Orchestrator registered with dynamic risk integration
2025-07-14 12:13:02,991 - INFO - core.dynamic_risk_integration - Dynamic risk manager registered with dynamic risk integration
2025-07-14 12:13:02,991 - INFO - core.dynamic_risk_integration - Emergency coordinator registered with dynamic risk integration
2025-07-14 12:13:02,991 - INFO - validation.live_trading_validator - System integration validation passed
2025-07-14 12:13:02,991 - INFO - validation.live_trading_validator - Risk management validation passed
2025-07-14 12:13:02,991 - INFO - core.emergency_stop_coordinator - Testing emergency systems...
2025-07-14 12:13:02,992 - INFO - core.emergency_stop_coordinator - Emergency systems test completed successfully
2025-07-14 12:13:02,992 - INFO - core.unified_execution_engine - Unified Execution Engine initialized with LIMIT orders enforcement
2025-07-14 12:13:02,993 - INFO - core.unified_execution_engine - Unified Execution Engine initialized with LIMIT orders enforcement
2025-07-14 12:13:02,993 - INFO - validation.live_trading_validator - Safety systems validation passed
2025-07-14 12:13:02,999 - INFO - validation.live_trading_validator - Quality thresholds validation passed
2025-07-14 12:13:03,005 - INFO - validation.live_trading_validator - Credentials validation passed
2025-07-14 12:13:11,320 - INFO - validation.live_trading_validator - Exchange connectivity validation passed (Balance: $116.59)
2025-07-14 12:13:11,325 - INFO - core.dynamic_risk_integration - Updating all systems with new risk parameters
2025-07-14 12:13:11,325 - INFO - core.dynamic_risk_integration - Orchestrator attributes updated
2025-07-14 12:13:11,326 - INFO - core.dynamic_risk_integration - Individual timer intervals updated
2025-07-14 12:13:11,326 - INFO - core.dynamic_risk_integration - All systems updated with new risk parameters
2025-07-14 12:13:11,326 - INFO - core.dynamic_risk_manager - Risk level changed from Ultra-Conservative to Ultra-Conservative: Live validation deployment
2025-07-14 12:13:36,337 - INFO - core.autonomous_trading_orchestrator - Autonomous Trading Orchestrator initialized in live mode
2025-07-14 12:13:36,337 - INFO - core.autonomous_trading_orchestrator - Initializing autonomous trading system...
2025-07-14 12:13:36,337 - INFO - core.error_recovery_system - [ERROR_RECOVERY] Error Recovery System initialized
2025-07-14 12:13:36,337 - INFO - core.error_recovery_system - [ERROR_RECOVERY] Health monitoring started
2025-07-14 12:13:36,337 - INFO - core.error_recovery_system - [ERROR_RECOVERY] Error Recovery System ready
2025-07-14 12:13:36,377 - ERROR - core.autonomous_trading_orchestrator - Failed to initialize data manager: No module named 'core.websocket_manager'
2025-07-14 12:13:36,377 - ERROR - core.autonomous_trading_orchestrator - Failed to initialize autonomous trading system: No module named 'core.websocket_manager'
2025-07-14 12:13:36,378 - INFO - validation.live_trading_validator - Orchestrator registered with live trading validator
2025-07-14 12:13:37,639 - INFO - core.dynamic_risk_manager - Registered parameter change callback: on_parameters_changed
2025-07-14 12:13:37,640 - INFO - gui.dynamic_risk_control_widget - Dynamic Risk Control Widget initialized
2025-07-14 12:13:37,664 - INFO - gui.live_validation_monitor - Live Validation Monitor initialized
2025-07-14 12:13:37,681 - INFO - gui.integrated_monitoring_dashboard - Integrated Monitoring Dashboard initialized
2025-07-14 12:13:37,681 - INFO - gui.integrated_monitoring_dashboard - Orchestrator connected to monitoring dashboard
