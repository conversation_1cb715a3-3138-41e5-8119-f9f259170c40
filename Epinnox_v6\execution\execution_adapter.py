"""
Execution Adapter for Epinnox Trading System
Provides backward compatibility while using the unified execution engine
"""

import asyncio
import logging
from typing import Dict, Optional, Any, List
from datetime import datetime

from .unified_execution_engine import UnifiedExecutionEngine, ExecutionMode, TradingDecision
from config.autonomous_trading_rules import AutonomousTradingRules
from core.websocket_stability_manager import WebSocketStabilityManager, ConnectionState

logger = logging.getLogger(__name__)

class ExecutionAdapter:
    """
    Adapter that provides backward compatibility for existing autonomous trading components
    while using the new unified execution engine under the hood
    """
    
    def __init__(self, mode: str = "paper", exchange_config: Optional[Dict] = None, 
                 initial_balance: float = 10000.0):
        """
        Initialize execution adapter
        
        Args:
            mode: Trading mode ('live', 'paper', 'simulation')
            exchange_config: Exchange configuration for live trading
            initial_balance: Initial balance for paper/simulation
        """
        # Convert mode string to enum
        mode_map = {
            'live': ExecutionMode.LIVE,
            'paper': ExecutionMode.PAPER,
            'simulation': ExecutionMode.SIMULATION
        }
        
        execution_mode = mode_map.get(mode.lower(), ExecutionMode.PAPER)
        
        # Initialize unified execution engine
        self.engine = UnifiedExecutionEngine(
            mode=execution_mode,
            exchange_config=exchange_config,
            initial_balance=initial_balance
        )
        
        self.mode = mode
        self.is_initialized = False

        # CRITICAL: WebSocket stability management for live trading
        self.websocket_manager = None
        self.websocket_health_check_interval = 30.0  # seconds
        self.last_websocket_health_check = None

        # WebSocket endpoints for different exchanges
        self.websocket_endpoints = {
            'htx': {
                'primary': 'wss://api.huobi.pro/ws',
                'backup': ['wss://api-aws.huobi.pro/ws', 'wss://api.hbdm.com/ws']
            },
            'binance': {
                'primary': 'wss://stream.binance.com:9443/ws',
                'backup': ['wss://stream.binance.com:443/ws', 'wss://fstream.binance.com/ws']
            }
        }
        
        logger.info(f"Execution adapter initialized in {mode} mode")
    
    async def initialize(self) -> bool:
        """Initialize the execution adapter with WebSocket stability"""
        try:
            # Initialize the execution engine
            success = await self.engine.initialize()
            if not success:
                return False

            # CRITICAL: Initialize WebSocket stability for live trading
            if self.mode == 'live':
                await self._initialize_websocket_stability()

            self.is_initialized = True
            logger.info(f"✅ Execution adapter initialized in {self.mode} mode")
            return True

        except Exception as e:
            logger.error(f"Failed to initialize execution adapter: {e}")
            return False

    async def _initialize_websocket_stability(self):
        """Initialize WebSocket stability manager for live trading"""
        try:
            # Determine exchange from config
            exchange_config = getattr(self.engine, 'exchange_config', {})
            exchange_name = exchange_config.get('exchange', 'htx').lower()

            if exchange_name not in self.websocket_endpoints:
                logger.warning(f"No WebSocket endpoints configured for {exchange_name}")
                return

            endpoints = self.websocket_endpoints[exchange_name]
            primary_url = endpoints['primary']
            backup_urls = endpoints['backup']

            # Create WebSocket stability manager
            self.websocket_manager = WebSocketStabilityManager(
                primary_url=primary_url,
                backup_urls=backup_urls
            )

            # Add message handlers
            self.websocket_manager.add_message_handler('ticker', self._handle_ticker_message)
            self.websocket_manager.add_message_handler('orderbook', self._handle_orderbook_message)
            self.websocket_manager.add_message_handler('trade', self._handle_trade_message)
            self.websocket_manager.add_message_handler('default', self._handle_default_message)

            # Connect to WebSocket
            connection_success = await self.websocket_manager.connect()

            if connection_success:
                logger.info("✅ WebSocket stability manager initialized and connected")

                # Start health monitoring
                asyncio.create_task(self._websocket_health_monitor())

                # Subscribe to essential data streams
                await self._subscribe_to_essential_streams()
            else:
                logger.error("❌ Failed to establish WebSocket connection")

        except Exception as e:
            logger.error(f"Failed to initialize WebSocket stability: {e}")

    async def _subscribe_to_essential_streams(self):
        """Subscribe to essential data streams for trading"""
        try:
            # Get active symbols from engine config
            active_symbols = getattr(self.engine, 'active_symbols', ['DOGE/USDT', 'BTC/USDT'])

            for symbol in active_symbols:
                # Convert symbol format for exchange
                exchange_symbol = self._convert_symbol_for_websocket(symbol)

                # Subscribe to ticker updates
                ticker_sub = {
                    "sub": f"market.{exchange_symbol}.ticker",
                    "id": f"ticker_{exchange_symbol}"
                }
                await self.websocket_manager.subscribe(ticker_sub)

                # Subscribe to orderbook updates
                orderbook_sub = {
                    "sub": f"market.{exchange_symbol}.depth.step0",
                    "id": f"depth_{exchange_symbol}"
                }
                await self.websocket_manager.subscribe(orderbook_sub)

                logger.info(f"✅ Subscribed to WebSocket streams for {symbol}")

        except Exception as e:
            logger.error(f"Failed to subscribe to essential streams: {e}")

    def _convert_symbol_for_websocket(self, symbol: str) -> str:
        """Convert trading symbol to WebSocket format"""
        try:
            # Convert DOGE/USDT:USDT to dogeusdt format for HTX
            if ':' in symbol:
                symbol = symbol.split(':')[0]  # Remove futures suffix

            return symbol.replace('/', '').lower()

        except Exception as e:
            logger.error(f"Error converting symbol {symbol}: {e}")
            return symbol.lower()

    async def _websocket_health_monitor(self):
        """Monitor WebSocket connection health continuously"""
        logger.info("🔍 Starting WebSocket health monitoring")

        while self.is_initialized and self.websocket_manager:
            try:
                # Get connection metrics
                metrics = self.websocket_manager.get_connection_metrics()

                # Log health status periodically
                if metrics['state'] == ConnectionState.CONNECTED.value:
                    logger.debug(f"📡 WebSocket healthy: {metrics['message_count']} messages, "
                               f"{metrics['latency_ms']:.1f}ms latency, "
                               f"{metrics['success_rate']:.1%} success rate")
                else:
                    logger.warning(f"⚠️ WebSocket unhealthy: state={metrics['state']}, "
                                 f"errors={metrics['error_count']}, "
                                 f"reconnections={metrics['reconnections']}")

                # Check for critical connection issues
                if metrics['error_count'] > 20:
                    logger.error(f"🚨 High WebSocket error count: {metrics['error_count']} - may need intervention")

                if metrics['latency_ms'] > 10000:  # 10 seconds
                    logger.warning(f"🐌 High WebSocket latency: {metrics['latency_ms']:.1f}ms")

                if metrics['success_rate'] < 0.8:  # Less than 80% success rate
                    logger.warning(f"📉 Low WebSocket success rate: {metrics['success_rate']:.1%}")

                # Update last health check time
                self.last_websocket_health_check = asyncio.get_event_loop().time()

                # Wait before next health check
                await asyncio.sleep(self.websocket_health_check_interval)

            except Exception as e:
                logger.error(f"❌ WebSocket health monitor error: {e}")
                await asyncio.sleep(self.websocket_health_check_interval)

        logger.info("🔍 WebSocket health monitoring stopped")

    async def _handle_ticker_message(self, data: Dict[str, Any]):
        """Handle ticker WebSocket messages for price updates"""
        try:
            # HTX ticker format: {"ch": "market.dogeusdt.ticker", "ts": ..., "tick": {...}}
            if 'ch' in data and 'tick' in data:
                channel = data['ch']
                tick_data = data['tick']

                # Extract symbol from channel
                if 'market.' in channel and '.ticker' in channel:
                    symbol_part = channel.split('.')[1]

                    # Extract price information
                    price_info = {
                        'symbol': symbol_part,
                        'last_price': tick_data.get('close'),
                        'bid': tick_data.get('bid'),
                        'ask': tick_data.get('ask'),
                        'volume': tick_data.get('vol'),
                        'timestamp': data.get('ts')
                    }

                    logger.debug(f"📈 Ticker update for {symbol_part}: ${price_info['last_price']}")

                    # Store price data for execution engine
                    if hasattr(self.engine, 'update_market_price'):
                        await self.engine.update_market_price(symbol_part, price_info)

        except Exception as e:
            logger.error(f"❌ Error handling ticker message: {e}")

    async def _handle_orderbook_message(self, data: Dict[str, Any]):
        """Handle orderbook WebSocket messages for spread analysis"""
        try:
            # HTX orderbook format: {"ch": "market.dogeusdt.depth.step0", "ts": ..., "tick": {...}}
            if 'ch' in data and 'tick' in data:
                channel = data['ch']
                tick_data = data['tick']

                # Extract symbol from channel
                if 'market.' in channel and '.depth.' in channel:
                    symbol_part = channel.split('.')[1]

                    # Extract orderbook information
                    orderbook_info = {
                        'symbol': symbol_part,
                        'bids': tick_data.get('bids', []),
                        'asks': tick_data.get('asks', []),
                        'timestamp': data.get('ts')
                    }

                    # Calculate spread
                    if orderbook_info['bids'] and orderbook_info['asks']:
                        best_bid = float(orderbook_info['bids'][0][0])
                        best_ask = float(orderbook_info['asks'][0][0])
                        spread = best_ask - best_bid
                        spread_pct = (spread / best_ask) * 100

                        logger.debug(f"📊 Orderbook update for {symbol_part}: "
                                   f"spread={spread:.6f} ({spread_pct:.3f}%)")

                        # Store orderbook data for execution engine
                        if hasattr(self.engine, 'update_orderbook'):
                            await self.engine.update_orderbook(symbol_part, orderbook_info)

        except Exception as e:
            logger.error(f"❌ Error handling orderbook message: {e}")

    async def _handle_trade_message(self, data: Dict[str, Any]):
        """Handle trade WebSocket messages for market activity monitoring"""
        try:
            # HTX trade format: {"ch": "market.dogeusdt.trade.detail", "ts": ..., "tick": {...}}
            if 'ch' in data and 'tick' in data:
                channel = data['ch']
                tick_data = data['tick']

                # Extract symbol from channel
                if 'market.' in channel and '.trade.' in channel:
                    symbol_part = channel.split('.')[1]

                    # Extract trade information
                    trades = tick_data.get('data', [])
                    if trades:
                        latest_trade = trades[0]  # Most recent trade

                        trade_info = {
                            'symbol': symbol_part,
                            'price': latest_trade.get('price'),
                            'amount': latest_trade.get('amount'),
                            'direction': latest_trade.get('direction'),
                            'timestamp': latest_trade.get('ts')
                        }

                        logger.debug(f"💱 Trade update for {symbol_part}: "
                                   f"{trade_info['direction']} {trade_info['amount']} @ ${trade_info['price']}")

                        # Store trade data for execution engine
                        if hasattr(self.engine, 'update_trade_data'):
                            await self.engine.update_trade_data(symbol_part, trade_info)

        except Exception as e:
            logger.error(f"❌ Error handling trade message: {e}")

    async def _handle_default_message(self, data: Dict[str, Any]):
        """Handle unknown or system WebSocket messages"""
        try:
            # Handle ping/pong and system messages
            if 'ping' in data:
                # Respond to ping with pong
                pong_message = {'pong': data['ping']}
                await self.websocket_manager.send_message(pong_message)
                logger.debug("🏓 Responded to WebSocket ping")

            elif 'subbed' in data:
                # Subscription confirmation
                logger.info(f"✅ WebSocket subscription confirmed: {data.get('subbed')}")

            elif 'status' in data:
                # Status message
                status = data.get('status')
                if status == 'ok':
                    logger.debug("✅ WebSocket status OK")
                else:
                    logger.warning(f"⚠️ WebSocket status: {status}")

            else:
                # Unknown message type
                logger.debug(f"❓ Unknown WebSocket message: {data}")

        except Exception as e:
            logger.error(f"❌ Error handling default message: {e}")

    def get_websocket_health_status(self) -> Dict[str, Any]:
        """Get current WebSocket health status"""
        if not self.websocket_manager:
            return {
                'enabled': False,
                'status': 'not_initialized',
                'message': 'WebSocket manager not initialized'
            }

        try:
            metrics = self.websocket_manager.get_connection_metrics()

            # Determine overall health
            is_healthy = (
                metrics['state'] == ConnectionState.CONNECTED.value and
                metrics['success_rate'] > 0.8 and
                metrics['latency_ms'] < 5000 and
                metrics['error_count'] < 10
            )

            return {
                'enabled': True,
                'healthy': is_healthy,
                'status': metrics['state'],
                'metrics': metrics,
                'last_health_check': self.last_websocket_health_check,
                'message': 'WebSocket connection healthy' if is_healthy else 'WebSocket connection issues detected'
            }

        except Exception as e:
            return {
                'enabled': True,
                'healthy': False,
                'status': 'error',
                'error': str(e),
                'message': f'Error getting WebSocket status: {e}'
            }

    async def emergency_stop(self, reason: str = "External emergency stop") -> dict:
        """
        Emergency stop method that includes WebSocket cleanup

        Args:
            reason: Reason for emergency stop

        Returns:
            dict: Emergency stop results
        """
        try:
            logger.critical(f"🚨 EXECUTION ADAPTER EMERGENCY STOP: {reason}")

            # Stop WebSocket connections
            websocket_stopped = False
            if self.websocket_manager:
                try:
                    await self.websocket_manager.disconnect()
                    websocket_stopped = True
                    logger.info("🔌 WebSocket connections stopped")
                except Exception as e:
                    logger.error(f"Error stopping WebSocket: {e}")

            # Stop execution engine
            engine_stopped = False
            if hasattr(self.engine, 'emergency_stop'):
                try:
                    engine_result = await self.engine.emergency_stop(reason)
                    engine_stopped = engine_result.get('success', False)
                except Exception as e:
                    logger.error(f"Error stopping execution engine: {e}")

            # Mark as not initialized
            self.is_initialized = False

            return {
                'success': True,
                'module': 'execution_adapter',
                'reason': reason,
                'websocket_stopped': websocket_stopped,
                'engine_stopped': engine_stopped,
                'timestamp': asyncio.get_event_loop().time()
            }

        except Exception as e:
            logger.error(f"Error in execution adapter emergency stop: {e}")
            return {
                'success': False,
                'module': 'execution_adapter',
                'error': str(e),
                'timestamp': asyncio.get_event_loop().time()
            }
    
    # Backward compatibility methods for AutonomousTradeExecutor interface
    async def execute_trading_decision(self, decision_data: Dict) -> Dict:
        """
        Execute trading decision (AutonomousTradeExecutor compatibility)
        
        Args:
            decision_data: Dict containing decision information
            
        Returns:
            Dict with execution results
        """
        try:
            # Convert legacy decision format to TradingDecision
            decision = self._convert_legacy_decision(decision_data)
            
            # Execute using unified engine
            result = await self.engine.execute_decision(decision)
            
            # Convert result back to legacy format
            return self._convert_result_to_legacy(result)
            
        except Exception as e:
            logger.error(f"Error executing trading decision: {e}")
            return {
                'status': 'ERROR',
                'reason': str(e),
                'timestamp': datetime.now().isoformat()
            }
    
    def _convert_legacy_decision(self, decision_data: Dict) -> TradingDecision:
        """Convert legacy decision format to TradingDecision"""
        # Extract decision components
        decision = decision_data.get('decision', 'WAIT')
        confidence = decision_data.get('confidence', 0) / 100  # Convert percentage to decimal
        symbol = decision_data.get('selected_symbol', decision_data.get('symbol', 'DOGE/USDT:USDT'))
        
        # Get position sizing data
        position_data = decision_data.get('leverage_position_sizing', {})
        position_size = position_data.get('position_units', 0)
        leverage = position_data.get('effective_leverage', 1.0)
        stop_loss = position_data.get('stop_loss_price')
        take_profit = position_data.get('take_profit_price')
        
        return TradingDecision(
            symbol=symbol,
            action=decision,
            confidence=confidence,
            position_size=position_size,
            leverage=leverage,
            stop_loss=stop_loss,
            take_profit=take_profit,
            reasoning=decision_data.get('reasoning', '')
        )
    
    def _convert_result_to_legacy(self, result) -> Dict:
        """Convert ExecutionResult to legacy format"""
        if result.success:
            return {
                'status': 'FILLED' if result.status == 'FILLED' else 'PENDING',
                'order_id': result.order_id,
                'symbol': result.symbol,
                'side': result.side,
                'amount': result.amount,
                'price': result.price,
                'timestamp': result.timestamp
            }
        else:
            return {
                'status': 'ERROR',
                'reason': result.error_message,
                'timestamp': result.timestamp
            }
    
    # Backward compatibility methods for SimulationExecutor interface
    async def execute_decision(self, decision) -> Dict:
        """Execute decision (SimulationExecutor compatibility)"""
        try:
            # Convert decision object to TradingDecision if needed
            if hasattr(decision, 'symbol'):
                trading_decision = TradingDecision(
                    symbol=decision.symbol,
                    action=decision.action,
                    confidence=decision.confidence,
                    position_size=decision.position_size,
                    leverage=getattr(decision, 'leverage', 1.0),
                    stop_loss=getattr(decision, 'stop_loss', None),
                    take_profit=getattr(decision, 'take_profit', None)
                )
            else:
                # Assume it's a dict
                trading_decision = self._convert_legacy_decision(decision)
            
            result = await self.engine.execute_decision(trading_decision)
            
            return {
                'success': result.success,
                'order_id': result.order_id,
                'execution_price': result.price,
                'amount': result.amount,
                'error': result.error_message if not result.success else None
            }
            
        except Exception as e:
            logger.error(f"Error in execute_decision: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    # Backward compatibility methods for CCXTTradingEngine interface
    async def create_order(self, symbol: str, order_type: str, side: str, amount: float,
                          price: Optional[float] = None, params: Optional[Dict] = None) -> Optional[Dict]:
        """Create order (CCXTTradingEngine compatibility)"""
        try:
            # CRITICAL: Validate order type using autonomous trading rules
            order_type_validation = AutonomousTradingRules.validate_order_type(order_type)
            if not order_type_validation['valid']:
                logger.error(f"🚨 ORDER REJECTED: {order_type_validation['reason']}")
                return None  # Reject non-LIMIT orders completely
            
            if not price:
                # Get current market price if no price specified
                ticker = await self.engine._get_ticker(symbol) if self.mode == 'live' else None
                if ticker:
                    price = ticker['last']
                else:
                    price = await self.engine._get_simulated_price(symbol)
            
            # Create TradingDecision
            action = 'LONG' if side.lower() == 'buy' else 'SHORT'
            leverage = params.get('leverage', 1.0) if params else 1.0
            
            decision = TradingDecision(
                symbol=symbol,
                action=action,
                confidence=0.8,  # Default confidence for manual orders
                position_size=amount,
                leverage=leverage
            )
            
            result = await self.engine.execute_decision(decision)
            
            if result.success:
                return {
                    'id': result.order_id,
                    'symbol': result.symbol,
                    'type': order_type,
                    'side': result.side,
                    'amount': result.amount,
                    'price': result.price,
                    'status': result.status,
                    'timestamp': result.timestamp
                }
            else:
                logger.error(f"Order creation failed: {result.error_message}")
                return None
                
        except Exception as e:
            logger.error(f"Error creating order: {e}")
            return None
    
    # Common interface methods
    async def get_balance(self) -> Dict:
        """Get account balance"""
        return await self.engine.get_balance()
    
    async def get_positions(self) -> Dict:
        """Get open positions"""
        return await self.engine.get_positions()
    
    async def close_position(self, symbol: str, reason: str = "Manual close") -> Dict:
        """Close position"""
        result = await self.engine.close_position(symbol, reason)
        return self._convert_result_to_legacy(result)
    
    async def cancel_all_orders(self, reason: str = "Emergency stop") -> List[Dict]:
        """Cancel all orders"""
        results = await self.engine.cancel_all_orders(reason)
        return [self._convert_result_to_legacy(r) for r in results]
    
    async def close_all_positions(self, reason: str = "Emergency stop") -> List[Dict]:
        """Close all positions"""
        results = await self.engine.close_all_positions(reason)
        return [self._convert_result_to_legacy(r) for r in results]
    
    # Emergency stop method is defined earlier in the class with WebSocket cleanup
    
    def get_performance_metrics(self) -> Dict:
        """Get performance metrics"""
        return self.engine.get_performance_metrics()
    
    def is_healthy(self) -> bool:
        """Check if execution system is healthy"""
        return self.engine.is_healthy()
    
    @property
    def exchange(self):
        """Provide access to underlying exchange for backward compatibility"""
        return self.engine.exchange
