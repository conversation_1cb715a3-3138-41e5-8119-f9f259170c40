"""
Execution Adapter for Epinnox Trading System
Provides backward compatibility while using the unified execution engine
"""

import asyncio
import logging
from typing import Dict, Optional, Any, List
from datetime import datetime

from .unified_execution_engine import UnifiedExecutionEngine, ExecutionMode, TradingDecision
from config.autonomous_trading_rules import AutonomousTradingRules
from core.websocket_stability_manager import WebSocketStabilityManager, ConnectionState

logger = logging.getLogger(__name__)

class ExecutionAdapter:
    """
    Adapter that provides backward compatibility for existing autonomous trading components
    while using the new unified execution engine under the hood
    """
    
    def __init__(self, mode: str = "paper", exchange_config: Optional[Dict] = None, 
                 initial_balance: float = 10000.0):
        """
        Initialize execution adapter
        
        Args:
            mode: Trading mode ('live', 'paper', 'simulation')
            exchange_config: Exchange configuration for live trading
            initial_balance: Initial balance for paper/simulation
        """
        # Convert mode string to enum
        mode_map = {
            'live': ExecutionMode.LIVE,
            'paper': ExecutionMode.PAPER,
            'simulation': ExecutionMode.SIMULATION
        }
        
        execution_mode = mode_map.get(mode.lower(), ExecutionMode.PAPER)
        
        # Initialize unified execution engine
        self.engine = UnifiedExecutionEngine(
            mode=execution_mode,
            exchange_config=exchange_config,
            initial_balance=initial_balance
        )
        
        self.mode = mode
        self.is_initialized = False

        # CRITICAL: WebSocket stability management for live trading
        self.websocket_manager = None
        self.websocket_health_check_interval = 30.0  # seconds
        self.last_websocket_health_check = None

        # WebSocket endpoints for different exchanges
        self.websocket_endpoints = {
            'htx': {
                'primary': 'wss://api.huobi.pro/ws',
                'backup': ['wss://api-aws.huobi.pro/ws', 'wss://api.hbdm.com/ws']
            },
            'binance': {
                'primary': 'wss://stream.binance.com:9443/ws',
                'backup': ['wss://stream.binance.com:443/ws', 'wss://fstream.binance.com/ws']
            }
        }
        
        logger.info(f"Execution adapter initialized in {mode} mode")
    
    async def initialize(self) -> bool:
        """Initialize the execution adapter with WebSocket stability"""
        try:
            # Initialize the execution engine
            success = await self.engine.initialize()
            if not success:
                return False

            # CRITICAL: Initialize WebSocket stability for live trading
            if self.mode == 'live':
                await self._initialize_websocket_stability()

            self.is_initialized = True
            logger.info(f"✅ Execution adapter initialized in {self.mode} mode")
            return True

        except Exception as e:
            logger.error(f"Failed to initialize execution adapter: {e}")
            return False
    
    # Backward compatibility methods for AutonomousTradeExecutor interface
    async def execute_trading_decision(self, decision_data: Dict) -> Dict:
        """
        Execute trading decision (AutonomousTradeExecutor compatibility)
        
        Args:
            decision_data: Dict containing decision information
            
        Returns:
            Dict with execution results
        """
        try:
            # Convert legacy decision format to TradingDecision
            decision = self._convert_legacy_decision(decision_data)
            
            # Execute using unified engine
            result = await self.engine.execute_decision(decision)
            
            # Convert result back to legacy format
            return self._convert_result_to_legacy(result)
            
        except Exception as e:
            logger.error(f"Error executing trading decision: {e}")
            return {
                'status': 'ERROR',
                'reason': str(e),
                'timestamp': datetime.now().isoformat()
            }
    
    def _convert_legacy_decision(self, decision_data: Dict) -> TradingDecision:
        """Convert legacy decision format to TradingDecision"""
        # Extract decision components
        decision = decision_data.get('decision', 'WAIT')
        confidence = decision_data.get('confidence', 0) / 100  # Convert percentage to decimal
        symbol = decision_data.get('selected_symbol', decision_data.get('symbol', 'DOGE/USDT:USDT'))
        
        # Get position sizing data
        position_data = decision_data.get('leverage_position_sizing', {})
        position_size = position_data.get('position_units', 0)
        leverage = position_data.get('effective_leverage', 1.0)
        stop_loss = position_data.get('stop_loss_price')
        take_profit = position_data.get('take_profit_price')
        
        return TradingDecision(
            symbol=symbol,
            action=decision,
            confidence=confidence,
            position_size=position_size,
            leverage=leverage,
            stop_loss=stop_loss,
            take_profit=take_profit,
            reasoning=decision_data.get('reasoning', '')
        )
    
    def _convert_result_to_legacy(self, result) -> Dict:
        """Convert ExecutionResult to legacy format"""
        if result.success:
            return {
                'status': 'FILLED' if result.status == 'FILLED' else 'PENDING',
                'order_id': result.order_id,
                'symbol': result.symbol,
                'side': result.side,
                'amount': result.amount,
                'price': result.price,
                'timestamp': result.timestamp
            }
        else:
            return {
                'status': 'ERROR',
                'reason': result.error_message,
                'timestamp': result.timestamp
            }
    
    # Backward compatibility methods for SimulationExecutor interface
    async def execute_decision(self, decision) -> Dict:
        """Execute decision (SimulationExecutor compatibility)"""
        try:
            # Convert decision object to TradingDecision if needed
            if hasattr(decision, 'symbol'):
                trading_decision = TradingDecision(
                    symbol=decision.symbol,
                    action=decision.action,
                    confidence=decision.confidence,
                    position_size=decision.position_size,
                    leverage=getattr(decision, 'leverage', 1.0),
                    stop_loss=getattr(decision, 'stop_loss', None),
                    take_profit=getattr(decision, 'take_profit', None)
                )
            else:
                # Assume it's a dict
                trading_decision = self._convert_legacy_decision(decision)
            
            result = await self.engine.execute_decision(trading_decision)
            
            return {
                'success': result.success,
                'order_id': result.order_id,
                'execution_price': result.price,
                'amount': result.amount,
                'error': result.error_message if not result.success else None
            }
            
        except Exception as e:
            logger.error(f"Error in execute_decision: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    # Backward compatibility methods for CCXTTradingEngine interface
    async def create_order(self, symbol: str, order_type: str, side: str, amount: float,
                          price: Optional[float] = None, params: Optional[Dict] = None) -> Optional[Dict]:
        """Create order (CCXTTradingEngine compatibility)"""
        try:
            # CRITICAL: Validate order type using autonomous trading rules
            order_type_validation = AutonomousTradingRules.validate_order_type(order_type)
            if not order_type_validation['valid']:
                logger.error(f"🚨 ORDER REJECTED: {order_type_validation['reason']}")
                return None  # Reject non-LIMIT orders completely
            
            if not price:
                # Get current market price if no price specified
                ticker = await self.engine._get_ticker(symbol) if self.mode == 'live' else None
                if ticker:
                    price = ticker['last']
                else:
                    price = await self.engine._get_simulated_price(symbol)
            
            # Create TradingDecision
            action = 'LONG' if side.lower() == 'buy' else 'SHORT'
            leverage = params.get('leverage', 1.0) if params else 1.0
            
            decision = TradingDecision(
                symbol=symbol,
                action=action,
                confidence=0.8,  # Default confidence for manual orders
                position_size=amount,
                leverage=leverage
            )
            
            result = await self.engine.execute_decision(decision)
            
            if result.success:
                return {
                    'id': result.order_id,
                    'symbol': result.symbol,
                    'type': order_type,
                    'side': result.side,
                    'amount': result.amount,
                    'price': result.price,
                    'status': result.status,
                    'timestamp': result.timestamp
                }
            else:
                logger.error(f"Order creation failed: {result.error_message}")
                return None
                
        except Exception as e:
            logger.error(f"Error creating order: {e}")
            return None
    
    # Common interface methods
    async def get_balance(self) -> Dict:
        """Get account balance"""
        return await self.engine.get_balance()
    
    async def get_positions(self) -> Dict:
        """Get open positions"""
        return await self.engine.get_positions()
    
    async def close_position(self, symbol: str, reason: str = "Manual close") -> Dict:
        """Close position"""
        result = await self.engine.close_position(symbol, reason)
        return self._convert_result_to_legacy(result)
    
    async def cancel_all_orders(self, reason: str = "Emergency stop") -> List[Dict]:
        """Cancel all orders"""
        results = await self.engine.cancel_all_orders(reason)
        return [self._convert_result_to_legacy(r) for r in results]
    
    async def close_all_positions(self, reason: str = "Emergency stop") -> List[Dict]:
        """Close all positions"""
        results = await self.engine.close_all_positions(reason)
        return [self._convert_result_to_legacy(r) for r in results]
    
    async def emergency_stop(self) -> Dict:
        """Emergency stop"""
        return await self.engine.emergency_stop()
    
    def get_performance_metrics(self) -> Dict:
        """Get performance metrics"""
        return self.engine.get_performance_metrics()
    
    def is_healthy(self) -> bool:
        """Check if execution system is healthy"""
        return self.engine.is_healthy()
    
    @property
    def exchange(self):
        """Provide access to underlying exchange for backward compatibility"""
        return self.engine.exchange
