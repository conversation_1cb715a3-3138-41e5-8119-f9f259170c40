# Phase 2 Autonomous Trading Configuration
# Complete configuration for all Phase 2 HIGH priority components
# Designed for 95%+ system readiness and live trading deployment

# =============================================================================
# UNIFIED LLM INTEGRATION CONFIGURATION
# =============================================================================
ai:
  # LLM Manager Settings
  llm_manager:
    max_response_time: 30.0
    max_consecutive_failures: 3
    health_check_interval: 60
    min_confidence_threshold: 0.5
  
  # Decision Loop Configuration
  decision_interval: 30  # 30-second autonomous decision loops
  max_decisions_per_minute: 3
  min_confidence_for_action: 0.55  # 55% minimum confidence for trades
  
  # Provider Configurations
  providers:
    chatgpt:
      enabled: true
      model: "gpt-3.5-turbo"
      temperature: 0.7
      max_tokens: 1024
    
    lmstudio:
      enabled: true
      api_url: "http://localhost:1234/v1"
      temperature: 0.1
      max_tokens: 512
    
    transformers:
      enabled: true
      model_name: "microsoft/DialoGPT-medium"
      temperature: 0.7
    
    mock:
      enabled: true
      response_time: 0.1
      success_rate: 0.95

# =============================================================================
# AUTONOMOUS POSITION MANAGEMENT CONFIGURATION
# =============================================================================
position_management:
  # Default TP/SL Settings
  default_stop_loss_pct: 0.02      # 2% default stop loss
  default_take_profit_pct: 0.04    # 4% default take profit
  default_trailing_distance: 0.02   # 2% trailing distance
  
  # Activation Thresholds
  trailing_stop_activation: 0.015   # 1.5% profit to activate trailing stop
  break_even_activation: 0.01       # 1% profit to move SL to break-even
  
  # Monitoring Settings
  monitoring_interval: 5             # 5-second position monitoring
  
  # Partial Take Profit Settings
  enable_partial_tp: true
  partial_tp_levels:
    - price_pct: 0.02               # 2% profit
      take_percentage: 25           # Take 25% of position
    - price_pct: 0.04               # 4% profit  
      take_percentage: 50           # Take 50% of remaining

# =============================================================================
# POSITION WATCHDOG CONFIGURATION
# =============================================================================
position_monitoring:
  # Monitoring Intervals
  scan_interval: 10                 # 10-second position scans
  health_check_interval: 30         # 30-second health checks
  
  # Detection Thresholds
  orphan_detection_threshold: 300   # 5 minutes for orphaned positions
  stale_position_threshold: 3600    # 1 hour for stale positions
  
  # Health Scoring Weights
  risk_weight: 0.4                  # 40% weight for risk factors
  performance_weight: 0.3           # 30% weight for performance
  time_weight: 0.3                  # 30% weight for time factors
  
  # Alert Thresholds
  critical_health_threshold: 40     # Below 40% = critical
  warning_health_threshold: 60      # Below 60% = warning
  
  # Recovery Settings
  auto_recovery_enabled: true
  max_recovery_attempts: 3

# =============================================================================
# ERROR RECOVERY SYSTEM CONFIGURATION
# =============================================================================
error_recovery:
  # Global Settings
  default_max_retries: 3
  default_base_delay: 1.0
  default_max_delay: 60.0
  default_exponential_base: 2.0
  default_jitter: true
  
  # Health Monitoring
  health_check_interval: 60
  max_error_history: 1000
  
  # Emergency Thresholds
  emergency_health_threshold: 30    # Below 30% = emergency mode
  recovery_health_threshold: 70     # Above 70% = exit emergency
  
  # Component-Specific Configurations
  component_configs:
    llm_integration:
      max_retries: 5
      base_delay: 2.0
      max_delay: 30.0
    
    execution_engine:
      max_retries: 3
      base_delay: 5.0
      max_delay: 60.0
    
    market_data:
      max_retries: 10
      base_delay: 0.5
      max_delay: 10.0
    
    position_manager:
      max_retries: 3
      base_delay: 3.0
      max_delay: 45.0

# =============================================================================
# TRADING CONFIGURATION
# =============================================================================
trading:
  # Order Settings
  use_limit_orders_only: true       # CRITICAL: Only LIMIT orders allowed
  order_timeout_seconds: 60
  max_spread_pct: 0.2
  auto_cancel_stale_orders: true
  
  # Position Limits
  max_open_positions: 3
  max_position_size_pct: 0.30       # 30% max position size
  max_leverage: 3.0
  
  # Risk Management
  max_daily_loss_pct: 0.20          # 20% max daily loss
  max_portfolio_risk_pct: 0.80      # 80% max portfolio risk
  emergency_stop_enabled: true
  
  # Quality Thresholds
  min_confidence: 0.55               # 55% minimum confidence
  min_spread_quality: 7.0
  min_decision_quality: 8.0
  min_symbol_score: 75.0

# =============================================================================
# LIVE TRADING SAFETY CONFIGURATION
# =============================================================================
live_trading_safety:
  # Ultra-Conservative Settings for Initial Deployment
  max_initial_balance: 100.0         # $100 maximum for first deployment
  min_position_size_pct: 0.01        # 1% minimum position size
  max_position_size_pct: 0.02        # 2% maximum position size (ultra-conservative)
  max_portfolio_risk_pct: 0.02       # 2% maximum portfolio risk
  
  # Validation Requirements
  require_paper_trading_validation: true
  min_paper_trading_days: 7          # 7 days minimum paper trading
  min_successful_trades: 50          # 50 successful trades required
  min_success_rate: 0.60             # 60% minimum success rate
  max_drawdown_pct: 0.10             # 10% maximum drawdown
  
  # Emergency Stops
  daily_loss_emergency_stop: 0.05    # 5% daily loss = emergency stop
  consecutive_loss_limit: 5          # 5 consecutive losses = stop
  
  # Monitoring Requirements
  real_time_monitoring_required: true
  manual_oversight_required: true
  trade_journal_required: true

# =============================================================================
# SYSTEM INTEGRATION CONFIGURATION
# =============================================================================
integration:
  # Component Integration
  auto_start_all_components: true
  component_startup_timeout: 30
  
  # Data Flow
  real_time_data_required: true
  data_quality_validation: true
  backup_data_sources: true
  
  # Monitoring Integration
  unified_logging: true
  performance_tracking: true
  health_monitoring: true
  
  # Safety Integration
  cross_component_validation: true
  emergency_stop_propagation: true
  system_wide_circuit_breakers: true

# =============================================================================
# PERFORMANCE AND MONITORING
# =============================================================================
monitoring:
  # Performance Metrics
  track_decision_latency: true
  track_execution_latency: true
  track_position_performance: true
  track_system_health: true
  
  # Logging Configuration
  log_level: "INFO"
  log_all_decisions: true
  log_all_trades: true
  log_performance_metrics: true
  
  # Alerting
  enable_critical_alerts: true
  enable_performance_alerts: true
  enable_health_alerts: true
  
  # Reporting
  generate_daily_reports: true
  generate_performance_summaries: true
  export_trade_journal: true

# =============================================================================
# DEVELOPMENT AND TESTING
# =============================================================================
development:
  # Testing Configuration
  enable_comprehensive_testing: true
  validate_all_components: true
  test_error_recovery: true
  test_emergency_procedures: true
  
  # Simulation Settings
  enable_simulation_mode: true
  simulation_balance: 10000.0
  simulation_symbols: ["DOGE/USDT:USDT", "BTC/USDT:USDT", "ETH/USDT:USDT"]
  
  # Validation Requirements
  require_100_percent_test_pass: true
  require_integration_testing: true
  require_stress_testing: true
  require_failover_testing: true

# =============================================================================
# PRODUCTION DEPLOYMENT
# =============================================================================
production:
  # Deployment Strategy
  gradual_deployment: true
  start_with_minimal_capital: true
  scale_based_on_performance: true
  
  # Validation Gates
  require_phase1_validation: true
  require_phase2_validation: true
  require_live_data_testing: true
  require_safety_validation: true
  
  # Operational Requirements
  24_7_monitoring: false             # Start with business hours only
  automated_reporting: true
  manual_oversight: true
  
  # Success Criteria for Scaling
  min_profitable_days: 14           # 14 profitable days before scaling
  min_roi_threshold: 0.05           # 5% minimum ROI before scaling
  max_acceptable_drawdown: 0.08     # 8% maximum drawdown
  
  # Scaling Parameters
  capital_scaling_factor: 2.0       # Double capital after success criteria
  position_size_scaling: 1.5        # 1.5x position size scaling
  max_scaled_balance: 1000.0        # $1000 maximum after scaling
