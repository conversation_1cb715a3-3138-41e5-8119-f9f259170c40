#!/usr/bin/env python3
"""
📊 LIVE VALIDATION MONITOR
Real-time monitoring interface for live trading validation

FEATURES:
- Real-time validation progress tracking
- Safety violation monitoring
- Performance metrics display
- Emergency stop controls
- Validation results analysis
"""

import sys
import os
from datetime import datetime, timedelta
from typing import Dict, Any, Optional

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

try:
    from PyQt5.QtWidgets import *
    from PyQt5.QtCore import *
    from PyQt5.QtGui import *
    PYQT_AVAILABLE = True
except ImportError:
    PYQT_AVAILABLE = False

if PYQT_AVAILABLE:
    from gui.matrix_theme import MatrixTheme
    from validation.live_trading_validator import live_trading_validator, ValidationMetrics
    
    import logging
    logger = logging.getLogger(__name__)


class LiveValidationMonitor(QWidget):
    """
    CRITICAL: Real-time live trading validation monitor
    
    Provides comprehensive monitoring interface for live trading validation
    with real-time metrics, safety monitoring, and emergency controls.
    """
    
    # Signals
    validation_started = pyqtSignal()
    validation_stopped = pyqtSignal()
    emergency_stop_triggered = pyqtSignal(str)
    
    def __init__(self, parent=None):
        super().__init__(parent)
        
        # Validation state
        self.validation_active = False
        self.validation_start_time = None
        
        # Update timers
        self.metrics_update_timer = QTimer()
        self.safety_update_timer = QTimer()
        
        # Setup UI
        self.setup_ui()
        self.apply_matrix_theme()
        self.setup_timers()
        
        logger.info("Live Validation Monitor initialized")
    
    def setup_ui(self):
        """Setup the validation monitoring UI"""
        main_layout = QVBoxLayout(self)
        
        # Header
        header_label = QLabel("📊 LIVE TRADING VALIDATION MONITOR")
        header_label.setProperty("class", "header")
        main_layout.addWidget(header_label)
        
        # Create monitoring sections
        self.create_validation_controls(main_layout)
        self.create_progress_monitoring(main_layout)
        self.create_safety_monitoring(main_layout)
        self.create_performance_metrics(main_layout)
        self.create_validation_results(main_layout)
    
    def create_validation_controls(self, parent_layout):
        """Create validation control section"""
        controls_group = QGroupBox("🎛️ VALIDATION CONTROLS")
        controls_layout = QGridLayout(controls_group)
        
        # Validation status
        controls_layout.addWidget(QLabel("Status:"), 0, 0)
        self.validation_status_label = QLabel("Ready to start validation")
        self.validation_status_label.setStyleSheet(f"color: {MatrixTheme.YELLOW}; font-weight: bold;")
        controls_layout.addWidget(self.validation_status_label, 0, 1)
        
        # Start validation button
        self.start_validation_btn = QPushButton("🚀 Start Live Validation")
        self.start_validation_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: {MatrixTheme.DARK_GREEN};
                color: {MatrixTheme.GREEN};
                font-weight: bold;
                padding: 10px;
                border: 2px solid {MatrixTheme.GREEN};
            }}
        """)
        self.start_validation_btn.clicked.connect(self.start_validation)
        controls_layout.addWidget(self.start_validation_btn, 1, 0)
        
        # Stop validation button
        self.stop_validation_btn = QPushButton("⏹️ Stop Validation")
        self.stop_validation_btn.clicked.connect(self.stop_validation)
        self.stop_validation_btn.setEnabled(False)
        controls_layout.addWidget(self.stop_validation_btn, 1, 1)
        
        # Emergency stop button
        self.emergency_stop_btn = QPushButton("🚨 EMERGENCY STOP")
        self.emergency_stop_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: {MatrixTheme.RED};
                color: white;
                font-size: 14px;
                font-weight: bold;
                padding: 10px;
                border: 2px solid {MatrixTheme.RED};
            }}
        """)
        self.emergency_stop_btn.clicked.connect(self.trigger_emergency_stop)
        controls_layout.addWidget(self.emergency_stop_btn, 2, 0, 1, 2)
        
        parent_layout.addWidget(controls_group)
    
    def create_progress_monitoring(self, parent_layout):
        """Create progress monitoring section"""
        progress_group = QGroupBox("📈 VALIDATION PROGRESS")
        progress_layout = QGridLayout(progress_group)
        
        # Validation duration
        progress_layout.addWidget(QLabel("Duration:"), 0, 0)
        self.duration_label = QLabel("00:00:00")
        progress_layout.addWidget(self.duration_label, 0, 1)
        
        # Progress bar
        progress_layout.addWidget(QLabel("Progress:"), 1, 0)
        self.validation_progress = QProgressBar()
        self.validation_progress.setRange(0, 100)
        progress_layout.addWidget(self.validation_progress, 1, 1)
        
        # Validation score
        progress_layout.addWidget(QLabel("Current Score:"), 2, 0)
        self.validation_score_label = QLabel("0.0/100")
        progress_layout.addWidget(self.validation_score_label, 2, 1)
        
        # Target completion
        progress_layout.addWidget(QLabel("Target Duration:"), 0, 2)
        self.target_duration_label = QLabel("24 hours")
        progress_layout.addWidget(self.target_duration_label, 0, 2)
        
        # Minimum trades
        progress_layout.addWidget(QLabel("Min Trades:"), 1, 2)
        self.min_trades_label = QLabel("10 trades")
        progress_layout.addWidget(self.min_trades_label, 1, 2)
        
        parent_layout.addWidget(progress_group)
    
    def create_safety_monitoring(self, parent_layout):
        """Create safety monitoring section"""
        safety_group = QGroupBox("🛡️ SAFETY MONITORING")
        safety_layout = QGridLayout(safety_group)
        
        # Emergency stops count
        safety_layout.addWidget(QLabel("Emergency Stops:"), 0, 0)
        self.emergency_stops_label = QLabel("0")
        safety_layout.addWidget(self.emergency_stops_label, 0, 1)
        
        # Safety violations count
        safety_layout.addWidget(QLabel("Safety Violations:"), 1, 0)
        self.safety_violations_label = QLabel("0")
        safety_layout.addWidget(self.safety_violations_label, 1, 1)
        
        # Max position size
        safety_layout.addWidget(QLabel("Max Position Size:"), 2, 0)
        self.max_position_label = QLabel("0.0%")
        safety_layout.addWidget(self.max_position_label, 2, 1)
        
        # System uptime
        safety_layout.addWidget(QLabel("System Uptime:"), 0, 2)
        self.system_uptime_label = QLabel("0.0%")
        safety_layout.addWidget(self.system_uptime_label, 0, 2)
        
        # Risk compliance
        safety_layout.addWidget(QLabel("Risk Compliance:"), 1, 2)
        self.risk_compliance_label = QLabel("✅ COMPLIANT")
        self.risk_compliance_label.setStyleSheet(f"color: {MatrixTheme.GREEN}; font-weight: bold;")
        safety_layout.addWidget(self.risk_compliance_label, 1, 2)
        
        # Safety violations list
        safety_layout.addWidget(QLabel("Recent Violations:"), 3, 0, 1, 3)
        self.safety_violations_list = QListWidget()
        self.safety_violations_list.setMaximumHeight(100)
        safety_layout.addWidget(self.safety_violations_list, 4, 0, 1, 3)
        
        parent_layout.addWidget(safety_group)
    
    def create_performance_metrics(self, parent_layout):
        """Create performance metrics section"""
        performance_group = QGroupBox("📊 PERFORMANCE METRICS")
        performance_layout = QGridLayout(performance_group)
        
        # Total trades
        performance_layout.addWidget(QLabel("Total Trades:"), 0, 0)
        self.total_trades_label = QLabel("0")
        performance_layout.addWidget(self.total_trades_label, 0, 1)
        
        # Win rate
        performance_layout.addWidget(QLabel("Win Rate:"), 1, 0)
        self.win_rate_label = QLabel("0.0%")
        performance_layout.addWidget(self.win_rate_label, 1, 1)
        
        # Total P&L
        performance_layout.addWidget(QLabel("Total P&L:"), 2, 0)
        self.total_pnl_label = QLabel("$0.00")
        performance_layout.addWidget(self.total_pnl_label, 2, 1)
        
        # Max drawdown
        performance_layout.addWidget(QLabel("Max Drawdown:"), 0, 2)
        self.max_drawdown_label = QLabel("0.0%")
        performance_layout.addWidget(self.max_drawdown_label, 0, 2)
        
        # Sharpe ratio
        performance_layout.addWidget(QLabel("Sharpe Ratio:"), 1, 2)
        self.sharpe_ratio_label = QLabel("0.00")
        performance_layout.addWidget(self.sharpe_ratio_label, 1, 2)
        
        # Average trade duration
        performance_layout.addWidget(QLabel("Avg Trade Duration:"), 2, 2)
        self.avg_duration_label = QLabel("0.0 min")
        performance_layout.addWidget(self.avg_duration_label, 2, 2)
        
        parent_layout.addWidget(performance_group)
    
    def create_validation_results(self, parent_layout):
        """Create validation results section"""
        results_group = QGroupBox("🏆 VALIDATION RESULTS")
        results_layout = QVBoxLayout(results_group)
        
        # Results text area
        self.validation_results_text = QTextEdit()
        self.validation_results_text.setMaximumHeight(150)
        self.validation_results_text.setReadOnly(True)
        self.validation_results_text.setText("Validation results will appear here...")
        results_layout.addWidget(self.validation_results_text)
        
        # Action buttons
        buttons_layout = QHBoxLayout()
        
        self.save_results_btn = QPushButton("💾 Save Results")
        self.save_results_btn.clicked.connect(self.save_validation_results)
        self.save_results_btn.setEnabled(False)
        buttons_layout.addWidget(self.save_results_btn)
        
        self.view_detailed_btn = QPushButton("📋 View Detailed Report")
        self.view_detailed_btn.clicked.connect(self.view_detailed_report)
        self.view_detailed_btn.setEnabled(False)
        buttons_layout.addWidget(self.view_detailed_btn)
        
        results_layout.addLayout(buttons_layout)
        parent_layout.addWidget(results_group)
    
    def setup_timers(self):
        """Setup update timers"""
        # Metrics updates every 5 seconds
        self.metrics_update_timer.timeout.connect(self.update_metrics_display)
        
        # Safety updates every 2 seconds
        self.safety_update_timer.timeout.connect(self.update_safety_display)
    
    def apply_matrix_theme(self):
        """Apply Matrix theme to the widget"""
        self.setStyleSheet(MatrixTheme.get_stylesheet())
    
    def start_validation(self):
        """Start live trading validation"""
        try:
            # Show confirmation dialog
            reply = QMessageBox.question(
                self,
                "Start Live Validation",
                "⚠️ WARNING: This will start LIVE trading validation with real money!\n\n"
                "Ultra-Conservative Settings:\n"
                "• Maximum $100 trading capital\n"
                "• 1% position size limit\n"
                "• 2% portfolio risk limit\n"
                "• LIMIT orders only\n"
                "• 24-hour validation period\n\n"
                "Are you sure you want to proceed?",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )
            
            if reply == QMessageBox.Yes:
                # Update UI state
                self.validation_active = True
                self.validation_start_time = datetime.now()
                
                self.start_validation_btn.setEnabled(False)
                self.stop_validation_btn.setEnabled(True)
                self.save_results_btn.setEnabled(False)
                self.view_detailed_btn.setEnabled(False)
                
                self.validation_status_label.setText("LIVE VALIDATION ACTIVE")
                self.validation_status_label.setStyleSheet(f"color: {MatrixTheme.GREEN}; font-weight: bold;")
                
                # Start timers
                self.metrics_update_timer.start(5000)  # 5 seconds
                self.safety_update_timer.start(2000)   # 2 seconds
                
                # Start validation (in real implementation, this would start the actual validation)
                self.validation_results_text.setText("Live trading validation started...\nMonitoring all systems...")
                
                # Emit signal
                self.validation_started.emit()
                
                logger.info("Live trading validation started from GUI")
            
        except Exception as e:
            logger.error(f"Error starting validation: {e}")
            QMessageBox.critical(self, "Error", f"Failed to start validation: {e}")
    
    def stop_validation(self):
        """Stop live trading validation"""
        try:
            # Show confirmation dialog
            reply = QMessageBox.question(
                self,
                "Stop Validation",
                "Stop live trading validation?\n\n"
                "This will halt all trading operations and generate validation results.",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )
            
            if reply == QMessageBox.Yes:
                # Update UI state
                self.validation_active = False
                
                self.start_validation_btn.setEnabled(True)
                self.stop_validation_btn.setEnabled(False)
                self.save_results_btn.setEnabled(True)
                self.view_detailed_btn.setEnabled(True)
                
                self.validation_status_label.setText("VALIDATION STOPPED")
                self.validation_status_label.setStyleSheet(f"color: {MatrixTheme.YELLOW}; font-weight: bold;")
                
                # Stop timers
                self.metrics_update_timer.stop()
                self.safety_update_timer.stop()
                
                # Generate final results
                self.generate_final_results()
                
                # Emit signal
                self.validation_stopped.emit()
                
                logger.info("Live trading validation stopped from GUI")
            
        except Exception as e:
            logger.error(f"Error stopping validation: {e}")
            QMessageBox.critical(self, "Error", f"Failed to stop validation: {e}")
    
    def trigger_emergency_stop(self):
        """Trigger emergency stop"""
        try:
            # Show confirmation dialog
            reply = QMessageBox.question(
                self,
                "Emergency Stop",
                "🚨 TRIGGER EMERGENCY STOP?\n\n"
                "This will immediately halt ALL trading operations!",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )
            
            if reply == QMessageBox.Yes:
                # Trigger emergency stop
                reason = "User triggered emergency stop from validation monitor"
                
                # Update UI
                self.validation_status_label.setText("🚨 EMERGENCY STOP ACTIVE")
                self.validation_status_label.setStyleSheet(f"color: {MatrixTheme.RED}; font-weight: bold;")
                
                # Stop validation
                self.validation_active = False
                self.start_validation_btn.setEnabled(True)
                self.stop_validation_btn.setEnabled(False)
                
                # Emit signal
                self.emergency_stop_triggered.emit(reason)
                
                logger.warning("Emergency stop triggered from validation monitor")
            
        except Exception as e:
            logger.error(f"Error triggering emergency stop: {e}")
            QMessageBox.critical(self, "Error", f"Failed to trigger emergency stop: {e}")
    
    def update_metrics_display(self):
        """Update metrics display"""
        try:
            if not self.validation_active:
                return
            
            # Update duration
            if self.validation_start_time:
                duration = datetime.now() - self.validation_start_time
                hours, remainder = divmod(duration.total_seconds(), 3600)
                minutes, seconds = divmod(remainder, 60)
                self.duration_label.setText(f"{int(hours):02d}:{int(minutes):02d}:{int(seconds):02d}")
                
                # Update progress (24-hour target)
                progress = min((duration.total_seconds() / (24 * 3600)) * 100, 100)
                self.validation_progress.setValue(int(progress))
            
            # Get metrics from validator (mock data for demonstration)
            metrics = self._get_current_metrics()
            
            # Update performance metrics
            self.total_trades_label.setText(str(metrics.get('total_trades', 0)))
            self.win_rate_label.setText(f"{metrics.get('win_rate', 0.0):.1%}")
            
            pnl = metrics.get('total_pnl', 0.0)
            self.total_pnl_label.setText(f"${pnl:.2f}")
            if pnl > 0:
                self.total_pnl_label.setStyleSheet(f"color: {MatrixTheme.GREEN}; font-weight: bold;")
            elif pnl < 0:
                self.total_pnl_label.setStyleSheet(f"color: {MatrixTheme.RED}; font-weight: bold;")
            
            self.max_drawdown_label.setText(f"{metrics.get('max_drawdown', 0.0):.1%}")
            self.sharpe_ratio_label.setText(f"{metrics.get('sharpe_ratio', 0.0):.2f}")
            self.avg_duration_label.setText(f"{metrics.get('avg_trade_duration', 0.0):.1f} min")
            
            # Update validation score
            score = metrics.get('validation_score', 0.0)
            self.validation_score_label.setText(f"{score:.1f}/100")
            
        except Exception as e:
            logger.error(f"Error updating metrics display: {e}")
    
    def update_safety_display(self):
        """Update safety monitoring display"""
        try:
            if not self.validation_active:
                return
            
            # Get safety metrics (mock data for demonstration)
            safety_metrics = self._get_safety_metrics()
            
            # Update safety metrics
            self.emergency_stops_label.setText(str(safety_metrics.get('emergency_stops', 0)))
            self.safety_violations_label.setText(str(safety_metrics.get('safety_violations', 0)))
            self.max_position_label.setText(f"{safety_metrics.get('max_position_size', 0.0):.1f}%")
            self.system_uptime_label.setText(f"{safety_metrics.get('system_uptime', 0.0):.1f}%")
            
            # Update compliance status
            violations = safety_metrics.get('safety_violations', 0)
            if violations == 0:
                self.risk_compliance_label.setText("✅ COMPLIANT")
                self.risk_compliance_label.setStyleSheet(f"color: {MatrixTheme.GREEN}; font-weight: bold;")
            else:
                self.risk_compliance_label.setText("⚠️ VIOLATIONS")
                self.risk_compliance_label.setStyleSheet(f"color: {MatrixTheme.YELLOW}; font-weight: bold;")
            
        except Exception as e:
            logger.error(f"Error updating safety display: {e}")
    
    def _get_current_metrics(self) -> Dict[str, Any]:
        """Get current validation metrics (mock implementation)"""
        # In real implementation, this would get metrics from live_trading_validator
        import random
        
        if self.validation_start_time:
            duration_hours = (datetime.now() - self.validation_start_time).total_seconds() / 3600
            trades = int(duration_hours * 2)  # ~2 trades per hour
        else:
            trades = 0
        
        return {
            'total_trades': trades,
            'win_rate': random.uniform(0.5, 0.7),
            'total_pnl': random.uniform(-5.0, 10.0),
            'max_drawdown': random.uniform(0.01, 0.05),
            'sharpe_ratio': random.uniform(0.8, 1.5),
            'avg_trade_duration': random.uniform(15, 45),
            'validation_score': random.uniform(70, 90)
        }
    
    def _get_safety_metrics(self) -> Dict[str, Any]:
        """Get current safety metrics (mock implementation)"""
        # In real implementation, this would get metrics from live_trading_validator
        return {
            'emergency_stops': 0,
            'safety_violations': 0,
            'max_position_size': 1.0,
            'system_uptime': 99.5
        }
    
    def generate_final_results(self):
        """Generate final validation results"""
        try:
            if self.validation_start_time:
                duration = datetime.now() - self.validation_start_time
                duration_hours = duration.total_seconds() / 3600
            else:
                duration_hours = 0
            
            metrics = self._get_current_metrics()
            
            results_text = "🏆 LIVE TRADING VALIDATION RESULTS\n\n"
            results_text += f"Duration: {duration_hours:.1f} hours\n"
            results_text += f"Total Trades: {metrics['total_trades']}\n"
            results_text += f"Win Rate: {metrics['win_rate']:.1%}\n"
            results_text += f"Total P&L: ${metrics['total_pnl']:.2f}\n"
            results_text += f"Max Drawdown: {metrics['max_drawdown']:.1%}\n"
            results_text += f"Sharpe Ratio: {metrics['sharpe_ratio']:.2f}\n"
            results_text += f"Validation Score: {metrics['validation_score']:.1f}/100\n\n"
            
            if metrics['validation_score'] >= 80:
                results_text += "✅ VALIDATION PASSED - Ready for scaling\n"
                results_text += "Recommendation: Consider increasing capital to $200-500"
            elif metrics['validation_score'] >= 70:
                results_text += "⚠️ VALIDATION MARGINAL - Proceed with caution\n"
                results_text += "Recommendation: Continue monitoring before scaling"
            else:
                results_text += "❌ VALIDATION FAILED - Improvements needed\n"
                results_text += "Recommendation: Address issues before live trading"
            
            self.validation_results_text.setText(results_text)
            
        except Exception as e:
            logger.error(f"Error generating final results: {e}")
    
    def save_validation_results(self):
        """Save validation results to file"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"validation_results_{timestamp}.txt"
            
            file_path, _ = QFileDialog.getSaveFileName(
                self,
                "Save Validation Results",
                filename,
                "Text Files (*.txt);;All Files (*)"
            )
            
            if file_path:
                with open(file_path, 'w') as f:
                    f.write(self.validation_results_text.toPlainText())
                
                QMessageBox.information(self, "Saved", f"Validation results saved to:\n{file_path}")
                logger.info(f"Validation results saved to: {file_path}")
            
        except Exception as e:
            logger.error(f"Error saving validation results: {e}")
            QMessageBox.critical(self, "Error", f"Failed to save results: {e}")
    
    def view_detailed_report(self):
        """View detailed validation report"""
        try:
            # Create detailed report dialog
            dialog = QDialog(self)
            dialog.setWindowTitle("Detailed Validation Report")
            dialog.setModal(True)
            dialog.resize(800, 600)
            
            layout = QVBoxLayout(dialog)
            
            # Detailed report text
            report_text = QTextEdit()
            report_text.setReadOnly(True)
            
            # Generate detailed report
            detailed_report = self._generate_detailed_report()
            report_text.setText(detailed_report)
            
            layout.addWidget(report_text)
            
            # Close button
            close_btn = QPushButton("Close")
            close_btn.clicked.connect(dialog.accept)
            layout.addWidget(close_btn)
            
            dialog.exec_()
            
        except Exception as e:
            logger.error(f"Error viewing detailed report: {e}")
            QMessageBox.critical(self, "Error", f"Failed to view detailed report: {e}")
    
    def _generate_detailed_report(self) -> str:
        """Generate detailed validation report"""
        try:
            report = "📋 DETAILED LIVE TRADING VALIDATION REPORT\n"
            report += "=" * 60 + "\n\n"
            
            # Validation overview
            report += "🔍 VALIDATION OVERVIEW:\n"
            if self.validation_start_time:
                duration = datetime.now() - self.validation_start_time
                report += f"Start Time: {self.validation_start_time.strftime('%Y-%m-%d %H:%M:%S')}\n"
                report += f"Duration: {duration.total_seconds() / 3600:.1f} hours\n"
            report += f"Status: {'Active' if self.validation_active else 'Completed'}\n\n"
            
            # Performance metrics
            metrics = self._get_current_metrics()
            report += "📊 PERFORMANCE METRICS:\n"
            report += f"Total Trades: {metrics['total_trades']}\n"
            report += f"Win Rate: {metrics['win_rate']:.1%}\n"
            report += f"Total P&L: ${metrics['total_pnl']:.2f}\n"
            report += f"Max Drawdown: {metrics['max_drawdown']:.1%}\n"
            report += f"Sharpe Ratio: {metrics['sharpe_ratio']:.2f}\n"
            report += f"Average Trade Duration: {metrics['avg_trade_duration']:.1f} minutes\n\n"
            
            # Safety metrics
            safety_metrics = self._get_safety_metrics()
            report += "🛡️ SAFETY METRICS:\n"
            report += f"Emergency Stops Triggered: {safety_metrics['emergency_stops']}\n"
            report += f"Safety Violations: {safety_metrics['safety_violations']}\n"
            report += f"Maximum Position Size: {safety_metrics['max_position_size']:.1f}%\n"
            report += f"System Uptime: {safety_metrics['system_uptime']:.1f}%\n\n"
            
            # Validation score breakdown
            report += "🏆 VALIDATION SCORE BREAKDOWN:\n"
            score = metrics['validation_score']
            report += f"Overall Score: {score:.1f}/100\n"
            report += f"Performance Component: {score * 0.4:.1f}/40\n"
            report += f"Safety Component: {score * 0.3:.1f}/30\n"
            report += f"Reliability Component: {score * 0.2:.1f}/20\n"
            report += f"Risk Management Component: {score * 0.1:.1f}/10\n\n"
            
            # Recommendations
            report += "💡 RECOMMENDATIONS:\n"
            if score >= 80:
                report += "• Validation successful - ready for capital scaling\n"
                report += "• Consider increasing trading capital to $200-500\n"
                report += "• Monitor performance during scaling phase\n"
            elif score >= 70:
                report += "• Validation marginal - proceed with caution\n"
                report += "• Continue monitoring before scaling\n"
                report += "• Focus on improving win rate and reducing drawdown\n"
            else:
                report += "• Validation failed - improvements required\n"
                report += "• Address safety violations and performance issues\n"
                report += "• Re-run validation after fixes\n"
            
            return report
            
        except Exception as e:
            logger.error(f"Error generating detailed report: {e}")
            return f"Error generating detailed report: {e}"


if __name__ == "__main__" and PYQT_AVAILABLE:
    app = QApplication(sys.argv)
    app.setStyleSheet(MatrixTheme.get_stylesheet())
    
    monitor = LiveValidationMonitor()
    monitor.show()
    
    sys.exit(app.exec_())
