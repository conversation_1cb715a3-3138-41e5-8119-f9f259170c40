#!/usr/bin/env python3
"""
Position Tracker Module
Tracks and manages trading positions for the Epinnox trading system

This module provides position tracking functionality that was missing
from the original implementation.
"""

import logging
from datetime import datetime
from typing import Dict, List, Optional, Any
from dataclasses import dataclass

# PyQt5 imports for signals
try:
    from PyQt5.QtCore import QObject, pyqtSignal
    PYQT_AVAILABLE = True
except ImportError:
    # Fallback for environments without PyQt5
    class QObject:
        pass
    def pyqtSignal(*args, **kwargs):
        return None
    PYQT_AVAILABLE = False

logger = logging.getLogger(__name__)

@dataclass
class Position:
    """Represents a trading position"""
    symbol: str
    side: str  # 'long' or 'short'
    size: float
    entry_price: float
    current_price: float
    unrealized_pnl: float
    realized_pnl: float
    timestamp: datetime
    position_id: str
    
    @property
    def pnl_percentage(self) -> float:
        """Calculate PnL percentage"""
        if self.entry_price == 0:
            return 0.0
        
        if self.side == 'long':
            return ((self.current_price - self.entry_price) / self.entry_price) * 100
        else:  # short
            return ((self.entry_price - self.current_price) / self.entry_price) * 100

class PositionTracker(QObject):
    """
    Tracks and manages trading positions
    """

    # PyQt signals
    position_updated = pyqtSignal(str, dict)  # position_id, position_data
    position_opened = pyqtSignal(str, dict)   # position_id, position_data
    position_closed = pyqtSignal(str, dict)   # position_id, position_data
    trade_executed = pyqtSignal(dict)         # trade_data
    pnl_updated = pyqtSignal(float, float)    # unrealized_pnl, realized_pnl
    margin_warning = pyqtSignal(str, float)   # symbol, margin_level

    def __init__(self):
        if PYQT_AVAILABLE:
            super().__init__()

        self.positions: Dict[str, Position] = {}
        self.closed_positions: List[Position] = []
        self.total_realized_pnl = 0.0

        logger.info("Position Tracker initialized")
    
    def add_position(self, symbol: str, side: str, size: float, entry_price: float, position_id: str = None) -> str:
        """Add a new position"""
        
        if position_id is None:
            position_id = f"{symbol}_{side}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        position = Position(
            symbol=symbol,
            side=side,
            size=size,
            entry_price=entry_price,
            current_price=entry_price,
            unrealized_pnl=0.0,
            realized_pnl=0.0,
            timestamp=datetime.now(),
            position_id=position_id
        )
        
        self.positions[position_id] = position
        logger.info(f"Added position: {position_id} - {symbol} {side} {size} @ {entry_price}")

        # Emit signal if PyQt is available
        if PYQT_AVAILABLE and hasattr(self, 'position_opened'):
            position_data = {
                'symbol': symbol,
                'side': side,
                'size': size,
                'entry_price': entry_price,
                'current_price': entry_price,
                'unrealized_pnl': 0.0,
                'timestamp': position.timestamp.isoformat()
            }
            self.position_opened.emit(position_id, position_data)

        return position_id
    
    def update_position_price(self, position_id: str, current_price: float):
        """Update the current price of a position"""
        
        if position_id not in self.positions:
            logger.warning(f"Position {position_id} not found")
            return
        
        position = self.positions[position_id]
        position.current_price = current_price
        
        # Calculate unrealized PnL
        if position.side == 'long':
            position.unrealized_pnl = (current_price - position.entry_price) * position.size
        else:  # short
            position.unrealized_pnl = (position.entry_price - current_price) * position.size

        # Emit signal if PyQt is available
        if PYQT_AVAILABLE and hasattr(self, 'position_updated'):
            position_data = {
                'symbol': position.symbol,
                'side': position.side,
                'size': position.size,
                'entry_price': position.entry_price,
                'current_price': current_price,
                'unrealized_pnl': position.unrealized_pnl,
                'timestamp': position.timestamp.isoformat()
            }
            self.position_updated.emit(position_id, position_data)
    
    def close_position(self, position_id: str, exit_price: float) -> Optional[Position]:
        """Close a position and calculate realized PnL"""
        
        if position_id not in self.positions:
            logger.warning(f"Position {position_id} not found")
            return None
        
        position = self.positions[position_id]
        
        # Calculate realized PnL
        if position.side == 'long':
            position.realized_pnl = (exit_price - position.entry_price) * position.size
        else:  # short
            position.realized_pnl = (position.entry_price - exit_price) * position.size
        
        position.current_price = exit_price
        position.unrealized_pnl = 0.0
        
        # Move to closed positions
        self.closed_positions.append(position)
        del self.positions[position_id]
        
        # Update total realized PnL
        self.total_realized_pnl += position.realized_pnl

        logger.info(f"Closed position: {position_id} - Realized PnL: {position.realized_pnl:.2f}")

        # Emit signal if PyQt is available
        if PYQT_AVAILABLE and hasattr(self, 'position_closed'):
            position_data = {
                'symbol': position.symbol,
                'side': position.side,
                'size': position.size,
                'entry_price': position.entry_price,
                'exit_price': exit_price,
                'realized_pnl': position.realized_pnl,
                'timestamp': position.timestamp.isoformat()
            }
            self.position_closed.emit(position_id, position_data)

        return position
    
    def get_position(self, position_id: str) -> Optional[Position]:
        """Get a specific position"""
        return self.positions.get(position_id)
    
    def get_all_positions(self) -> Dict[str, Position]:
        """Get all open positions"""
        return self.positions.copy()
    
    def get_positions_by_symbol(self, symbol: str) -> List[Position]:
        """Get all positions for a specific symbol"""
        return [pos for pos in self.positions.values() if pos.symbol == symbol]
    
    def get_total_unrealized_pnl(self) -> float:
        """Get total unrealized PnL across all positions"""
        return sum(pos.unrealized_pnl for pos in self.positions.values())
    
    def get_total_realized_pnl(self) -> float:
        """Get total realized PnL"""
        return self.total_realized_pnl
    
    def get_position_count(self) -> int:
        """Get number of open positions"""
        return len(self.positions)
    
    def get_position_summary(self) -> Dict[str, Any]:
        """Get summary of all positions"""
        
        total_unrealized = self.get_total_unrealized_pnl()
        position_count = self.get_position_count()
        
        summary = {
            'open_positions': position_count,
            'total_unrealized_pnl': total_unrealized,
            'total_realized_pnl': self.total_realized_pnl,
            'total_pnl': total_unrealized + self.total_realized_pnl,
            'positions': []
        }
        
        for pos_id, position in self.positions.items():
            summary['positions'].append({
                'id': pos_id,
                'symbol': position.symbol,
                'side': position.side,
                'size': position.size,
                'entry_price': position.entry_price,
                'current_price': position.current_price,
                'unrealized_pnl': position.unrealized_pnl,
                'pnl_percentage': position.pnl_percentage,
                'timestamp': position.timestamp.isoformat()
            })
        
        return summary
    
    def update_mark_price(self, symbol: str, mark_price: float):
        """Update mark price for positions of a specific symbol"""

        for position_id, position in self.positions.items():
            if position.symbol == symbol:
                self.update_position_price(position_id, mark_price)

    def update_all_positions(self, price_data: Dict[str, float]):
        """Update all positions with new price data"""

        for position_id, position in self.positions.items():
            if position.symbol in price_data:
                self.update_position_price(position_id, price_data[position.symbol])

        # Emit PnL update signal
        if PYQT_AVAILABLE and hasattr(self, 'pnl_updated'):
            total_unrealized = self.get_total_unrealized_pnl()
            self.pnl_updated.emit(total_unrealized, self.total_realized_pnl)
    
    def emit_trade_executed(self, trade_data: Dict[str, Any]):
        """Emit trade executed signal"""

        if PYQT_AVAILABLE and hasattr(self, 'trade_executed'):
            self.trade_executed.emit(trade_data)

    def check_margin_levels(self, margin_threshold: float = 0.8):
        """Check margin levels and emit warnings if needed"""

        # This is a placeholder - in a real implementation you would
        # calculate actual margin levels based on account balance and positions
        for position_id, position in self.positions.items():
            # Simplified margin check - emit warning if unrealized loss is significant
            if position.unrealized_pnl < -1000:  # Example threshold
                if PYQT_AVAILABLE and hasattr(self, 'margin_warning'):
                    margin_level = 0.7  # Example margin level
                    self.margin_warning.emit(position.symbol, margin_level)

    def clear_all_positions(self):
        """Clear all positions (for testing/reset)"""

        logger.warning("Clearing all positions")
        self.positions.clear()
        self.closed_positions.clear()
        self.total_realized_pnl = 0.0

# Global position tracker instance
position_tracker = PositionTracker()

def get_position_tracker() -> PositionTracker:
    """Get the global position tracker instance"""
    return position_tracker
