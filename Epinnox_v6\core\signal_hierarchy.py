#!/usr/bin/env python3
"""
Signal Hierarchy Module
Manages trading signal priorities and hierarchical decision making

This module provides signal hierarchy functionality that was missing
from the original implementation.
"""

import logging
from datetime import datetime
from typing import Dict, List, Optional, Any, Union
from dataclasses import dataclass
from enum import Enum

logger = logging.getLogger(__name__)

class SignalType(Enum):
    """Types of trading signals"""
    EMERGENCY = "emergency"
    RISK_MANAGEMENT = "risk_management"
    LLM_DECISION = "llm_decision"
    TECHNICAL_ANALYSIS = "technical_analysis"
    MARKET_REGIME = "market_regime"
    SCALPING = "scalping"
    MANUAL = "manual"

class SignalPriority(Enum):
    """Signal priority levels"""
    CRITICAL = 1    # Emergency stops, risk management
    HIGH = 2        # LLM decisions, major signals
    MEDIUM = 3      # Technical analysis, market regime
    LOW = 4         # Scalping signals, minor indicators

@dataclass
class TradingSignal:
    """Represents a trading signal"""
    signal_type: SignalType
    priority: SignalPriority
    action: str  # 'BUY', 'SELL', 'HOLD', 'CLOSE'
    confidence: float  # 0.0 to 1.0
    symbol: str
    timestamp: datetime
    source: str
    metadata: Dict[str, Any]
    
    def __post_init__(self):
        """Validate signal after initialization"""
        if not 0.0 <= self.confidence <= 1.0:
            raise ValueError(f"Confidence must be between 0.0 and 1.0, got {self.confidence}")
        
        if self.action not in ['BUY', 'SELL', 'HOLD', 'CLOSE', 'WAIT']:
            raise ValueError(f"Invalid action: {self.action}")

class SignalHierarchy:
    """
    Manages trading signal priorities and hierarchical decision making
    """
    
    def __init__(self):
        self.signals: List[TradingSignal] = []
        self.signal_weights = {
            SignalType.EMERGENCY: 1.0,
            SignalType.RISK_MANAGEMENT: 0.9,
            SignalType.LLM_DECISION: 0.8,
            SignalType.TECHNICAL_ANALYSIS: 0.6,
            SignalType.MARKET_REGIME: 0.5,
            SignalType.SCALPING: 0.4,
            SignalType.MANUAL: 0.7
        }
        
        logger.info("Signal Hierarchy initialized")
    
    def add_signal(self, signal: TradingSignal):
        """Add a new signal to the hierarchy"""
        
        # Remove old signals from the same source for the same symbol
        self.signals = [s for s in self.signals 
                       if not (s.source == signal.source and s.symbol == signal.symbol)]
        
        # Add new signal
        self.signals.append(signal)
        
        # Keep only recent signals (last 100)
        if len(self.signals) > 100:
            self.signals = self.signals[-100:]
        
        logger.debug(f"Added signal: {signal.signal_type.value} - {signal.action} for {signal.symbol}")
    
    def get_dominant_signal(self, symbol: str) -> Optional[TradingSignal]:
        """Get the dominant signal for a symbol based on priority and confidence"""
        
        # Filter signals for the symbol
        symbol_signals = [s for s in self.signals if s.symbol == symbol]
        
        if not symbol_signals:
            return None
        
        # Sort by priority (lower number = higher priority) and confidence
        symbol_signals.sort(key=lambda s: (s.priority.value, -s.confidence))
        
        return symbol_signals[0]
    
    def get_weighted_decision(self, symbol: str) -> Dict[str, Any]:
        """Get weighted decision based on all signals for a symbol"""
        
        symbol_signals = [s for s in self.signals if s.symbol == symbol]
        
        if not symbol_signals:
            return {
                'action': 'WAIT',
                'confidence': 0.0,
                'reasoning': 'No signals available'
            }
        
        # Calculate weighted scores for each action
        action_scores = {'BUY': 0.0, 'SELL': 0.0, 'HOLD': 0.0, 'CLOSE': 0.0, 'WAIT': 0.0}
        total_weight = 0.0
        
        for signal in symbol_signals:
            weight = self.signal_weights.get(signal.signal_type, 0.5)
            priority_multiplier = 1.0 / signal.priority.value  # Higher priority = higher multiplier
            final_weight = weight * priority_multiplier * signal.confidence
            
            action_scores[signal.action] += final_weight
            total_weight += final_weight
        
        # Normalize scores
        if total_weight > 0:
            for action in action_scores:
                action_scores[action] /= total_weight
        
        # Get dominant action
        dominant_action = max(action_scores, key=action_scores.get)
        confidence = action_scores[dominant_action]
        
        # Build reasoning
        signal_summary = []
        for signal in symbol_signals:
            signal_summary.append(f"{signal.signal_type.value}:{signal.action}({signal.confidence:.2f})")
        
        reasoning = f"Weighted decision from {len(symbol_signals)} signals: {', '.join(signal_summary)}"
        
        return {
            'action': dominant_action,
            'confidence': confidence,
            'reasoning': reasoning,
            'signal_count': len(symbol_signals),
            'action_scores': action_scores
        }
    
    def get_emergency_signals(self) -> List[TradingSignal]:
        """Get all emergency signals"""
        return [s for s in self.signals if s.signal_type == SignalType.EMERGENCY]
    
    def has_emergency_signal(self, symbol: str = None) -> bool:
        """Check if there are any emergency signals"""
        emergency_signals = self.get_emergency_signals()
        
        if symbol:
            emergency_signals = [s for s in emergency_signals if s.symbol == symbol]
        
        return len(emergency_signals) > 0
    
    def clear_signals(self, symbol: str = None, signal_type: SignalType = None):
        """Clear signals by symbol and/or type"""
        
        if symbol and signal_type:
            self.signals = [s for s in self.signals 
                           if not (s.symbol == symbol and s.signal_type == signal_type)]
        elif symbol:
            self.signals = [s for s in self.signals if s.symbol != symbol]
        elif signal_type:
            self.signals = [s for s in self.signals if s.signal_type != signal_type]
        else:
            self.signals.clear()
        
        logger.info(f"Cleared signals - symbol: {symbol}, type: {signal_type}")
    
    def get_signal_summary(self) -> Dict[str, Any]:
        """Get summary of all signals"""
        
        summary = {
            'total_signals': len(self.signals),
            'by_type': {},
            'by_priority': {},
            'by_symbol': {}
        }
        
        for signal in self.signals:
            # By type
            type_name = signal.signal_type.value
            if type_name not in summary['by_type']:
                summary['by_type'][type_name] = 0
            summary['by_type'][type_name] += 1
            
            # By priority
            priority_name = signal.priority.name
            if priority_name not in summary['by_priority']:
                summary['by_priority'][priority_name] = 0
            summary['by_priority'][priority_name] += 1
            
            # By symbol
            if signal.symbol not in summary['by_symbol']:
                summary['by_symbol'][signal.symbol] = 0
            summary['by_symbol'][signal.symbol] += 1
        
        return summary

# Global signal hierarchy instance
signal_hierarchy = SignalHierarchy()

def get_signal_hierarchy() -> SignalHierarchy:
    """Get the global signal hierarchy instance"""
    return signal_hierarchy

def create_signal(signal_type: SignalType, action: str, confidence: float, 
                 symbol: str, source: str, metadata: Dict[str, Any] = None) -> TradingSignal:
    """Helper function to create a trading signal"""
    
    # Map signal type to priority
    priority_map = {
        SignalType.EMERGENCY: SignalPriority.CRITICAL,
        SignalType.RISK_MANAGEMENT: SignalPriority.CRITICAL,
        SignalType.LLM_DECISION: SignalPriority.HIGH,
        SignalType.MANUAL: SignalPriority.HIGH,
        SignalType.TECHNICAL_ANALYSIS: SignalPriority.MEDIUM,
        SignalType.MARKET_REGIME: SignalPriority.MEDIUM,
        SignalType.SCALPING: SignalPriority.LOW
    }
    
    priority = priority_map.get(signal_type, SignalPriority.MEDIUM)
    
    return TradingSignal(
        signal_type=signal_type,
        priority=priority,
        action=action,
        confidence=confidence,
        symbol=symbol,
        timestamp=datetime.now(),
        source=source,
        metadata=metadata or {}
    )
