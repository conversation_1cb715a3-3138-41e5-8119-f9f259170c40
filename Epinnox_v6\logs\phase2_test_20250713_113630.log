2025-07-13 11:36:30,780 - __main__ - INFO - [PHASE2_TEST] Loaded configuration from config/phase2_autonomous_config.yaml
2025-07-13 11:36:30,780 - __main__ - INFO - [PHASE2_TEST] Phase 2 Integration Tester initialized
2025-07-13 11:36:30,780 - __main__ - INFO - [PHASE2_TEST] Starting comprehensive Phase 2 integration tests
2025-07-13 11:36:30,781 - __main__ - INFO - [PHASE2_TEST] Running Test 1: Unified LLM Integration...
2025-07-13 11:36:32,725 - core.unified_llm_manager - INFO - [UNIFIED_LLM] Unified LLM Manager initialized
2025-07-13 11:36:32,725 - core.unified_llm_manager - INFO - [UNIFIED_LLM] Initializing LLM providers...
2025-07-13 11:36:32,741 - config.autonomous_config - INFO - Configuration loaded from configs/autonomous_trading.yaml
2025-07-13 11:36:32,745 - llama.runner - INFO - Loaded OpenAI API key from models_config.yaml
2025-07-13 11:36:32,745 - llama.runner - INFO - Using model path: C:\Users\<USER>\.lmstudio\models\lmstudio-community\Phi-3.1-mini-128k-instruct-GGUF\Phi-3.1-mini-128k-instruct-Q4_K_M.gguf
2025-07-13 11:36:32,745 - llama.runner - INFO - Using guard path: C:\Users\<USER>\Documents\dev\Epinnox_v6\models\Llama-Guard-3-8B.gguf
2025-07-13 11:36:32,745 - llama.runner - INFO - Using binary path: C:\Users\<USER>\Documents\dev\llama.cpp\main
2025-07-13 11:36:32,751 - core.unified_llm_manager - INFO - [UNIFIED_LLM] ChatGPT provider initialized
2025-07-13 11:36:32,756 - llama.lmstudio_runner - INFO - Loaded LMStudio configuration from models_config.yaml
2025-07-13 11:36:34,844 - llama.lmstudio_runner - INFO - Discovered 8 models: ['phi-3.1-mini-128k-instruct', 'openthinker3-7b', 'mistral-7b-claude-chat', 'fine_tuned_qwen1.7b', 'tsfc-model-0.0.1', 'codestral-22b-v0.1', 'deepseek-r1-distill-qwen-7b', 'text-embedding-nomic-embed-text-v1.5']
2025-07-13 11:36:34,845 - llama.lmstudio_runner - INFO - Selected preferred model from config: phi-3.1-mini-128k-instruct
2025-07-13 11:36:34,845 - llama.lmstudio_runner - INFO - Initialized LMStudio with model: phi-3.1-mini-128k-instruct
2025-07-13 11:36:36,915 - core.unified_llm_manager - INFO - [UNIFIED_LLM] LMStudio provider initialized
2025-07-13 11:36:36,919 - core.unified_llm_manager - WARNING - [UNIFIED_LLM] Failed to initialize Transformers: __init__() missing 1 required positional argument: 'model_path'
2025-07-13 11:36:36,919 - core.unified_llm_manager - WARNING - [UNIFIED_LLM] Failed to initialize Llama: __init__() missing 1 required positional argument: 'bin_path'
2025-07-13 11:36:36,920 - llama.mock_runner - INFO - Initialized MockRunner with personality: {'caution': 0.8629211787700182, 'optimism': 0.5654160063881706, 'consistency': 0.7769693555485777, 'detail_level': 0.8654672383240676, 'confidence_bias': -0.8698015930934586}
2025-07-13 11:36:36,920 - core.unified_llm_manager - INFO - [UNIFIED_LLM] Mock provider initialized
2025-07-13 11:36:36,920 - core.unified_llm_manager - INFO - [UNIFIED_LLM] Selected provider: mock (score: 109.90)
2025-07-13 11:36:36,920 - core.unified_llm_manager - INFO - [UNIFIED_LLM] Health monitoring started
2025-07-13 11:36:36,921 - core.unified_llm_manager - INFO - [UNIFIED_LLM] Initialized with 3 providers
2025-07-13 11:36:36,921 - core.unified_llm_manager - INFO - [UNIFIED_LLM] Current provider: LLMProvider.MOCK
2025-07-13 11:36:36,921 - core.standardized_prompt_handler - INFO - [PROMPT_HANDLER] Standardized prompt handler initialized
2025-07-13 11:36:36,921 - llama.mock_runner - INFO - Running mock inference
2025-07-13 11:36:36,921 - core.unified_llm_manager - ERROR - [UNIFIED_LLM] Failed to parse response: 'LLMResponse' object has no attribute 'split'
2025-07-13 11:36:36,921 - core.unified_llm_manager - WARNING - [UNIFIED_LLM] mock response below confidence threshold
2025-07-13 11:36:36,921 - core.unified_llm_manager - WARNING - [UNIFIED_LLM] Failing over to chatgpt
2025-07-13 11:36:36,922 - llama.chatgpt_runner - ERROR - ChatGPT inference failed
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\dev\Epinnox_v6\llama\chatgpt_runner.py", line 34, in run_inference
    raise ImportError("OpenAI package not available or client not initialized")
ImportError: OpenAI package not available or client not initialized
2025-07-13 11:36:36,922 - core.unified_llm_manager - ERROR - [UNIFIED_LLM] Provider call failed: OpenAI package not available or client not initialized
2025-07-13 11:36:36,922 - core.unified_llm_manager - WARNING - [UNIFIED_LLM] Failing over to lmstudio
2025-07-13 11:36:42,019 - __main__ - INFO - [PHASE2_TEST] LLM response received from lmstudio
2025-07-13 11:36:42,019 - core.unified_llm_manager - INFO - [UNIFIED_LLM] Unified LLM Manager initialized
2025-07-13 11:36:42,019 - core.standardized_prompt_handler - INFO - [PROMPT_HANDLER] Standardized prompt handler initialized
2025-07-13 11:36:42,019 - core.autonomous_llm_integration - INFO - [AUTONOMOUS_LLM] Autonomous LLM Integration initialized
2025-07-13 11:36:42,019 - core.autonomous_llm_integration - INFO - [AUTONOMOUS_LLM] Initializing autonomous LLM integration...
2025-07-13 11:36:42,019 - core.unified_llm_manager - INFO - [UNIFIED_LLM] Initializing LLM providers...
2025-07-13 11:36:42,023 - core.unified_llm_manager - INFO - [UNIFIED_LLM] ChatGPT provider initialized
2025-07-13 11:36:42,026 - llama.lmstudio_runner - INFO - Loaded LMStudio configuration from models_config.yaml
2025-07-13 11:36:44,077 - llama.lmstudio_runner - INFO - Discovered 8 models: ['phi-3.1-mini-128k-instruct', 'openthinker3-7b', 'mistral-7b-claude-chat', 'fine_tuned_qwen1.7b', 'tsfc-model-0.0.1', 'codestral-22b-v0.1', 'deepseek-r1-distill-qwen-7b', 'text-embedding-nomic-embed-text-v1.5']
2025-07-13 11:36:44,078 - llama.lmstudio_runner - INFO - Selected preferred model from config: phi-3.1-mini-128k-instruct
2025-07-13 11:36:44,078 - llama.lmstudio_runner - INFO - Initialized LMStudio with model: phi-3.1-mini-128k-instruct
2025-07-13 11:36:46,103 - core.unified_llm_manager - INFO - [UNIFIED_LLM] LMStudio provider initialized
2025-07-13 11:36:46,103 - core.unified_llm_manager - WARNING - [UNIFIED_LLM] Failed to initialize Transformers: __init__() missing 1 required positional argument: 'model_path'
2025-07-13 11:36:46,104 - core.unified_llm_manager - WARNING - [UNIFIED_LLM] Failed to initialize Llama: __init__() missing 1 required positional argument: 'bin_path'
2025-07-13 11:36:46,104 - llama.mock_runner - INFO - Initialized MockRunner with personality: {'caution': 0.8982876431877033, 'optimism': 0.525415571328258, 'consistency': 0.8303212400628894, 'detail_level': 0.8306928090710799, 'confidence_bias': 2.4178967257018087}
2025-07-13 11:36:46,104 - core.unified_llm_manager - INFO - [UNIFIED_LLM] Mock provider initialized
2025-07-13 11:36:46,105 - core.unified_llm_manager - INFO - [UNIFIED_LLM] Selected provider: mock (score: 109.90)
2025-07-13 11:36:46,105 - core.unified_llm_manager - INFO - [UNIFIED_LLM] Health monitoring started
2025-07-13 11:36:46,105 - core.unified_llm_manager - INFO - [UNIFIED_LLM] Initialized with 3 providers
2025-07-13 11:36:46,106 - core.unified_llm_manager - INFO - [UNIFIED_LLM] Current provider: LLMProvider.MOCK
2025-07-13 11:36:46,106 - core.autonomous_llm_integration - INFO - [AUTONOMOUS_LLM] Autonomous LLM integration initialized successfully
2025-07-13 11:36:46,106 - core.unified_llm_manager - INFO - [UNIFIED_LLM] Unified LLM Manager shutdown
2025-07-13 11:36:46,106 - __main__ - INFO - [PHASE2_TEST] Unified LLM integration test completed
2025-07-13 11:36:46,109 - __main__ - INFO - [PHASE2_TEST] Running Test 2: Autonomous Position Manager...
2025-07-13 11:36:46,111 - core.autonomous_position_manager - INFO - [POSITION_MANAGER] Autonomous Position Manager initialized
2025-07-13 11:36:46,111 - core.autonomous_position_manager - INFO - [POSITION_MANAGER] Position monitoring started (interval: 5s)
2025-07-13 11:36:46,112 - core.autonomous_position_manager - INFO - [POSITION_MANAGER] Autonomous Position Manager ready
2025-07-13 11:36:46,112 - core.autonomous_position_manager - INFO - [POSITION_MANAGER] Added position pos_1_1752424606: LONG 0.001 BTC/USDT:USDT @ 50000.0000
2025-07-13 11:36:46,112 - core.autonomous_position_manager - INFO - [POSITION_MANAGER] SL: 49000.0000, TP: 52000.0000
2025-07-13 11:36:46,112 - core.autonomous_position_manager - INFO - [POSITION_MANAGER] Updated SL for pos_1_1752424606: 49000.0000
2025-07-13 11:36:46,112 - core.autonomous_position_manager - INFO - [POSITION_MANAGER] Updated TP for pos_1_1752424606: 52000.0000
2025-07-13 11:36:46,112 - core.autonomous_position_manager - INFO - [POSITION_MANAGER] Exiting position pos_1_1752424606 - Reason: manual
2025-07-13 11:36:46,112 - core.autonomous_position_manager - INFO - [POSITION_MANAGER] Successfully exited position pos_1_1752424606
2025-07-13 11:36:46,112 - core.autonomous_position_manager - INFO - [POSITION_MANAGER] Position monitoring stopped
2025-07-13 11:36:46,112 - core.autonomous_position_manager - INFO - [POSITION_MANAGER] Autonomous Position Manager shutdown complete
2025-07-13 11:36:46,112 - __main__ - INFO - [PHASE2_TEST] Autonomous position manager test completed
2025-07-13 11:36:46,113 - __main__ - INFO - [PHASE2_TEST] Running Test 3: Position Watchdog System...
2025-07-13 11:36:46,115 - core.position_watchdog - INFO - [WATCHDOG] Position Watchdog initialized
2025-07-13 11:36:46,115 - core.position_watchdog - INFO - [WATCHDOG] Monitoring started (scan: 10s, health: 30s)
2025-07-13 11:36:46,115 - core.position_watchdog - INFO - [WATCHDOG] Position Watchdog ready
2025-07-13 11:36:46,116 - core.position_watchdog - WARNING - [WATCHDOG] 1 positions in critical state
2025-07-13 11:36:46,116 - core.position_watchdog - WARNING - [WATCHDOG] Generated 1 critical alerts
2025-07-13 11:36:48,138 - core.position_watchdog - INFO - [WATCHDOG] Forcing immediate health check
2025-07-13 11:36:48,139 - core.position_watchdog - WARNING - [WATCHDOG] 1 positions in critical state
2025-07-13 11:36:48,140 - core.position_watchdog - WARNING - [WATCHDOG] Generated 1 critical alerts
2025-07-13 11:36:48,140 - core.position_watchdog - INFO - [WATCHDOG] Monitoring stopped
2025-07-13 11:36:48,141 - core.position_watchdog - INFO - [WATCHDOG] Position Watchdog shutdown complete
2025-07-13 11:36:48,141 - __main__ - INFO - [PHASE2_TEST] Position watchdog test completed
2025-07-13 11:36:48,145 - __main__ - INFO - [PHASE2_TEST] Running Test 4: Error Recovery System...
2025-07-13 11:36:48,148 - core.error_recovery_system - INFO - [ERROR_RECOVERY] Error Recovery System initialized
2025-07-13 11:36:48,148 - core.error_recovery_system - INFO - [ERROR_RECOVERY] Health monitoring started
2025-07-13 11:36:48,148 - core.error_recovery_system - INFO - [ERROR_RECOVERY] Error Recovery System ready
2025-07-13 11:36:48,149 - core.error_recovery_system - WARNING - [ERROR_RECOVERY] Retry 1/3 for test_function_with_retries in 0.74s: Test failure for retry testing
2025-07-13 11:36:48,900 - core.error_recovery_system - INFO - [ERROR_RECOVERY] Error Recovery System shutdown complete
2025-07-13 11:36:48,900 - __main__ - INFO - [PHASE2_TEST] Error recovery system test completed
2025-07-13 11:36:48,903 - __main__ - INFO - [PHASE2_TEST] Running Test 5: Component Integration...
2025-07-13 11:36:48,905 - core.autonomous_trading_orchestrator - INFO - Autonomous Trading Orchestrator initialized in paper mode
2025-07-13 11:36:48,905 - core.autonomous_trading_orchestrator - INFO - Initializing autonomous trading system...
2025-07-13 11:36:48,905 - core.error_recovery_system - INFO - [ERROR_RECOVERY] Error Recovery System initialized
2025-07-13 11:36:48,905 - core.error_recovery_system - INFO - [ERROR_RECOVERY] Health monitoring started
2025-07-13 11:36:48,905 - core.error_recovery_system - INFO - [ERROR_RECOVERY] Error Recovery System ready
2025-07-13 11:36:48,927 - core.websocket_manager - INFO - WebSocket manager initialized for htx_market: wss://api.huobi.pro/ws
2025-07-13 11:36:48,927 - core.websocket_manager - INFO - WebSocket manager initialized for htx_futures: wss://api.hbdm.com/swap-ws
2025-07-13 11:36:48,927 - core.websocket_manager - INFO - WebSocket manager initialized for htx_backup: wss://api-aws.huobi.pro/ws
2025-07-13 11:36:48,927 - data.market_data_manager - INFO - Initialized 3 WebSocket endpoints
2025-07-13 11:36:48,927 - data.market_data_manager - INFO - Market data manager initialized for htx
2025-07-13 11:36:48,927 - data.market_data_manager - INFO - Starting market data manager...
2025-07-13 11:36:48,929 - data.market_data_manager - INFO - Started WebSocket connection: market
2025-07-13 11:36:48,929 - data.market_data_manager - INFO - Started WebSocket connection: futures
2025-07-13 11:36:48,929 - data.market_data_manager - INFO - Started WebSocket connection: backup
2025-07-13 11:36:48,929 - core.websocket_manager - INFO - htx_market: Connecting to wss://api.huobi.pro/ws (attempt 1)
2025-07-13 11:36:48,985 - core.websocket_manager - INFO - htx_futures: Connecting to wss://api.hbdm.com/swap-ws (attempt 1)
2025-07-13 11:36:48,991 - core.websocket_manager - INFO - htx_backup: Connecting to wss://api-aws.huobi.pro/ws (attempt 1)
2025-07-13 11:36:50,843 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:36:50,950 - core.unified_llm_manager - INFO - [UNIFIED_LLM] Unified LLM Manager initialized
2025-07-13 11:36:50,950 - core.standardized_prompt_handler - INFO - [PROMPT_HANDLER] Standardized prompt handler initialized
2025-07-13 11:36:50,950 - core.autonomous_llm_integration - INFO - [AUTONOMOUS_LLM] Autonomous LLM Integration initialized
2025-07-13 11:36:50,950 - core.autonomous_llm_integration - INFO - [AUTONOMOUS_LLM] Initializing autonomous LLM integration...
2025-07-13 11:36:50,950 - core.unified_llm_manager - INFO - [UNIFIED_LLM] Initializing LLM providers...
2025-07-13 11:36:50,954 - core.unified_llm_manager - INFO - [UNIFIED_LLM] ChatGPT provider initialized
2025-07-13 11:36:50,958 - llama.lmstudio_runner - INFO - Loaded LMStudio configuration from models_config.yaml
2025-07-13 11:36:53,015 - llama.lmstudio_runner - INFO - Discovered 8 models: ['phi-3.1-mini-128k-instruct', 'openthinker3-7b', 'mistral-7b-claude-chat', 'fine_tuned_qwen1.7b', 'tsfc-model-0.0.1', 'codestral-22b-v0.1', 'deepseek-r1-distill-qwen-7b', 'text-embedding-nomic-embed-text-v1.5']
2025-07-13 11:36:53,015 - llama.lmstudio_runner - INFO - Selected preferred model from config: phi-3.1-mini-128k-instruct
2025-07-13 11:36:53,015 - llama.lmstudio_runner - INFO - Initialized LMStudio with model: phi-3.1-mini-128k-instruct
2025-07-13 11:36:55,075 - core.unified_llm_manager - INFO - [UNIFIED_LLM] LMStudio provider initialized
2025-07-13 11:36:55,076 - core.unified_llm_manager - WARNING - [UNIFIED_LLM] Failed to initialize Transformers: __init__() missing 1 required positional argument: 'model_path'
2025-07-13 11:36:55,076 - core.unified_llm_manager - WARNING - [UNIFIED_LLM] Failed to initialize Llama: __init__() missing 1 required positional argument: 'bin_path'
2025-07-13 11:36:55,077 - llama.mock_runner - INFO - Initialized MockRunner with personality: {'caution': 0.8447656608579375, 'optimism': 0.5709150572016498, 'consistency': 0.6524431035400718, 'detail_level': 0.6936920334426628, 'confidence_bias': -2.3890646392518864}
2025-07-13 11:36:55,077 - core.unified_llm_manager - INFO - [UNIFIED_LLM] Mock provider initialized
2025-07-13 11:36:55,078 - core.unified_llm_manager - INFO - [UNIFIED_LLM] Selected provider: mock (score: 109.90)
2025-07-13 11:36:55,078 - core.unified_llm_manager - INFO - [UNIFIED_LLM] Health monitoring started
2025-07-13 11:36:55,078 - core.unified_llm_manager - INFO - [UNIFIED_LLM] Initialized with 3 providers
2025-07-13 11:36:55,078 - core.unified_llm_manager - INFO - [UNIFIED_LLM] Current provider: LLMProvider.MOCK
2025-07-13 11:36:55,078 - core.autonomous_llm_integration - INFO - [AUTONOMOUS_LLM] Autonomous LLM integration initialized successfully
2025-07-13 11:36:55,094 - utils.cache_manager - INFO - Cache manager initialized: cache, max_age=24h, max_size=100MB
2025-07-13 11:36:55,095 - utils.cache_manager - INFO - Cache manager initialized: cache, max_age=24h, max_size=100MB
2025-07-13 11:36:55,095 - core.llm_orchestrator - INFO - LLM Prompt Orchestrator initialized
2025-07-13 11:36:57,981 - ml.models - INFO - TensorFlow available for LSTM models
2025-07-13 11:36:57,981 - ml.models - INFO - ML Model Manager initialized
2025-07-13 11:36:58,102 - absl - WARNING - Compiled the loaded model, but the compiled metrics have yet to be built. `model.compile_metrics` will be empty until you train or evaluate the model.
2025-07-13 11:36:58,132 - ml.models - ERROR - Error loading models: Could not locate function 'mse'. Make sure custom classes are decorated with `@keras.saving.register_keras_serializable()`. Full object config: {'module': 'keras.metrics', 'class_name': 'function', 'config': 'mse', 'registered_name': 'mse'}
2025-07-13 11:36:58,132 - core.autonomous_position_manager - INFO - [POSITION_MANAGER] Autonomous Position Manager initialized
2025-07-13 11:36:58,147 - core.adaptive_risk - INFO - TA-Lib not available, using pandas-ta fallback
2025-07-13 11:36:58,148 - core.adaptive_risk - INFO - Initialized adaptive risk manager
2025-07-13 11:36:58,154 - execution.unified_execution_engine - INFO - Unified execution engine initialized in paper mode
2025-07-13 11:36:58,155 - execution.execution_adapter - INFO - Execution adapter initialized in paper mode
2025-07-13 11:36:58,158 - core.autonomous_position_manager - INFO - [POSITION_MANAGER] Position monitoring started (interval: 5s)
2025-07-13 11:36:58,159 - core.autonomous_position_manager - INFO - [POSITION_MANAGER] Autonomous Position Manager ready
2025-07-13 11:36:58,160 - core.position_watchdog - INFO - [WATCHDOG] Position Watchdog initialized
2025-07-13 11:36:58,160 - core.position_watchdog - INFO - [WATCHDOG] Monitoring started (scan: 10s, health: 30s)
2025-07-13 11:36:58,161 - core.position_watchdog - INFO - [WATCHDOG] Position Watchdog ready
2025-07-13 11:36:58,164 - portfolio.portfolio_manager - INFO - Portfolio manager initialized with $10000.00 balance
2025-07-13 11:36:58,167 - core.autonomous_trading_orchestrator - WARNING - No market data available - this is expected during testing
2025-07-13 11:36:58,168 - __main__ - INFO - [PHASE2_TEST] Component unified_llm_integration successfully initialized
2025-07-13 11:36:58,168 - __main__ - INFO - [PHASE2_TEST] Component autonomous_position_manager successfully initialized
2025-07-13 11:36:58,168 - __main__ - INFO - [PHASE2_TEST] Component position_watchdog successfully initialized
2025-07-13 11:36:58,168 - __main__ - INFO - [PHASE2_TEST] Component error_recovery_system successfully initialized
2025-07-13 11:36:58,170 - __main__ - INFO - [PHASE2_TEST] System health check: False
2025-07-13 11:36:58,170 - core.autonomous_trading_orchestrator - INFO - Stopping autonomous trading...
2025-07-13 11:36:58,171 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:36:58,171 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:36:58,171 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:36:58,171 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:36:58,171 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:36:58,171 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:36:58,172 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:36:58,172 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:36:58,172 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:36:58,172 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:36:58,172 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:36:58,172 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:36:58,172 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:36:58,172 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:36:58,172 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:36:58,172 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:36:58,172 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:36:58,172 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:36:58,172 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:36:58,173 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:36:58,173 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:36:58,173 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:36:58,173 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:36:58,173 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:36:58,173 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:36:58,173 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:36:58,173 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:36:58,173 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:36:58,173 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:36:58,173 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:36:58,173 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:36:58,174 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:36:58,174 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:36:58,174 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:36:58,174 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:36:58,174 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:36:58,174 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:36:58,174 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:36:58,174 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:36:58,174 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:36:58,174 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:36:58,174 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:36:58,174 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:36:58,174 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:36:58,174 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:36:58,175 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:36:58,175 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:36:58,175 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:36:58,175 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:36:58,175 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:36:58,175 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:36:58,175 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:36:58,175 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:36:58,175 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:36:58,175 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:36:58,175 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:36:58,175 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:36:58,176 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:36:58,176 - core.websocket_manager - ERROR - htx_futures: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:36:58,176 - core.websocket_manager - ERROR - htx_futures: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:36:58,211 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:36:58,223 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:36:58,313 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:36:58,324 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:36:58,348 - core.websocket_manager - ERROR - htx_futures: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:36:58,352 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:36:58,352 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:36:58,359 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:36:58,360 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:36:58,360 - core.websocket_manager - ERROR - htx_futures: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:36:58,413 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:36:58,422 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:36:58,512 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:36:58,522 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:36:58,613 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:36:58,624 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:36:58,712 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:36:58,813 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:36:58,822 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:36:58,912 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:36:58,924 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:36:59,013 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:36:59,022 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:36:59,114 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:36:59,124 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:36:59,414 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:36:59,422 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:36:59,621 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:36:59,622 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:36:59,622 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:36:59,713 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:36:59,718 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:36:59,724 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:36:59,727 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:36:59,813 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:36:59,814 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:36:59,822 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:36:59,822 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:36:59,913 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:36:59,914 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:36:59,923 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:36:59,923 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:37:00,013 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:37:00,024 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:37:00,112 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:37:00,123 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:37:00,174 - core.autonomous_trading_orchestrator - INFO - Autonomous trading stopped gracefully
2025-07-13 11:37:00,174 - __main__ - INFO - [PHASE2_TEST] Component integration test completed
2025-07-13 11:37:00,178 - __main__ - INFO - [PHASE2_TEST] Running Test 6: End-to-End Workflow...
2025-07-13 11:37:00,178 - __main__ - INFO - [PHASE2_TEST] Testing end-to-end workflow simulation
2025-07-13 11:37:00,178 - __main__ - INFO - [PHASE2_TEST] Simulating: Market data collection
2025-07-13 11:37:00,212 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:37:00,223 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:37:00,287 - __main__ - INFO - [PHASE2_TEST] Simulating: LLM analysis and decision
2025-07-13 11:37:00,400 - __main__ - INFO - [PHASE2_TEST] Simulating: Position entry execution
2025-07-13 11:37:00,411 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:37:00,423 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:37:00,513 - __main__ - INFO - [PHASE2_TEST] Simulating: Position monitoring
2025-07-13 11:37:00,611 - __main__ - INFO - [PHASE2_TEST] Simulating: Risk assessment
2025-07-13 11:37:00,612 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:37:00,622 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:37:00,713 - __main__ - INFO - [PHASE2_TEST] Simulating: Position exit execution
2025-07-13 11:37:00,713 - core.websocket_manager - ERROR - htx_market: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:37:00,723 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:37:00,723 - core.websocket_manager - ERROR - htx_backup: Error handling message: 'utf-8' codec can't decode byte 0x8b in position 1: invalid start byte
2025-07-13 11:37:00,811 - __main__ - INFO - [PHASE2_TEST] End-to-end workflow test completed
2025-07-13 11:37:00,817 - __main__ - INFO - [PHASE2_TEST] Running Test 7: Safety Systems Validation...
2025-07-13 11:37:00,817 - __main__ - INFO - [PHASE2_TEST] Testing safety systems
2025-07-13 11:37:00,817 - __main__ - INFO - [PHASE2_TEST] Safety systems test completed
2025-07-13 11:37:00,821 - __main__ - INFO - [PHASE2_TEST] Running Test 8: Performance Validation...
2025-07-13 11:37:00,821 - __main__ - INFO - [PHASE2_TEST] Testing performance validation
2025-07-13 11:37:00,821 - __main__ - INFO - [PHASE2_TEST] Performance validation test completed
2025-07-13 11:37:00,824 - __main__ - INFO - [PHASE2_TEST] Phase 2 integration tests completed - Overall: PASSED
