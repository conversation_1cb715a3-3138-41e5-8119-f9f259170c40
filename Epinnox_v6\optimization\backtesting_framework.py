#!/usr/bin/env python3
"""
📊 BACKTESTING FRAMEWORK
Comprehensive backtesting system for hyperparameter optimization validation

FEATURES:
- Historical data simulation
- Performance metrics calculation
- Risk analysis and drawdown calculation
- Multi-timeframe backtesting
- Parameter sensitivity analysis
"""

import logging
import numpy as np
import pandas as pd
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime, timedelta
from dataclasses import dataclass
import warnings
warnings.filterwarnings('ignore')

logger = logging.getLogger(__name__)


@dataclass
class BacktestResult:
    """Backtesting result container"""
    total_return: float
    sharpe_ratio: float
    max_drawdown: float
    win_rate: float
    profit_factor: float
    total_trades: int
    avg_trade_duration: float
    volatility: float
    calmar_ratio: float
    sortino_ratio: float


class BacktestingFramework:
    """
    CRITICAL: Comprehensive backtesting framework for parameter optimization
    
    Provides realistic simulation environment for testing trading parameters
    with comprehensive performance metrics and risk analysis.
    """
    
    def __init__(self):
        self.market_data = None
        self.trading_costs = 0.001  # 0.1% trading costs
        self.initial_capital = 10000.0
        
        logger.info("Backtesting Framework initialized")
    
    def load_market_data(self, data: pd.DataFrame):
        """Load historical market data for backtesting"""
        try:
            # Ensure required columns exist
            required_columns = ['timestamp', 'open', 'high', 'low', 'close', 'volume']
            missing_columns = [col for col in required_columns if col not in data.columns]
            
            if missing_columns:
                logger.warning(f"Missing columns in market data: {missing_columns}")
                # Create mock data if columns are missing
                data = self._create_mock_market_data(len(data))
            
            self.market_data = data.copy()
            self.market_data['timestamp'] = pd.to_datetime(self.market_data['timestamp'])
            self.market_data = self.market_data.sort_values('timestamp').reset_index(drop=True)
            
            logger.info(f"Market data loaded: {len(self.market_data)} records")
            
        except Exception as e:
            logger.error(f"Error loading market data: {e}")
            # Create mock data as fallback
            self.market_data = self._create_mock_market_data(1000)
    
    def _create_mock_market_data(self, n_periods: int = 1000) -> pd.DataFrame:
        """Create mock market data for testing"""
        np.random.seed(42)
        
        # Generate realistic price movements
        base_price = 0.35
        returns = np.random.normal(0.0001, 0.02, n_periods)  # Small positive drift with volatility
        prices = [base_price]
        
        for ret in returns:
            new_price = prices[-1] * (1 + ret)
            prices.append(max(new_price, 0.01))  # Prevent negative prices
        
        prices = prices[1:]  # Remove initial price
        
        # Create OHLCV data
        data = []
        for i, close in enumerate(prices):
            high = close * (1 + abs(np.random.normal(0, 0.005)))
            low = close * (1 - abs(np.random.normal(0, 0.005)))
            open_price = prices[i-1] if i > 0 else close
            volume = np.random.uniform(50000, 200000)
            
            data.append({
                'timestamp': datetime.now() - timedelta(minutes=n_periods-i),
                'open': open_price,
                'high': high,
                'low': low,
                'close': close,
                'volume': volume
            })
        
        return pd.DataFrame(data)
    
    def run_scalper_gpt_backtest(self, params: Dict[str, Any], data: pd.DataFrame) -> Dict[str, float]:
        """Run backtest for ScalperGPT parameters"""
        try:
            # Extract parameters
            spread_threshold = params.get('spread_quality_threshold', 7.0)
            decision_threshold = params.get('decision_quality_threshold', 8.0)
            momentum_weight = params.get('momentum_weight', 0.3)
            volatility_weight = params.get('volatility_weight', 0.2)
            volume_weight = params.get('volume_weight', 0.2)
            spread_weight = params.get('spread_weight', 0.3)
            lookback_periods = params.get('analysis_lookback_periods', 10)
            
            # Calculate technical indicators
            data = self._calculate_technical_indicators(data, lookback_periods)
            
            # Generate trading signals
            signals = self._generate_scalper_signals(
                data, spread_threshold, decision_threshold,
                momentum_weight, volatility_weight, volume_weight, spread_weight
            )
            
            # Execute trades and calculate performance
            trades = self._execute_trades(data, signals)
            performance = self._calculate_performance_metrics(trades)
            
            return performance
            
        except Exception as e:
            logger.error(f"Error in ScalperGPT backtest: {e}")
            return self._get_default_performance()
    
    def run_symbol_scanner_backtest(self, params: Dict[str, Any], data: pd.DataFrame) -> Dict[str, float]:
        """Run backtest for Symbol Scanner parameters"""
        try:
            # Extract parameters
            quality_threshold = params.get('quality_threshold', 75.0)
            spread_weight = params.get('spread_score_weight', 0.3)
            atr_weight = params.get('tick_atr_score_weight', 0.25)
            flow_weight = params.get('flow_score_weight', 0.1)
            depth_weight = params.get('depth_score_weight', 0.25)
            volume_weight = params.get('volume_score_weight', 0.1)
            min_volume = params.get('min_volume_threshold', 50000)
            max_spread = params.get('max_spread_threshold', 0.005)
            
            # Calculate symbol quality scores
            quality_scores = self._calculate_symbol_quality(
                data, spread_weight, atr_weight, flow_weight, depth_weight, volume_weight
            )
            
            # Filter by quality threshold
            high_quality_periods = quality_scores >= quality_threshold
            
            # Generate signals only for high-quality periods
            signals = self._generate_quality_filtered_signals(data, high_quality_periods)
            
            # Execute trades and calculate performance
            trades = self._execute_trades(data, signals)
            performance = self._calculate_performance_metrics(trades)
            
            return performance
            
        except Exception as e:
            logger.error(f"Error in Symbol Scanner backtest: {e}")
            return self._get_default_performance()
    
    def run_timer_coordination_backtest(self, params: Dict[str, Any], data: pd.DataFrame) -> Dict[str, float]:
        """Run backtest for Timer Coordination parameters"""
        try:
            # Extract parameters
            decision_interval = params.get('decision_loop_interval', 30.0)
            analysis_interval = params.get('scalper_analysis_interval', 5.0)
            scanner_interval = params.get('symbol_scanner_interval', 30.0)
            
            # Simulate timer-based decision making
            decision_points = self._get_timer_decision_points(data, decision_interval)
            analysis_points = self._get_timer_decision_points(data, analysis_interval)
            
            # Generate signals based on timer coordination
            signals = self._generate_timer_coordinated_signals(data, decision_points, analysis_points)
            
            # Execute trades and calculate performance
            trades = self._execute_trades(data, signals)
            performance = self._calculate_performance_metrics(trades)
            
            return performance
            
        except Exception as e:
            logger.error(f"Error in Timer Coordination backtest: {e}")
            return self._get_default_performance()
    
    def _calculate_technical_indicators(self, data: pd.DataFrame, lookback: int) -> pd.DataFrame:
        """Calculate technical indicators for backtesting"""
        df = data.copy()
        
        # Price-based indicators
        df['returns'] = df['close'].pct_change()
        df['sma'] = df['close'].rolling(window=lookback).mean()
        df['volatility'] = df['returns'].rolling(window=lookback).std()
        
        # Volume indicators
        df['volume_sma'] = df['volume'].rolling(window=lookback).mean()
        df['volume_ratio'] = df['volume'] / df['volume_sma']
        
        # Momentum indicators
        df['momentum'] = df['close'] / df['close'].shift(lookback) - 1
        
        # Spread simulation (mock)
        df['bid'] = df['close'] * 0.999
        df['ask'] = df['close'] * 1.001
        df['spread'] = (df['ask'] - df['bid']) / df['close']
        
        return df
    
    def _generate_scalper_signals(
        self, data: pd.DataFrame, spread_threshold: float, decision_threshold: float,
        momentum_weight: float, volatility_weight: float, volume_weight: float, spread_weight: float
    ) -> pd.Series:
        """Generate trading signals based on ScalperGPT logic"""
        
        # Calculate quality scores
        spread_quality = (1 / (data['spread'] + 0.0001)) * 10  # Lower spread = higher quality
        momentum_quality = np.abs(data['momentum']) * 100  # Higher momentum = higher quality
        volatility_quality = data['volatility'] * 100  # Moderate volatility preferred
        volume_quality = np.minimum(data['volume_ratio'], 2.0) * 5  # Higher volume = higher quality
        
        # Weighted quality score
        total_quality = (
            spread_quality * spread_weight +
            momentum_quality * momentum_weight +
            volatility_quality * volatility_weight +
            volume_quality * volume_weight
        )
        
        # Generate signals based on quality thresholds
        signals = pd.Series(0, index=data.index)
        
        # Long signals
        long_condition = (
            (total_quality >= decision_threshold) &
            (spread_quality >= spread_threshold) &
            (data['momentum'] > 0.001)  # Positive momentum
        )
        signals[long_condition] = 1
        
        # Short signals
        short_condition = (
            (total_quality >= decision_threshold) &
            (spread_quality >= spread_threshold) &
            (data['momentum'] < -0.001)  # Negative momentum
        )
        signals[short_condition] = -1
        
        return signals
    
    def _calculate_symbol_quality(
        self, data: pd.DataFrame, spread_weight: float, atr_weight: float,
        flow_weight: float, depth_weight: float, volume_weight: float
    ) -> pd.Series:
        """Calculate symbol quality scores"""
        
        # Normalize scores to 0-100 scale
        spread_score = (1 / (data['spread'] + 0.0001)) * 10
        spread_score = np.minimum(spread_score, 100)
        
        atr_score = data['volatility'] * 1000  # ATR proxy
        atr_score = np.minimum(atr_score, 100)
        
        flow_score = np.minimum(data['volume_ratio'] * 50, 100)
        depth_score = np.random.uniform(60, 90, len(data))  # Mock depth score
        volume_score = np.minimum(data['volume'] / 1000, 100)
        
        # Weighted quality score
        quality_score = (
            spread_score * spread_weight +
            atr_score * atr_weight +
            flow_score * flow_weight +
            depth_score * depth_weight +
            volume_score * volume_weight
        )
        
        return quality_score
    
    def _generate_quality_filtered_signals(self, data: pd.DataFrame, high_quality_mask: pd.Series) -> pd.Series:
        """Generate signals filtered by quality"""
        signals = pd.Series(0, index=data.index)
        
        # Simple momentum-based signals for high-quality periods
        momentum_signals = np.where(data['momentum'] > 0.002, 1, np.where(data['momentum'] < -0.002, -1, 0))
        signals[high_quality_mask] = momentum_signals[high_quality_mask]
        
        return signals
    
    def _get_timer_decision_points(self, data: pd.DataFrame, interval_seconds: float) -> List[int]:
        """Get decision points based on timer intervals"""
        # Convert interval to data points (assuming 1-minute data)
        interval_points = max(1, int(interval_seconds / 60))
        return list(range(0, len(data), interval_points))
    
    def _generate_timer_coordinated_signals(
        self, data: pd.DataFrame, decision_points: List[int], analysis_points: List[int]
    ) -> pd.Series:
        """Generate signals based on timer coordination"""
        signals = pd.Series(0, index=data.index)
        
        # Generate signals only at decision points
        for point in decision_points:
            if point < len(data):
                # Simple momentum-based decision
                if data.iloc[point]['momentum'] > 0.001:
                    signals.iloc[point] = 1
                elif data.iloc[point]['momentum'] < -0.001:
                    signals.iloc[point] = -1
        
        return signals
    
    def _execute_trades(self, data: pd.DataFrame, signals: pd.Series) -> List[Dict[str, Any]]:
        """Execute trades based on signals"""
        trades = []
        position = 0
        entry_price = 0
        entry_time = None
        
        for i, signal in enumerate(signals):
            if signal != 0 and position == 0:
                # Enter position
                position = signal
                entry_price = data.iloc[i]['close']
                entry_time = data.iloc[i]['timestamp']
                
            elif signal != 0 and position != 0 and signal != position:
                # Exit current position and enter new one
                exit_price = data.iloc[i]['close']
                exit_time = data.iloc[i]['timestamp']
                
                # Calculate trade result
                if position == 1:  # Long position
                    pnl = (exit_price - entry_price) / entry_price
                else:  # Short position
                    pnl = (entry_price - exit_price) / entry_price
                
                # Apply trading costs
                pnl -= self.trading_costs * 2  # Entry and exit costs
                
                trades.append({
                    'entry_time': entry_time,
                    'exit_time': exit_time,
                    'entry_price': entry_price,
                    'exit_price': exit_price,
                    'position': position,
                    'pnl': pnl,
                    'duration': (exit_time - entry_time).total_seconds() / 60  # Duration in minutes
                })
                
                # Enter new position
                position = signal
                entry_price = exit_price
                entry_time = exit_time
        
        return trades
    
    def _calculate_performance_metrics(self, trades: List[Dict[str, Any]]) -> Dict[str, float]:
        """Calculate comprehensive performance metrics"""
        if not trades:
            return self._get_default_performance()
        
        # Extract PnL series
        pnls = [trade['pnl'] for trade in trades]
        
        # Basic metrics
        total_return = sum(pnls)
        total_trades = len(trades)
        win_rate = len([pnl for pnl in pnls if pnl > 0]) / total_trades if total_trades > 0 else 0
        
        # Risk metrics
        returns_array = np.array(pnls)
        volatility = np.std(returns_array) if len(returns_array) > 1 else 0
        
        # Sharpe ratio (assuming risk-free rate = 0)
        sharpe_ratio = np.mean(returns_array) / volatility if volatility > 0 else 0
        
        # Maximum drawdown
        cumulative_returns = np.cumsum(returns_array)
        running_max = np.maximum.accumulate(cumulative_returns)
        drawdowns = cumulative_returns - running_max
        max_drawdown = abs(np.min(drawdowns)) if len(drawdowns) > 0 else 0
        
        # Profit factor
        winning_trades = [pnl for pnl in pnls if pnl > 0]
        losing_trades = [pnl for pnl in pnls if pnl < 0]
        
        gross_profit = sum(winning_trades) if winning_trades else 0
        gross_loss = abs(sum(losing_trades)) if losing_trades else 0.001  # Avoid division by zero
        profit_factor = gross_profit / gross_loss
        
        # Average trade duration
        durations = [trade['duration'] for trade in trades]
        avg_trade_duration = np.mean(durations) if durations else 0
        
        # Calmar ratio
        calmar_ratio = total_return / max_drawdown if max_drawdown > 0 else 0
        
        # Sortino ratio (downside deviation)
        negative_returns = [pnl for pnl in pnls if pnl < 0]
        downside_deviation = np.std(negative_returns) if negative_returns else 0.001
        sortino_ratio = np.mean(returns_array) / downside_deviation if downside_deviation > 0 else 0
        
        return {
            'total_return': total_return,
            'sharpe_ratio': sharpe_ratio,
            'max_drawdown': max_drawdown,
            'win_rate': win_rate,
            'profit_factor': profit_factor,
            'total_trades': total_trades,
            'avg_trade_duration': avg_trade_duration,
            'volatility': volatility,
            'calmar_ratio': calmar_ratio,
            'sortino_ratio': sortino_ratio
        }
    
    def _get_default_performance(self) -> Dict[str, float]:
        """Get default performance metrics for failed backtests"""
        return {
            'total_return': -0.1,
            'sharpe_ratio': -1.0,
            'max_drawdown': 0.2,
            'win_rate': 0.3,
            'profit_factor': 0.5,
            'total_trades': 0,
            'avg_trade_duration': 0,
            'volatility': 0.1,
            'calmar_ratio': -0.5,
            'sortino_ratio': -1.0
        }


# Global instance
backtesting_framework = BacktestingFramework()
