#!/usr/bin/env python3
"""
Epinnox Main Window
Modular main window class extracted from launch_epinnox.py
"""

import sys
import os
from PyQt5.QtWidgets import *
from PyQt5.QtCore import *
from PyQt5.QtGui import *

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

class EpinnoxMainWindow(QMainWindow):
    """Main window for Epinnox v6 Trading System"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Epinnox v6 Trading System")
        self.setGeometry(100, 100, 1600, 1000)
        
        # Initialize core components
        self.setup_core_components()
        
        # Setup UI
        self.setup_ui()
        
        # Setup connections
        self.setup_connections()
        
        print("✅ Epinnox Main Window initialized")
    
    def setup_core_components(self):
        """Initialize core trading components"""
        try:
            # Import and initialize autonomous trading orchestrator
            from core.autonomous_trading_orchestrator import AutonomousTradingOrchestrator
            self.orchestrator = AutonomousTradingOrchestrator()
            
            # Import and initialize other core components
            from execution.autonomous_executor import AutonomousTradeExecutor
            from data.live_data_manager import LiveDataManager
            
            self.executor = None  # Will be initialized with exchange
            self.data_manager = LiveDataManager()
            
            print("✅ Core components initialized")
            
        except Exception as e:
            print(f"⚠️ Error initializing core components: {e}")
    
    def setup_ui(self):
        """Setup the user interface"""
        try:
            # Create central widget
            central_widget = QWidget()
            self.setCentralWidget(central_widget)
            
            # Create main layout
            main_layout = QHBoxLayout(central_widget)
            
            # Create tabs
            self.tab_widget = QTabWidget()
            main_layout.addWidget(self.tab_widget)
            
            # Add tabs
            self.setup_tabs()
            
            # Setup menu bar
            self.setup_menu_bar()
            
            # Setup status bar
            self.setup_status_bar()

            # Apply Matrix theme
            self.apply_matrix_theme()

            print("✅ UI setup complete")
            
        except Exception as e:
            print(f"❌ Error setting up UI: {e}")
    
    def setup_tabs(self):
        """Setup trading tabs with integrated monitoring dashboard"""
        try:
            # Import tab classes
            from gui.autonomous_trading_tab import AutonomousTradingTab
            from gui.integrated_monitoring_dashboard import IntegratedMonitoringDashboard

            # Create and add tabs
            self.autonomous_tab = AutonomousTradingTab()
            self.integrated_dashboard = IntegratedMonitoringDashboard()

            # Connect integrated dashboard to orchestrator
            if hasattr(self, 'orchestrator') and self.orchestrator:
                self.integrated_dashboard.set_orchestrator(self.orchestrator)

            self.tab_widget.addTab(self.autonomous_tab, "🤖 Autonomous Trading")
            self.tab_widget.addTab(self.integrated_dashboard, "🖥️ Integrated Monitoring")

            # Connect signals
            self.setup_dashboard_connections()

            print("✅ Tabs setup complete with integrated monitoring dashboard")
            
        except Exception as e:
            print(f"⚠️ Error setting up tabs: {e}")
            # Create fallback simple tab
            fallback_tab = QWidget()
            fallback_layout = QVBoxLayout(fallback_tab)
            fallback_layout.addWidget(QLabel("Epinnox v6 Trading System"))
            fallback_layout.addWidget(QLabel("Loading components..."))
            self.tab_widget.addTab(fallback_tab, "Main")
    
    def setup_menu_bar(self):
        """Setup menu bar"""
        try:
            menubar = self.menuBar()
            
            # File menu
            file_menu = menubar.addMenu('File')
            
            # Add actions
            exit_action = QAction('Exit', self)
            exit_action.setShortcut('Ctrl+Q')
            exit_action.triggered.connect(self.close)
            file_menu.addAction(exit_action)
            
            # Trading menu
            trading_menu = menubar.addMenu('Trading')
            
            start_autonomous_action = QAction('Start Autonomous Trading', self)
            start_autonomous_action.triggered.connect(self.start_autonomous_trading)
            trading_menu.addAction(start_autonomous_action)
            
            stop_autonomous_action = QAction('Stop Autonomous Trading', self)
            stop_autonomous_action.triggered.connect(self.stop_autonomous_trading)
            trading_menu.addAction(stop_autonomous_action)
            
            # Help menu
            help_menu = menubar.addMenu('Help')
            
            about_action = QAction('About', self)
            about_action.triggered.connect(self.show_about)
            help_menu.addAction(about_action)
            
            print("✅ Menu bar setup complete")
            
        except Exception as e:
            print(f"❌ Error setting up menu bar: {e}")
    
    def setup_status_bar(self):
        """Setup status bar"""
        try:
            self.status_bar = self.statusBar()
            self.status_bar.showMessage("Epinnox v6 Ready")
            
            # Add permanent widgets
            self.connection_status = QLabel("🔴 Disconnected")
            self.status_bar.addPermanentWidget(self.connection_status)
            
            self.balance_status = QLabel("Balance: $0.00")
            self.status_bar.addPermanentWidget(self.balance_status)
            
            print("✅ Status bar setup complete")
            
        except Exception as e:
            print(f"❌ Error setting up status bar: {e}")
    
    def setup_connections(self):
        """Setup signal connections"""
        try:
            # Connect orchestrator signals if available
            if hasattr(self.orchestrator, 'status_updated'):
                self.orchestrator.status_updated.connect(self.update_status)
            
            print("✅ Connections setup complete")
            
        except Exception as e:
            print(f"⚠️ Error setting up connections: {e}")

    def setup_dashboard_connections(self):
        """Setup connections for integrated monitoring dashboard"""
        try:
            if hasattr(self, 'integrated_dashboard'):
                # Connect emergency stop signal
                self.integrated_dashboard.emergency_stop_triggered.connect(self.handle_emergency_stop)

                # Connect system restart signal
                self.integrated_dashboard.system_restart_requested.connect(self.handle_system_restart)

                # Connect deployment signal
                self.integrated_dashboard.deployment_requested.connect(self.handle_deployment_request)

                print("✅ Dashboard connections setup complete")

        except Exception as e:
            print(f"⚠️ Error setting up dashboard connections: {e}")

    def apply_matrix_theme(self):
        """Apply Matrix theme to the main window"""
        try:
            from gui.matrix_theme import MatrixTheme
            self.setStyleSheet(MatrixTheme.get_stylesheet())
            print("✅ Matrix theme applied")

        except Exception as e:
            print(f"⚠️ Error applying Matrix theme: {e}")

    def handle_emergency_stop(self, reason: str):
        """Handle emergency stop signal from dashboard"""
        try:
            print(f"🚨 Emergency stop triggered: {reason}")

            # Stop orchestrator if running
            if self.orchestrator:
                self.orchestrator.stop_autonomous_trading()

            # Update status
            if hasattr(self, 'status_bar'):
                self.status_bar.showMessage(f"🚨 EMERGENCY STOP: {reason}")

        except Exception as e:
            print(f"❌ Error handling emergency stop: {e}")

    def handle_system_restart(self):
        """Handle system restart signal from dashboard"""
        try:
            print("🔄 System restart requested")

            # Stop current operations
            if self.orchestrator:
                self.orchestrator.stop_autonomous_trading()

            # Reinitialize core components
            self.setup_core_components()

            # Reconnect dashboard
            if hasattr(self, 'integrated_dashboard') and self.orchestrator:
                self.integrated_dashboard.set_orchestrator(self.orchestrator)

            # Update status
            if hasattr(self, 'status_bar'):
                self.status_bar.showMessage("🔄 System restarted")

            print("✅ System restart completed")

        except Exception as e:
            print(f"❌ Error handling system restart: {e}")

    def handle_deployment_request(self, mode: str):
        """Handle deployment request signal from dashboard"""
        try:
            print(f"🚀 Deployment requested: {mode} mode")

            # This would integrate with the deployment scripts
            try:
                from deploy_ultra_conservative_live import UltraConservativeLiveDeployment

                deployment_manager = UltraConservativeLiveDeployment()

                # Connect deployment manager to dashboard
                if hasattr(self, 'integrated_dashboard'):
                    self.integrated_dashboard.set_deployment_manager(deployment_manager)

                # Update status
                if hasattr(self, 'status_bar'):
                    self.status_bar.showMessage(f"🚀 Deploying {mode} trading...")

                print(f"✅ Deployment initiated: {mode} mode")

            except ImportError as e:
                print(f"⚠️ Deployment scripts not available: {e}")
                if hasattr(self, 'status_bar'):
                    self.status_bar.showMessage("⚠️ Deployment scripts not available")

        except Exception as e:
            print(f"❌ Error handling deployment request: {e}")

    def start_autonomous_trading(self):
        """Start autonomous trading"""
        try:
            if self.orchestrator:
                # Start orchestrator
                self.orchestrator.start_autonomous_trading()
                self.status_bar.showMessage("🤖 Autonomous trading started")
                self.connection_status.setText("🟢 Active")
                print("✅ Autonomous trading started")
            else:
                self.status_bar.showMessage("❌ Orchestrator not available")
                
        except Exception as e:
            print(f"❌ Error starting autonomous trading: {e}")
            self.status_bar.showMessage(f"❌ Error: {e}")
    
    def stop_autonomous_trading(self):
        """Stop autonomous trading"""
        try:
            if self.orchestrator:
                # Stop orchestrator
                self.orchestrator.stop_autonomous_trading()
                self.status_bar.showMessage("🛑 Autonomous trading stopped")
                self.connection_status.setText("🔴 Stopped")
                print("✅ Autonomous trading stopped")
            else:
                self.status_bar.showMessage("❌ Orchestrator not available")
                
        except Exception as e:
            print(f"❌ Error stopping autonomous trading: {e}")
            self.status_bar.showMessage(f"❌ Error: {e}")
    
    def update_status(self, message):
        """Update status bar message"""
        self.status_bar.showMessage(message)
    
    def show_about(self):
        """Show about dialog"""
        QMessageBox.about(self, "About Epinnox v6", 
                         "Epinnox v6 Trading System\n\n"
                         "Autonomous futures trading with AI decision making\n"
                         "Version 6.0\n\n"
                         "Features:\n"
                         "• Autonomous trading orchestrator\n"
                         "• LLM integration for decision making\n"
                         "• Real-time market data\n"
                         "• Risk management\n"
                         "• Performance tracking")
    
    def closeEvent(self, event):
        """Handle window close event"""
        try:
            # Stop autonomous trading if running
            if self.orchestrator:
                self.orchestrator.stop_autonomous_trading()
            
            # Accept the close event
            event.accept()
            print("✅ Epinnox Main Window closed gracefully")
            
        except Exception as e:
            print(f"⚠️ Error during close: {e}")
            event.accept()

def run_epinnox_main_window():
    """Run the main window"""
    try:
        # Create QApplication
        app = QApplication(sys.argv)
        app.setApplicationName("Epinnox v6 Trading System")
        app.setApplicationVersion("6.0")
        
        # Create and show main window
        window = EpinnoxMainWindow()
        window.show()
        
        print("✅ Epinnox v6 Main Window started")
        
        # Run application
        return app.exec_()
        
    except Exception as e:
        print(f"❌ Error running main window: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(run_epinnox_main_window())
