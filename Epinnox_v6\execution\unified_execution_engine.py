"""
Unified Execution Engine for Epinnox Trading System
Consolidates all execution paths into a single, coherent autonomous trading engine
"""

import asyncio
import time
import logging
from typing import Dict, Optional, List, Union
from dataclasses import dataclass
from enum import Enum
import ccxt
from datetime import datetime

# Import autonomous trading rules for enforcement
from config.autonomous_trading_rules import AutonomousTradingRules
from core.emergency_stop_coordinator import emergency_coordinator

logger = logging.getLogger(__name__)

class ExecutionMode(Enum):
    """Execution modes for the unified engine"""
    LIVE = "live"
    PAPER = "paper"
    SIMULATION = "simulation"

class OrderType(Enum):
    """Order types - LIMIT ONLY for autonomous trading"""
    LIMIT = "limit"
    STOP_LOSS = "stop_loss"
    TAKE_PROFIT = "take_profit"
    # MARKET ORDERS EXPLICITLY EXCLUDED FOR AUTONOMOUS TRADING

class OrderSide(Enum):
    """Order sides"""
    BUY = "buy"
    SELL = "sell"

@dataclass
class TradingDecision:
    """Standardized trading decision structure"""
    symbol: str
    action: str  # 'LONG', 'SHORT', 'CLOSE', 'WAIT'
    confidence: float  # 0.0 to 1.0
    position_size: float
    leverage: float = 1.0
    stop_loss: Optional[float] = None
    take_profit: Optional[float] = None
    reasoning: str = ""

@dataclass
class ExecutionResult:
    """Standardized execution result structure"""
    success: bool
    order_id: Optional[str] = None
    symbol: str = ""
    side: str = ""
    amount: float = 0.0
    price: float = 0.0
    status: str = ""
    error_message: str = ""
    timestamp: float = 0.0

class UnifiedExecutionEngine:
    """
    CONSOLIDATED Unified execution engine that absorbs all trading execution paths
    Supports live, paper, and simulation trading with consistent interface
    Replaces: AutonomousTradeExecutor, TradingManager, SimulationExecutor
    """

    def __init__(self, mode: ExecutionMode = ExecutionMode.PAPER,
                 exchange_config: Optional[Dict] = None,
                 initial_balance: float = 10000.0,
                 min_confidence: float = 0.7):
        """
        Initialize the unified execution engine

        Args:
            mode: Execution mode (live, paper, simulation)
            exchange_config: Exchange configuration for live trading
            initial_balance: Initial balance for paper/simulation trading
            min_confidence: Minimum confidence threshold for autonomous trading
        """
        self.mode = mode
        self.exchange_config = exchange_config or {}
        self.initial_balance = initial_balance
        self.min_confidence = min_confidence

        # Exchange connection (for live trading)
        self.exchange = None
        self.is_connected = False

        # Paper/Simulation state
        self.balance = initial_balance
        self.positions = {}
        self.orders = {}
        self.order_counter = 0

        # CONSOLIDATED: Absorb AutonomousTradeExecutor functionality
        self.active_orders = {}
        self.position_tracker = {}
        self.emergency_stop_active = False
        self.emergency_stop_reason = None

        # CONSOLIDATED: Absorb TradingManager functionality
        self.balance_history = []
        self.last_balance_check = datetime.now()
        self.balance_check_interval = 300  # 5 minutes

        # CONSOLIDATED: Absorb SimulationExecutor functionality
        self.slippage = 0.001  # 0.1% slippage
        self.commission = 0.0005  # 0.05% commission
        
        # Risk management
        self.max_position_size_pct = 0.30  # 30% max position size
        self.max_leverage = 3.0  # 3x max leverage
        self.max_daily_loss_pct = 0.20  # 20% max daily loss
        
        # Performance tracking
        self.daily_pnl = 0.0
        self.total_trades = 0
        self.successful_trades = 0
        
        # Order tracking
        self.active_orders = {}
        self.order_history = []
        
        logger.info(f"CONSOLIDATED Unified execution engine initialized in {mode.value} mode")
        logger.info(f"🔧 Absorbed functionality from: AutonomousTradeExecutor, TradingManager, SimulationExecutor")
    
    async def initialize(self) -> bool:
        """Initialize the execution engine based on mode"""
        try:
            if self.mode == ExecutionMode.LIVE:
                success = await self._initialize_live_trading()
            else:
                success = await self._initialize_paper_simulation()

            if success:
                # CRITICAL: Register with emergency stop coordinator
                emergency_coordinator.register_module("unified_execution_engine", self)
                logger.info("🛡️ Registered with emergency stop coordinator")

            return success

        except Exception as e:
            logger.error(f"Failed to initialize execution engine: {e}")
            return False

    # CONSOLIDATED: AutonomousTradeExecutor interface
    async def execute_trading_decision(self, decision_data: dict) -> dict:
        """
        CONSOLIDATED: Execute autonomous trading decision (from AutonomousTradeExecutor)

        Args:
            decision_data: Dict containing:
                - decision: 'LONG', 'SHORT', 'WAIT'
                - confidence: 0-100
                - symbol: trading symbol
                - leverage_position_sizing: position sizing data

        Returns:
            Dict with execution results
        """
        try:
            # CRITICAL: Check emergency stop first
            if self.emergency_stop_active:
                logger.error(f"🚨 EMERGENCY STOP ACTIVE: {self.emergency_stop_reason}")
                return {
                    'status': 'EMERGENCY_STOP',
                    'reason': f'Emergency stop active: {self.emergency_stop_reason}',
                    'action': 'BLOCKED'
                }

            decision = decision_data.get('decision', 'WAIT')
            confidence = decision_data.get('confidence', 0) / 100
            symbol = decision_data.get('selected_symbol', 'DOGE/USDT')

            # AUTONOMOUS DECISION: Only trade if confidence is high enough
            if confidence < self.min_confidence:
                return {
                    'status': 'SKIPPED',
                    'reason': f'Confidence {confidence:.1%} below threshold {self.min_confidence:.1%}',
                    'action': 'WAIT'
                }

            if decision == 'WAIT':
                return {'status': 'WAIT', 'reason': 'System decided to wait'}

            # Get position sizing data
            position_data = decision_data.get('leverage_position_sizing', {})
            if not position_data:
                return {'status': 'ERROR', 'reason': 'No position sizing data'}

            # Convert to standardized TradingDecision
            trading_decision = TradingDecision(
                symbol=symbol,
                action=decision,
                confidence=confidence,
                position_size=position_data.get('position_units', 0),
                leverage=position_data.get('effective_leverage', 1.0),
                stop_loss=position_data.get('stop_loss_price'),
                take_profit=position_data.get('take_profit_price'),
                reasoning=f"Autonomous decision with {confidence:.1%} confidence"
            )

            # Execute using unified engine
            result = await self.execute_decision(trading_decision)

            # Convert result to legacy format
            if result.success:
                return {
                    'status': 'FILLED',
                    'order_id': result.order_id,
                    'symbol': result.symbol,
                    'side': result.side,
                    'amount': result.amount,
                    'price': result.price,
                    'timestamp': result.timestamp
                }
            else:
                return {
                    'status': 'FAILED',
                    'reason': result.error_message,
                    'timestamp': result.timestamp
                }

        except Exception as e:
            logger.error(f"Execution error: {e}")
            return {'status': 'ERROR', 'reason': f'Execution error: {str(e)}'}

    # CONSOLIDATED: Price calculation methods
    async def get_current_price(self, symbol: str) -> Optional[float]:
        """Get current market price for symbol"""
        try:
            if self.mode == ExecutionMode.LIVE and self.exchange:
                if asyncio.iscoroutinefunction(self.exchange.fetch_ticker):
                    ticker = await self.exchange.fetch_ticker(symbol)
                else:
                    ticker = self.exchange.fetch_ticker(symbol)
                return ticker['last']
            else:
                # Simulation price
                return await self._get_simulated_price(symbol)
        except Exception as e:
            logger.error(f"Failed to get current price for {symbol}: {e}")
            return None

    def calculate_limit_price(self, current_price: float, side: str) -> float:
        """
        Calculate LIMIT order price with small buffer for execution

        Args:
            current_price: Current market price
            side: 'buy' or 'sell'

        Returns:
            LIMIT order price with execution buffer
        """
        # Small buffer to ensure execution (0.1% for buy orders, -0.1% for sell orders)
        buffer = 0.001  # 0.1%

        if side.lower() in ['buy', 'long']:
            # For buy orders, set price slightly above market to ensure execution
            return current_price * (1 + buffer)
        else:
            # For sell orders, set price slightly below market to ensure execution
            return current_price * (1 - buffer)

    # CONSOLIDATED: Balance and position management
    async def get_account_balance(self) -> float:
        """Get account balance"""
        try:
            if self.mode == ExecutionMode.LIVE and self.exchange:
                if asyncio.iscoroutinefunction(self.exchange.fetch_balance):
                    balance = await self.exchange.fetch_balance()
                else:
                    balance = self.exchange.fetch_balance()
                return balance.get('USDT', {}).get('free', 0)
            else:
                return self.balance
        except Exception as e:
            logger.error(f"Error getting account balance: {e}")
            return self.balance if self.mode != ExecutionMode.LIVE else 0.0

    def update_balance(self):
        """Update balance history (from TradingManager)"""
        try:
            current_time = datetime.now()
            if (current_time - self.last_balance_check).seconds >= self.balance_check_interval:
                # In live mode, this would fetch from exchange
                # In simulation, balance is tracked internally
                self.balance_history.append({
                    'timestamp': current_time,
                    'balance': self.balance
                })
                self.last_balance_check = current_time
                logger.debug(f"Balance updated: ${self.balance:.2f}")
        except Exception as e:
            logger.error(f"Error updating balance: {e}")

    # CONSOLIDATED: Emergency stop functionality
    async def emergency_stop(self, reason: str = "Manual emergency stop") -> dict:
        """
        CONSOLIDATED: Emergency stop - immediately halt all trading activities

        Args:
            reason: Reason for emergency stop

        Returns:
            Dict with emergency stop results
        """
        try:
            logger.critical(f"🚨 UNIFIED ENGINE EMERGENCY STOP: {reason}")

            # Set emergency stop state
            self.emergency_stop_active = True
            self.emergency_stop_reason = reason

            # Cancel all active orders
            cancelled_orders = await self.cancel_all_orders(f"Emergency stop: {reason}")

            # Close all positions
            closed_positions = await self.close_all_positions(f"Emergency stop: {reason}")

            # Clear tracking
            self.position_tracker.clear()
            self.active_orders.clear()

            logger.critical(f"🛑 EMERGENCY STOP COMPLETED: Cancelled {len(cancelled_orders)} orders, closed {len(closed_positions)} positions")

            return {
                'status': 'EMERGENCY_STOP_COMPLETED',
                'reason': reason,
                'cancelled_orders': len(cancelled_orders),
                'closed_positions': len(closed_positions),
                'timestamp': time.time()
            }

        except Exception as e:
            logger.error(f"CRITICAL ERROR during emergency stop: {e}")
            return {
                'status': 'EMERGENCY_STOP_FAILED',
                'reason': reason,
                'error': str(e),
                'timestamp': time.time()
            }

    def reset_emergency_stop(self, authorization_code: str = None) -> bool:
        """
        Reset emergency stop (requires authorization)

        Args:
            authorization_code: Authorization code for reset

        Returns:
            bool: True if reset successful
        """
        try:
            # Simple authorization check (in production, use proper authentication)
            if authorization_code != "RESET_EMERGENCY_2024":
                logger.error("🚨 UNAUTHORIZED emergency stop reset attempt")
                return False

            self.emergency_stop_active = False
            self.emergency_stop_reason = None

            logger.warning("⚠️ Emergency stop reset - trading activities can resume")
            return True

        except Exception as e:
            logger.error(f"Error resetting emergency stop: {e}")
            return False

        except Exception as e:
            logger.error(f"Failed to initialize execution engine: {e}")
            return False
    
    async def _initialize_live_trading(self) -> bool:
        """Initialize live trading with real exchange connection"""
        try:
            if not self.exchange_config:
                raise ValueError("Exchange configuration required for live trading")
            
            # Initialize CCXT exchange
            exchange_name = self.exchange_config.get('exchange', 'htx')
            api_key = self.exchange_config.get('api_key')
            secret = self.exchange_config.get('secret')
            
            if not api_key or not secret:
                raise ValueError("API credentials required for live trading")
            
            # Create exchange instance
            exchange_class = getattr(ccxt, exchange_name)
            self.exchange = exchange_class({
                'apiKey': api_key,
                'secret': secret,
                'enableRateLimit': True,
                'sandbox': False,  # Live trading
                'options': {
                    'defaultType': 'swap',  # Futures trading
                },
                'timeout': 30000,
                'rateLimit': 100,
            })
            
            # Test connection
            await self._test_exchange_connection()
            self.is_connected = True
            
            logger.info("✅ Live trading initialized successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to initialize live trading: {e}")
            return False
    
    async def _initialize_paper_simulation(self) -> bool:
        """Initialize paper/simulation trading"""
        try:
            # Reset state for paper/simulation
            self.balance = self.initial_balance
            self.positions = {}
            self.orders = {}
            self.order_counter = 0
            self.daily_pnl = 0.0
            
            logger.info(f"✅ {self.mode.value.title()} trading initialized with ${self.initial_balance} balance")
            return True
            
        except Exception as e:
            logger.error(f"Failed to initialize {self.mode.value} trading: {e}")
            return False

    async def _test_exchange_connection(self):
        """Test exchange connection and load markets"""
        if self.exchange:
            # Load markets to test connection
            if asyncio.iscoroutinefunction(self.exchange.load_markets):
                await self.exchange.load_markets()
            else:
                self.exchange.load_markets()
            
            # Test balance fetch
            if asyncio.iscoroutinefunction(self.exchange.fetch_balance):
                balance = await self.exchange.fetch_balance()
            else:
                balance = self.exchange.fetch_balance()
            
            logger.info(f"Exchange connection test successful. Balance: {balance.get('USDT', {}).get('total', 0)} USDT")

    async def execute_decision(self, decision: TradingDecision) -> ExecutionResult:
        """
        Execute a trading decision using LIMIT orders only
        
        Args:
            decision: TradingDecision object with trade details
            
        Returns:
            ExecutionResult with execution details
        """
        try:
            # Validate decision
            validation_result = await self._validate_decision(decision)
            if not validation_result['valid']:
                return ExecutionResult(
                    success=False,
                    error_message=validation_result['reason'],
                    timestamp=time.time()
                )
            
            # Execute based on mode
            if self.mode == ExecutionMode.LIVE:
                return await self._execute_live_order(decision)
            else:
                return await self._execute_simulated_order(decision)
                
        except Exception as e:
            logger.error(f"Error executing decision: {e}")
            return ExecutionResult(
                success=False,
                error_message=str(e),
                timestamp=time.time()
            )

    async def _validate_decision(self, decision: TradingDecision) -> Dict:
        """Validate trading decision against autonomous trading rules"""
        try:
            # Prepare order data for comprehensive rule validation
            position_value = decision.position_size * decision.leverage
            current_positions = len(self.positions)

            order_data = {
                'order_type': 'limit',  # Always LIMIT for autonomous trading
                'position_value': position_value,
                'balance': self.balance,
                'leverage': decision.leverage,
                'confidence': decision.confidence,
                'current_positions': current_positions,
                'daily_pnl': self.daily_pnl
            }

            # Use autonomous trading rules for validation
            validation_result = AutonomousTradingRules.validate_all_rules(order_data)

            if not validation_result['valid']:
                # Log rule violation
                logger.error(f"🚨 AUTONOMOUS TRADING RULE VIOLATION: {validation_result['reason']}")

                # Check if this requires emergency action
                if validation_result.get('action') == 'EMERGENCY_STOP':
                    logger.critical("🚨 EMERGENCY STOP TRIGGERED BY RULE VIOLATION")
                    # Trigger emergency stop if needed
                    asyncio.create_task(self.emergency_stop())

                return validation_result

            logger.debug("✅ All autonomous trading rules validated successfully")
            return validation_result

        except Exception as e:
            logger.error(f"Error in decision validation: {e}")
            return {'valid': False, 'reason': f'Validation error: {e}'}

    async def _execute_live_order(self, decision: TradingDecision) -> ExecutionResult:
        """Execute order on live exchange using LIMIT orders only"""
        try:
            if not self.is_connected or not self.exchange:
                raise Exception("Exchange not connected")
            
            # CONSOLIDATED: Get current market price using unified method
            current_price = await self.get_current_price(decision.symbol)
            if not current_price:
                raise Exception(f"Could not get current price for {decision.symbol}")

            # CONSOLIDATED: Calculate limit price using unified method
            if decision.action == 'LONG':
                side = OrderSide.BUY.value
                limit_price = self.calculate_limit_price(current_price, 'buy')
            elif decision.action == 'SHORT':
                side = OrderSide.SELL.value
                limit_price = self.calculate_limit_price(current_price, 'sell')
            else:
                raise ValueError(f"Unsupported action: {decision.action}")

            logger.info(f"🎯 CONSOLIDATED: Executing LIMIT order: {side} {decision.position_size} {decision.symbol} @ {limit_price}")
            
            # Place LIMIT order only
            order_params = {
                'symbol': decision.symbol,
                'type': OrderType.LIMIT.value,
                'side': side,
                'amount': decision.position_size,
                'price': limit_price,
            }
            
            # Add leverage for futures
            if ':' in decision.symbol:
                order_params['params'] = {'leverage': decision.leverage}
            
            # Execute order
            if asyncio.iscoroutinefunction(self.exchange.create_order):
                order = await self.exchange.create_order(**order_params)
            else:
                order = self.exchange.create_order(**order_params)
            
            # Track order
            self.active_orders[order['id']] = {
                'order': order,
                'decision': decision,
                'timestamp': time.time()
            }
            
            # Update statistics
            self.total_trades += 1
            
            logger.info(f"✅ LIMIT order placed: {side} {decision.position_size} {decision.symbol} @ {limit_price}")
            
            return ExecutionResult(
                success=True,
                order_id=order['id'],
                symbol=decision.symbol,
                side=side,
                amount=decision.position_size,
                price=limit_price,
                status='PENDING',
                timestamp=time.time()
            )
            
        except Exception as e:
            logger.error(f"Failed to execute live order: {e}")
            return ExecutionResult(
                success=False,
                error_message=str(e),
                timestamp=time.time()
            )

    async def _execute_simulated_order(self, decision: TradingDecision) -> ExecutionResult:
        """Execute order in simulation/paper trading mode"""
        try:
            # Generate order ID
            self.order_counter += 1
            order_id = f"sim_{self.order_counter}_{int(time.time())}"

            # Get simulated market price
            current_price = await self._get_simulated_price(decision.symbol)

            # Simulate limit order execution with slippage
            slippage = 0.001  # 0.1% slippage
            if decision.action == 'LONG':
                side = OrderSide.BUY.value
                execution_price = current_price * (1 + slippage)
            elif decision.action == 'SHORT':
                side = OrderSide.SELL.value
                execution_price = current_price * (1 - slippage)
            else:
                raise ValueError(f"Unsupported action: {decision.action}")

            # Calculate position value and commission
            position_value = decision.position_size * execution_price
            commission = position_value * 0.0005  # 0.05% commission

            # Update balance (deduct margin for futures)
            margin_required = position_value / decision.leverage
            if self.balance < margin_required + commission:
                raise Exception(f"Insufficient balance: ${self.balance:.2f} < ${margin_required + commission:.2f}")

            self.balance -= (margin_required + commission)

            # Create position
            position_id = f"pos_{order_id}"
            self.positions[position_id] = {
                'symbol': decision.symbol,
                'side': side,
                'size': decision.position_size,
                'entry_price': execution_price,
                'leverage': decision.leverage,
                'margin': margin_required,
                'unrealized_pnl': 0.0,
                'timestamp': time.time()
            }

            # Update statistics
            self.total_trades += 1
            self.successful_trades += 1

            logger.info(f"✅ Simulated order executed: {side} {decision.position_size} {decision.symbol} @ {execution_price}")

            return ExecutionResult(
                success=True,
                order_id=order_id,
                symbol=decision.symbol,
                side=side,
                amount=decision.position_size,
                price=execution_price,
                status='FILLED',
                timestamp=time.time()
            )

        except Exception as e:
            logger.error(f"Failed to execute simulated order: {e}")
            return ExecutionResult(
                success=False,
                error_message=str(e),
                timestamp=time.time()
            )

    async def _get_ticker(self, symbol: str) -> Dict:
        """Get ticker data from exchange"""
        if self.exchange:
            if asyncio.iscoroutinefunction(self.exchange.fetch_ticker):
                return await self.exchange.fetch_ticker(symbol)
            else:
                return self.exchange.fetch_ticker(symbol)
        else:
            raise Exception("Exchange not available")

    async def _get_simulated_price(self, symbol: str) -> float:
        """Get simulated price for paper/simulation trading"""
        # Base prices for common symbols
        base_prices = {
            'BTC/USDT:USDT': 50000.0,
            'ETH/USDT:USDT': 3000.0,
            'DOGE/USDT:USDT': 0.08,
            'ADA/USDT:USDT': 0.5,
            'SOL/USDT:USDT': 100.0,
            'MATIC/USDT:USDT': 0.8,
            'AVAX/USDT:USDT': 25.0,
            'DOT/USDT:USDT': 6.0
        }

        base_price = base_prices.get(symbol, 1000.0)

        # Add random variation (±1%)
        import random
        variation = random.uniform(-0.01, 0.01)
        return base_price * (1 + variation)

    async def get_balance(self) -> Dict:
        """Get current account balance"""
        try:
            if self.mode == ExecutionMode.LIVE and self.exchange:
                if asyncio.iscoroutinefunction(self.exchange.fetch_balance):
                    balance = await self.exchange.fetch_balance()
                else:
                    balance = self.exchange.fetch_balance()
                return balance
            else:
                # Return simulated balance
                return {
                    'USDT': {'total': self.balance, 'free': self.balance, 'used': 0.0},
                    'total': self.balance
                }
        except Exception as e:
            logger.error(f"Error fetching balance: {e}")
            return {'total': 0.0}

    async def get_positions(self) -> Dict:
        """Get current open positions"""
        try:
            if self.mode == ExecutionMode.LIVE and self.exchange:
                if asyncio.iscoroutinefunction(self.exchange.fetch_positions):
                    positions = await self.exchange.fetch_positions()
                else:
                    positions = self.exchange.fetch_positions()
                return {pos['symbol']: pos for pos in positions if pos['contracts'] > 0}
            else:
                # Return simulated positions
                return self.positions.copy()
        except Exception as e:
            logger.error(f"Error fetching positions: {e}")
            return {}

    async def close_position(self, symbol: str, reason: str = "Manual close") -> ExecutionResult:
        """Close an open position"""
        try:
            positions = await self.get_positions()

            if symbol not in positions:
                return ExecutionResult(
                    success=False,
                    error_message=f"No open position found for {symbol}",
                    timestamp=time.time()
                )

            position = positions[symbol]

            if self.mode == ExecutionMode.LIVE:
                return await self._close_live_position(symbol, position)
            else:
                return await self._close_simulated_position(symbol, position)

        except Exception as e:
            logger.error(f"Error closing position for {symbol}: {e}")
            return ExecutionResult(
                success=False,
                error_message=str(e),
                timestamp=time.time()
            )

    async def _close_live_position(self, symbol: str, position: Dict) -> ExecutionResult:
        """Close position on live exchange"""
        try:
            # Determine close side (opposite of position side)
            position_side = position.get('side', 'long')
            close_side = 'sell' if position_side == 'long' else 'buy'
            amount = abs(position.get('contracts', 0))

            # Get current price for limit order
            ticker = await self._get_ticker(symbol)
            current_price = ticker['last']

            # Set limit price for quick fill
            if close_side == 'sell':
                limit_price = current_price * 0.999  # Slightly below market
            else:
                limit_price = current_price * 1.001  # Slightly above market

            # Place close order (LIMIT only)
            order_params = {
                'symbol': symbol,
                'type': OrderType.LIMIT.value,
                'side': close_side,
                'amount': amount,
                'price': limit_price,
                'params': {'reduceOnly': True}  # Ensure it's a closing order
            }

            if asyncio.iscoroutinefunction(self.exchange.create_order):
                order = await self.exchange.create_order(**order_params)
            else:
                order = self.exchange.create_order(**order_params)

            logger.info(f"✅ Position close order placed: {close_side} {amount} {symbol} @ {limit_price}")

            return ExecutionResult(
                success=True,
                order_id=order['id'],
                symbol=symbol,
                side=close_side,
                amount=amount,
                price=limit_price,
                status='PENDING',
                timestamp=time.time()
            )

        except Exception as e:
            logger.error(f"Failed to close live position: {e}")
            return ExecutionResult(
                success=False,
                error_message=str(e),
                timestamp=time.time()
            )

    async def _close_simulated_position(self, symbol: str, position: Dict) -> ExecutionResult:
        """Close position in simulation mode"""
        try:
            # Get current price
            current_price = await self._get_simulated_price(symbol)

            # Calculate PnL
            entry_price = position['entry_price']
            size = position['size']
            side = position['side']
            leverage = position['leverage']

            if side == 'buy':  # Long position
                pnl = (current_price - entry_price) * size * leverage
            else:  # Short position
                pnl = (entry_price - current_price) * size * leverage

            # Update balance with PnL and return margin
            margin = position['margin']
            commission = size * current_price * 0.0005  # Close commission

            self.balance += margin + pnl - commission
            self.daily_pnl += pnl

            # Remove position
            position_id = None
            for pid, pos in self.positions.items():
                if pos['symbol'] == symbol:
                    position_id = pid
                    break

            if position_id:
                del self.positions[position_id]

            logger.info(f"✅ Simulated position closed: {symbol} PnL: ${pnl:.2f}")

            return ExecutionResult(
                success=True,
                order_id=f"close_{int(time.time())}",
                symbol=symbol,
                side='close',
                amount=size,
                price=current_price,
                status='FILLED',
                timestamp=time.time()
            )

        except Exception as e:
            logger.error(f"Failed to close simulated position: {e}")
            return ExecutionResult(
                success=False,
                error_message=str(e),
                timestamp=time.time()
            )

    async def cancel_all_orders(self, reason: str = "Emergency stop") -> List[ExecutionResult]:
        """Cancel all open orders"""
        results = []

        try:
            if self.mode == ExecutionMode.LIVE and self.exchange:
                # Cancel live orders
                for order_id in list(self.active_orders.keys()):
                    try:
                        if asyncio.iscoroutinefunction(self.exchange.cancel_order):
                            await self.exchange.cancel_order(order_id)
                        else:
                            self.exchange.cancel_order(order_id)

                        results.append(ExecutionResult(
                            success=True,
                            order_id=order_id,
                            status='CANCELLED',
                            timestamp=time.time()
                        ))

                        # Remove from active orders
                        if order_id in self.active_orders:
                            del self.active_orders[order_id]

                    except Exception as e:
                        results.append(ExecutionResult(
                            success=False,
                            order_id=order_id,
                            error_message=str(e),
                            timestamp=time.time()
                        ))
            else:
                # Cancel simulated orders (just remove them)
                for order_id in list(self.orders.keys()):
                    del self.orders[order_id]
                    results.append(ExecutionResult(
                        success=True,
                        order_id=order_id,
                        status='CANCELLED',
                        timestamp=time.time()
                    ))

            logger.info(f"Cancelled {len(results)} orders. Reason: {reason}")

        except Exception as e:
            logger.error(f"Error cancelling orders: {e}")

        return results

    async def close_all_positions(self, reason: str = "Emergency stop") -> List[ExecutionResult]:
        """Close all open positions"""
        results = []

        try:
            positions = await self.get_positions()

            for symbol in positions.keys():
                result = await self.close_position(symbol, reason)
                results.append(result)

            logger.info(f"Closed {len(results)} positions. Reason: {reason}")

        except Exception as e:
            logger.error(f"Error closing all positions: {e}")

        return results

    def get_performance_metrics(self) -> Dict:
        """Get performance metrics"""
        win_rate = (self.successful_trades / self.total_trades * 100) if self.total_trades > 0 else 0

        return {
            'total_trades': self.total_trades,
            'successful_trades': self.successful_trades,
            'win_rate': win_rate,
            'daily_pnl': self.daily_pnl,
            'current_balance': self.balance,
            'initial_balance': self.initial_balance,
            'total_return': ((self.balance / self.initial_balance) - 1) * 100,
            'active_positions': len(self.positions),
            'active_orders': len(self.active_orders)
        }

    def is_healthy(self) -> bool:
        """Check if execution engine is healthy"""
        try:
            # Check connection for live mode
            if self.mode == ExecutionMode.LIVE:
                return self.is_connected and self.exchange is not None

            # Check balance for paper/simulation
            return self.balance > 0

        except Exception:
            return False

    async def emergency_stop(self) -> Dict:
        """Emergency stop - cancel all orders and close all positions"""
        logger.critical("🚨 EMERGENCY STOP ACTIVATED")

        try:
            # Cancel all orders
            cancel_results = await self.cancel_all_orders("Emergency stop")

            # Close all positions
            close_results = await self.close_all_positions("Emergency stop")

            # Get final metrics
            metrics = self.get_performance_metrics()

            logger.critical("🛑 Emergency stop completed")

            return {
                'success': True,
                'cancelled_orders': len(cancel_results),
                'closed_positions': len(close_results),
                'final_metrics': metrics,
                'timestamp': time.time()
            }

        except Exception as e:
            logger.error(f"Error during emergency stop: {e}")
            return {
                'success': False,
                'error': str(e),
                'timestamp': time.time()
            }
